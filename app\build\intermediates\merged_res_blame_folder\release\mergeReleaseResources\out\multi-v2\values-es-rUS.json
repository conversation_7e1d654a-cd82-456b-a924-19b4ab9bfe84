{"logs": [{"outputFile": "com.ainative.mountainsurvival.app-mergeReleaseResources-22:/values-es-rUS/values-es-rUS.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\daefd50eed0506e326ae247974906cd2\\transformed\\support-compat-28.0.0\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2920", "endColumns": "100", "endOffsets": "3016"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a3ae461f31008f99a87e29f3c99a3c83\\transformed\\appcompat-v7-28.0.0\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,334,442,527,628,751,836,918,1009,1102,1198,1292,1392,1485,1584,1680,1771,1862,1944,2056,2156,2257,2365,2472,2579,2738,2838", "endColumns": "119,108,107,84,100,122,84,81,90,92,95,93,99,92,98,95,90,90,81,111,99,100,107,106,106,158,99,81", "endOffsets": "220,329,437,522,623,746,831,913,1004,1097,1193,1287,1387,1480,1579,1675,1766,1857,1939,2051,2151,2252,2360,2467,2574,2733,2833,2915"}}]}]}