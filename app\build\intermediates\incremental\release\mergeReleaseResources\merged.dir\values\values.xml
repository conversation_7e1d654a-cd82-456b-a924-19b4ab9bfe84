<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:ns1="urn:oasis:names:tc:xliff:document:1.2" xmlns:ns2="http://schemas.android.com/tools">
    <attr format="reference|dimension" name="adSetMaxHeight"/>
    <attr format="reference|dimension" name="adSetMaxWidth"/>
    <attr format="boolean" name="barrierAllowsGoneWidgets"/>
    <attr format="enum" name="barrierDirection">
        <enum name="left" value="0"/>
        <enum name="right" value="1"/>
        <enum name="top" value="2"/>
        <enum name="bottom" value="3"/>
        <enum name="start" value="5"/>
        <enum name="end" value="6"/>
    </attr>
    <attr format="string" name="beizi_adSize"/>
    <attr format="string" name="beizi_adSizes"/>
    <attr format="string" name="beizi_adUnitId"/>
    <attr format="reference" name="bottomAppBarStyle"/>
    <attr format="reference" name="bottomNavigationStyle"/>
    <attr format="reference" name="bottomSheetDialogTheme"/>
    <attr format="reference" name="bottomSheetStyle"/>
    <attr format="reference" name="cardViewStyle"/>
    <attr format="boolean" name="chainUseRtl"/>
    <attr format="reference" name="chipGroupStyle"/>
    <attr format="reference" name="chipStandaloneStyle"/>
    <attr format="reference" name="chipStyle"/>
    <attr format="color" name="colorSecondary"/>
    <attr format="reference" name="constraintSet"/>
    <attr format="string" name="constraint_referenced_ids"/>
    <attr format="reference" name="content"/>
    <attr format="reference" name="coordinatorLayoutStyle"/>
    <attr format="reference" name="drawerArrowStyle"/>
    <attr name="emptyVisibility">
        <enum name="gone" value="0"/>
        <enum name="invisible" value="1"/>
    </attr>
    <attr name="floatingActionButtonStyle"/>
    <attr format="dimension" name="height"/>
    <attr format="reference" name="hideMotionSpec"/>
    <attr format="boolean" name="isLightTheme"/>
    <attr format="boolean" name="layout_constrainedHeight"/>
    <attr format="boolean" name="layout_constrainedWidth"/>
    <attr format="integer" name="layout_constraintBaseline_creator"/>
    <attr format="reference|enum" name="layout_constraintBaseline_toBaselineOf">
        <enum name="parent" value="0"/>
    </attr>
    <attr format="integer" name="layout_constraintBottom_creator"/>
    <attr format="reference|enum" name="layout_constraintBottom_toBottomOf">
        <enum name="parent" value="0"/>
    </attr>
    <attr format="reference|enum" name="layout_constraintBottom_toTopOf">
        <enum name="parent" value="0"/>
    </attr>
    <attr format="reference" name="layout_constraintCircle"/>
    <attr format="integer" name="layout_constraintCircleAngle"/>
    <attr format="dimension" name="layout_constraintCircleRadius"/>
    <attr format="string" name="layout_constraintDimensionRatio"/>
    <attr format="reference|enum" name="layout_constraintEnd_toEndOf">
        <enum name="parent" value="0"/>
    </attr>
    <attr format="reference|enum" name="layout_constraintEnd_toStartOf">
        <enum name="parent" value="0"/>
    </attr>
    <attr format="dimension" name="layout_constraintGuide_begin"/>
    <attr format="dimension" name="layout_constraintGuide_end"/>
    <attr format="float" name="layout_constraintGuide_percent"/>
    <attr name="layout_constraintHeight_default">
        <enum name="spread" value="0"/>
        <enum name="wrap" value="1"/>
        <enum name="percent" value="2"/>
    </attr>
    <attr format="dimension|enum" name="layout_constraintHeight_max">
        <enum name="wrap" value="-2"/>
    </attr>
    <attr format="dimension|enum" name="layout_constraintHeight_min">
        <enum name="wrap" value="-2"/>
    </attr>
    <attr format="float" name="layout_constraintHeight_percent"/>
    <attr format="float" name="layout_constraintHorizontal_bias"/>
    <attr format="enum" name="layout_constraintHorizontal_chainStyle">
        <enum name="spread" value="0"/>
        <enum name="spread_inside" value="1"/>
        <enum name="packed" value="2"/>
    </attr>
    <attr format="float" name="layout_constraintHorizontal_weight"/>
    <attr format="integer" name="layout_constraintLeft_creator"/>
    <attr format="reference|enum" name="layout_constraintLeft_toLeftOf">
        <enum name="parent" value="0"/>
    </attr>
    <attr format="reference|enum" name="layout_constraintLeft_toRightOf">
        <enum name="parent" value="0"/>
    </attr>
    <attr format="integer" name="layout_constraintRight_creator"/>
    <attr format="reference|enum" name="layout_constraintRight_toLeftOf">
        <enum name="parent" value="0"/>
    </attr>
    <attr format="reference|enum" name="layout_constraintRight_toRightOf">
        <enum name="parent" value="0"/>
    </attr>
    <attr format="reference|enum" name="layout_constraintStart_toEndOf">
        <enum name="parent" value="0"/>
    </attr>
    <attr format="reference|enum" name="layout_constraintStart_toStartOf">
        <enum name="parent" value="0"/>
    </attr>
    <attr format="integer" name="layout_constraintTop_creator"/>
    <attr format="reference|enum" name="layout_constraintTop_toBottomOf">
        <enum name="parent" value="0"/>
    </attr>
    <attr format="reference|enum" name="layout_constraintTop_toTopOf">
        <enum name="parent" value="0"/>
    </attr>
    <attr format="float" name="layout_constraintVertical_bias"/>
    <attr format="enum" name="layout_constraintVertical_chainStyle">
        <enum name="spread" value="0"/>
        <enum name="spread_inside" value="1"/>
        <enum name="packed" value="2"/>
    </attr>
    <attr format="float" name="layout_constraintVertical_weight"/>
    <attr name="layout_constraintWidth_default">
        <enum name="spread" value="0"/>
        <enum name="wrap" value="1"/>
        <enum name="percent" value="2"/>
    </attr>
    <attr format="dimension|enum" name="layout_constraintWidth_max">
        <enum name="wrap" value="-2"/>
    </attr>
    <attr format="dimension|enum" name="layout_constraintWidth_min">
        <enum name="wrap" value="-2"/>
    </attr>
    <attr format="float" name="layout_constraintWidth_percent"/>
    <attr format="dimension" name="layout_editor_absoluteX"/>
    <attr format="dimension" name="layout_editor_absoluteY"/>
    <attr format="dimension" name="layout_goneMarginBottom"/>
    <attr format="dimension" name="layout_goneMarginEnd"/>
    <attr format="dimension" name="layout_goneMarginLeft"/>
    <attr format="dimension" name="layout_goneMarginRight"/>
    <attr format="dimension" name="layout_goneMarginStart"/>
    <attr format="dimension" name="layout_goneMarginTop"/>
    <attr name="layout_optimizationLevel">
        <flag name="none" value="0"/>
        <flag name="standard" value="7"/> 
        <flag name="direct" value="1"/>
        <flag name="barrier" value="2"/>
        <flag name="chains" value="4"/>
        <flag name="dimensions" value="8"/>
        <flag name="groups" value="32"/>
    </attr>
    <attr format="boolean" name="load_landing_page_in_background"/>
    <attr format="reference" name="materialButtonStyle"/>
    <attr format="reference" name="materialCardViewStyle"/>
    <attr format="reference" name="navigationViewStyle"/>
    <attr format="color" name="rippleColor"/>
    <attr format="color|reference" name="scrimBackground"/>
    <attr format="reference" name="showMotionSpec"/>
    <attr format="boolean" name="show_loading_indicator"/>
    <attr format="color" name="strokeColor"/>
    <attr format="dimension" name="strokeWidth"/>
    <attr format="reference" name="tabStyle"/>
    <attr format="boolean" name="test"/>
    <attr format="reference" name="textAppearanceBody1"/>
    <attr format="reference" name="textAppearanceBody2"/>
    <attr format="reference" name="textAppearanceButton"/>
    <attr format="reference" name="textAppearanceCaption"/>
    <attr format="reference" name="textAppearanceHeadline1"/>
    <attr format="reference" name="textAppearanceHeadline2"/>
    <attr format="reference" name="textAppearanceHeadline3"/>
    <attr format="reference" name="textAppearanceHeadline4"/>
    <attr format="reference" name="textAppearanceHeadline5"/>
    <attr format="reference" name="textAppearanceHeadline6"/>
    <attr format="reference" name="textAppearanceOverline"/>
    <attr format="reference" name="textAppearanceSubtitle1"/>
    <attr format="reference" name="textAppearanceSubtitle2"/>
    <attr format="reference" name="textInputStyle"/>
    <attr format="string" name="title"/>
    <bool name="abc_action_bar_embed_tabs">true</bool>
    <bool name="abc_allow_stacked_button_bar">false</bool>
    <bool name="abc_config_actionMenuItemAllCaps">true</bool>
    <bool name="mtrl_btn_textappearance_all_caps">true</bool>
    <color name="abc_input_method_navigation_guard">@android:color/black</color>
    <color name="abc_search_url_text_normal">#7fa87f</color>
    <color name="abc_search_url_text_pressed">@android:color/black</color>
    <color name="abc_search_url_text_selected">@android:color/black</color>
    <color name="accent_material_dark">@color/material_deep_teal_200</color>
    <color name="accent_material_light">@color/material_deep_teal_500</color>
    <color name="accent_orange">#E67E22</color>
    <color name="appinfo_tab_selected_color">#3D7BF9</color>
    <color name="appinfo_tab_unselected_color">#C2C3C5</color>
    <color name="background_floating_material_dark">@color/material_grey_800</color>
    <color name="background_floating_material_light">@android:color/white</color>
    <color name="background_light">#ECF0F1</color>
    <color name="background_material_dark">@color/material_grey_850</color>
    <color name="background_material_light">@color/material_grey_50</color>
    <color name="black">#FF000000</color>
    <color name="bright_foreground_disabled_material_dark">#80ffffff</color>
    <color name="bright_foreground_disabled_material_light">#80000000</color>
    <color name="bright_foreground_inverse_material_dark">@color/bright_foreground_material_light</color>
    <color name="bright_foreground_inverse_material_light">@color/bright_foreground_material_dark</color>
    <color name="bright_foreground_material_dark">@android:color/white</color>
    <color name="bright_foreground_material_light">@android:color/black</color>
    <color name="button_material_dark">#ff5a595b</color>
    <color name="button_material_light">#ffd6d7d7</color>
    <color name="cardview_dark_background">#FF424242</color>
    <color name="cardview_light_background">#FFFFFFFF</color>
    <color name="cardview_shadow_end_color">#03000000</color>
    <color name="cardview_shadow_start_color">#37000000</color>
    <color name="design_bottom_navigation_shadow_color">#14000000</color>
    <color name="design_default_color_primary">#3F51B5</color>
    <color name="design_default_color_primary_dark">#303F9F</color>
    <color name="design_fab_shadow_end_color">@android:color/transparent</color>
    <color name="design_fab_shadow_mid_color">#14000000</color>
    <color name="design_fab_shadow_start_color">#44000000</color>
    <color name="design_fab_stroke_end_inner_color">#0A000000</color>
    <color name="design_fab_stroke_end_outer_color">#0F000000</color>
    <color name="design_fab_stroke_top_inner_color">#1AFFFFFF</color>
    <color name="design_fab_stroke_top_outer_color">#2EFFFFFF</color>
    <color name="design_snackbar_background_color">#323232</color>
    <color name="dim_foreground_disabled_material_dark">#80bebebe</color>
    <color name="dim_foreground_disabled_material_light">#80323232</color>
    <color name="dim_foreground_material_dark">#ffbebebe</color>
    <color name="dim_foreground_material_light">#ff323232</color>
    <color name="ec_store_window_background">#FFF5F6F9</color>
    <color name="error_color_material_dark">#ff7043</color>
    <color name="error_color_material_light">#ff5722</color>
    <color name="firewood_color">#8B4513</color>
    <color name="food_color">#C0392B</color>
    <color name="foreground_material_dark">@android:color/white</color>
    <color name="foreground_material_light">@android:color/black</color>
    <color name="highlighted_text_material_dark">#6680cbc4</color>
    <color name="highlighted_text_material_light">#66009688</color>
    <color name="ic_launcher_background">#114089</color>
    <color name="ksad_88_white">#88FFFFFF</color>
    <color name="ksad_99_black">#99000000</color>
    <color name="ksad_99_white">#99FFFFFF</color>
    <color name="ksad_black_6c">#FF6C6C6C</color>
    <color name="ksad_black_alpha100">#ff000000</color>
    <color name="ksad_black_alpha20">#33000000</color>
    <color name="ksad_black_alpha35">#23000000</color>
    <color name="ksad_black_alpha50">#80000000</color>
    <color name="ksad_default_dialog_bg_color">#4D666666</color>
    <color name="ksad_default_privacy_link_color">#FFFFFF</color>
    <color name="ksad_default_shake_btn_bg_color">#66000000</color>
    <color name="ksad_feed_main_color">#FE3666</color>
    <color name="ksad_for_liteapi">#FF000000</color>
    <color name="ksad_gray_9c">#FF9C9C9C</color>
    <color name="ksad_jinniu_end_origin_color">#FF9C9C9C</color>
    <color name="ksad_no_title_common_dialog_negativebtn_color">#666666</color>
    <color name="ksad_no_title_common_dialog_positivebtn_color">#5181FB</color>
    <color name="ksad_play_again_horizontal_bg">#B3000000</color>
    <color name="ksad_play_again_horizontal_bg_light">#4D000000</color>
    <color name="ksad_playable_pre_tips_icon_bg">#33FFFFFF</color>
    <color name="ksad_reward_main_color">#FFFE3666</color>
    <color name="ksad_reward_original_price">#FF666666</color>
    <color name="ksad_reward_undone_color">#FF9C9C9C</color>
    <color name="ksad_secondary_btn_color">#1AFE3666</color>
    <color name="ksad_shake_icon_bg_start_color">#66000000</color>
    <color name="ksad_splash_endcard_appdesc_color">#FFFFFF</color>
    <color name="ksad_splash_endcard_appversion_color">#B9B9B9</color>
    <color name="ksad_splash_endcard_bg_color">#99000000</color>
    <color name="ksad_splash_endcard_developer_color">#66FFFFFF</color>
    <color name="ksad_splash_endcard_name_color">#222222</color>
    <color name="ksad_splash_endcard_ompliance_color">#99FFFFFF</color>
    <color name="ksad_splash_endcard_title_color">#9A0009</color>
    <color name="ksad_text_black_222">#222222</color>
    <color name="ksad_translucent">#00000000</color>
    <color name="ksad_white">#FFFFFF</color>
    <color name="ksad_white_alpha_20">#33FFFFFF</color>
    <color name="material_blue_grey_800">#ff37474f</color>
    <color name="material_blue_grey_900">#ff263238</color>
    <color name="material_blue_grey_950">#ff21272b</color>
    <color name="material_deep_teal_200">#ff80cbc4</color>
    <color name="material_deep_teal_500">#ff009688</color>
    <color name="material_grey_100">#fff5f5f5</color>
    <color name="material_grey_300">#ffe0e0e0</color>
    <color name="material_grey_50">#fffafafa</color>
    <color name="material_grey_600">#ff757575</color>
    <color name="material_grey_800">#ff424242</color>
    <color name="material_grey_850">#ff303030</color>
    <color name="material_grey_900">#ff212121</color>
    <color name="mtrl_btn_bg_color_disabled">#1F000000</color>
    <color name="mtrl_btn_text_color_disabled">#61000000</color>
    <color name="mtrl_btn_transparent_bg_color">#00ffffff</color>
    <color name="mtrl_scrim_color">#52000000</color>
    <color name="mtrl_textinput_default_box_stroke_color">#6B000000</color>
    <color name="mtrl_textinput_disabled_color">#1F000000</color>
    <color name="mtrl_textinput_filled_box_default_background_color">#0A000000</color>
    <color name="mtrl_textinput_hovered_box_stroke_color">#DE000000</color>
    <color name="notification_action_color_filter">#ffffffff</color>
    <color name="notification_icon_bg_color">#ff9e9e9e</color>
    <color name="notification_material_background_media_default_color">#ff424242</color>
    <color name="oset_colorAccent">#FF4081</color>
    <color name="oset_colorPrimary">#3F51B5</color>
    <color name="oset_colorPrimaryDark">#303F9F</color>
    <color name="oset_colorWhite">#FFFFFF</color>
    <color name="oset_progress_red">#F35A49</color>
    <color name="oset_text_select">#5179e9</color>
    <color name="oset_text_type_select">#FF4500</color>
    <color name="oset_text_type_unselect">#3E454E</color>
    <color name="oset_text_unselect">#96979a</color>
    <color name="oset_weather_theme">#4662EA</color>
    <color name="oset_yd_video_bg">#f5f5f5</color>
    <color name="primary_blue">#2C3E50</color>
    <color name="primary_blue_dark">#1A252F</color>
    <color name="primary_dark_material_dark">@android:color/black</color>
    <color name="primary_dark_material_light">@color/material_grey_600</color>
    <color name="primary_material_dark">@color/material_grey_900</color>
    <color name="primary_material_light">@color/material_grey_100</color>
    <color name="primary_text_default_material_dark">#ffffffff</color>
    <color name="primary_text_default_material_light">#de000000</color>
    <color name="primary_text_disabled_material_dark">#4Dffffff</color>
    <color name="primary_text_disabled_material_light">#39000000</color>
    <color name="purple_200">#FFBB86FC</color>
    <color name="purple_500">#FF6200EE</color>
    <color name="purple_700">#FF3700B3</color>
    <color name="ripple_material_dark">#33ffffff</color>
    <color name="ripple_material_light">#1f000000</color>
    <color name="secondary_text_default_material_dark">#b3ffffff</color>
    <color name="secondary_text_default_material_light">#8a000000</color>
    <color name="secondary_text_disabled_material_dark">#36ffffff</color>
    <color name="secondary_text_disabled_material_light">#24000000</color>
    <color name="stamina_color">#27AE60</color>
    <color name="switch_thumb_disabled_material_dark">#ff616161</color>
    <color name="switch_thumb_disabled_material_light">#ffbdbdbd</color>
    <color name="switch_thumb_normal_material_dark">#ffbdbdbd</color>
    <color name="switch_thumb_normal_material_light">#fff1f1f1</color>
    <color name="teal_200">#FF03DAC5</color>
    <color name="teal_700">#FF018786</color>
    <color name="text_primary">#2C3E50</color>
    <color name="text_secondary">#95A5A6</color>
    <color name="tooltip_background_dark">#e6616161</color>
    <color name="tooltip_background_light">#e6FFFFFF</color>
    <color name="tt_adx_logo_des_bg">#1F161823</color>
    <color name="tt_adx_logo_desc">#BFFFFFFF</color>
    <color name="tt_app_detail_line_bg">#66161823</color>
    <color name="tt_app_detail_privacy_text_bg">#80161823</color>
    <color name="tt_app_detail_stroke_bg">#EDEDED</color>
    <color name="tt_app_tag_background">#0F161823</color>
    <color name="tt_app_tag_text_color">#161823</color>
    <color name="tt_appdownloader_notification_material_background_color">#fffafafa</color>
    <color name="tt_appdownloader_notification_title_color">#7f0b0198</color>
    <color name="tt_appdownloader_s1">#333333</color>
    <color name="tt_appdownloader_s13">#cccccc</color>
    <color name="tt_appdownloader_s18">#eeeeee</color>
    <color name="tt_appdownloader_s4">#ff819f</color>
    <color name="tt_appdownloader_s8">#ffffff</color>
    <color name="tt_cancle_bg">#FDDCDCDC</color>
    <color name="tt_dislike_dialog_background">#161923</color>
    <color name="tt_dislike_transparent">#80000000</color>
    <color name="tt_divider">#DCDCDC</color>
    <color name="tt_download_app_name">#333333</color>
    <color name="tt_download_bar_background">#C21E1E1E</color>
    <color name="tt_download_bar_background_new">#E4FFFFFF</color>
    <color name="tt_download_text_background">#4A90E2</color>
    <color name="tt_draw_btn_back">#D33F57</color>
    <color name="tt_full_background">#000000</color>
    <color name="tt_full_interaction_bar_background">#26000000</color>
    <color name="tt_full_interaction_dialog_background">#88000000</color>
    <color name="tt_full_screen_skip_bg">#904A4A4A</color>
    <color name="tt_full_status_bar_color">#000000</color>
    <color name="tt_header_font">#FF9B9B9B</color>
    <color name="tt_heise3">#999999</color>
    <color name="tt_listview">#FDFFFFFF</color>
    <color name="tt_listview_press">#FDE6E6E6</color>
    <color name="tt_mediation_transparent">#00000000</color>
    <color name="tt_rating_comment">#999999</color>
    <color name="tt_rating_comment_vertical">#4A4A4A</color>
    <color name="tt_rating_star">#F5A623</color>
    <color name="tt_reward_live_dialog_bg">#66000000</color>
    <color name="tt_reward_slide_up_bg">#50000000</color>
    <color name="tt_skip_red">#FFDF112A</color>
    <color name="tt_splash_click_bar_text_shadow">#4D000000</color>
    <color name="tt_ssxinbaise4">@color/tt_white</color>
    <color name="tt_ssxinbaise4_press">#7fffffff</color>
    <color name="tt_ssxinheihui3">#999999</color>
    <color name="tt_ssxinhongse1">#f85959</color>
    <color name="tt_ssxinmian1">#e0e0e0</color>
    <color name="tt_ssxinmian11">#e5000000</color>
    <color name="tt_ssxinmian15">#7f000000</color>
    <color name="tt_ssxinmian6">@color/tt_ssxinheihui3</color>
    <color name="tt_ssxinmian7">@color/tt_ssxinhongse1</color>
    <color name="tt_ssxinmian8">#2a90d7</color>
    <color name="tt_ssxinxian11">@color/tt_ssxinbaise4</color>
    <color name="tt_ssxinxian11_selected">@color/tt_ssxinbaise4_press</color>
    <color name="tt_ssxinxian3">#2a90d7</color>
    <color name="tt_ssxinxian3_press">#7f2a90d7</color>
    <color name="tt_ssxinzi12">@color/tt_white</color>
    <color name="tt_ssxinzi15">@color/tt_ssxinbaise4</color>
    <color name="tt_ssxinzi4">@color/tt_ssxinhongse1</color>
    <color name="tt_ssxinzi9">#cacaca</color>
    <color name="tt_text_font">#FF4A4A4A</color>
    <color name="tt_titlebar_background_dark">#46000000</color>
    <color name="tt_titlebar_background_ffffff">#ffffff</color>
    <color name="tt_titlebar_background_light">#e0e0e0</color>
    <color name="tt_trans_black">#00000000</color>
    <color name="tt_trans_half_black">#FF1A1A1A</color>
    <color name="tt_transparent">#00000000</color>
    <color name="tt_video_player_text">@color/tt_white</color>
    <color name="tt_video_player_text_withoutnight">@color/tt_white</color>
    <color name="tt_video_shadow_color">#72000000</color>
    <color name="tt_video_shaoow_color_fullscreen">#B2000000</color>
    <color name="tt_video_time_color">#ffffff</color>
    <color name="tt_video_traffic_tip_background_color">#000000</color>
    <color name="tt_video_transparent">#1E000000</color>
    <color name="tt_white">#ffffff</color>
    <color name="ttdownloader_transparent">#00000000</color>
    <color name="view_divider_bg">#E8E9EB</color>
    <color name="warmth_color">#E67E22</color>
    <color name="white">#FFFFFFFF</color>
    <dimen name="abc_action_bar_content_inset_material">16dp</dimen>
    <dimen name="abc_action_bar_content_inset_with_nav">72dp</dimen>
    <dimen name="abc_action_bar_default_height_material">56dp</dimen>
    <dimen name="abc_action_bar_default_padding_end_material">0dp</dimen>
    <dimen name="abc_action_bar_default_padding_start_material">0dp</dimen>
    <dimen name="abc_action_bar_elevation_material">4dp</dimen>
    <dimen name="abc_action_bar_icon_vertical_padding_material">16dp</dimen>
    <dimen name="abc_action_bar_overflow_padding_end_material">10dp</dimen>
    <dimen name="abc_action_bar_overflow_padding_start_material">6dp</dimen>
    <dimen name="abc_action_bar_stacked_max_height">48dp</dimen>
    <dimen name="abc_action_bar_stacked_tab_max_width">180dp</dimen>
    <dimen name="abc_action_bar_subtitle_bottom_margin_material">5dp</dimen>
    <dimen name="abc_action_bar_subtitle_top_margin_material">-3dp</dimen>
    <dimen name="abc_action_button_min_height_material">48dp</dimen>
    <dimen name="abc_action_button_min_width_material">48dp</dimen>
    <dimen name="abc_action_button_min_width_overflow_material">36dp</dimen>
    <dimen name="abc_alert_dialog_button_bar_height">48dp</dimen>
    <dimen name="abc_alert_dialog_button_dimen">48dp</dimen>
    <dimen name="abc_button_inset_horizontal_material">@dimen/abc_control_inset_material</dimen>
    <dimen name="abc_button_inset_vertical_material">6dp</dimen>
    <dimen name="abc_button_padding_horizontal_material">8dp</dimen>
    <dimen name="abc_button_padding_vertical_material">@dimen/abc_control_padding_material</dimen>
    <dimen name="abc_cascading_menus_min_smallest_width">720dp</dimen>
    <dimen name="abc_config_prefDialogWidth">320dp</dimen>
    <dimen name="abc_control_corner_material">2dp</dimen>
    <dimen name="abc_control_inset_material">4dp</dimen>
    <dimen name="abc_control_padding_material">4dp</dimen>
    <dimen name="abc_dialog_corner_radius_material">2dp</dimen>
    <item name="abc_dialog_fixed_height_major" type="dimen">80%</item>
    <item name="abc_dialog_fixed_height_minor" type="dimen">100%</item>
    <item name="abc_dialog_fixed_width_major" type="dimen">320dp</item>
    <item name="abc_dialog_fixed_width_minor" type="dimen">320dp</item>
    <dimen name="abc_dialog_list_padding_bottom_no_buttons">8dp</dimen>
    <dimen name="abc_dialog_list_padding_top_no_title">8dp</dimen>
    <item name="abc_dialog_min_width_major" type="dimen">65%</item>
    <item name="abc_dialog_min_width_minor" type="dimen">95%</item>
    <dimen name="abc_dialog_padding_material">24dp</dimen>
    <dimen name="abc_dialog_padding_top_material">18dp</dimen>
    <dimen name="abc_dialog_title_divider_material">8dp</dimen>
    <item format="float" name="abc_disabled_alpha_material_dark" type="dimen">0.30</item>
    <item format="float" name="abc_disabled_alpha_material_light" type="dimen">0.26</item>
    <dimen name="abc_dropdownitem_icon_width">32dip</dimen>
    <dimen name="abc_dropdownitem_text_padding_left">8dip</dimen>
    <dimen name="abc_dropdownitem_text_padding_right">8dip</dimen>
    <dimen name="abc_edit_text_inset_bottom_material">7dp</dimen>
    <dimen name="abc_edit_text_inset_horizontal_material">4dp</dimen>
    <dimen name="abc_edit_text_inset_top_material">10dp</dimen>
    <dimen name="abc_floating_window_z">16dp</dimen>
    <dimen name="abc_list_item_padding_horizontal_material">@dimen/abc_action_bar_content_inset_material</dimen>
    <dimen name="abc_panel_menu_list_width">296dp</dimen>
    <dimen name="abc_progress_bar_height_material">4dp</dimen>
    <dimen name="abc_search_view_preferred_height">48dip</dimen>
    <dimen name="abc_search_view_preferred_width">320dip</dimen>
    <dimen name="abc_seekbar_track_background_height_material">2dp</dimen>
    <dimen name="abc_seekbar_track_progress_height_material">2dp</dimen>
    <dimen name="abc_select_dialog_padding_start_material">20dp</dimen>
    <dimen name="abc_switch_padding">3dp</dimen>
    <dimen name="abc_text_size_body_1_material">14sp</dimen>
    <dimen name="abc_text_size_body_2_material">14sp</dimen>
    <dimen name="abc_text_size_button_material">14sp</dimen>
    <dimen name="abc_text_size_caption_material">12sp</dimen>
    <dimen name="abc_text_size_display_1_material">34sp</dimen>
    <dimen name="abc_text_size_display_2_material">45sp</dimen>
    <dimen name="abc_text_size_display_3_material">56sp</dimen>
    <dimen name="abc_text_size_display_4_material">112sp</dimen>
    <dimen name="abc_text_size_headline_material">24sp</dimen>
    <dimen name="abc_text_size_large_material">22sp</dimen>
    <dimen name="abc_text_size_medium_material">18sp</dimen>
    <dimen name="abc_text_size_menu_header_material">14sp</dimen>
    <dimen name="abc_text_size_menu_material">16sp</dimen>
    <dimen name="abc_text_size_small_material">14sp</dimen>
    <dimen name="abc_text_size_subhead_material">16sp</dimen>
    <dimen name="abc_text_size_subtitle_material_toolbar">16dp</dimen>
    <dimen name="abc_text_size_title_material">20sp</dimen>
    <dimen name="abc_text_size_title_material_toolbar">20dp</dimen>
    <dimen name="cardview_compat_inset_shadow">1dp</dimen>
    <dimen name="cardview_default_elevation">2dp</dimen>
    <dimen name="cardview_default_radius">2dp</dimen>
    <dimen name="compat_button_inset_horizontal_material">4dp</dimen>
    <dimen name="compat_button_inset_vertical_material">6dp</dimen>
    <dimen name="compat_button_padding_horizontal_material">8dp</dimen>
    <dimen name="compat_button_padding_vertical_material">4dp</dimen>
    <dimen name="compat_control_corner_material">2dp</dimen>
    <dimen name="compat_notification_large_icon_max_height">320dp</dimen>
    <dimen name="compat_notification_large_icon_max_width">320dp</dimen>
    <dimen name="design_appbar_elevation">4dp</dimen>
    <dimen name="design_bottom_navigation_active_item_max_width">168dp</dimen>
    <dimen name="design_bottom_navigation_active_item_min_width">96dp</dimen>
    <dimen name="design_bottom_navigation_active_text_size">14sp</dimen>
    <dimen name="design_bottom_navigation_elevation">8dp</dimen>
    <dimen name="design_bottom_navigation_height">56dp</dimen>
    <dimen name="design_bottom_navigation_icon_size">24dp</dimen>
    <dimen name="design_bottom_navigation_item_max_width">96dp</dimen>
    <dimen name="design_bottom_navigation_item_min_width">56dp</dimen>
    <dimen name="design_bottom_navigation_margin">8dp</dimen>
    <dimen name="design_bottom_navigation_shadow_height">1dp</dimen>
    <dimen name="design_bottom_navigation_text_size">12sp</dimen>
    <dimen name="design_bottom_sheet_modal_elevation">16dp</dimen>
    <dimen name="design_bottom_sheet_peek_height_min">64dp</dimen>
    <dimen name="design_fab_border_width">0.5dp</dimen>
    <dimen name="design_fab_elevation">6dp</dimen>
    <dimen name="design_fab_image_size">24dp</dimen>
    <dimen name="design_fab_size_mini">40dp</dimen>
    <dimen name="design_fab_size_normal">56dp</dimen>
    <dimen name="design_fab_translation_z_hovered_focused">6dp</dimen>
    <dimen name="design_fab_translation_z_pressed">6dp</dimen>
    <dimen name="design_navigation_elevation">16dp</dimen>
    <dimen name="design_navigation_icon_padding">32dp</dimen>
    <dimen name="design_navigation_icon_size">24dp</dimen>
    <dimen name="design_navigation_item_horizontal_padding">16dp</dimen>
    <dimen name="design_navigation_item_icon_padding">32dp</dimen>
    <dimen name="design_navigation_max_width">280dp</dimen>
    <dimen name="design_navigation_padding_bottom">8dp</dimen>
    <dimen name="design_navigation_separator_vertical_padding">8dp</dimen>
    <dimen name="design_snackbar_action_inline_max_width">128dp</dimen>
    <dimen name="design_snackbar_background_corner_radius">0dp</dimen>
    <dimen name="design_snackbar_elevation">6dp</dimen>
    <dimen name="design_snackbar_extra_spacing_horizontal">0dp</dimen>
    <dimen name="design_snackbar_max_width">-1px</dimen>
    <dimen name="design_snackbar_min_width">-1px</dimen>
    <dimen name="design_snackbar_padding_horizontal">12dp</dimen>
    <dimen name="design_snackbar_padding_vertical">14dp</dimen>
    <dimen name="design_snackbar_padding_vertical_2lines">24dp</dimen>
    <dimen name="design_snackbar_text_size">14sp</dimen>
    <dimen name="design_tab_max_width">264dp</dimen>
    <dimen name="design_tab_scrollable_min_width">72dp</dimen>
    <dimen name="design_tab_text_size">14sp</dimen>
    <dimen name="design_tab_text_size_2line">12sp</dimen>
    <dimen name="design_textinput_caption_translate_y">5dp</dimen>
    <item format="float" name="disabled_alpha_material_dark" type="dimen">0.30</item>
    <item format="float" name="disabled_alpha_material_light" type="dimen">0.26</item>
    <dimen name="fastscroll_default_thickness">8dp</dimen>
    <dimen name="fastscroll_margin">0dp</dimen>
    <dimen name="fastscroll_minimum_range">50dp</dimen>
    <item format="float" name="highlight_alpha_material_colored" type="dimen">0.26</item>
    <item format="float" name="highlight_alpha_material_dark" type="dimen">0.20</item>
    <item format="float" name="highlight_alpha_material_light" type="dimen">0.12</item>
    <item format="float" name="hint_alpha_material_dark" type="dimen">0.50</item>
    <item format="float" name="hint_alpha_material_light" type="dimen">0.38</item>
    <item format="float" name="hint_pressed_alpha_material_dark" type="dimen">0.70</item>
    <item format="float" name="hint_pressed_alpha_material_light" type="dimen">0.54</item>
    <dimen name="item_touch_helper_max_drag_scroll_per_frame">20dp</dimen>
    <dimen name="item_touch_helper_swipe_escape_max_velocity">800dp</dimen>
    <dimen name="item_touch_helper_swipe_escape_velocity">120dp</dimen>
    <dimen name="ksad_action_bar_height">44dp</dimen>
    <dimen name="ksad_activity_title_bar_height">44dp</dimen>
    <dimen name="ksad_coupon_dialog_height">304dp</dimen>
    <dimen name="ksad_coupon_dialog_value_prefix_text_size">32sp</dimen>
    <dimen name="ksad_coupon_dialog_width">263dp</dimen>
    <dimen name="ksad_draw_author_end_icon_width">100dp</dimen>
    <dimen name="ksad_draw_author_icon_stroke_width">1dp</dimen>
    <dimen name="ksad_draw_author_icon_width">70dp</dimen>
    <dimen name="ksad_fullscreen_shake_center_hand_size">46dp</dimen>
    <dimen name="ksad_fullscreen_shake_center_icon_size">92dp</dimen>
    <dimen name="ksad_fullscreen_shake_center_tips_height">30dp</dimen>
    <dimen name="ksad_fullscreen_shake_center_tips_start_width">40dp</dimen>
    <dimen name="ksad_fullscreen_shake_center_tips_width">90dp</dimen>
    <dimen name="ksad_fullscreen_shake_tips_height">42dp</dimen>
    <dimen name="ksad_fullscreen_shake_tips_icon_live_shop_marginBottom">280dp</dimen>
    <dimen name="ksad_fullscreen_shake_tips_icon_marginBottom">185dp</dimen>
    <dimen name="ksad_fullscreen_shake_tips_icon_marginLeft">39dp</dimen>
    <dimen name="ksad_fullscreen_shake_tips_icon_padding">7dp</dimen>
    <dimen name="ksad_fullscreen_shake_tips_icon_size">28dp</dimen>
    <dimen name="ksad_fullscreen_shake_tips_icon_stroke_size">1dp</dimen>
    <dimen name="ksad_fullscreen_shake_tips_title_live_shop_marginBottom">259dp</dimen>
    <dimen name="ksad_fullscreen_shake_tips_title_marginBottom">164dp</dimen>
    <dimen name="ksad_fullscreen_shake_tips_width">90dp</dimen>
    <dimen name="ksad_hand_slide_hand_height">63dp</dimen>
    <dimen name="ksad_hand_slide_height">191dp</dimen>
    <dimen name="ksad_hand_slide_tail_height_end">170dp</dimen>
    <dimen name="ksad_hand_slide_tail_height_start">36dp</dimen>
    <dimen name="ksad_hand_slide_tail_shadow_width">20dp</dimen>
    <dimen name="ksad_hand_slide_tail_width">36dp</dimen>
    <dimen name="ksad_hand_slide_up">128dp</dimen>
    <dimen name="ksad_hand_slide_width">76dp</dimen>
    <dimen name="ksad_image_player_sweep_wave_height_end">345dp</dimen>
    <dimen name="ksad_image_player_sweep_wave_height_start">138dp</dimen>
    <dimen name="ksad_image_player_sweep_wave_width_end">700dp</dimen>
    <dimen name="ksad_image_player_sweep_wave_width_start">259dp</dimen>
    <dimen name="ksad_install_tips_bottom_height">100dp</dimen>
    <dimen name="ksad_install_tips_bottom_margin_bottom">16dp</dimen>
    <dimen name="ksad_install_tips_bottom_margin_left">36dp</dimen>
    <dimen name="ksad_install_tips_card_elevation">2dp</dimen>
    <dimen name="ksad_install_tips_card_height">80dp</dimen>
    <dimen name="ksad_install_tips_card_margin">8dp</dimen>
    <dimen name="ksad_install_tips_card_padding_left">10dp</dimen>
    <dimen name="ksad_install_tips_card_padding_right">12dp</dimen>
    <dimen name="ksad_interstitial_card_radius">8dp</dimen>
    <dimen name="ksad_interstitial_download_bar_height">70dp</dimen>
    <dimen name="ksad_interstitial_icon_radius">4dp</dimen>
    <dimen name="ksad_jinniu_light_sweep_margin_left">-72dp</dimen>
    <dimen name="ksad_jinniu_light_sweep_width">72dp</dimen>
    <dimen name="ksad_live_base_card_full_height">180dp</dimen>
    <dimen name="ksad_live_card_tips_animation_y">44dp</dimen>
    <dimen name="ksad_live_card_tips_height">28dp</dimen>
    <dimen name="ksad_live_card_tips_margin_bottom">-28dp</dimen>
    <dimen name="ksad_live_card_tips_margin_left">16dp</dimen>
    <dimen name="ksad_live_origin_dialog_height">338dp</dimen>
    <dimen name="ksad_live_shop_card_full_height">88dp</dimen>
    <dimen name="ksad_live_subscribe_card_count_area_margin_top">42dp</dimen>
    <dimen name="ksad_live_subscribe_card_count_area_trans_y">-42dp</dimen>
    <dimen name="ksad_live_subscribe_card_follower_avatar_size">20dp</dimen>
    <dimen name="ksad_live_subscribe_card_full_height">121dp</dimen>
    <dimen name="ksad_live_subscribe_card_height">84dp</dimen>
    <dimen name="ksad_live_subscribe_card_logo_margin_bottom">110dp</dimen>
    <dimen name="ksad_live_subscribe_card_margin">16dp</dimen>
    <dimen name="ksad_live_subscribe_card_width_horizontal">343dp</dimen>
    <dimen name="ksad_live_subscribe_dialog_height">320dp</dimen>
    <dimen name="ksad_live_subscribe_dialog_icon_size">56dp</dimen>
    <dimen name="ksad_live_subscribe_dialog_width">271dp</dimen>
    <dimen name="ksad_live_subscribe_end_dialog_height">249dp</dimen>
    <dimen name="ksad_live_subscribe_end_dialog_icon_size">82dp</dimen>
    <dimen name="ksad_live_subscribe_end_dialog_width">311dp</dimen>
    <dimen name="ksad_play_again_dialog_btn_height">48dp</dimen>
    <dimen name="ksad_play_again_end_icon_size">80dp</dimen>
    <dimen name="ksad_play_again_end_icon_size_horizontal">54dp</dimen>
    <dimen name="ksad_playable_action_btn_height">40dp</dimen>
    <dimen name="ksad_playable_end_btn_margin_top">32dp</dimen>
    <dimen name="ksad_playable_end_btn_margin_top_small">24dp</dimen>
    <dimen name="ksad_playable_end_content_width">263dp</dimen>
    <dimen name="ksad_playable_end_desc_margin_top">16dp</dimen>
    <dimen name="ksad_playable_end_desc_margin_top_small">12dp</dimen>
    <dimen name="ksad_reward_apk_info_card_actionbar_text_size">15dp</dimen>
    <dimen name="ksad_reward_apk_info_card_height">156dp</dimen>
    <dimen name="ksad_reward_apk_info_card_icon_size">58dp</dimen>
    <dimen name="ksad_reward_apk_info_card_margin">16dp</dimen>
    <dimen name="ksad_reward_apk_info_card_step_area_height">79dp</dimen>
    <dimen name="ksad_reward_apk_info_card_step_divider_height">16dp</dimen>
    <dimen name="ksad_reward_apk_info_card_step_icon_radius">12dp</dimen>
    <dimen name="ksad_reward_apk_info_card_step_icon_size">12dp</dimen>
    <dimen name="ksad_reward_apk_info_card_step_icon_text_size">8sp</dimen>
    <dimen name="ksad_reward_apk_info_card_tags_height">18dp</dimen>
    <dimen name="ksad_reward_apk_info_card_width">343dp</dimen>
    <dimen name="ksad_reward_author_height">97dp</dimen>
    <dimen name="ksad_reward_author_icon_anim_start">62dp</dimen>
    <dimen name="ksad_reward_author_icon_inner_width">62dp</dimen>
    <dimen name="ksad_reward_author_icon_stroke_width">1dp</dimen>
    <dimen name="ksad_reward_author_icon_width">70dp</dimen>
    <dimen name="ksad_reward_author_width">70dp</dimen>
    <dimen name="ksad_reward_follow_author_icon_margin_bottom">242dp</dimen>
    <dimen name="ksad_reward_follow_card_height">64dp</dimen>
    <dimen name="ksad_reward_follow_card_margin">12dp</dimen>
    <dimen name="ksad_reward_follow_card_width_horizontal">351dp</dimen>
    <dimen name="ksad_reward_follow_dialog_card_height">222dp</dimen>
    <dimen name="ksad_reward_follow_dialog_height">243dp</dimen>
    <dimen name="ksad_reward_follow_dialog_icon_size">80dp</dimen>
    <dimen name="ksad_reward_follow_dialog_width">280dp</dimen>
    <dimen name="ksad_reward_follow_end_card_height">247dp</dimen>
    <dimen name="ksad_reward_follow_end_height">318dp</dimen>
    <dimen name="ksad_reward_follow_end_width">280dp</dimen>
    <dimen name="ksad_reward_follow_logo_margin_bottom">87dp</dimen>
    <dimen name="ksad_reward_followed_card_height">64dp</dimen>
    <dimen name="ksad_reward_followed_card_width">216dp</dimen>
    <dimen name="ksad_reward_jinniu_card_btn_height">40dp</dimen>
    <dimen name="ksad_reward_jinniu_card_height">105dp</dimen>
    <dimen name="ksad_reward_jinniu_card_height_full">155dp</dimen>
    <dimen name="ksad_reward_jinniu_card_icon_size">85dp</dimen>
    <dimen name="ksad_reward_jinniu_card_margin">12dp</dimen>
    <dimen name="ksad_reward_jinniu_card_padding">10dp</dimen>
    <dimen name="ksad_reward_jinniu_dialog_close_size">30dp</dimen>
    <dimen name="ksad_reward_jinniu_dialog_height">334dp</dimen>
    <dimen name="ksad_reward_jinniu_dialog_icon_size">58dp</dimen>
    <dimen name="ksad_reward_jinniu_dialog_width">286dp</dimen>
    <dimen name="ksad_reward_jinniu_end_height">344dp</dimen>
    <dimen name="ksad_reward_jinniu_end_icon_size">100dp</dimen>
    <dimen name="ksad_reward_jinniu_end_max_width">355dp</dimen>
    <dimen name="ksad_reward_jinniu_end_origin_text_size">12sp</dimen>
    <dimen name="ksad_reward_jinniu_logo_margin_bottom">125dp</dimen>
    <dimen name="ksad_reward_js_actionbar_height">120dp</dimen>
    <dimen name="ksad_reward_middle_end_card_logo_view_height">16dp</dimen>
    <dimen name="ksad_reward_middle_end_card_logo_view_margin_bottom">10dp</dimen>
    <dimen name="ksad_reward_native_normal_actionbar_height">90dp</dimen>
    <dimen name="ksad_reward_order_card_coupon_height">18dp</dimen>
    <dimen name="ksad_reward_order_card_height">102dp</dimen>
    <dimen name="ksad_reward_order_card_icon_size">82dp</dimen>
    <dimen name="ksad_reward_order_card_margin">12dp</dimen>
    <dimen name="ksad_reward_order_card_padding">10dp</dimen>
    <dimen name="ksad_reward_order_coupon_divider">4dp</dimen>
    <dimen name="ksad_reward_order_dialog_height">242dp</dimen>
    <dimen name="ksad_reward_order_dialog_icon_size">60dp</dimen>
    <dimen name="ksad_reward_order_dialog_width">280dp</dimen>
    <dimen name="ksad_reward_order_end_dialog_height">236dp</dimen>
    <dimen name="ksad_reward_order_end_dialog_width">280dp</dimen>
    <dimen name="ksad_reward_order_logo_margin_bottom">123dp</dimen>
    <dimen name="ksad_reward_order_original_price_size">12sp</dimen>
    <dimen name="ksad_reward_order_price_size">15sp</dimen>
    <dimen name="ksad_reward_playable_pre_tips_default_margin_bottom">40dp</dimen>
    <dimen name="ksad_reward_playable_pre_tips_height">40dp</dimen>
    <dimen name="ksad_reward_playable_pre_tips_icon_padding">5dp</dimen>
    <dimen name="ksad_reward_playable_pre_tips_icon_size">34dp</dimen>
    <dimen name="ksad_reward_playable_pre_tips_margin_bottom">16dp</dimen>
    <dimen name="ksad_reward_playable_pre_tips_margin_bottom_without_actionbar">43dp</dimen>
    <dimen name="ksad_reward_playable_pre_tips_margin_right">-71dp</dimen>
    <dimen name="ksad_reward_playable_pre_tips_transx">-70dp</dimen>
    <dimen name="ksad_reward_playable_pre_tips_width">117dp</dimen>
    <dimen name="ksad_reward_shake_center_hand_size">46dp</dimen>
    <dimen name="ksad_reward_shake_center_icon_size">92dp</dimen>
    <dimen name="ksad_reward_shake_center_tips_height">30dp</dimen>
    <dimen name="ksad_reward_shake_center_tips_start_width">40dp</dimen>
    <dimen name="ksad_reward_shake_center_tips_width">90dp</dimen>
    <dimen name="ksad_reward_shake_tips_height">42dp</dimen>
    <dimen name="ksad_reward_shake_tips_icon_live_shop_marginBottom">280dp</dimen>
    <dimen name="ksad_reward_shake_tips_icon_marginBottom">185dp</dimen>
    <dimen name="ksad_reward_shake_tips_icon_marginLeft">39dp</dimen>
    <dimen name="ksad_reward_shake_tips_icon_padding">7dp</dimen>
    <dimen name="ksad_reward_shake_tips_icon_size">28dp</dimen>
    <dimen name="ksad_reward_shake_tips_icon_stroke_size">1dp</dimen>
    <dimen name="ksad_reward_shake_tips_title_live_shop_marginBottom">259dp</dimen>
    <dimen name="ksad_reward_shake_tips_title_marginBottom">164dp</dimen>
    <dimen name="ksad_reward_shake_tips_width">90dp</dimen>
    <dimen name="ksad_reward_task_dialog_height">264dp</dimen>
    <dimen name="ksad_reward_task_dialog_width">290dp</dimen>
    <dimen name="ksad_seek_bar_progress_text_margin">10dp</dimen>
    <dimen name="ksad_skip_view_divider_height">12dp</dimen>
    <dimen name="ksad_skip_view_divider_margin_horizontal">8dp</dimen>
    <dimen name="ksad_skip_view_divider_margin_left">52dp</dimen>
    <dimen name="ksad_skip_view_divider_margin_vertical">10dp</dimen>
    <dimen name="ksad_skip_view_divider_width">1dp</dimen>
    <dimen name="ksad_skip_view_height">32dp</dimen>
    <dimen name="ksad_skip_view_padding_horizontal">16dp</dimen>
    <dimen name="ksad_skip_view_radius">16dp</dimen>
    <dimen name="ksad_skip_view_text_size">14sp</dimen>
    <dimen name="ksad_skip_view_width">128dp</dimen>
    <dimen name="ksad_splash_actionbar_height">52dp</dimen>
    <dimen name="ksad_splash_actionbar_margin_bottom">20dp</dimen>
    <dimen name="ksad_splash_actionbar_width">280dp</dimen>
    <dimen name="ksad_splash_endcard_ab_subtitle_text_sp">10sp</dimen>
    <dimen name="ksad_splash_endcard_ab_subtitle_text_sp_land">7sp</dimen>
    <dimen name="ksad_splash_endcard_ab_title_text_sp">16sp</dimen>
    <dimen name="ksad_splash_endcard_ab_title_text_sp_land">13sp</dimen>
    <dimen name="ksad_splash_endcard_actionbar_iconh">48dp</dimen>
    <dimen name="ksad_splash_endcard_actionbar_iconh_land">39dp</dimen>
    <dimen name="ksad_splash_endcard_actionbar_iconw">187dp</dimen>
    <dimen name="ksad_splash_endcard_actionbar_iconw_land">152dp</dimen>
    <dimen name="ksad_splash_endcard_app_iconh">100dp</dimen>
    <dimen name="ksad_splash_endcard_app_iconh_land">60dp</dimen>
    <dimen name="ksad_splash_endcard_app_iconw">100dp</dimen>
    <dimen name="ksad_splash_endcard_app_iconw_land">60dp</dimen>
    <dimen name="ksad_splash_endcard_app_margin_top">124dp</dimen>
    <dimen name="ksad_splash_endcard_app_margin_top_land">80dp</dimen>
    <dimen name="ksad_splash_endcard_appdesc_h">48dp</dimen>
    <dimen name="ksad_splash_endcard_appdesc_h_land">30dp</dimen>
    <dimen name="ksad_splash_endcard_appdesc_margin_top">40dp</dimen>
    <dimen name="ksad_splash_endcard_appdesc_margin_top_land">28dp</dimen>
    <dimen name="ksad_splash_endcard_appdesc_text_sp">16sp</dimen>
    <dimen name="ksad_splash_endcard_appdesc_text_sp_land">11sp</dimen>
    <dimen name="ksad_splash_endcard_appname_h">30dp</dimen>
    <dimen name="ksad_splash_endcard_appname_h_land">18dp</dimen>
    <dimen name="ksad_splash_endcard_appname_margin_top">8dp</dimen>
    <dimen name="ksad_splash_endcard_appname_margin_top_land">4dp</dimen>
    <dimen name="ksad_splash_endcard_appname_text_sp">20sp</dimen>
    <dimen name="ksad_splash_endcard_appname_text_sp_land">14sp</dimen>
    <dimen name="ksad_splash_endcard_appver_h">14dp</dimen>
    <dimen name="ksad_splash_endcard_appver_h_land">10dp</dimen>
    <dimen name="ksad_splash_endcard_appver_text_sp">10sp</dimen>
    <dimen name="ksad_splash_endcard_appver_text_sp_land">8sp</dimen>
    <dimen name="ksad_splash_endcard_close_root_h">24dp</dimen>
    <dimen name="ksad_splash_endcard_close_root_h_land">17dp</dimen>
    <dimen name="ksad_splash_endcard_close_root_margin_top">14dp</dimen>
    <dimen name="ksad_splash_endcard_close_root_margin_top_land">12dp</dimen>
    <dimen name="ksad_splash_endcard_gift_iconh">412dp</dimen>
    <dimen name="ksad_splash_endcard_gift_iconh_land">256dp</dimen>
    <dimen name="ksad_splash_endcard_gift_iconw">327dp</dimen>
    <dimen name="ksad_splash_endcard_gift_iconw_land">206dp</dimen>
    <dimen name="ksad_splash_endcard_title_iconh">32dp</dimen>
    <dimen name="ksad_splash_endcard_title_iconh_land">20dp</dimen>
    <dimen name="ksad_splash_endcard_title_iconw">208dp</dimen>
    <dimen name="ksad_splash_endcard_title_iconw_land">130dp</dimen>
    <dimen name="ksad_splash_hand_bgh">168dp</dimen>
    <dimen name="ksad_splash_hand_bgw">60dp</dimen>
    <dimen name="ksad_splash_rotate_view_height">88dp</dimen>
    <dimen name="ksad_splash_rotate_view_margin_bottom">34dp</dimen>
    <dimen name="ksad_splash_rotate_view_margin_top">24dp</dimen>
    <dimen name="ksad_splash_rotate_view_width">88dp</dimen>
    <dimen name="ksad_splash_shake_animator_height">8dp</dimen>
    <dimen name="ksad_splash_shake_view_height">92dp</dimen>
    <dimen name="ksad_splash_shake_view_margin_bottom">30dp</dimen>
    <dimen name="ksad_splash_shake_view_margin_top">16dp</dimen>
    <dimen name="ksad_splash_shake_view_width">92dp</dimen>
    <dimen name="ksad_title_bar_height">45dp</dimen>
    <dimen name="mtrl_bottomappbar_fabOffsetEndMode">60dp</dimen>
    <dimen name="mtrl_bottomappbar_fab_cradle_margin">5dp</dimen>
    <dimen name="mtrl_bottomappbar_fab_cradle_rounded_corner_radius">8dp</dimen>
    <dimen name="mtrl_bottomappbar_fab_cradle_vertical_offset">0dp</dimen>
    <dimen name="mtrl_bottomappbar_height">56dp</dimen>
    <dimen name="mtrl_btn_corner_radius">4dp</dimen>
    <dimen name="mtrl_btn_dialog_btn_min_width">64dp</dimen>
    <dimen name="mtrl_btn_disabled_elevation">0dp</dimen>
    <dimen name="mtrl_btn_disabled_z">0dp</dimen>
    <dimen name="mtrl_btn_elevation">2dp</dimen>
    <dimen name="mtrl_btn_focused_z">2dp</dimen>
    <dimen name="mtrl_btn_hovered_z">2dp</dimen>
    <dimen name="mtrl_btn_icon_btn_padding_left">12dp</dimen>
    <dimen name="mtrl_btn_icon_padding">8dp</dimen>
    <dimen name="mtrl_btn_inset">6dp</dimen>
    <item format="float" name="mtrl_btn_letter_spacing" type="dimen">0.07</item>
    <dimen name="mtrl_btn_padding_bottom">4dp</dimen>
    <dimen name="mtrl_btn_padding_left">16dp</dimen>
    <dimen name="mtrl_btn_padding_right">16dp</dimen>
    <dimen name="mtrl_btn_padding_top">4dp</dimen>
    <dimen name="mtrl_btn_pressed_z">6dp</dimen>
    <dimen name="mtrl_btn_stroke_size">1dp</dimen>
    <dimen name="mtrl_btn_text_btn_icon_padding">4dp</dimen>
    <dimen name="mtrl_btn_text_btn_padding_left">8dp</dimen>
    <dimen name="mtrl_btn_text_btn_padding_right">8dp</dimen>
    <dimen name="mtrl_btn_text_size">14sp</dimen>
    <dimen name="mtrl_btn_z">0dp</dimen>
    <dimen name="mtrl_card_elevation">1dp</dimen>
    <dimen name="mtrl_card_spacing">8dp</dimen>
    <dimen name="mtrl_chip_pressed_translation_z">3dp</dimen>
    <dimen name="mtrl_chip_text_size">14sp</dimen>
    <dimen name="mtrl_fab_elevation">6dp</dimen>
    <dimen name="mtrl_fab_translation_z_hovered_focused">2dp</dimen>
    <dimen name="mtrl_fab_translation_z_pressed">6dp</dimen>
    <dimen name="mtrl_navigation_elevation">0dp</dimen>
    <dimen name="mtrl_navigation_item_horizontal_padding">22dp</dimen>
    <dimen name="mtrl_navigation_item_icon_padding">14dp</dimen>
    <dimen name="mtrl_snackbar_background_corner_radius">4dp</dimen>
    <dimen name="mtrl_snackbar_margin">8dp</dimen>
    <dimen name="mtrl_textinput_box_bottom_offset">3dp</dimen>
    <dimen name="mtrl_textinput_box_corner_radius_medium">4dp</dimen>
    <dimen name="mtrl_textinput_box_corner_radius_small">0dp</dimen>
    <dimen name="mtrl_textinput_box_label_cutout_padding">4dp</dimen>
    <dimen name="mtrl_textinput_box_padding_end">12dp</dimen>
    <dimen name="mtrl_textinput_box_stroke_width_default">1dp</dimen>
    <dimen name="mtrl_textinput_box_stroke_width_focused">2dp</dimen>
    <dimen name="mtrl_textinput_outline_box_expanded_padding">16dp</dimen>
    <dimen name="mtrl_toolbar_default_height">56dp</dimen>
    <dimen name="notification_action_icon_size">32dp</dimen>
    <dimen name="notification_action_text_size">13sp</dimen>
    <dimen name="notification_big_circle_margin">12dp</dimen>
    <dimen name="notification_content_margin_start">8dp</dimen>
    <dimen name="notification_large_icon_height">64dp</dimen>
    <dimen name="notification_large_icon_width">64dp</dimen>
    <dimen name="notification_main_column_padding_top">10dp</dimen>
    <dimen name="notification_media_narrow_margin">@dimen/notification_content_margin_start</dimen>
    <dimen name="notification_right_icon_size">16dp</dimen>
    <dimen name="notification_right_side_padding_top">2dp</dimen>
    <dimen name="notification_small_icon_background_padding">3dp</dimen>
    <dimen name="notification_small_icon_size_as_large">24dp</dimen>
    <dimen name="notification_subtext_size">13sp</dimen>
    <dimen name="notification_top_pad">10dp</dimen>
    <dimen name="notification_top_pad_large_text">5dp</dimen>
    <dimen name="subtitle_corner_radius">2dp</dimen>
    <dimen name="subtitle_outline_width">2dp</dimen>
    <dimen name="subtitle_shadow_offset">2dp</dimen>
    <dimen name="subtitle_shadow_radius">2dp</dimen>
    <dimen name="tooltip_corner_radius">2dp</dimen>
    <dimen name="tooltip_horizontal_padding">16dp</dimen>
    <dimen name="tooltip_margin">8dp</dimen>
    <dimen name="tooltip_precise_anchor_extra_offset">8dp</dimen>
    <dimen name="tooltip_precise_anchor_threshold">96dp</dimen>
    <dimen name="tooltip_vertical_padding">6.5dp</dimen>
    <dimen name="tooltip_y_offset_non_touch">0dp</dimen>
    <dimen name="tooltip_y_offset_touch">16dp</dimen>
    <dimen name="tt_download_dialog_marginHorizontal">16dp</dimen>
    <dimen name="tt_video_container_maxheight">228dp</dimen>
    <dimen name="tt_video_container_minheight">160dp</dimen>
    <dimen name="tt_video_cover_padding_horizon">15dp</dimen>
    <dimen name="tt_video_cover_padding_vertical">14dp</dimen>
    <drawable name="notification_template_icon_bg">#3333B5E5</drawable>
    <drawable name="notification_template_icon_low_bg">#0cffffff</drawable>
    <item name="action_bar_activity_content" type="id"/>
    <item name="action_bar_spinner" type="id"/>
    <item name="action_menu_divider" type="id"/>
    <item name="action_menu_presenter" type="id"/>
    <item name="ghost_view" type="id"/>
    <item name="home" type="id"/>
    <item name="item_touch_helper_previous_elevation" type="id"/>
    <item name="ksad_container" type="id"/>
    <item name="ksad_video_immerse_text_container" type="id"/>
    <item name="line1" type="id"/>
    <item name="line3" type="id"/>
    <item name="mtrl_child_content_container" type="id"/>
    <item name="mtrl_internal_children_alpha_tag" type="id"/>
    <item name="parent_matrix" type="id"/>
    <item name="progress_circular" type="id"/>
    <item name="progress_horizontal" type="id"/>
    <item name="save_image_matrix" type="id"/>
    <item name="save_non_transition_alpha" type="id"/>
    <item name="save_scale_type" type="id"/>
    <item name="snackbar_action" type="id"/>
    <item name="snackbar_text" type="id"/>
    <item name="split_action_bar" type="id"/>
    <item name="srl_tag" type="id"/>
    <item name="tag_transition_group" type="id"/>
    <item name="tag_unhandled_key_event_manager" type="id"/>
    <item name="tag_unhandled_key_listeners" type="id"/>
    <item name="text" type="id"/>
    <item name="text2" type="id"/>
    <item name="textinput_counter" type="id"/>
    <item name="textinput_error" type="id"/>
    <item name="textinput_helper_text" type="id"/>
    <item name="title" type="id"/>
    <item name="transition_current_scene" type="id"/>
    <item name="transition_layout_save" type="id"/>
    <item name="transition_position" type="id"/>
    <item name="transition_scene_layoutid_cache" type="id"/>
    <item name="transition_transform" type="id"/>
    <item name="tt_id_render_tag" type="id"/>
    <item name="tt_id_root_web_view" type="id"/>
    <item name="tt_mediation_admob_developer_view_root_tag_key" type="id"/>
    <item name="tt_mediation_admob_developer_view_tag_key" type="id"/>
    <item name="tt_mediation_gdt_developer_view_logo_tag_key" type="id"/>
    <item name="tt_mediation_gdt_developer_view_root_tag_key" type="id"/>
    <item name="tt_mediation_gdt_developer_view_tag_key" type="id"/>
    <item name="tt_mediation_mtg_ad_choice" type="id"/>
    <item name="tt_shake_tag_key" type="id"/>
    <item name="up" type="id"/>
    <item name="view_offset_helper" type="id"/>
    <integer name="abc_config_activityDefaultDur">220</integer>
    <integer name="abc_config_activityShortDur">150</integer>
    <integer name="app_bar_elevation_anim_duration">150</integer>
    <integer name="bottom_sheet_slide_duration">150</integer>
    <integer name="cancel_button_image_alpha">127</integer>
    <integer name="config_tooltipAnimTime">150</integer>
    <integer name="design_snackbar_text_max_lines">2</integer>
    <integer name="design_tab_indicator_anim_duration_ms">300</integer>
    <integer name="hide_password_duration">320</integer>
    <integer name="min_screen_width_bucket">1</integer>
    <integer name="mtrl_btn_anim_delay_ms">100</integer>
    <integer name="mtrl_btn_anim_duration_ms">100</integer>
    <integer name="mtrl_chip_anim_duration">100</integer>
    <integer name="mtrl_tab_indicator_anim_duration_ms">250</integer>
    <integer name="show_password_duration">200</integer>
    <integer name="status_bar_notification_info_maxnum">999</integer>
    <string name="abc_action_bar_home_description">Navigate home</string>
    <string name="abc_action_bar_up_description">Navigate up</string>
    <string name="abc_action_menu_overflow_description">More options</string>
    <string name="abc_action_mode_done">Done</string>
    <string name="abc_activity_chooser_view_see_all">See all</string>
    <string name="abc_activitychooserview_choose_application">Choose an app</string>
    <string name="abc_capital_off">OFF</string>
    <string name="abc_capital_on">ON</string>
    <string name="abc_font_family_body_1_material">sans-serif</string>
    <string name="abc_font_family_body_2_material">sans-serif-medium</string>
    <string name="abc_font_family_button_material">sans-serif-medium</string>
    <string name="abc_font_family_caption_material">sans-serif</string>
    <string name="abc_font_family_display_1_material">sans-serif</string>
    <string name="abc_font_family_display_2_material">sans-serif</string>
    <string name="abc_font_family_display_3_material">sans-serif</string>
    <string name="abc_font_family_display_4_material">sans-serif-light</string>
    <string name="abc_font_family_headline_material">sans-serif</string>
    <string name="abc_font_family_menu_material">sans-serif</string>
    <string name="abc_font_family_subhead_material">sans-serif</string>
    <string name="abc_font_family_title_material">sans-serif-medium</string>
    <string name="abc_menu_alt_shortcut_label">Alt+</string>
    <string name="abc_menu_ctrl_shortcut_label">Ctrl+</string>
    <string name="abc_menu_delete_shortcut_label">delete</string>
    <string name="abc_menu_enter_shortcut_label">enter</string>
    <string name="abc_menu_function_shortcut_label">Function+</string>
    <string name="abc_menu_meta_shortcut_label">Meta+</string>
    <string name="abc_menu_shift_shortcut_label">Shift+</string>
    <string name="abc_menu_space_shortcut_label">space</string>
    <string name="abc_menu_sym_shortcut_label">Sym+</string>
    <string name="abc_prepend_shortcut_label">Menu+</string>
    <string name="abc_search_hint">Search…</string>
    <string name="abc_searchview_description_clear">Clear query</string>
    <string name="abc_searchview_description_query">Search query</string>
    <string name="abc_searchview_description_search">Search</string>
    <string name="abc_searchview_description_submit">Submit query</string>
    <string name="abc_searchview_description_voice">Voice search</string>
    <string name="abc_shareactionprovider_share_with">Share with</string>
    <string name="abc_shareactionprovider_share_with_application">Share with <ns1:g example="Mail" id="application_name">%s</ns1:g></string>
    <string name="abc_toolbar_collapse_description">Collapse</string>
    <string name="app_name">雪山求生</string>
    <string name="app_name_en">MountainSurvival</string>
    <string name="appbar_scrolling_view_behavior" translatable="false">android.support.design.widget.AppBarLayout$ScrollingViewBehavior</string>
    <string name="beizi_action_cant_be_completed">The requested action could not be completed.</string>
    <string name="beizi_allow">Allow</string>
    <string name="beizi_cancel">取消</string>
    <string name="beizi_confirm">确定</string>
    <string name="beizi_deny">Deny</string>
    <string name="beizi_dialog_text_hint"><font size="13">其他投诉</font></string>
    <string name="beizi_html5_geo_permission_prompt">Would you like to allow this app to access your location?</string>
    <string name="beizi_html5_geo_permission_prompt_title">%s would like to access your location</string>
    <string name="beizi_loading">Loading</string>
    <string name="beizi_native_tag">View has been registered.</string>
    <string name="beizi_skip_ad">跳过</string>
    <string name="beizi_store_picture_accept">Accept</string>
    <string name="beizi_store_picture_decline">Decline</string>
    <string name="beizi_store_picture_message">"This Ad would like permission to save a picture."</string>
    <string name="beizi_store_picture_title">Store Picture?</string>
    <string name="bottom_sheet_behavior" translatable="false">android.support.design.widget.BottomSheetBehavior</string>
    <string name="character_counter_content_description">Character limit exceeded %1$d of %2$d</string>
    <string name="character_counter_pattern" translatable="false">%1$d / %2$d</string>
    <string name="exit_game">退出游戏</string>
    <string name="fab_transformation_scrim_behavior" translatable="false">android.support.design.transformation.FabTransformationScrimBehavior</string>
    <string name="fab_transformation_sheet_behavior" translatable="false">android.support.design.transformation.FabTransformationSheetBehavior</string>
    <string name="game_description">你被困在雪山中的一间小屋里\n管理你的体温、体力和资源\n坚持7天等待救援\n体温或体力归零即死亡\n\n你能在严酷的雪山中生存下来吗？</string>
    <string name="hide_bottom_view_on_scroll_behavior" translatable="false">android.support.design.behavior.HideBottomViewOnScrollBehavior</string>
    <string name="init">初始化</string>
    <string name="interstitialAd">插屏视频</string>
    <string name="interstitialImageAd">插屏图片</string>
    <string name="ksad_ad_default_username_normal">可爱的广告君</string>
    <string name="ksad_card_tips_interested">感兴趣</string>
    <string name="ksad_card_tips_pre">预告</string>
    <string name="ksad_click_immediate">立即点击</string>
    <string name="ksad_data_error_toast">内容获取失败</string>
    <string name="ksad_deep_link_dialog_content">再次开启 【%s】</string>
    <string name="ksad_default_no_more_tip_or_toast_txt">更多内容请前往快手App查看</string>
    <string name="ksad_download_kwai_waiting">下载信息获取中，请稍后再试。</string>
    <string name="ksad_half_page_loading_error_tip">加载失败，请点击重试</string>
    <string name="ksad_install_tips">“ %1$s ”已完成下载，点击完成安装</string>
    <string name="ksad_launch_tips">您已安装“ %1$s ”，是否打开该应用</string>
    <string name="ksad_leave_persist">残忍离开</string>
    <string name="ksad_left_slide_to_next">左滑试一试</string>
    <string name="ksad_live_end">直播已结束</string>
    <string name="ksad_network_error_toast">网络错误</string>
    <string name="ksad_no_title_common_dialog_negativebtn_title">取消</string>
    <string name="ksad_no_title_common_dialog_positivebtn_title">确定</string>
    <string name="ksad_page_load_no_more_tip">无更多内容</string>
    <string name="ksad_page_loading_data_error_sub_title">内容获取失败请点击重试</string>
    <string name="ksad_page_loading_data_error_title">空空如也</string>
    <string name="ksad_page_loading_error_retry">重试</string>
    <string name="ksad_page_loading_network_error_sub_title">请检查网络连接后重试</string>
    <string name="ksad_page_loading_network_error_title">网络不给力</string>
    <string name="ksad_request_install_content">安装后，可以在快手查看商品详情和下单</string>
    <string name="ksad_request_install_nagative">暂不开启</string>
    <string name="ksad_request_install_positive">去开启</string>
    <string name="ksad_request_install_title">授权安装快手</string>
    <string name="ksad_reward_playable_load_error_toast">请稍后重试，内容准备中</string>
    <string name="ksad_reward_success_tip">领取奖励</string>
    <string name="ksad_right_slide_to_return">右滑返回</string>
    <string name="ksad_see_detail">了解详情 ></string>
    <string name="ksad_skip_text">跳过</string>
    <string name="ksad_splash_preload_tips_text">已提前加载</string>
    <string name="ksad_splash_rotate_combo_rotate_text">"扭动或点击了解更多"</string>
    <string name="ksad_splash_shake_combo_action_text">"摇一摇"</string>
    <string name="ksad_splash_shake_combo_sub_text">"或点击跳转详情页/三方应用"</string>
    <string name="ksad_splash_slide_combo_guide_text">"任意方向滑动或点击"</string>
    <string name="ksad_splash_slide_combo_sub_text">"跳转详情或第三方应用"</string>
    <string name="ksad_splash_slide_guide_text">轻滑试试</string>
    <string name="ksad_watch_continue">留下看看</string>
    <string name="language">语言</string>
    <string name="language_cancel">取消</string>
    <string name="language_english">English</string>
    <string name="language_selection_title">选择语言</string>
    <string name="language_simplified_chinese">简体中文</string>
    <string name="language_traditional_chinese">繁體中文</string>
    <string name="live_in_loading">正在加载</string>
    <string name="live_in_loading_failed">加载失败，请稍后重试~</string>
    <string name="mtrl_chip_close_icon_content_description">Remove %1$s</string>
    <string name="music_off">音乐：关</string>
    <string name="music_on">音乐：开</string>
    <string name="oset_function"><u>功能</u></string>
    <string name="oset_permission"><u>权限</u></string>
    <string name="oset_privacy"><u>隐私</u></string>
    <string name="password_toggle_content_description">Show password</string>
    <string name="path_password_eye" translatable="false">M12,4.5C7,4.5 2.73,7.61 1,12c1.73,4.39 6,7.5 11,7.5s9.27,-3.11 11,-7.5c-1.73,-4.39 -6,-7.5 -11,-7.5zM12,17c-2.76,0 -5,-2.24 -5,-5s2.24,-5 5,-5 5,2.24 5,5 -2.24,5 -5,5zM12,9c-1.66,0 -3,1.34 -3,3s1.34,3 3,3 3,-1.34 3,-3 -1.34,-3 -3,-3z</string>
    <string name="path_password_eye_mask_strike_through" translatable="false">M2,4.27 L19.73,22 L22.27,19.46 L4.54,1.73 L4.54,1 L23,1 L23,23 L1,23 L1,4.27 Z</string>
    <string name="path_password_eye_mask_visible" translatable="false">M2,4.27 L2,4.27 L4.54,1.73 L4.54,1.73 L4.54,1 L23,1 L23,23 L1,23 L1,4.27 Z</string>
    <string name="path_password_strike_through" translatable="false">M3.27,4.27 L19.74,20.74</string>
    <string name="privacy_policy_agree">同意</string>
    <string name="privacy_policy_content">欢迎您使用【雪山求生】！我们非常重视保护您的个人信息和隐私。您可以通过《雪山求生隐私政策》了解我们收集、使用、存储用户个人信息的情况，以及您所享有的相关权利。请您仔细阅读并充分理解相关内容：\n\n1. 为向您提供游戏服务，我们将依据《雪山求生隐私政策》收集、使用、存储必要的信息。\n\n2. 我们会使用一些基础的设备权限来保障应用正常运行，这些权限不需要您的额外授权。\n\n3. 您可以访问、更正、删除您的个人信息，还可以撤回授权同意、投诉举报以及调整其他隐私设置。\n\n4. 我们已采取符合业界标准的安全防护措施保护您的个人信息。\n\n5. 如您是未成年人，请您和您的监护人仔细阅读本隐私政策，并在征得您的监护人授权同意的前提下使用我们的服务或向我们提供个人信息。\n\n请您阅读完整版《雪山求生隐私政策》了解详细信息。\n\n如您同意《雪山求生隐私政策》，请您点击"同意"开始使用我们的产品和服务，我们将尽全力保护您的个人信息安全。\n\n更新日期：2025年7月18日\n生效日期：2025年7月18日</string>
    <string name="privacy_policy_disagree">不同意</string>
    <string name="privacy_policy_link_text">《雪山求生隐私政策》</string>
    <string name="privacy_policy_subtitle">温馨提示</string>
    <string name="privacy_policy_title">雪山求生隐私政策</string>
    <string name="privacy_policy_url">https://vwk4xgjlcg1.feishu.cn/docx/WRqEdaUEJo7pn3xNuZicsa1lnhh</string>
    <string name="rewardedAd">激励视频</string>
    <string name="search_menu_title">Search</string>
    <string name="sig_ad">Ad</string>
    <string name="sig_back">Close</string>
    <string name="sig_close">Skip</string>
    <string name="sig_close_ad_cancel">Cancel</string>
    <string name="sig_close_ad_message">You will be rewarded with only _SEC_ seconds</string>
    <string name="sig_close_ad_ok">Confirm</string>
    <string name="sig_close_ad_title">Close video?</string>
    <string name="sig_close_args">%1$d Skip</string>
    <string name="sig_skip_ad_args">Skip AD %1$d</string>
    <string name="sig_skip_args_1">Skip %1$d</string>
    <string name="sig_skip_args_2">%1$d Skip</string>
    <string name="splash_ad">开屏广告</string>
    <string formatted="false" name="srl_component_falsify">%s falsify area,\n Represents the height[%.1fdp] of drag at run time,\n It does not show anything.</string>
    <string name="srl_content_empty">The content view in SmartRefreshLayout is empty. Do you forget to add it in xml layout file?</string>
    <string name="start_game">开始游戏</string>
    <string name="status_bar_notification_info_overflow">999+</string>
    <string name="title_content_description">雪山求生标题</string>
    <string name="tt_00_00">00:00</string>
    <string name="tt_ad">Ad</string>
    <string name="tt_ad_logo_txt">广告</string>
    <string name="tt_agg_page_close">关闭</string>
    <string name="tt_app_name">tt_open_ad_sdk</string>
    <string name="tt_app_privacy_dialog_title">隐私政策</string>
    <string name="tt_appdownloader_button_cancel_download">"取消"</string>
    <string name="tt_appdownloader_button_queue_for_wifi">"排队"</string>
    <string name="tt_appdownloader_button_start_now">"立即开始"</string>
    <string name="tt_appdownloader_download_percent">"<ns1:g id="NUMBER">%1$d</ns1:g><ns1:g id="PERCENT">%%</ns1:g>"</string>
    <string name="tt_appdownloader_download_remaining">"剩余时间：<ns1:g id="DURATION">%1$s</ns1:g>"</string>
    <string name="tt_appdownloader_download_unknown_title">"&lt;未命名>"</string>
    <string name="tt_appdownloader_duration_hours"><ns1:g id="count">%1$d</ns1:g> 小时</string>
    <string name="tt_appdownloader_duration_minutes"><ns1:g id="count">%1$d</ns1:g> 分钟</string>
    <string name="tt_appdownloader_duration_seconds"><ns1:g id="count">%1$d</ns1:g> 秒</string>
    <string name="tt_appdownloader_jump_unknown_source">请打开应用安装权限，以便顺利安装！</string>
    <string name="tt_appdownloader_label_cancel">取消</string>
    <string name="tt_appdownloader_label_cancel_directly">直接取消</string>
    <string name="tt_appdownloader_label_ok">确定</string>
    <string name="tt_appdownloader_label_reserve_wifi">Wi-Fi下恢复</string>
    <string name="tt_appdownloader_notification_download">"下载"</string>
    <string name="tt_appdownloader_notification_download_complete_open">"下载完成，点击打开。"</string>
    <string name="tt_appdownloader_notification_download_complete_with_install">"下载完成，点击安装。"</string>
    <string name="tt_appdownloader_notification_download_complete_without_install">"下载完成"</string>
    <string name="tt_appdownloader_notification_download_continue">"下载暂停中，点击继续。"</string>
    <string name="tt_appdownloader_notification_download_delete">"确认要删除%1$s的下载任务吗？"</string>
    <string name="tt_appdownloader_notification_download_failed">"下载失败。"</string>
    <string name="tt_appdownloader_notification_download_install">"安装"</string>
    <string name="tt_appdownloader_notification_download_open">"打开"</string>
    <string name="tt_appdownloader_notification_download_pause">"暂停"</string>
    <string name="tt_appdownloader_notification_download_restart">"重新下载"</string>
    <string name="tt_appdownloader_notification_download_resume">"继续"</string>
    <string name="tt_appdownloader_notification_download_space_failed">"SdCard空间不足, 下载失败。"</string>
    <string name="tt_appdownloader_notification_download_waiting_net">"等待网络继续下载"</string>
    <string name="tt_appdownloader_notification_download_waiting_wifi">"等待wifi开始下载"</string>
    <string name="tt_appdownloader_notification_downloading">"正在下载"</string>
    <string name="tt_appdownloader_notification_install_finished_open">"安装完成，点击打开。"</string>
    <string name="tt_appdownloader_notification_insufficient_space_error">"空间不足 还需<ns1:g>%1$s</ns1:g>"</string>
    <string name="tt_appdownloader_notification_need_wifi_for_size">"文件过大，需要通过 WLAN 下载。"</string>
    <string name="tt_appdownloader_notification_no_internet_error">"下载异常，请检查网络"</string>
    <string name="tt_appdownloader_notification_no_wifi_and_in_net">"无Wi-Fi 已暂停"</string>
    <string name="tt_appdownloader_notification_paused_in_background">"已在后台暂停。"</string>
    <string name="tt_appdownloader_notification_pausing">"暂停中"</string>
    <string name="tt_appdownloader_notification_prepare">"准备中"</string>
    <string name="tt_appdownloader_notification_request_btn_no">"立即下载"</string>
    <string name="tt_appdownloader_notification_request_btn_yes">"去设置"</string>
    <string name="tt_appdownloader_notification_request_message">"为了您能在方便地在通知栏控制下载任务，请到设置中开启通知，如果不需要可直接下载"</string>
    <string name="tt_appdownloader_notification_request_title">"设置"</string>
    <string name="tt_appdownloader_notification_waiting_download_complete_handler">"处理中"</string>
    <string name="tt_appdownloader_resume_in_wifi">"您当前处于移动网络，是否需要在Wi-Fi环境下恢复下载？"</string>
    <string name="tt_appdownloader_tip">提示</string>
    <string name="tt_appdownloader_wifi_recommended_body">"立即开始下载此内容 (<ns1:g id="SIZE">%1$s </ns1:g>) 可能会缩短电池的使用时间并/或导致过量使用移动数据连接流量（这可能导致移动运营商向您收费，具体取决于您的流量套餐）。\n\n触摸“<ns1:g id="QUEUE_TEXT">%2$s</ns1:g>”可在下次连接到 WLAN 网络时开始下载此内容。"</string>
    <string name="tt_appdownloader_wifi_recommended_title">"稍后再加入下载队列吗？"</string>
    <string name="tt_appdownloader_wifi_required_body">"您必须使用 WLAN 完成此内容 (<ns1:g id="SIZE">%1$s </ns1:g>) 的下载。\n\n触摸“<ns1:g id="QUEUE_TEXT">%2$s </ns1:g>”可在下次连接到 WLAN 网络时开始下载此内容。"</string>
    <string name="tt_appdownloader_wifi_required_title">"文件太大，不适于通过运营商网络下载"</string>
    <string name="tt_application_detail">"应用详情"</string>
    <string name="tt_auto_play_cancel_text">取消</string>
    <string name="tt_cancel">取消</string>
    <string name="tt_change_other">换一个</string>
    <string name="tt_click_replay">点击重播</string>
    <string name="tt_comment_num">%1$s个评分</string>
    <string name="tt_comment_num_backup">(%1$s个评论)</string>
    <string name="tt_comment_score">评价</string>
    <string name="tt_common_download_app_detail">应用权限</string>
    <string name="tt_common_download_app_privacy">隐私协议</string>
    <string name="tt_common_download_cancel">放弃下载</string>
    <string name="tt_confirm_download">"正在使用非Wifi网络，确认要进行下载吗？"</string>
    <string name="tt_confirm_download_have_app_name">"准备下载%s应用"</string>
    <string name="tt_dislike_comment_hint">请具体说明问题，我们将尽快处理</string>
    <string name="tt_dislike_feedback_repeat">您已成功提交反馈，请勿重复提交哦！</string>
    <string name="tt_dislike_feedback_success">感谢您的反馈！\n我们将为您带来更优质的广告体验</string>
    <string name="tt_dislike_header_tv_back">返回</string>
    <string name="tt_dislike_header_tv_title">选择</string>
    <string name="tt_dislike_other_suggest">其他建议</string>
    <string name="tt_dislike_other_suggest_out"> 其他建议</string>
    <string name="tt_dislike_submit">提交</string>
    <string name="tt_download">立即下载</string>
    <string name="tt_download_finish">官方app下载完成，是否立即安装？</string>
    <string name="tt_ecomm_page_reward_acquire">"暂无更多内容，奖励已发放"</string>
    <string name="tt_ecomm_page_reward_slide_tip">"需要下滑浏览更多才能领取奖励哦"</string>
    <string name="tt_ecomm_page_reward_tip">"暂无更多内容，继续浏览%1$s秒可得奖励"</string>
    <string name="tt_ensure_exit">"确定退出吗？"</string>
    <string name="tt_feedback">反馈</string>
    <string name="tt_full_screen_skip_tx">5s后可跳过</string>
    <string name="tt_image_download_apk">下载</string>
    <string name="tt_install">立即安装</string>
    <string name="tt_label_cancel">取消</string>
    <string name="tt_label_ok">确定</string>
    <string name="tt_live_back_btn">返回</string>
    <string name="tt_live_fans_text">粉丝 %1$s</string>
    <string name="tt_live_feed_btn">去抖音直播间</string>
    <string name="tt_live_feed_logo">穿山甲联盟</string>
    <string name="tt_live_finish">直播已结束</string>
    <string name="tt_live_full_reward_btn">点击进入直播间</string>
    <string name="tt_live_loading_btn">进入直播间</string>
    <string name="tt_live_loading_text">数据加载中...</string>
    <string name="tt_live_watch_text">观看 %1$s</string>
    <string name="tt_logo_cn">AD</string>
    <string name="tt_logo_en">AD</string>
    <string name="tt_mediation_format_adapter_name">%1$s%2$sAdapter</string>
    <string name="tt_mediation_format_error_msg">test error_adn:%1$s  error_slot_id: %2$s  error_code:%3$d  error_message:%4$s</string>
    <string name="tt_mediation_format_no_ad_error_msg">error_adn:%1$s no ads，please check ad network</string>
    <string name="tt_mediation_format_setting_error_msg">"slot setting error, pangle default ads will be displayed</string>
    <string name="tt_mediation_format_show_success_msg">test_suceess ,test_adn:%1$s,slot_id : %2$s</string>
    <string name="tt_mediation_format_success_msg">fill_suceess ,test_adn:%1$s,slot_id : %2$s</string>
    <string name="tt_mediation_label_cancel">取消</string>
    <string name="tt_mediation_label_ok">确定</string>
    <string name="tt_mediation_permission_denied">您已禁止授权操作，请授权</string>
    <string name="tt_mediation_request_permission_descript_external_storage">存储</string>
    <string name="tt_mediation_request_permission_descript_location">位置信息</string>
    <string name="tt_mediation_request_permission_descript_read_phone_state">拨打电话</string>
    <string name="tt_no_network">"无网络，请稍后再试"</string>
    <string name="tt_open_app_detail_developer">开发者：%1$s</string>
    <string name="tt_open_app_detail_privacy">隐私协议：</string>
    <string name="tt_open_app_detail_privacy_list">权限列表</string>
    <string name="tt_open_app_name">应用名称：%1$s</string>
    <string name="tt_open_app_version">版本号：%1$s</string>
    <string name="tt_open_landing_page_app_name">应用名称：%1$s   版本：%2$s</string>
    <string name="tt_permission_denied">您已禁止授权操作，请授权</string>
    <string name="tt_permission_list">"权限列表"</string>
    <string name="tt_privacy_back">"上一步"</string>
    <string name="tt_privacy_policy">"隐私政策"</string>
    <string name="tt_privacy_start_download">"立即下载"</string>
    <string name="tt_quit">退出</string>
    <string name="tt_request_permission_descript_external_storage">存储</string>
    <string name="tt_request_permission_descript_location">位置信息</string>
    <string name="tt_request_permission_descript_read_phone_state">拨打电话</string>
    <string name="tt_retain_tips_message">"再观看20秒，可得奖励？"</string>
    <string name="tt_reward_auto_jump_live">%1$s后自动进入直播间</string>
    <string name="tt_reward_empty">Empty</string>
    <string name="tt_reward_feedback">反馈</string>
    <string name="tt_reward_full_skip_count_down">%1$ss后可跳过</string>
    <string name="tt_reward_live_dialog_btn_text">马上进入抖音直播间</string>
    <string name="tt_reward_live_dialog_cancel_count_down_text">残忍拒绝(%1$s)</string>
    <string name="tt_reward_live_dialog_cancel_text">残忍拒绝</string>
    <string name="tt_reward_live_grant">"奖励发放中"</string>
    <string name="tt_reward_screen_skip_tx">跳过</string>
    <string name="tt_reward_slip_up_lp_tip">向上滑动浏览更多</string>
    <string name="tt_reward_slip_up_tip">上滑浏览更多视频领奖励</string>
    <string name="tt_reward_slip_up_tip2">上滑查看更多内容</string>
    <string name="tt_slide_up_3d">wipe up</string>
    <string name="tt_splash_backup_ad_btn">立即下载</string>
    <string name="tt_splash_backup_ad_title">今日推荐</string>
    <string name="tt_splash_brush_mask_hint">Jump to details page or third party application</string>
    <string name="tt_splash_brush_mask_title">Slide or click to Polish</string>
    <string name="tt_splash_click_bar_text">查看详情</string>
    <string name="tt_splash_default_click_shake">Shake or click to learn more</string>
    <string name="tt_splash_rock_desc">前往详情页或第三方应用</string>
    <string name="tt_splash_rock_text">Go to details page or third-party application</string>
    <string name="tt_splash_rock_top">摇一摇</string>
    <string name="tt_splash_rock_top_text">Shake or click the icon</string>
    <string name="tt_splash_skip_tv_text">3s | 跳过</string>
    <string name="tt_splash_wriggle_text">Go to details page or third-party application</string>
    <string name="tt_splash_wriggle_top_text">Twist the phone</string>
    <string name="tt_splash_wriggle_top_text_style_17">Twist the phone or tap the icon</string>
    <string name="tt_text_privacy_app_version">Version：V</string>
    <string name="tt_text_privacy_development">Developer：</string>
    <string name="tt_tip">提示</string>
    <string name="tt_unlike">不感兴趣</string>
    <string name="tt_video_bytesize">流量</string>
    <string name="tt_video_bytesize_M">M</string>
    <string name="tt_video_bytesize_MB">MB</string>
    <string name="tt_video_continue_play">继续播放</string>
    <string name="tt_video_dial_phone">立即拨打</string>
    <string name="tt_video_dial_replay">重新播放</string>
    <string name="tt_video_download_apk">立即下载</string>
    <string name="tt_video_mobile_go_detail">查看详情</string>
    <string name="tt_video_retry_des_txt">加载失败，点击重试</string>
    <string name="tt_video_without_wifi_tips">播放将消耗</string>
    <string name="tt_web_title_default">广告</string>
    <string name="tt_will_play">即将播放</string>
    <string name="version">版本 1.2.0</string>
    <style name="AlertDialog.AppCompat" parent="Base.AlertDialog.AppCompat"/>
    <style name="AlertDialog.AppCompat.Light" parent="Base.AlertDialog.AppCompat.Light"/>
    <style name="AlphaAnimation" parent="android:Animation">
        <item name="android:windowEnterAnimation">@anim/ec_alpha_in</item>
        <item name="android:windowExitAnimation">@anim/ec_alpha_out</item>
    </style>
    <style name="Animation.AppCompat.Dialog" parent="Base.Animation.AppCompat.Dialog"/>
    <style name="Animation.AppCompat.DropDownUp" parent="Base.Animation.AppCompat.DropDownUp"/>
    <style name="Animation.AppCompat.Tooltip" parent="Base.Animation.AppCompat.Tooltip"/>
    <style name="Animation.Design.BottomSheetDialog" parent="Animation.AppCompat.Dialog">
    <item name="android:windowEnterAnimation">@anim/design_bottom_sheet_slide_in</item>
    <item name="android:windowExitAnimation">@anim/design_bottom_sheet_slide_out</item>
  </style>
    <style name="AppTheme" parent="android:Theme.Holo.Light.NoActionBar">
        
    </style>
    <style name="Base.AlertDialog.AppCompat" parent="android:Widget">
        <item name="android:layout">@layout/abc_alert_dialog_material</item>
        <item name="listLayout">@layout/abc_select_dialog_material</item>
        <item name="listItemLayout">@layout/select_dialog_item_material</item>
        <item name="multiChoiceItemLayout">@layout/select_dialog_multichoice_material</item>
        <item name="singleChoiceItemLayout">@layout/select_dialog_singlechoice_material</item>
        <item name="buttonIconDimen">@dimen/abc_alert_dialog_button_dimen</item>
    </style>
    <style name="Base.AlertDialog.AppCompat.Light" parent="Base.AlertDialog.AppCompat"/>
    <style name="Base.Animation.AppCompat.Dialog" parent="android:Animation">
        <item name="android:windowEnterAnimation">@anim/abc_popup_enter</item>
        <item name="android:windowExitAnimation">@anim/abc_popup_exit</item>
    </style>
    <style name="Base.Animation.AppCompat.DropDownUp" parent="android:Animation">
        <item name="android:windowEnterAnimation">@anim/abc_grow_fade_in_from_bottom</item>
        <item name="android:windowExitAnimation">@anim/abc_shrink_fade_out_from_bottom</item>
    </style>
    <style name="Base.Animation.AppCompat.Tooltip" parent="android:Animation">
        <item name="android:windowEnterAnimation">@anim/abc_tooltip_enter</item>
        <item name="android:windowExitAnimation">@anim/abc_tooltip_exit</item>
    </style>
    <style name="Base.CardView" parent="android:Widget">
        <item name="cardCornerRadius">@dimen/cardview_default_radius</item>
        <item name="cardElevation">@dimen/cardview_default_elevation</item>
        <item name="cardMaxElevation">@dimen/cardview_default_elevation</item>
        <item name="cardUseCompatPadding">false</item>
        <item name="cardPreventCornerOverlap">true</item>
    </style>
    <style name="Base.DialogWindowTitle.AppCompat" parent="android:Widget">
        <item name="android:maxLines">1</item>
        <item name="android:scrollHorizontally">true</item>
        <item name="android:textAppearance">@style/TextAppearance.AppCompat.Title</item>
    </style>
    <style name="Base.DialogWindowTitleBackground.AppCompat" parent="android:Widget">
        <item name="android:background">@null</item>
        <item name="android:paddingLeft">?attr/dialogPreferredPadding</item>
        <item name="android:paddingRight">?attr/dialogPreferredPadding</item>
        <item name="android:paddingTop">@dimen/abc_dialog_padding_top_material</item>
    </style>
    <style name="Base.TextAppearance.AppCompat" parent="android:TextAppearance">
        <item name="android:textColor">?android:textColorPrimary</item>
        <item name="android:textColorHint">?android:textColorHint</item>
        <item name="android:textColorHighlight">?android:textColorHighlight</item>
        <item name="android:textColorLink">?android:textColorLink</item>
        <item name="android:textSize">@dimen/abc_text_size_body_1_material</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Body1">
        <item name="android:textSize">@dimen/abc_text_size_body_1_material</item>
        <item name="android:textColor">?android:textColorPrimary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Body2">
        <item name="android:textSize">@dimen/abc_text_size_body_2_material</item>
        <item name="android:textColor">?android:textColorPrimary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Button">
        <item name="android:textSize">@dimen/abc_text_size_button_material</item>
        <item name="android:textAllCaps">true</item>
        <item name="android:textColor">?android:textColorPrimary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Caption">
        <item name="android:textSize">@dimen/abc_text_size_caption_material</item>
        <item name="android:textColor">?android:textColorSecondary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Display1">
        <item name="android:textSize">@dimen/abc_text_size_display_1_material</item>
        <item name="android:textColor">?android:textColorSecondary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Display2">
        <item name="android:textSize">@dimen/abc_text_size_display_2_material</item>
        <item name="android:textColor">?android:textColorSecondary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Display3">
        <item name="android:textSize">@dimen/abc_text_size_display_3_material</item>
        <item name="android:textColor">?android:textColorSecondary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Display4">
        <item name="android:textSize">@dimen/abc_text_size_display_4_material</item>
        <item name="android:textColor">?android:textColorSecondary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Headline">
        <item name="android:textSize">@dimen/abc_text_size_headline_material</item>
        <item name="android:textColor">?android:textColorPrimary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Inverse">
        <item name="android:textColor">?android:attr/textColorPrimaryInverse</item>
        <item name="android:textColorHint">?android:attr/textColorHintInverse</item>
        <item name="android:textColorHighlight">?android:attr/textColorHighlightInverse</item>
        <item name="android:textColorLink">?android:attr/textColorLinkInverse</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Large">
        <item name="android:textSize">@dimen/abc_text_size_large_material</item>
        <item name="android:textColor">?android:attr/textColorPrimary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Large.Inverse">
        <item name="android:textColor">?android:attr/textColorPrimaryInverse</item>
        <item name="android:textColorHint">?android:attr/textColorHintInverse</item>
        <item name="android:textColorHighlight">?android:attr/textColorHighlightInverse</item>
        <item name="android:textColorLink">?android:attr/textColorLinkInverse</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Medium">
        <item name="android:textSize">@dimen/abc_text_size_medium_material</item>
        <item name="android:textColor">?android:attr/textColorSecondary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Medium.Inverse">
        <item name="android:textColor">?android:attr/textColorSecondaryInverse</item>
        <item name="android:textColorHint">?android:attr/textColorHintInverse</item>
        <item name="android:textColorHighlight">?android:attr/textColorHighlightInverse</item>
        <item name="android:textColorLink">?android:attr/textColorLinkInverse</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Menu">
        <item name="android:textSize">@dimen/abc_text_size_menu_material</item>
        <item name="android:textColor">?android:textColorPrimary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.SearchResult" parent="">
        <item name="android:textStyle">normal</item>
        <item name="android:textColor">?android:textColorPrimary</item>
        <item name="android:textColorHint">?android:textColorHint</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.SearchResult.Subtitle">
        <item name="android:textSize">14sp</item>
        <item name="android:textColor">?android:textColorSecondary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.SearchResult.Title">
        <item name="android:textSize">18sp</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Small">
        <item name="android:textSize">@dimen/abc_text_size_small_material</item>
        <item name="android:textColor">?android:attr/textColorTertiary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Small.Inverse">
        <item name="android:textColor">?android:attr/textColorTertiaryInverse</item>
        <item name="android:textColorHint">?android:attr/textColorHintInverse</item>
        <item name="android:textColorHighlight">?android:attr/textColorHighlightInverse</item>
        <item name="android:textColorLink">?android:attr/textColorLinkInverse</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Subhead">
        <item name="android:textSize">@dimen/abc_text_size_subhead_material</item>
        <item name="android:textColor">?android:textColorPrimary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Subhead.Inverse">
        <item name="android:textColor">?android:attr/textColorPrimaryInverse</item>
        <item name="android:textColorHint">?android:attr/textColorHintInverse</item>
        <item name="android:textColorHighlight">?android:attr/textColorHighlightInverse</item>
        <item name="android:textColorLink">?android:attr/textColorLinkInverse</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Title">
        <item name="android:textSize">@dimen/abc_text_size_title_material</item>
        <item name="android:textColor">?android:textColorPrimary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Title.Inverse">
        <item name="android:textColor">?android:attr/textColorPrimaryInverse</item>
        <item name="android:textColorHint">?android:attr/textColorHintInverse</item>
        <item name="android:textColorHighlight">?android:attr/textColorHighlightInverse</item>
        <item name="android:textColorLink">?android:attr/textColorLinkInverse</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Tooltip">
        <item name="android:textSize">14sp</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.ActionBar.Menu" parent="TextAppearance.AppCompat.Button">
        <item name="android:textColor">?attr/actionMenuTextColor</item>
        <item name="textAllCaps">@bool/abc_config_actionMenuItemAllCaps</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle" parent="TextAppearance.AppCompat.Subhead">
        <item name="android:textSize">@dimen/abc_text_size_subtitle_material_toolbar</item>
        <item name="android:textColor">?android:attr/textColorSecondary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse" parent="TextAppearance.AppCompat.Subhead.Inverse">
        <item name="android:textSize">@dimen/abc_text_size_subtitle_material_toolbar</item>
        <item name="android:textColor">?android:attr/textColorSecondaryInverse</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.ActionBar.Title" parent="TextAppearance.AppCompat.Title">
        <item name="android:textSize">@dimen/abc_text_size_title_material_toolbar</item>
        <item name="android:textColor">?android:attr/textColorPrimary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse" parent="TextAppearance.AppCompat.Title.Inverse">
        <item name="android:textSize">@dimen/abc_text_size_title_material_toolbar</item>
        <item name="android:textColor">?android:attr/textColorPrimaryInverse</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.ActionMode.Subtitle" parent="TextAppearance.AppCompat.Widget.ActionBar.Subtitle"/>
    <style name="Base.TextAppearance.AppCompat.Widget.ActionMode.Title" parent="TextAppearance.AppCompat.Widget.ActionBar.Title"/>
    <style name="Base.TextAppearance.AppCompat.Widget.Button" parent="TextAppearance.AppCompat.Button"/>
    <style name="Base.TextAppearance.AppCompat.Widget.Button.Borderless.Colored" parent="Base.TextAppearance.AppCompat.Widget.Button">
        <item name="android:textColor">@color/abc_btn_colored_borderless_text_material</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.Button.Colored">
        <item name="android:textColor">@color/abc_btn_colored_text_material</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.Button.Inverse" parent="TextAppearance.AppCompat.Button">
        <item name="android:textColor">?android:textColorPrimaryInverse</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.DropDownItem" parent="android:TextAppearance.Small">
        <item name="android:textColor">?android:attr/textColorPrimaryDisableOnly</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.PopupMenu.Header" parent="TextAppearance.AppCompat">
        <item name="android:textSize">@dimen/abc_text_size_menu_header_material</item>
        <item name="android:textColor">?attr/colorAccent</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.PopupMenu.Large" parent="TextAppearance.AppCompat.Menu"/>
    <style name="Base.TextAppearance.AppCompat.Widget.PopupMenu.Small" parent="TextAppearance.AppCompat.Menu"/>
    <style name="Base.TextAppearance.AppCompat.Widget.Switch" parent="TextAppearance.AppCompat.Button"/>
    <style name="Base.TextAppearance.AppCompat.Widget.TextView.SpinnerItem" parent="TextAppearance.AppCompat.Menu"/>
    <style name="Base.TextAppearance.Widget.AppCompat.ExpandedMenu.Item" parent="android:TextAppearance.Medium">
        <item name="android:textColor">?android:attr/textColorPrimaryDisableOnly</item>
    </style>
    <style name="Base.TextAppearance.Widget.AppCompat.Toolbar.Subtitle" parent="TextAppearance.AppCompat.Widget.ActionBar.Subtitle">
    </style>
    <style name="Base.TextAppearance.Widget.AppCompat.Toolbar.Title" parent="TextAppearance.AppCompat.Widget.ActionBar.Title">
    </style>
    <style name="Base.Theme.AppCompat" parent="Base.V7.Theme.AppCompat">
    </style>
    <style name="Base.Theme.AppCompat.CompactMenu" parent="">
        <item name="android:itemTextAppearance">?android:attr/textAppearanceMedium</item>
        <item name="android:listViewStyle">@style/Widget.AppCompat.ListView.Menu</item>
        <item name="android:windowAnimationStyle">@style/Animation.AppCompat.DropDownUp</item>
    </style>
    <style name="Base.Theme.AppCompat.Dialog" parent="Base.V7.Theme.AppCompat.Dialog"/>
    <style name="Base.Theme.AppCompat.Dialog.Alert">
        <item name="android:windowMinWidthMajor">@dimen/abc_dialog_min_width_major</item>
        <item name="android:windowMinWidthMinor">@dimen/abc_dialog_min_width_minor</item>
    </style>
    <style name="Base.Theme.AppCompat.Dialog.FixedSize">
        <item name="windowFixedWidthMajor">@dimen/abc_dialog_fixed_width_major</item>
        <item name="windowFixedWidthMinor">@dimen/abc_dialog_fixed_width_minor</item>
        <item name="windowFixedHeightMajor">@dimen/abc_dialog_fixed_height_major</item>
        <item name="windowFixedHeightMinor">@dimen/abc_dialog_fixed_height_minor</item>
    </style>
    <style name="Base.Theme.AppCompat.Dialog.MinWidth">
        <item name="android:windowMinWidthMajor">@dimen/abc_dialog_min_width_major</item>
        <item name="android:windowMinWidthMinor">@dimen/abc_dialog_min_width_minor</item>
    </style>
    <style name="Base.Theme.AppCompat.DialogWhenLarge" parent="Theme.AppCompat"/>
    <style name="Base.Theme.AppCompat.Light" parent="Base.V7.Theme.AppCompat.Light">
    </style>
    <style name="Base.Theme.AppCompat.Light.DarkActionBar" parent="Base.Theme.AppCompat.Light">
        <item name="actionBarPopupTheme">@style/ThemeOverlay.AppCompat.Light</item>
        <item name="actionBarWidgetTheme">@null</item>
        <item name="actionBarTheme">@style/ThemeOverlay.AppCompat.Dark.ActionBar</item>

        
        <item name="listChoiceBackgroundIndicator">@drawable/abc_list_selector_holo_dark</item>

        <item name="colorPrimaryDark">@color/primary_dark_material_dark</item>
        <item name="colorPrimary">@color/primary_material_dark</item>
    </style>
    <style name="Base.Theme.AppCompat.Light.Dialog" parent="Base.V7.Theme.AppCompat.Light.Dialog"/>
    <style name="Base.Theme.AppCompat.Light.Dialog.Alert">
        <item name="android:windowMinWidthMajor">@dimen/abc_dialog_min_width_major</item>
        <item name="android:windowMinWidthMinor">@dimen/abc_dialog_min_width_minor</item>
    </style>
    <style name="Base.Theme.AppCompat.Light.Dialog.FixedSize">
        <item name="windowFixedWidthMajor">@dimen/abc_dialog_fixed_width_major</item>
        <item name="windowFixedWidthMinor">@dimen/abc_dialog_fixed_width_minor</item>
        <item name="windowFixedHeightMajor">@dimen/abc_dialog_fixed_height_major</item>
        <item name="windowFixedHeightMinor">@dimen/abc_dialog_fixed_height_minor</item>
    </style>
    <style name="Base.Theme.AppCompat.Light.Dialog.MinWidth">
        <item name="android:windowMinWidthMajor">@dimen/abc_dialog_min_width_major</item>
        <item name="android:windowMinWidthMinor">@dimen/abc_dialog_min_width_minor</item>
    </style>
    <style name="Base.Theme.AppCompat.Light.DialogWhenLarge" parent="Theme.AppCompat.Light"/>
    <style name="Base.Theme.MaterialComponents" parent="Base.V14.Theme.MaterialComponents"/>
    <style name="Base.Theme.MaterialComponents.Bridge" parent="Base.V14.Theme.MaterialComponents.Bridge"/>
    <style name="Base.Theme.MaterialComponents.CompactMenu" parent="">
    <item name="android:itemTextAppearance">?android:attr/textAppearanceMedium</item>
    <item name="android:listViewStyle">@style/Widget.AppCompat.ListView.Menu</item>
    <item name="android:windowAnimationStyle">@style/Animation.AppCompat.DropDownUp</item>
  </style>
    <style name="Base.Theme.MaterialComponents.Dialog" parent="Base.V14.Theme.MaterialComponents.Dialog"/>
    <style name="Base.Theme.MaterialComponents.Dialog.Alert">
    <item name="windowMinWidthMajor">@dimen/abc_dialog_min_width_major</item>
    <item name="windowMinWidthMinor">@dimen/abc_dialog_min_width_minor</item>
  </style>
    <style name="Base.Theme.MaterialComponents.Dialog.FixedSize">
    <item name="windowFixedWidthMajor">@dimen/abc_dialog_fixed_width_major</item>
    <item name="windowFixedWidthMinor">@dimen/abc_dialog_fixed_width_minor</item>
    <item name="windowFixedHeightMajor">@dimen/abc_dialog_fixed_height_major</item>
    <item name="windowFixedHeightMinor">@dimen/abc_dialog_fixed_height_minor</item>
  </style>
    <style name="Base.Theme.MaterialComponents.Dialog.MinWidth">
    <item name="windowMinWidthMajor">@dimen/abc_dialog_min_width_major</item>
    <item name="windowMinWidthMinor">@dimen/abc_dialog_min_width_minor</item>
  </style>
    <style name="Base.Theme.MaterialComponents.DialogWhenLarge" parent="Theme.MaterialComponents"/>
    <style name="Base.Theme.MaterialComponents.Light" parent="Base.V14.Theme.MaterialComponents.Light"/>
    <style name="Base.Theme.MaterialComponents.Light.Bridge" parent="Base.V14.Theme.MaterialComponents.Light.Bridge"/>
    <style name="Base.Theme.MaterialComponents.Light.DarkActionBar" parent="Base.Theme.MaterialComponents.Light">
    <item name="actionBarPopupTheme">@style/ThemeOverlay.MaterialComponents.Light</item>
    <item name="actionBarWidgetTheme">@null</item>
    <item name="actionBarTheme">@style/ThemeOverlay.MaterialComponents.Dark.ActionBar</item>

    
    <item name="listChoiceBackgroundIndicator">@drawable/abc_list_selector_holo_dark</item>

    <item name="colorPrimaryDark">@color/primary_dark_material_dark</item>
    <item name="colorPrimary">@color/primary_material_dark</item>
  </style>
    <style name="Base.Theme.MaterialComponents.Light.DarkActionBar.Bridge" parent="Base.V14.Theme.MaterialComponents.Light.DarkActionBar.Bridge"/>
    <style name="Base.Theme.MaterialComponents.Light.Dialog" parent="Base.V14.Theme.MaterialComponents.Light.Dialog"/>
    <style name="Base.Theme.MaterialComponents.Light.Dialog.Alert">
    <item name="windowMinWidthMajor">@dimen/abc_dialog_min_width_major</item>
    <item name="windowMinWidthMinor">@dimen/abc_dialog_min_width_minor</item>
  </style>
    <style name="Base.Theme.MaterialComponents.Light.Dialog.FixedSize">
    <item name="windowFixedWidthMajor">@dimen/abc_dialog_fixed_width_major</item>
    <item name="windowFixedWidthMinor">@dimen/abc_dialog_fixed_width_minor</item>
    <item name="windowFixedHeightMajor">@dimen/abc_dialog_fixed_height_major</item>
    <item name="windowFixedHeightMinor">@dimen/abc_dialog_fixed_height_minor</item>
  </style>
    <style name="Base.Theme.MaterialComponents.Light.Dialog.MinWidth">
    <item name="windowMinWidthMajor">@dimen/abc_dialog_min_width_major</item>
    <item name="windowMinWidthMinor">@dimen/abc_dialog_min_width_minor</item>
  </style>
    <style name="Base.Theme.MaterialComponents.Light.DialogWhenLarge" parent="Theme.MaterialComponents.Light"/>
    <style name="Base.ThemeOverlay.AppCompat" parent="Platform.ThemeOverlay.AppCompat"/>
    <style name="Base.ThemeOverlay.AppCompat.ActionBar">
        <item name="colorControlNormal">?android:attr/textColorPrimary</item>
        <item name="searchViewStyle">@style/Widget.AppCompat.SearchView.ActionBar</item>
    </style>
    <style name="Base.ThemeOverlay.AppCompat.Dark" parent="Platform.ThemeOverlay.AppCompat.Dark">
        <item name="android:windowBackground">@color/background_material_dark</item>
        <item name="android:colorForeground">@color/foreground_material_dark</item>
        <item name="android:colorForegroundInverse">@color/foreground_material_light</item>
        <item name="android:colorBackground">@color/background_material_dark</item>
        <item name="android:colorBackgroundCacheHint">@color/abc_background_cache_hint_selector_material_dark</item>
        <item name="colorBackgroundFloating">@color/background_floating_material_dark</item>

        <item name="android:textColorPrimary">@color/abc_primary_text_material_dark</item>
        <item name="android:textColorPrimaryInverse">@color/abc_primary_text_material_light</item>
        <item name="android:textColorPrimaryDisableOnly">@color/abc_primary_text_disable_only_material_dark</item>
        <item name="android:textColorSecondary">@color/abc_secondary_text_material_dark</item>
        <item name="android:textColorSecondaryInverse">@color/abc_secondary_text_material_light</item>
        <item name="android:textColorTertiary">@color/abc_secondary_text_material_dark</item>
        <item name="android:textColorTertiaryInverse">@color/abc_secondary_text_material_light</item>
        <item name="android:textColorHint">@color/abc_hint_foreground_material_dark</item>
        <item name="android:textColorHintInverse">@color/abc_hint_foreground_material_light</item>
        <item name="android:textColorHighlight">@color/highlighted_text_material_dark</item>

        <item name="colorControlNormal">?android:attr/textColorSecondary</item>
        <item name="colorControlHighlight">@color/ripple_material_dark</item>
        <item name="colorButtonNormal">@color/button_material_dark</item>
        <item name="colorSwitchThumbNormal">@color/switch_thumb_material_dark</item>

        
        <item name="isLightTheme">false</item>
    </style>
    <style name="Base.ThemeOverlay.AppCompat.Dark.ActionBar">
        <item name="colorControlNormal">?android:attr/textColorPrimary</item>
        <item name="searchViewStyle">@style/Widget.AppCompat.SearchView.ActionBar</item>
    </style>
    <style name="Base.ThemeOverlay.AppCompat.Dialog" parent="Base.V7.ThemeOverlay.AppCompat.Dialog"/>
    <style name="Base.ThemeOverlay.AppCompat.Dialog.Alert">
        <item name="android:windowMinWidthMajor">@dimen/abc_dialog_min_width_major</item>
        <item name="android:windowMinWidthMinor">@dimen/abc_dialog_min_width_minor</item>
    </style>
    <style name="Base.ThemeOverlay.AppCompat.Light" parent="Platform.ThemeOverlay.AppCompat.Light">
        <item name="android:windowBackground">@color/background_material_light</item>
        <item name="android:colorForeground">@color/foreground_material_light</item>
        <item name="android:colorForegroundInverse">@color/foreground_material_dark</item>
        <item name="android:colorBackground">@color/background_material_light</item>
        <item name="android:colorBackgroundCacheHint">@color/abc_background_cache_hint_selector_material_light</item>
        <item name="colorBackgroundFloating">@color/background_floating_material_light</item>

        <item name="android:textColorPrimary">@color/abc_primary_text_material_light</item>
        <item name="android:textColorPrimaryInverse">@color/abc_primary_text_material_dark</item>
        <item name="android:textColorSecondary">@color/abc_secondary_text_material_light</item>
        <item name="android:textColorSecondaryInverse">@color/abc_secondary_text_material_dark</item>
        <item name="android:textColorTertiary">@color/abc_secondary_text_material_light</item>
        <item name="android:textColorTertiaryInverse">@color/abc_secondary_text_material_dark</item>
        <item name="android:textColorPrimaryDisableOnly">@color/abc_primary_text_disable_only_material_light</item>
        <item name="android:textColorPrimaryInverseDisableOnly">@color/abc_primary_text_disable_only_material_dark</item>
        <item name="android:textColorHint">@color/abc_hint_foreground_material_light</item>
        <item name="android:textColorHintInverse">@color/abc_hint_foreground_material_dark</item>
        <item name="android:textColorHighlight">@color/highlighted_text_material_light</item>

        <item name="colorControlNormal">?android:attr/textColorSecondary</item>
        <item name="colorControlHighlight">@color/ripple_material_light</item>
        <item name="colorButtonNormal">@color/button_material_light</item>
        <item name="colorSwitchThumbNormal">@color/switch_thumb_material_light</item>

        
        <item name="isLightTheme">true</item>
    </style>
    <style name="Base.ThemeOverlay.MaterialComponents.Dialog" parent="Base.V14.ThemeOverlay.MaterialComponents.Dialog"/>
    <style name="Base.ThemeOverlay.MaterialComponents.Dialog.Alert" parent="Base.V14.ThemeOverlay.MaterialComponents.Dialog.Alert"/>
    <style name="Base.V14.Theme.MaterialComponents" parent="Base.V14.Theme.MaterialComponents.Bridge">
    <item name="viewInflaterClass">android.support.design.theme.MaterialComponentsViewInflater</item>

    
    <item name="colorPrimary">@color/design_default_color_primary</item>
    <item name="colorPrimaryDark">@color/design_default_color_primary_dark</item>
    <item name="colorAccent">?attr/colorSecondary</item>

    
    <item name="borderlessButtonStyle">@style/Widget.MaterialComponents.Button.TextButton</item>
    <item name="bottomAppBarStyle">@style/Widget.MaterialComponents.BottomAppBar</item>
    <item name="bottomNavigationStyle">@style/Widget.MaterialComponents.BottomNavigationView.Colored</item>
    <item name="chipStyle">@style/Widget.MaterialComponents.Chip.Action</item>
    <item name="chipGroupStyle">@style/Widget.MaterialComponents.ChipGroup</item>
    <item name="chipStandaloneStyle">@style/Widget.MaterialComponents.Chip.Entry</item>
    <item name="floatingActionButtonStyle">@style/Widget.MaterialComponents.FloatingActionButton</item>
    <item name="materialButtonStyle">@style/Widget.MaterialComponents.Button</item>
    <item name="materialCardViewStyle">@style/Widget.MaterialComponents.CardView</item>
    <item name="navigationViewStyle">@style/Widget.MaterialComponents.NavigationView</item>
    <item name="snackbarStyle">@style/Widget.MaterialComponents.Snackbar</item>
    <item name="snackbarButtonStyle">?attr/borderlessButtonStyle</item>
    <item name="tabStyle">@style/Widget.MaterialComponents.TabLayout.Colored</item>
    <item name="textInputStyle">@style/Widget.Design.TextInputLayout</item>
    <item name="toolbarStyle">@style/Widget.MaterialComponents.Toolbar</item>

    
    <item name="alertDialogTheme">@style/ThemeOverlay.MaterialComponents.Dialog.Alert</item>
    <item name="android:datePickerDialogTheme" ns2:ignore="NewApi">@style/ThemeOverlay.MaterialComponents.Dialog</item>
    <item name="android:timePickerDialogTheme" ns2:ignore="NewApi">@style/ThemeOverlay.MaterialComponents.Dialog</item>
  </style>
    <style name="Base.V14.Theme.MaterialComponents.Bridge" parent="Platform.MaterialComponents">
    <item name="colorSecondary">?attr/colorPrimary</item>

    <item name="scrimBackground">@color/mtrl_scrim_color</item>

    
    <item name="textAppearanceHeadline1">@style/TextAppearance.MaterialComponents.Headline1</item>
    <item name="textAppearanceHeadline2">@style/TextAppearance.MaterialComponents.Headline2</item>
    <item name="textAppearanceHeadline3">@style/TextAppearance.MaterialComponents.Headline3</item>
    <item name="textAppearanceHeadline4">@style/TextAppearance.MaterialComponents.Headline4</item>
    <item name="textAppearanceHeadline5">@style/TextAppearance.MaterialComponents.Headline5</item>
    <item name="textAppearanceHeadline6">@style/TextAppearance.MaterialComponents.Headline6</item>
    <item name="textAppearanceSubtitle1">@style/TextAppearance.MaterialComponents.Subtitle1</item>
    <item name="textAppearanceSubtitle2">@style/TextAppearance.MaterialComponents.Subtitle2</item>
    <item name="textAppearanceBody1">@style/TextAppearance.MaterialComponents.Body1</item>
    <item name="textAppearanceBody2">@style/TextAppearance.MaterialComponents.Body2</item>
    <item name="textAppearanceCaption">@style/TextAppearance.MaterialComponents.Caption</item>
    <item name="textAppearanceButton">@style/TextAppearance.MaterialComponents.Button</item>
    <item name="textAppearanceOverline">@style/TextAppearance.MaterialComponents.Overline</item>
  </style>
    <style name="Base.V14.Theme.MaterialComponents.Dialog" parent="Platform.MaterialComponents.Dialog">
    <item name="viewInflaterClass">android.support.design.theme.MaterialComponentsViewInflater</item>

    
    <item name="colorPrimary">@color/design_default_color_primary</item>
    <item name="colorPrimaryDark">@color/design_default_color_primary_dark</item>

    <item name="colorSecondary">?attr/colorPrimary</item>

    <item name="colorAccent">?attr/colorSecondary</item>

    <item name="scrimBackground">@color/mtrl_scrim_color</item>

    
    <item name="borderlessButtonStyle">@style/Widget.MaterialComponents.Button.TextButton</item>
    <item name="bottomAppBarStyle">@style/Widget.MaterialComponents.BottomAppBar</item>
    <item name="bottomNavigationStyle">@style/Widget.MaterialComponents.BottomNavigationView.Colored</item>
    <item name="buttonBarButtonStyle">@style/Widget.MaterialComponents.Button.TextButton.Dialog</item>
    <item name="chipStyle">@style/Widget.MaterialComponents.Chip.Action</item>
    <item name="chipGroupStyle">@style/Widget.MaterialComponents.ChipGroup</item>
    <item name="chipStandaloneStyle">@style/Widget.MaterialComponents.Chip.Entry</item>
    <item name="floatingActionButtonStyle">@style/Widget.MaterialComponents.FloatingActionButton</item>
    <item name="materialButtonStyle">@style/Widget.MaterialComponents.Button</item>
    <item name="materialCardViewStyle">@style/Widget.MaterialComponents.CardView</item>
    <item name="navigationViewStyle">@style/Widget.MaterialComponents.NavigationView</item>
    <item name="snackbarStyle">@style/Widget.MaterialComponents.Snackbar</item>
    <item name="snackbarButtonStyle">?attr/borderlessButtonStyle</item>
    <item name="tabStyle">@style/Widget.MaterialComponents.TabLayout.Colored</item>
    <item name="textInputStyle">@style/Widget.Design.TextInputLayout</item>
    <item name="toolbarStyle">@style/Widget.MaterialComponents.Toolbar</item>

    
    <item name="textAppearanceHeadline1">@style/TextAppearance.MaterialComponents.Headline1</item>
    <item name="textAppearanceHeadline2">@style/TextAppearance.MaterialComponents.Headline2</item>
    <item name="textAppearanceHeadline3">@style/TextAppearance.MaterialComponents.Headline3</item>
    <item name="textAppearanceHeadline4">@style/TextAppearance.MaterialComponents.Headline4</item>
    <item name="textAppearanceHeadline5">@style/TextAppearance.MaterialComponents.Headline5</item>
    <item name="textAppearanceHeadline6">@style/TextAppearance.MaterialComponents.Headline6</item>
    <item name="textAppearanceSubtitle1">@style/TextAppearance.MaterialComponents.Subtitle1</item>
    <item name="textAppearanceSubtitle2">@style/TextAppearance.MaterialComponents.Subtitle2</item>
    <item name="textAppearanceBody1">@style/TextAppearance.MaterialComponents.Body1</item>
    <item name="textAppearanceBody2">@style/TextAppearance.MaterialComponents.Body2</item>
    <item name="textAppearanceCaption">@style/TextAppearance.MaterialComponents.Caption</item>
    <item name="textAppearanceButton">@style/TextAppearance.MaterialComponents.Button</item>
    <item name="textAppearanceOverline">@style/TextAppearance.MaterialComponents.Overline</item>
  </style>
    <style name="Base.V14.Theme.MaterialComponents.Light" parent="Base.V14.Theme.MaterialComponents.Light.Bridge">
    <item name="viewInflaterClass">android.support.design.theme.MaterialComponentsViewInflater</item>

    
    <item name="colorPrimary">@color/design_default_color_primary</item>
    <item name="colorPrimaryDark">@color/design_default_color_primary_dark</item>
    <item name="colorAccent">?attr/colorSecondary</item>

    
    <item name="borderlessButtonStyle">@style/Widget.MaterialComponents.Button.TextButton</item>
    <item name="bottomAppBarStyle">@style/Widget.MaterialComponents.BottomAppBar</item>
    <item name="bottomNavigationStyle">@style/Widget.MaterialComponents.BottomNavigationView</item>
    <item name="chipStyle">@style/Widget.MaterialComponents.Chip.Action</item>
    <item name="chipGroupStyle">@style/Widget.MaterialComponents.ChipGroup</item>
    <item name="chipStandaloneStyle">@style/Widget.MaterialComponents.Chip.Entry</item>
    <item name="floatingActionButtonStyle">@style/Widget.MaterialComponents.FloatingActionButton</item>
    <item name="materialButtonStyle">@style/Widget.MaterialComponents.Button</item>
    <item name="materialCardViewStyle">@style/Widget.MaterialComponents.CardView</item>
    <item name="navigationViewStyle">@style/Widget.MaterialComponents.NavigationView</item>
    <item name="snackbarStyle">@style/Widget.MaterialComponents.Snackbar</item>
    <item name="snackbarButtonStyle">?attr/borderlessButtonStyle</item>
    <item name="tabStyle">@style/Widget.MaterialComponents.TabLayout</item>
    <item name="textInputStyle">@style/Widget.Design.TextInputLayout</item>
    <item name="toolbarStyle">@style/Widget.MaterialComponents.Toolbar</item>

    
    <item name="alertDialogTheme">@style/ThemeOverlay.MaterialComponents.Dialog.Alert</item>
    <item name="android:datePickerDialogTheme" ns2:ignore="NewApi">@style/ThemeOverlay.MaterialComponents.Dialog</item>
    <item name="android:timePickerDialogTheme" ns2:ignore="NewApi">@style/ThemeOverlay.MaterialComponents.Dialog</item>
  </style>
    <style name="Base.V14.Theme.MaterialComponents.Light.Bridge" parent="Platform.MaterialComponents.Light">
    <item name="colorSecondary">?attr/colorPrimary</item>

    <item name="scrimBackground">@color/mtrl_scrim_color</item>

    
    <item name="textAppearanceHeadline1">@style/TextAppearance.MaterialComponents.Headline1</item>
    <item name="textAppearanceHeadline2">@style/TextAppearance.MaterialComponents.Headline2</item>
    <item name="textAppearanceHeadline3">@style/TextAppearance.MaterialComponents.Headline3</item>
    <item name="textAppearanceHeadline4">@style/TextAppearance.MaterialComponents.Headline4</item>
    <item name="textAppearanceHeadline5">@style/TextAppearance.MaterialComponents.Headline5</item>
    <item name="textAppearanceHeadline6">@style/TextAppearance.MaterialComponents.Headline6</item>
    <item name="textAppearanceSubtitle1">@style/TextAppearance.MaterialComponents.Subtitle1</item>
    <item name="textAppearanceSubtitle2">@style/TextAppearance.MaterialComponents.Subtitle2</item>
    <item name="textAppearanceBody1">@style/TextAppearance.MaterialComponents.Body1</item>
    <item name="textAppearanceBody2">@style/TextAppearance.MaterialComponents.Body2</item>
    <item name="textAppearanceCaption">@style/TextAppearance.MaterialComponents.Caption</item>
    <item name="textAppearanceButton">@style/TextAppearance.MaterialComponents.Button</item>
    <item name="textAppearanceOverline">@style/TextAppearance.MaterialComponents.Overline</item>
  </style>
    <style name="Base.V14.Theme.MaterialComponents.Light.DarkActionBar.Bridge" parent="Theme.AppCompat.Light.DarkActionBar">
    <item name="colorSecondary">?attr/colorPrimary</item>

    <item name="scrimBackground">@color/mtrl_scrim_color</item>

    
    <item name="textAppearanceHeadline1">@style/TextAppearance.MaterialComponents.Headline1</item>
    <item name="textAppearanceHeadline2">@style/TextAppearance.MaterialComponents.Headline2</item>
    <item name="textAppearanceHeadline3">@style/TextAppearance.MaterialComponents.Headline3</item>
    <item name="textAppearanceHeadline4">@style/TextAppearance.MaterialComponents.Headline4</item>
    <item name="textAppearanceHeadline5">@style/TextAppearance.MaterialComponents.Headline5</item>
    <item name="textAppearanceHeadline6">@style/TextAppearance.MaterialComponents.Headline6</item>
    <item name="textAppearanceSubtitle1">@style/TextAppearance.MaterialComponents.Subtitle1</item>
    <item name="textAppearanceSubtitle2">@style/TextAppearance.MaterialComponents.Subtitle2</item>
    <item name="textAppearanceBody1">@style/TextAppearance.MaterialComponents.Body1</item>
    <item name="textAppearanceBody2">@style/TextAppearance.MaterialComponents.Body2</item>
    <item name="textAppearanceCaption">@style/TextAppearance.MaterialComponents.Caption</item>
    <item name="textAppearanceButton">@style/TextAppearance.MaterialComponents.Button</item>
    <item name="textAppearanceOverline">@style/TextAppearance.MaterialComponents.Overline</item>
  </style>
    <style name="Base.V14.Theme.MaterialComponents.Light.Dialog" parent="Platform.MaterialComponents.Light.Dialog">
    <item name="viewInflaterClass">android.support.design.theme.MaterialComponentsViewInflater</item>

    
    <item name="colorPrimary">@color/design_default_color_primary</item>
    <item name="colorPrimaryDark">@color/design_default_color_primary_dark</item>

    <item name="colorSecondary">?attr/colorPrimary</item>

    <item name="colorAccent">?attr/colorSecondary</item>

    <item name="scrimBackground">@color/mtrl_scrim_color</item>

    
    <item name="borderlessButtonStyle">@style/Widget.MaterialComponents.Button.TextButton</item>
    <item name="bottomAppBarStyle">@style/Widget.MaterialComponents.BottomAppBar</item>
    <item name="bottomNavigationStyle">@style/Widget.MaterialComponents.BottomNavigationView</item>
    <item name="buttonBarButtonStyle">@style/Widget.MaterialComponents.Button.TextButton.Dialog</item>
    <item name="chipStyle">@style/Widget.MaterialComponents.Chip.Action</item>
    <item name="chipGroupStyle">@style/Widget.MaterialComponents.ChipGroup</item>
    <item name="chipStandaloneStyle">@style/Widget.MaterialComponents.Chip.Entry</item>
    <item name="floatingActionButtonStyle">@style/Widget.MaterialComponents.FloatingActionButton</item>
    <item name="materialButtonStyle">@style/Widget.MaterialComponents.Button</item>
    <item name="materialCardViewStyle">@style/Widget.MaterialComponents.CardView</item>
    <item name="navigationViewStyle">@style/Widget.MaterialComponents.NavigationView</item>
    <item name="snackbarStyle">@style/Widget.MaterialComponents.Snackbar</item>
    <item name="snackbarButtonStyle">?attr/borderlessButtonStyle</item>
    <item name="tabStyle">@style/Widget.MaterialComponents.TabLayout</item>
    <item name="textInputStyle">@style/Widget.Design.TextInputLayout</item>
    <item name="toolbarStyle">@style/Widget.MaterialComponents.Toolbar</item>

    
    <item name="textAppearanceHeadline1">@style/TextAppearance.MaterialComponents.Headline1</item>
    <item name="textAppearanceHeadline2">@style/TextAppearance.MaterialComponents.Headline2</item>
    <item name="textAppearanceHeadline3">@style/TextAppearance.MaterialComponents.Headline3</item>
    <item name="textAppearanceHeadline4">@style/TextAppearance.MaterialComponents.Headline4</item>
    <item name="textAppearanceHeadline5">@style/TextAppearance.MaterialComponents.Headline5</item>
    <item name="textAppearanceHeadline6">@style/TextAppearance.MaterialComponents.Headline6</item>
    <item name="textAppearanceSubtitle1">@style/TextAppearance.MaterialComponents.Subtitle1</item>
    <item name="textAppearanceSubtitle2">@style/TextAppearance.MaterialComponents.Subtitle2</item>
    <item name="textAppearanceBody1">@style/TextAppearance.MaterialComponents.Body1</item>
    <item name="textAppearanceBody2">@style/TextAppearance.MaterialComponents.Body2</item>
    <item name="textAppearanceCaption">@style/TextAppearance.MaterialComponents.Caption</item>
    <item name="textAppearanceButton">@style/TextAppearance.MaterialComponents.Button</item>
    <item name="textAppearanceOverline">@style/TextAppearance.MaterialComponents.Overline</item>
  </style>
    <style name="Base.V14.ThemeOverlay.MaterialComponents.Dialog" parent="ThemeOverlay.AppCompat.Dialog">
    
    <item name="materialButtonStyle">@style/Widget.MaterialComponents.Button.TextButton.Dialog</item>
  </style>
    <style name="Base.V14.ThemeOverlay.MaterialComponents.Dialog.Alert" parent="ThemeOverlay.AppCompat.Dialog.Alert">
    
    <item name="buttonBarButtonStyle">@style/Widget.MaterialComponents.Button.TextButton.Dialog</item>
  </style>
    <style name="Base.V7.Theme.AppCompat" parent="Platform.AppCompat">
        <item name="viewInflaterClass">android.support.v7.app.AppCompatViewInflater</item>
        <item name="windowNoTitle">false</item>
        <item name="windowActionBar">true</item>
        <item name="windowActionBarOverlay">false</item>
        <item name="windowActionModeOverlay">false</item>
        <item name="actionBarPopupTheme">@null</item>

        <item name="colorBackgroundFloating">@color/background_floating_material_dark</item>

        
        <item name="isLightTheme">false</item>

        <item name="selectableItemBackground">@drawable/abc_item_background_holo_dark</item>
        <item name="selectableItemBackgroundBorderless">?attr/selectableItemBackground</item>
        <item name="borderlessButtonStyle">@style/Widget.AppCompat.Button.Borderless</item>
        <item name="homeAsUpIndicator">@drawable/abc_ic_ab_back_material</item>

        <item name="dividerVertical">@drawable/abc_list_divider_mtrl_alpha</item>
        <item name="dividerHorizontal">@drawable/abc_list_divider_mtrl_alpha</item>

        
        <item name="actionBarTabStyle">@style/Widget.AppCompat.ActionBar.TabView</item>
        <item name="actionBarTabBarStyle">@style/Widget.AppCompat.ActionBar.TabBar</item>
        <item name="actionBarTabTextStyle">@style/Widget.AppCompat.ActionBar.TabText</item>
        <item name="actionButtonStyle">@style/Widget.AppCompat.ActionButton</item>
        <item name="actionOverflowButtonStyle">@style/Widget.AppCompat.ActionButton.Overflow</item>
        <item name="actionOverflowMenuStyle">@style/Widget.AppCompat.PopupMenu.Overflow</item>
        <item name="actionBarStyle">@style/Widget.AppCompat.ActionBar.Solid</item>
        <item name="actionBarSplitStyle">?attr/actionBarStyle</item>
        <item name="actionBarWidgetTheme">@null</item>
        <item name="actionBarTheme">@style/ThemeOverlay.AppCompat.ActionBar</item>
        <item name="actionBarSize">@dimen/abc_action_bar_default_height_material</item>
        <item name="actionBarDivider">?attr/dividerVertical</item>
        <item name="actionBarItemBackground">?attr/selectableItemBackgroundBorderless</item>
        <item name="actionMenuTextAppearance">@style/TextAppearance.AppCompat.Widget.ActionBar.Menu</item>
        <item name="actionMenuTextColor">?android:attr/textColorPrimaryDisableOnly</item>

        
        <item name="actionDropDownStyle">@style/Widget.AppCompat.Spinner.DropDown.ActionBar</item>

        
        <item name="actionModeStyle">@style/Widget.AppCompat.ActionMode</item>
        <item name="actionModeBackground">@drawable/abc_cab_background_top_material</item>
        <item name="actionModeSplitBackground">?attr/colorPrimaryDark</item>
        <item name="actionModeCloseDrawable">@drawable/abc_ic_ab_back_material</item>
        <item name="actionModeCloseButtonStyle">@style/Widget.AppCompat.ActionButton.CloseMode</item>

        <item name="actionModeCutDrawable">@drawable/abc_ic_menu_cut_mtrl_alpha</item>
        <item name="actionModeCopyDrawable">@drawable/abc_ic_menu_copy_mtrl_am_alpha</item>
        <item name="actionModePasteDrawable">@drawable/abc_ic_menu_paste_mtrl_am_alpha</item>
        <item name="actionModeSelectAllDrawable">@drawable/abc_ic_menu_selectall_mtrl_alpha</item>
        <item name="actionModeShareDrawable">@drawable/abc_ic_menu_share_mtrl_alpha</item>

        
        <item name="panelMenuListWidth">@dimen/abc_panel_menu_list_width</item>
        <item name="panelMenuListTheme">@style/Theme.AppCompat.CompactMenu</item>
        <item name="panelBackground">@drawable/abc_menu_hardkey_panel_mtrl_mult</item>
        <item name="android:panelBackground">@android:color/transparent</item>
        <item name="listChoiceBackgroundIndicator">@drawable/abc_list_selector_holo_dark</item>

        
        <item name="textAppearanceListItem">@style/TextAppearance.AppCompat.Subhead</item>
        <item name="textAppearanceListItemSmall">@style/TextAppearance.AppCompat.Subhead</item>
        <item name="textAppearanceListItemSecondary">@style/TextAppearance.AppCompat.Body1</item>
        <item name="listPreferredItemHeight">64dp</item>
        <item name="listPreferredItemHeightSmall">48dp</item>
        <item name="listPreferredItemHeightLarge">80dp</item>
        <item name="listPreferredItemPaddingLeft">@dimen/abc_list_item_padding_horizontal_material</item>
        <item name="listPreferredItemPaddingRight">@dimen/abc_list_item_padding_horizontal_material</item>

        
        <item name="spinnerStyle">@style/Widget.AppCompat.Spinner</item>
        <item name="android:spinnerItemStyle">@style/Widget.AppCompat.TextView.SpinnerItem</item>
        <item name="android:dropDownListViewStyle">@style/Widget.AppCompat.ListView.DropDown</item>

        
        <item name="spinnerDropDownItemStyle">@style/Widget.AppCompat.DropDownItem.Spinner</item>
        <item name="dropdownListPreferredItemHeight">?attr/listPreferredItemHeightSmall</item>

        
        <item name="popupMenuStyle">@style/Widget.AppCompat.PopupMenu</item>
        <item name="textAppearanceLargePopupMenu">@style/TextAppearance.AppCompat.Widget.PopupMenu.Large</item>
        <item name="textAppearanceSmallPopupMenu">@style/TextAppearance.AppCompat.Widget.PopupMenu.Small</item>
        <item name="textAppearancePopupMenuHeader">@style/TextAppearance.AppCompat.Widget.PopupMenu.Header</item>
        <item name="listPopupWindowStyle">@style/Widget.AppCompat.ListPopupWindow</item>
        <item name="dropDownListViewStyle">?android:attr/dropDownListViewStyle</item>
        <item name="listMenuViewStyle">@style/Widget.AppCompat.ListMenuView</item>

        
        <item name="searchViewStyle">@style/Widget.AppCompat.SearchView</item>
        <item name="android:dropDownItemStyle">@style/Widget.AppCompat.DropDownItem.Spinner</item>
        <item name="textColorSearchUrl">@color/abc_search_url_text</item>
        <item name="textAppearanceSearchResultTitle">@style/TextAppearance.AppCompat.SearchResult.Title</item>
        <item name="textAppearanceSearchResultSubtitle">@style/TextAppearance.AppCompat.SearchResult.Subtitle</item>

        
        <item name="activityChooserViewStyle">@style/Widget.AppCompat.ActivityChooserView</item>

        
        <item name="toolbarStyle">@style/Widget.AppCompat.Toolbar</item>
        <item name="toolbarNavigationButtonStyle">@style/Widget.AppCompat.Toolbar.Button.Navigation</item>

        <item name="editTextStyle">@style/Widget.AppCompat.EditText</item>
        <item name="editTextBackground">@drawable/abc_edit_text_material</item>
        <item name="editTextColor">?android:attr/textColorPrimary</item>
        <item name="autoCompleteTextViewStyle">@style/Widget.AppCompat.AutoCompleteTextView</item>

        
        <item name="colorPrimaryDark">@color/primary_dark_material_dark</item>
        <item name="colorPrimary">@color/primary_material_dark</item>
        <item name="colorAccent">@color/accent_material_dark</item>

        <item name="colorControlNormal">?android:attr/textColorSecondary</item>
        <item name="colorControlActivated">?attr/colorAccent</item>
        <item name="colorControlHighlight">@color/ripple_material_dark</item>
        <item name="colorButtonNormal">@color/button_material_dark</item>
        <item name="colorSwitchThumbNormal">@color/switch_thumb_material_dark</item>
        <item name="controlBackground">?attr/selectableItemBackgroundBorderless</item>

        <item name="drawerArrowStyle">@style/Widget.AppCompat.DrawerArrowToggle</item>

        <item name="checkboxStyle">@style/Widget.AppCompat.CompoundButton.CheckBox</item>
        <item name="radioButtonStyle">@style/Widget.AppCompat.CompoundButton.RadioButton</item>
        <item name="switchStyle">@style/Widget.AppCompat.CompoundButton.Switch</item>

        <item name="ratingBarStyle">@style/Widget.AppCompat.RatingBar</item>
        <item name="ratingBarStyleIndicator">@style/Widget.AppCompat.RatingBar.Indicator</item>
        <item name="ratingBarStyleSmall">@style/Widget.AppCompat.RatingBar.Small</item>
        <item name="seekBarStyle">@style/Widget.AppCompat.SeekBar</item>

        
        <item name="buttonStyle">@style/Widget.AppCompat.Button</item>
        <item name="buttonStyleSmall">@style/Widget.AppCompat.Button.Small</item>
        <item name="android:textAppearanceButton">@style/TextAppearance.AppCompat.Widget.Button</item>

        <item name="imageButtonStyle">@style/Widget.AppCompat.ImageButton</item>

        <item name="buttonBarStyle">@style/Widget.AppCompat.ButtonBar</item>
        <item name="buttonBarButtonStyle">@style/Widget.AppCompat.Button.ButtonBar.AlertDialog</item>
        <item name="buttonBarPositiveButtonStyle">?attr/buttonBarButtonStyle</item>
        <item name="buttonBarNegativeButtonStyle">?attr/buttonBarButtonStyle</item>
        <item name="buttonBarNeutralButtonStyle">?attr/buttonBarButtonStyle</item>

        
        <item name="dialogTheme">@style/ThemeOverlay.AppCompat.Dialog</item>
        <item name="dialogPreferredPadding">@dimen/abc_dialog_padding_material</item>
        <item name="dialogCornerRadius">@dimen/abc_dialog_corner_radius_material</item>

        <item name="alertDialogTheme">@style/ThemeOverlay.AppCompat.Dialog.Alert</item>
        <item name="alertDialogStyle">@style/AlertDialog.AppCompat</item>
        <item name="alertDialogCenterButtons">false</item>
        <item name="textColorAlertDialogListItem">@color/abc_primary_text_material_dark</item>
        <item name="listDividerAlertDialog">@null</item>

        
        <item name="windowFixedWidthMajor">@null</item>
        <item name="windowFixedWidthMinor">@null</item>
        <item name="windowFixedHeightMajor">@null</item>
        <item name="windowFixedHeightMinor">@null</item>

        
        <item name="tooltipFrameBackground">@drawable/tooltip_frame_light</item>
        <item name="tooltipForegroundColor">@color/foreground_material_light</item>

        <item name="colorError">@color/error_color_material_dark</item>
    </style>
    <style name="Base.V7.Theme.AppCompat.Dialog" parent="Base.Theme.AppCompat">
        <item name="android:colorBackground">?attr/colorBackgroundFloating</item>
        <item name="android:colorBackgroundCacheHint">@null</item>

        <item name="android:windowFrame">@null</item>
        <item name="android:windowTitleStyle">@style/RtlOverlay.DialogWindowTitle.AppCompat</item>
        <item name="android:windowTitleBackgroundStyle">@style/Base.DialogWindowTitleBackground.AppCompat</item>
        <item name="android:windowBackground">@drawable/abc_dialog_material_background</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:backgroundDimEnabled">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowAnimationStyle">@style/Animation.AppCompat.Dialog</item>
        <item name="android:windowSoftInputMode">stateUnspecified|adjustPan</item>

        <item name="windowActionBar">false</item>
        <item name="windowActionModeOverlay">true</item>

        <item name="listPreferredItemPaddingLeft">24dip</item>
        <item name="listPreferredItemPaddingRight">24dip</item>

        <item name="android:listDivider">@null</item>

        <item name="android:buttonBarStyle">@style/Widget.AppCompat.ButtonBar.AlertDialog</item>
        <item name="android:borderlessButtonStyle">@style/Widget.AppCompat.Button.Borderless</item>
        <item name="android:windowCloseOnTouchOutside">true</item>
    </style>
    <style name="Base.V7.Theme.AppCompat.Light" parent="Platform.AppCompat.Light">
        <item name="viewInflaterClass">android.support.v7.app.AppCompatViewInflater</item>

        <item name="windowNoTitle">false</item>
        <item name="windowActionBar">true</item>
        <item name="windowActionBarOverlay">false</item>
        <item name="windowActionModeOverlay">false</item>
        <item name="actionBarPopupTheme">@null</item>

        <item name="colorBackgroundFloating">@color/background_floating_material_light</item>

        
        <item name="isLightTheme">true</item>

        <item name="selectableItemBackground">@drawable/abc_item_background_holo_light</item>
        <item name="selectableItemBackgroundBorderless">?attr/selectableItemBackground</item>
        <item name="borderlessButtonStyle">@style/Widget.AppCompat.Button.Borderless</item>
        <item name="homeAsUpIndicator">@drawable/abc_ic_ab_back_material</item>

        <item name="dividerVertical">@drawable/abc_list_divider_mtrl_alpha</item>
        <item name="dividerHorizontal">@drawable/abc_list_divider_mtrl_alpha</item>

        
        <item name="actionBarTabStyle">@style/Widget.AppCompat.Light.ActionBar.TabView</item>
        <item name="actionBarTabBarStyle">@style/Widget.AppCompat.Light.ActionBar.TabBar</item>
        <item name="actionBarTabTextStyle">@style/Widget.AppCompat.Light.ActionBar.TabText</item>
        <item name="actionButtonStyle">@style/Widget.AppCompat.Light.ActionButton</item>
        <item name="actionOverflowButtonStyle">@style/Widget.AppCompat.Light.ActionButton.Overflow</item>
        <item name="actionOverflowMenuStyle">@style/Widget.AppCompat.Light.PopupMenu.Overflow</item>
        <item name="actionBarStyle">@style/Widget.AppCompat.Light.ActionBar.Solid</item>
        <item name="actionBarSplitStyle">?attr/actionBarStyle</item>
        <item name="actionBarWidgetTheme">@null</item>
        <item name="actionBarTheme">@style/ThemeOverlay.AppCompat.ActionBar</item>
        <item name="actionBarSize">@dimen/abc_action_bar_default_height_material</item>
        <item name="actionBarDivider">?attr/dividerVertical</item>
        <item name="actionBarItemBackground">?attr/selectableItemBackgroundBorderless</item>
        <item name="actionMenuTextAppearance">@style/TextAppearance.AppCompat.Widget.ActionBar.Menu</item>
        <item name="actionMenuTextColor">?android:attr/textColorPrimaryDisableOnly</item>

        
        <item name="actionModeStyle">@style/Widget.AppCompat.ActionMode</item>
        <item name="actionModeBackground">@drawable/abc_cab_background_top_material</item>
        <item name="actionModeSplitBackground">?attr/colorPrimaryDark</item>
        <item name="actionModeCloseDrawable">@drawable/abc_ic_ab_back_material</item>
        <item name="actionModeCloseButtonStyle">@style/Widget.AppCompat.ActionButton.CloseMode</item>

        <item name="actionModeCutDrawable">@drawable/abc_ic_menu_cut_mtrl_alpha</item>
        <item name="actionModeCopyDrawable">@drawable/abc_ic_menu_copy_mtrl_am_alpha</item>
        <item name="actionModePasteDrawable">@drawable/abc_ic_menu_paste_mtrl_am_alpha</item>
        <item name="actionModeSelectAllDrawable">@drawable/abc_ic_menu_selectall_mtrl_alpha</item>
        <item name="actionModeShareDrawable">@drawable/abc_ic_menu_share_mtrl_alpha</item>

        
        <item name="actionDropDownStyle">@style/Widget.AppCompat.Light.Spinner.DropDown.ActionBar</item>

        
        <item name="panelMenuListWidth">@dimen/abc_panel_menu_list_width</item>
        <item name="panelMenuListTheme">@style/Theme.AppCompat.CompactMenu</item>
        <item name="panelBackground">@drawable/abc_menu_hardkey_panel_mtrl_mult</item>
        <item name="android:panelBackground">@android:color/transparent</item>
        <item name="listChoiceBackgroundIndicator">@drawable/abc_list_selector_holo_light</item>

        
        <item name="textAppearanceListItem">@style/TextAppearance.AppCompat.Subhead</item>
        <item name="textAppearanceListItemSmall">@style/TextAppearance.AppCompat.Subhead</item>
        <item name="textAppearanceListItemSecondary">@style/TextAppearance.AppCompat.Body1</item>
        <item name="listPreferredItemHeight">64dp</item>
        <item name="listPreferredItemHeightSmall">48dp</item>
        <item name="listPreferredItemHeightLarge">80dp</item>
        <item name="listPreferredItemPaddingLeft">@dimen/abc_list_item_padding_horizontal_material</item>
        <item name="listPreferredItemPaddingRight">@dimen/abc_list_item_padding_horizontal_material</item>

        
        <item name="spinnerStyle">@style/Widget.AppCompat.Spinner</item>
        <item name="android:spinnerItemStyle">@style/Widget.AppCompat.TextView.SpinnerItem</item>
        <item name="android:dropDownListViewStyle">@style/Widget.AppCompat.ListView.DropDown</item>

        
        <item name="spinnerDropDownItemStyle">@style/Widget.AppCompat.DropDownItem.Spinner</item>
        <item name="dropdownListPreferredItemHeight">?attr/listPreferredItemHeightSmall</item>

        
        <item name="popupMenuStyle">@style/Widget.AppCompat.Light.PopupMenu</item>
        <item name="textAppearanceLargePopupMenu">@style/TextAppearance.AppCompat.Light.Widget.PopupMenu.Large</item>
        <item name="textAppearanceSmallPopupMenu">@style/TextAppearance.AppCompat.Light.Widget.PopupMenu.Small</item>
        <item name="textAppearancePopupMenuHeader">@style/TextAppearance.AppCompat.Widget.PopupMenu.Header</item>
        <item name="listPopupWindowStyle">@style/Widget.AppCompat.ListPopupWindow</item>
        <item name="dropDownListViewStyle">?android:attr/dropDownListViewStyle</item>
        <item name="listMenuViewStyle">@style/Widget.AppCompat.ListMenuView</item>

        
        <item name="searchViewStyle">@style/Widget.AppCompat.Light.SearchView</item>
        <item name="android:dropDownItemStyle">@style/Widget.AppCompat.DropDownItem.Spinner</item>
        <item name="textColorSearchUrl">@color/abc_search_url_text</item>
        <item name="textAppearanceSearchResultTitle">@style/TextAppearance.AppCompat.SearchResult.Title</item>
        <item name="textAppearanceSearchResultSubtitle">@style/TextAppearance.AppCompat.SearchResult.Subtitle</item>

        
        <item name="activityChooserViewStyle">@style/Widget.AppCompat.ActivityChooserView</item>

        
        <item name="toolbarStyle">@style/Widget.AppCompat.Toolbar</item>
        <item name="toolbarNavigationButtonStyle">@style/Widget.AppCompat.Toolbar.Button.Navigation</item>

        <item name="editTextStyle">@style/Widget.AppCompat.EditText</item>
        <item name="editTextBackground">@drawable/abc_edit_text_material</item>
        <item name="editTextColor">?android:attr/textColorPrimary</item>
        <item name="autoCompleteTextViewStyle">@style/Widget.AppCompat.AutoCompleteTextView</item>

        
        <item name="colorPrimaryDark">@color/primary_dark_material_light</item>
        <item name="colorPrimary">@color/primary_material_light</item>
        <item name="colorAccent">@color/accent_material_light</item>

        <item name="colorControlNormal">?android:attr/textColorSecondary</item>
        <item name="colorControlActivated">?attr/colorAccent</item>
        <item name="colorControlHighlight">@color/ripple_material_light</item>
        <item name="colorButtonNormal">@color/button_material_light</item>
        <item name="colorSwitchThumbNormal">@color/switch_thumb_material_light</item>
        <item name="controlBackground">?attr/selectableItemBackgroundBorderless</item>

        <item name="drawerArrowStyle">@style/Widget.AppCompat.DrawerArrowToggle</item>

        <item name="checkboxStyle">@style/Widget.AppCompat.CompoundButton.CheckBox</item>
        <item name="radioButtonStyle">@style/Widget.AppCompat.CompoundButton.RadioButton</item>
        <item name="switchStyle">@style/Widget.AppCompat.CompoundButton.Switch</item>

        <item name="ratingBarStyle">@style/Widget.AppCompat.RatingBar</item>
        <item name="ratingBarStyleIndicator">@style/Widget.AppCompat.RatingBar.Indicator</item>
        <item name="ratingBarStyleSmall">@style/Widget.AppCompat.RatingBar.Small</item>
        <item name="seekBarStyle">@style/Widget.AppCompat.SeekBar</item>

        
        <item name="buttonStyle">@style/Widget.AppCompat.Button</item>
        <item name="buttonStyleSmall">@style/Widget.AppCompat.Button.Small</item>
        <item name="android:textAppearanceButton">@style/TextAppearance.AppCompat.Widget.Button</item>

        <item name="imageButtonStyle">@style/Widget.AppCompat.ImageButton</item>

        <item name="buttonBarStyle">@style/Widget.AppCompat.ButtonBar</item>
        <item name="buttonBarButtonStyle">@style/Widget.AppCompat.Button.ButtonBar.AlertDialog</item>
        <item name="buttonBarPositiveButtonStyle">?attr/buttonBarButtonStyle</item>
        <item name="buttonBarNegativeButtonStyle">?attr/buttonBarButtonStyle</item>
        <item name="buttonBarNeutralButtonStyle">?attr/buttonBarButtonStyle</item>

        
        <item name="dialogTheme">@style/ThemeOverlay.AppCompat.Dialog</item>
        <item name="dialogPreferredPadding">@dimen/abc_dialog_padding_material</item>
        <item name="dialogCornerRadius">@dimen/abc_dialog_corner_radius_material</item>

        <item name="alertDialogTheme">@style/ThemeOverlay.AppCompat.Dialog.Alert</item>
        <item name="alertDialogStyle">@style/AlertDialog.AppCompat.Light</item>
        <item name="alertDialogCenterButtons">false</item>
        <item name="textColorAlertDialogListItem">@color/abc_primary_text_material_light</item>
        <item name="listDividerAlertDialog">@null</item>

        
        <item name="windowFixedWidthMajor">@null</item>
        <item name="windowFixedWidthMinor">@null</item>
        <item name="windowFixedHeightMajor">@null</item>
        <item name="windowFixedHeightMinor">@null</item>

        
        <item name="tooltipFrameBackground">@drawable/tooltip_frame_dark</item>
        <item name="tooltipForegroundColor">@color/foreground_material_dark</item>

        <item name="colorError">@color/error_color_material_light</item>
    </style>
    <style name="Base.V7.Theme.AppCompat.Light.Dialog" parent="Base.Theme.AppCompat.Light">
        <item name="android:colorBackground">?attr/colorBackgroundFloating</item>
        <item name="android:colorBackgroundCacheHint">@null</item>

        <item name="android:windowFrame">@null</item>
        <item name="android:windowTitleStyle">@style/RtlOverlay.DialogWindowTitle.AppCompat</item>
        <item name="android:windowTitleBackgroundStyle">@style/Base.DialogWindowTitleBackground.AppCompat</item>
        <item name="android:windowBackground">@drawable/abc_dialog_material_background</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:backgroundDimEnabled">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowAnimationStyle">@style/Animation.AppCompat.Dialog</item>
        <item name="android:windowSoftInputMode">stateUnspecified|adjustPan</item>

        <item name="windowActionBar">false</item>
        <item name="windowActionModeOverlay">true</item>

        <item name="listPreferredItemPaddingLeft">24dip</item>
        <item name="listPreferredItemPaddingRight">24dip</item>

        <item name="android:listDivider">@null</item>

        <item name="android:buttonBarStyle">@style/Widget.AppCompat.ButtonBar.AlertDialog</item>
        <item name="android:borderlessButtonStyle">@style/Widget.AppCompat.Button.Borderless</item>
        <item name="android:windowCloseOnTouchOutside">true</item>
    </style>
    <style name="Base.V7.ThemeOverlay.AppCompat.Dialog" parent="Base.ThemeOverlay.AppCompat">
        <item name="android:colorBackgroundCacheHint">@null</item>
        <item name="android:colorBackground">?attr/colorBackgroundFloating</item>

        <item name="android:windowFrame">@null</item>
        <item name="android:windowTitleStyle">@style/RtlOverlay.DialogWindowTitle.AppCompat</item>
        <item name="android:windowTitleBackgroundStyle">@style/Base.DialogWindowTitleBackground.AppCompat</item>
        <item name="android:windowBackground">@drawable/abc_dialog_material_background</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:backgroundDimEnabled">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowAnimationStyle">@style/Animation.AppCompat.Dialog</item>
        <item name="android:windowSoftInputMode">stateUnspecified|adjustPan</item>

        <item name="windowActionBar">false</item>
        <item name="windowActionModeOverlay">true</item>

        <item name="listPreferredItemPaddingLeft">24dip</item>
        <item name="listPreferredItemPaddingRight">24dip</item>

        <item name="android:listDivider">@null</item>

        <item name="windowFixedWidthMajor">@null</item>
        <item name="windowFixedWidthMinor">@null</item>
        <item name="windowFixedHeightMajor">@null</item>
        <item name="windowFixedHeightMinor">@null</item>

        <item name="android:buttonBarStyle">@style/Widget.AppCompat.ButtonBar.AlertDialog</item>
        <item name="android:borderlessButtonStyle">@style/Widget.AppCompat.Button.Borderless</item>
        <item name="android:windowCloseOnTouchOutside">true</item>
    </style>
    <style name="Base.V7.Widget.AppCompat.AutoCompleteTextView" parent="android:Widget.AutoCompleteTextView">
        <item name="android:dropDownSelector">?attr/listChoiceBackgroundIndicator</item>
        <item name="android:popupBackground">@drawable/abc_popup_background_mtrl_mult</item>
        <item name="android:background">?attr/editTextBackground</item>
        <item name="android:textColor">?attr/editTextColor</item>
        <item name="android:textAppearance">?android:attr/textAppearanceMediumInverse</item>
        <item name="android:textCursorDrawable">@drawable/abc_text_cursor_material</item>
    </style>
    <style name="Base.V7.Widget.AppCompat.EditText" parent="android:Widget.EditText">
        <item name="android:background">?attr/editTextBackground</item>
        <item name="android:textColor">?attr/editTextColor</item>
        <item name="android:textAppearance">?android:attr/textAppearanceMediumInverse</item>
        <item name="android:textCursorDrawable">@drawable/abc_text_cursor_material</item>
    </style>
    <style name="Base.V7.Widget.AppCompat.Toolbar" parent="android:Widget">
        <item name="titleTextAppearance">@style/TextAppearance.Widget.AppCompat.Toolbar.Title</item>
        <item name="subtitleTextAppearance">@style/TextAppearance.Widget.AppCompat.Toolbar.Subtitle</item>
        <item name="android:minHeight">?attr/actionBarSize</item>
        <item name="titleMargin">4dp</item>
        <item name="maxButtonHeight">@dimen/abc_action_bar_default_height_material</item>
        <item name="buttonGravity">top</item>
        <item name="collapseIcon">?attr/homeAsUpIndicator</item>
        <item name="collapseContentDescription">@string/abc_toolbar_collapse_description</item>
        <item name="contentInsetStart">16dp</item>
        <item name="contentInsetStartWithNavigation">@dimen/abc_action_bar_content_inset_with_nav</item>
        <item name="android:paddingLeft">@dimen/abc_action_bar_default_padding_start_material</item>
        <item name="android:paddingRight">@dimen/abc_action_bar_default_padding_end_material</item>
    </style>
    <style name="Base.Widget.AppCompat.ActionBar" parent="">
        <item name="displayOptions">showTitle</item>
        <item name="divider">?attr/dividerVertical</item>
        <item name="height">?attr/actionBarSize</item>

        <item name="titleTextStyle">@style/TextAppearance.AppCompat.Widget.ActionBar.Title</item>
        <item name="subtitleTextStyle">@style/TextAppearance.AppCompat.Widget.ActionBar.Subtitle</item>

        <item name="background">@null</item>
        <item name="backgroundStacked">@null</item>
        <item name="backgroundSplit">@null</item>

        <item name="actionButtonStyle">@style/Widget.AppCompat.ActionButton</item>
        <item name="actionOverflowButtonStyle">@style/Widget.AppCompat.ActionButton.Overflow</item>

        <item name="android:gravity">center_vertical</item>
        <item name="contentInsetStart">@dimen/abc_action_bar_content_inset_material</item>
        <item name="contentInsetStartWithNavigation">@dimen/abc_action_bar_content_inset_with_nav</item>
        <item name="contentInsetEnd">@dimen/abc_action_bar_content_inset_material</item>
        <item name="elevation">@dimen/abc_action_bar_elevation_material</item>
        <item name="popupTheme">?attr/actionBarPopupTheme</item>
    </style>
    <style name="Base.Widget.AppCompat.ActionBar.Solid">
        <item name="background">?attr/colorPrimary</item>
        <item name="backgroundStacked">?attr/colorPrimary</item>
        <item name="backgroundSplit">?attr/colorPrimary</item>
    </style>
    <style name="Base.Widget.AppCompat.ActionBar.TabBar" parent="">
        <item name="divider">?attr/actionBarDivider</item>
        <item name="showDividers">middle</item>
        <item name="dividerPadding">8dip</item>
    </style>
    <style name="Base.Widget.AppCompat.ActionBar.TabText" parent="">
        <item name="android:textAppearance">@style/TextAppearance.AppCompat.Medium</item>
        <item name="android:textColor">?android:attr/textColorPrimary</item>
        <item name="android:textSize">12sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:ellipsize">marquee</item>
        <item name="android:maxLines">2</item>
        <item name="android:maxWidth">180dp</item>
        <item name="textAllCaps">true</item>
    </style>
    <style name="Base.Widget.AppCompat.ActionBar.TabView" parent="">
        <item name="android:background">@drawable/abc_tab_indicator_material</item>
        <item name="android:gravity">center_horizontal</item>
        <item name="android:paddingLeft">16dip</item>
        <item name="android:paddingRight">16dip</item>
        <item name="android:layout_width">0dip</item>
        <item name="android:layout_weight">1</item>
        <item name="android:minWidth">80dip</item>
    </style>
    <style name="Base.Widget.AppCompat.ActionButton" parent="RtlUnderlay.Widget.AppCompat.ActionButton">
        <item name="android:background">?attr/actionBarItemBackground</item>
        <item name="android:minWidth">@dimen/abc_action_button_min_width_material</item>
        <item name="android:minHeight">@dimen/abc_action_button_min_height_material</item>
        <item name="android:scaleType">center</item>
        <item name="android:gravity">center</item>
        <item name="android:maxLines">2</item>
        <item name="textAllCaps">@bool/abc_config_actionMenuItemAllCaps</item>
    </style>
    <style name="Base.Widget.AppCompat.ActionButton.CloseMode">
        <item name="android:background">?attr/controlBackground</item>
        <item name="android:minWidth">56dp</item>
    </style>
    <style name="Base.Widget.AppCompat.ActionButton.Overflow" parent="RtlUnderlay.Widget.AppCompat.ActionButton.Overflow">
        <item name="srcCompat">@drawable/abc_ic_menu_overflow_material</item>
        <item name="android:background">?attr/actionBarItemBackground</item>
        <item name="android:contentDescription">@string/abc_action_menu_overflow_description</item>
        <item name="android:minWidth">@dimen/abc_action_button_min_width_overflow_material</item>
        <item name="android:minHeight">@dimen/abc_action_button_min_height_material</item>
    </style>
    <style name="Base.Widget.AppCompat.ActionMode" parent="">
        <item name="background">?attr/actionModeBackground</item>
        <item name="backgroundSplit">?attr/actionModeSplitBackground</item>
        <item name="height">?attr/actionBarSize</item>
        <item name="titleTextStyle">@style/TextAppearance.AppCompat.Widget.ActionMode.Title</item>
        <item name="subtitleTextStyle">@style/TextAppearance.AppCompat.Widget.ActionMode.Subtitle</item>
        <item name="closeItemLayout">@layout/abc_action_mode_close_item_material</item>
    </style>
    <style name="Base.Widget.AppCompat.ActivityChooserView" parent="">
        <item name="android:gravity">center</item>
        <item name="android:background">@drawable/abc_ab_share_pack_mtrl_alpha</item>
        <item name="divider">?attr/dividerVertical</item>
        <item name="showDividers">middle</item>
        <item name="dividerPadding">6dip</item>
    </style>
    <style name="Base.Widget.AppCompat.AutoCompleteTextView" parent="Base.V7.Widget.AppCompat.AutoCompleteTextView"/>
    <style name="Base.Widget.AppCompat.Button" parent="android:Widget">
        <item name="android:background">@drawable/abc_btn_default_mtrl_shape</item>
        <item name="android:textAppearance">?android:attr/textAppearanceButton</item>
        <item name="android:minHeight">48dip</item>
        <item name="android:minWidth">88dip</item>
        <item name="android:focusable">true</item>
        <item name="android:clickable">true</item>
        <item name="android:gravity">center_vertical|center_horizontal</item>
    </style>
    <style name="Base.Widget.AppCompat.Button.Borderless">
        <item name="android:background">@drawable/abc_btn_borderless_material</item>
    </style>
    <style name="Base.Widget.AppCompat.Button.Borderless.Colored">
        <item name="android:textAppearance">@style/TextAppearance.AppCompat.Widget.Button.Borderless.Colored</item>
    </style>
    <style name="Base.Widget.AppCompat.Button.ButtonBar.AlertDialog" parent="Widget.AppCompat.Button.Borderless.Colored">
        <item name="android:minWidth">64dp</item>
        <item name="android:minHeight">@dimen/abc_alert_dialog_button_bar_height</item>
    </style>
    <style name="Base.Widget.AppCompat.Button.Colored">
        <item name="android:background">@drawable/abc_btn_colored_material</item>
        <item name="android:textAppearance">@style/TextAppearance.AppCompat.Widget.Button.Colored</item>
    </style>
    <style name="Base.Widget.AppCompat.Button.Small">
        <item name="android:minHeight">48dip</item>
        <item name="android:minWidth">48dip</item>
    </style>
    <style name="Base.Widget.AppCompat.ButtonBar" parent="android:Widget">
        <item name="android:background">@null</item>
    </style>
    <style name="Base.Widget.AppCompat.ButtonBar.AlertDialog"/>
    <style name="Base.Widget.AppCompat.CompoundButton.CheckBox" parent="android:Widget.CompoundButton.CheckBox">
        <item name="android:button">?android:attr/listChoiceIndicatorMultiple</item>
        <item name="android:background">?attr/controlBackground</item>
    </style>
    <style name="Base.Widget.AppCompat.CompoundButton.RadioButton" parent="android:Widget.CompoundButton.RadioButton">
        <item name="android:button">?android:attr/listChoiceIndicatorSingle</item>
        <item name="android:background">?attr/controlBackground</item>
    </style>
    <style name="Base.Widget.AppCompat.CompoundButton.Switch" parent="android:Widget.CompoundButton">
        <item name="track">@drawable/abc_switch_track_mtrl_alpha</item>
        <item name="android:thumb">@drawable/abc_switch_thumb_material</item>
        <item name="switchTextAppearance">@style/TextAppearance.AppCompat.Widget.Switch</item>
        <item name="android:background">?attr/controlBackground</item>
        <item name="showText">false</item>
        <item name="switchPadding">@dimen/abc_switch_padding</item>
        <item name="android:textOn">@string/abc_capital_on</item>
        <item name="android:textOff">@string/abc_capital_off</item>
    </style>
    <style name="Base.Widget.AppCompat.DrawerArrowToggle" parent="Base.Widget.AppCompat.DrawerArrowToggle.Common">
        <item name="barLength">18dp</item>
        <item name="gapBetweenBars">3dp</item>
        <item name="drawableSize">24dp</item>
    </style>
    <style name="Base.Widget.AppCompat.DrawerArrowToggle.Common" parent="">
        <item name="color">?android:attr/textColorSecondary</item>
        <item name="spinBars">true</item>
        <item name="thickness">2dp</item>
        <item name="arrowShaftLength">16dp</item>
        <item name="arrowHeadLength">8dp</item>
    </style>
    <style name="Base.Widget.AppCompat.DropDownItem.Spinner" parent="">
        <item name="android:textAppearance">@style/TextAppearance.AppCompat.Widget.DropDownItem</item>
        <item name="android:paddingLeft">8dp</item>
        <item name="android:paddingRight">8dp</item>
        <item name="android:gravity">center_vertical</item>
    </style>
    <style name="Base.Widget.AppCompat.EditText" parent="Base.V7.Widget.AppCompat.EditText"/>
    <style name="Base.Widget.AppCompat.ImageButton" parent="android:Widget.ImageButton">
        <item name="android:background">@drawable/abc_btn_default_mtrl_shape</item>
    </style>
    <style name="Base.Widget.AppCompat.Light.ActionBar" parent="Base.Widget.AppCompat.ActionBar">
        <item name="actionButtonStyle">@style/Widget.AppCompat.Light.ActionButton</item>
        <item name="actionOverflowButtonStyle">@style/Widget.AppCompat.Light.ActionButton.Overflow</item>
    </style>
    <style name="Base.Widget.AppCompat.Light.ActionBar.Solid">
        <item name="background">?attr/colorPrimary</item>
        <item name="backgroundStacked">?attr/colorPrimary</item>
        <item name="backgroundSplit">?attr/colorPrimary</item>
    </style>
    <style name="Base.Widget.AppCompat.Light.ActionBar.TabBar" parent="Base.Widget.AppCompat.ActionBar.TabBar">
    </style>
    <style name="Base.Widget.AppCompat.Light.ActionBar.TabText" parent="Base.Widget.AppCompat.ActionBar.TabText">
    </style>
    <style name="Base.Widget.AppCompat.Light.ActionBar.TabText.Inverse" parent="Base.Widget.AppCompat.Light.ActionBar.TabText">
        <item name="android:textAppearance">@style/TextAppearance.AppCompat.Medium.Inverse</item>
    </style>
    <style name="Base.Widget.AppCompat.Light.ActionBar.TabView" parent="Base.Widget.AppCompat.ActionBar.TabView">
        <item name="android:background">@drawable/abc_tab_indicator_material</item>
    </style>
    <style name="Base.Widget.AppCompat.Light.PopupMenu" parent="@style/Widget.AppCompat.ListPopupWindow">
    </style>
    <style name="Base.Widget.AppCompat.Light.PopupMenu.Overflow">
        <item name="overlapAnchor">true</item>
        <item name="android:dropDownHorizontalOffset">-4dip</item>
    </style>
    <style name="Base.Widget.AppCompat.ListMenuView" parent="android:Widget">
        <item name="subMenuArrow">@drawable/abc_ic_arrow_drop_right_black_24dp</item>
    </style>
    <style name="Base.Widget.AppCompat.ListPopupWindow" parent="">
        <item name="android:dropDownSelector">?attr/listChoiceBackgroundIndicator</item>
        <item name="android:popupBackground">@drawable/abc_popup_background_mtrl_mult</item>
        <item name="android:dropDownVerticalOffset">0dip</item>
        <item name="android:dropDownHorizontalOffset">0dip</item>
        <item name="android:dropDownWidth">wrap_content</item>
    </style>
    <style name="Base.Widget.AppCompat.ListView" parent="android:Widget.ListView">
        <item name="android:listSelector">?attr/listChoiceBackgroundIndicator</item>
    </style>
    <style name="Base.Widget.AppCompat.ListView.DropDown">
        <item name="android:divider">@null</item>
    </style>
    <style name="Base.Widget.AppCompat.ListView.Menu" parent="android:Widget.ListView.Menu">
        <item name="android:listSelector">?attr/listChoiceBackgroundIndicator</item>
        <item name="android:divider">?attr/dividerHorizontal</item>
    </style>
    <style name="Base.Widget.AppCompat.PopupMenu" parent="@style/Widget.AppCompat.ListPopupWindow">
    </style>
    <style name="Base.Widget.AppCompat.PopupMenu.Overflow">
        <item name="overlapAnchor">true</item>
        <item name="android:dropDownHorizontalOffset">-4dip</item>
    </style>
    <style name="Base.Widget.AppCompat.PopupWindow" parent="android:Widget.PopupWindow">
    </style>
    <style name="Base.Widget.AppCompat.ProgressBar" parent="android:Widget.Holo.ProgressBar">
    </style>
    <style name="Base.Widget.AppCompat.ProgressBar.Horizontal" parent="android:Widget.Holo.ProgressBar.Horizontal">
    </style>
    <style name="Base.Widget.AppCompat.RatingBar" parent="android:Widget.RatingBar">
        <item name="android:progressDrawable">@drawable/abc_ratingbar_material</item>
        <item name="android:indeterminateDrawable">@drawable/abc_ratingbar_material</item>
    </style>
    <style name="Base.Widget.AppCompat.RatingBar.Indicator" parent="android:Widget.RatingBar">
        <item name="android:progressDrawable">@drawable/abc_ratingbar_indicator_material</item>
        <item name="android:indeterminateDrawable">@drawable/abc_ratingbar_indicator_material</item>
        <item name="android:minHeight">36dp</item>
        <item name="android:maxHeight">36dp</item>
        <item name="android:isIndicator">true</item>
        <item name="android:thumb">@null</item>
    </style>
    <style name="Base.Widget.AppCompat.RatingBar.Small" parent="android:Widget.RatingBar">
        <item name="android:progressDrawable">@drawable/abc_ratingbar_small_material</item>
        <item name="android:indeterminateDrawable">@drawable/abc_ratingbar_small_material</item>
        <item name="android:minHeight">16dp</item>
        <item name="android:maxHeight">16dp</item>
        <item name="android:isIndicator">true</item>
        <item name="android:thumb">@null</item>
    </style>
    <style name="Base.Widget.AppCompat.SearchView" parent="android:Widget">
        <item name="layout">@layout/abc_search_view</item>
        <item name="queryBackground">@drawable/abc_textfield_search_material</item>
        <item name="submitBackground">@drawable/abc_textfield_search_material</item>
        <item name="closeIcon">@drawable/abc_ic_clear_material</item>
        <item name="searchIcon">@drawable/abc_ic_search_api_material</item>
        <item name="searchHintIcon">@drawable/abc_ic_search_api_material</item>
        <item name="goIcon">@drawable/abc_ic_go_search_api_material</item>
        <item name="voiceIcon">@drawable/abc_ic_voice_search_api_material</item>
        <item name="commitIcon">@drawable/abc_ic_commit_search_api_mtrl_alpha</item>
        <item name="suggestionRowLayout">@layout/abc_search_dropdown_item_icons_2line</item>
    </style>
    <style name="Base.Widget.AppCompat.SearchView.ActionBar">
        <item name="queryBackground">@null</item>
        <item name="submitBackground">@null</item>
        <item name="searchHintIcon">@null</item>
        <item name="defaultQueryHint">@string/abc_search_hint</item>
    </style>
    <style name="Base.Widget.AppCompat.SeekBar" parent="android:Widget">
        <item name="android:indeterminateOnly">false</item>
        <item name="android:progressDrawable">@drawable/abc_seekbar_track_material</item>
        <item name="android:indeterminateDrawable">@drawable/abc_seekbar_track_material</item>
        <item name="android:thumb">@drawable/abc_seekbar_thumb_material</item>
        <item name="android:focusable">true</item>
        <item name="android:paddingLeft">16dip</item>
        <item name="android:paddingRight">16dip</item>
    </style>
    <style name="Base.Widget.AppCompat.SeekBar.Discrete">
        <item name="tickMark">@drawable/abc_seekbar_tick_mark_material</item>
    </style>
    <style name="Base.Widget.AppCompat.Spinner" parent="Platform.Widget.AppCompat.Spinner">
        <item name="android:background">@drawable/abc_spinner_mtrl_am_alpha</item>
        <item name="android:popupBackground">@drawable/abc_popup_background_mtrl_mult</item>
        <item name="android:dropDownSelector">?attr/listChoiceBackgroundIndicator</item>
        <item name="android:dropDownVerticalOffset">0dip</item>
        <item name="android:dropDownHorizontalOffset">0dip</item>
        <item name="android:dropDownWidth">wrap_content</item>
        <item name="android:clickable">true</item>
        <item name="android:gravity">left|start|center_vertical</item>
        <item name="overlapAnchor">true</item>
    </style>
    <style name="Base.Widget.AppCompat.Spinner.Underlined">
        <item name="android:background">@drawable/abc_spinner_textfield_background_material</item>
    </style>
    <style name="Base.Widget.AppCompat.TextView.SpinnerItem" parent="android:Widget.TextView.SpinnerItem">
        <item name="android:textAppearance">@style/TextAppearance.AppCompat.Widget.TextView.SpinnerItem</item>
        <item name="android:paddingLeft">8dp</item>
        <item name="android:paddingRight">8dp</item>
    </style>
    <style name="Base.Widget.AppCompat.Toolbar" parent="Base.V7.Widget.AppCompat.Toolbar"/>
    <style name="Base.Widget.AppCompat.Toolbar.Button.Navigation" parent="android:Widget">
        <item name="android:background">?attr/controlBackground</item>
        <item name="android:minWidth">56dp</item>
        <item name="android:scaleType">center</item>
    </style>
    <style name="Base.Widget.Design.TabLayout" parent="android:Widget">
    <item name="android:background">@null</item>
    <item name="tabIconTint">@null</item>
    <item name="tabMaxWidth">@dimen/design_tab_max_width</item>
    <item name="tabIndicatorAnimationDuration">@integer/design_tab_indicator_anim_duration_ms</item>
    <item name="tabIndicatorColor">?attr/colorAccent</item>
    <item name="tabIndicatorGravity">bottom</item>
    <item name="tabIndicator">@drawable/mtrl_tabs_default_indicator</item>
    <item name="tabPaddingStart">12dp</item>
    <item name="tabPaddingEnd">12dp</item>
    <item name="tabTextAppearance">@style/TextAppearance.Design.Tab</item>
    <item name="tabRippleColor">?attr/colorControlHighlight</item>
    <item name="tabUnboundedRipple">false</item>
  </style>
    <style name="Base.Widget.MaterialComponents.Chip" parent="android:Widget">
    <item name="android:focusable">true</item>
    <item name="android:clickable">true</item>
    <item name="android:checkable">false</item>
    <item name="android:stateListAnimator" ns2:ignore="NewApi">
      @animator/mtrl_chip_state_list_anim
    </item>

    <item name="chipIconVisible">true</item>
    <item name="checkedIconVisible">true</item>
    <item name="closeIconVisible">true</item>

    <item name="chipIcon">@null</item>
    <item name="checkedIcon">@drawable/ic_mtrl_chip_checked_circle</item>
    <item name="closeIcon">@drawable/ic_mtrl_chip_close_circle</item>

    <item name="android:text">@null</item>
    <item name="enforceTextAppearance">true</item>
    <item name="android:textAppearance">?attr/textAppearanceBody2</item>
    <item name="android:textColor">@color/mtrl_chip_text_color</item>
    <item name="closeIconTint">@color/mtrl_chip_close_icon_tint</item>

    <item name="chipBackgroundColor">@color/mtrl_chip_background_color</item>
    <item name="chipStrokeColor">#00000000</item>
    <item name="chipStrokeWidth">0dp</item>
    <item name="rippleColor">@color/mtrl_chip_ripple_color</item>

    <item name="chipMinHeight">32dp</item>
    <item name="chipCornerRadius">16dp</item>
    <item name="chipIconSize">24dp</item>
    <item name="closeIconSize">18dp</item>

    <item name="chipStartPadding">4dp</item>
    <item name="iconStartPadding">0dp</item>
    <item name="iconEndPadding">0dp</item>
    <item name="textStartPadding">8dp</item>
    <item name="textEndPadding">6dp</item>
    <item name="closeIconStartPadding">2dp</item>
    <item name="closeIconEndPadding">2dp</item>
    <item name="chipEndPadding">6dp</item>
  </style>
    <style name="Base.Widget.MaterialComponents.TextInputEditText" parent="Widget.AppCompat.EditText">
    <item name="android:paddingStart" ns2:ignore="NewApi">12dp</item>
    <item name="android:paddingEnd" ns2:ignore="NewApi">12dp</item>
    <item name="android:paddingLeft">12dp</item>
    <item name="android:paddingRight">12dp</item>
    <item name="android:paddingTop">16dp</item>
    <item name="android:paddingBottom">16dp</item>
  </style>
    <style name="Base.Widget.MaterialComponents.TextInputLayout" parent="Widget.Design.TextInputLayout">
    <item name="boxBackgroundMode">outline</item>
    <item name="boxBackgroundColor">@null</item>
    <item name="boxCollapsedPaddingTop">0dp</item>
    <item name="boxCornerRadiusTopStart">@dimen/mtrl_textinput_box_corner_radius_medium</item>
    <item name="boxCornerRadiusTopEnd">@dimen/mtrl_textinput_box_corner_radius_medium</item>
    <item name="boxCornerRadiusBottomEnd">@dimen/mtrl_textinput_box_corner_radius_medium</item>
    <item name="boxCornerRadiusBottomStart">@dimen/mtrl_textinput_box_corner_radius_medium</item>
    <item name="boxStrokeColor">?attr/colorControlActivated</item>
  </style>
    <style name="BeiZiAlertDialogStyle" parent="Theme.AppCompat.Light.NoActionBar">
        <item name="android:windowBackground">@android:color/white</item>
        <item name="android:windowFrame">@null</item>
        <item name="android:windowNoTitle">true</item>　　　　　　　　
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowAnimationStyle">@android:style/Animation.Dialog</item>
        <item name="android:backgroundDimEnabled">true</item>
        <item name="android:windowIsTranslucent">true</item>
    </style>
    <style name="BeiZiDialogStyle" parent="Theme.AppCompat.Light.NoActionBar">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowFrame">@null</item>
        <item name="android:windowNoTitle">true</item>　　　　　　　　
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowAnimationStyle">@android:style/Animation.Dialog</item>
        <item name="android:backgroundDimEnabled">true</item>
        <item name="android:windowIsTranslucent">true</item>
    </style>
    <style name="BeiZiDownloadConfirmDialogAnimationRight">
        <item name="android:windowEnterAnimation">@anim/download_confirm_dialog_slide_right_in</item>
    </style>
    <style name="BeiZiDownloadConfirmDialogAnimationUp">
        <item name="android:windowEnterAnimation">@anim/download_confirm_dialog_slide_up</item>
    </style>
    <style name="BeiZiDownloadConfirmDialogFullScreen" parent="android:Theme.Dialog">
        <item name="android:windowFullscreen">true</item>
    </style>
    <style name="BeiZiTheme" parent="android:Theme.Holo.Light.NoActionBar">
        
    </style>
    <style name="BeiZiTheme.Transparent" parent="BeiZiTheme">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:colorBackgroundCacheHint">@null</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowAnimationStyle">@android:style/Animation</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowContentOverlay">@null</item>
    </style>
    <style name="BottomAnimation" parent="android:Animation">
        <item name="android:windowEnterAnimation">@anim/ec_bottom_in</item>
        <item name="android:windowExitAnimation">@anim/ec_bottom_out</item>
    </style>
    <style name="Bullet.Bottom.Dialog.Animation" parent="android:Animation">
        <item name="android:windowEnterAnimation">@anim/bullet_bottom_dialog_enter</item>
        <item name="android:windowExitAnimation">@anim/bullet_bottom_dialog_exit</item>
    </style>
    <style name="CardView" parent="Base.CardView">
    </style>
    <style name="CardView.Dark">
        <item name="cardBackgroundColor">@color/cardview_dark_background</item>
    </style>
    <style name="CardView.Light">
        <item name="cardBackgroundColor">@color/cardview_light_background</item>
    </style>
    <style name="Dialog.BottomSheet.Transparent" parent="EC.Widget.Design.BottomSheet.Modal">
        <item name="android:background">#00000000</item>
    </style>
    <style name="DialogAnimationRight">
        <item name="android:windowEnterAnimation">@anim/slide_right_in</item>
    </style>
    <style name="DialogAnimationUp">
        <item name="android:windowEnterAnimation">@anim/slide_up</item>
    </style>
    <style name="DialogFullScreen" parent="android:Theme.Dialog">
        <item name="android:windowFullscreen">true</item>
    </style>
    <style name="EC.Widget.Design.BottomSheet.Modal" parent="android:Widget">
        <item name="android:background">?android:attr/colorBackground</item>
        <item name="android:elevation" ns2:ignore="NewApi">16dp</item>
        <item name="behavior_peekHeight">auto</item>
        <item name="behavior_hideable">true</item>
        <item name="behavior_skipCollapsed">false</item>
    </style>
    <style name="ECBaseDialogFragmentAnimation">
        <item name="android:windowEnterAnimation">@anim/ec_base_enter</item>
        <item name="android:windowExitAnimation">@anim/ec_base_exit</item>
    </style>
    <style name="ECBottomInWindowAnimation">
        <item name="android:windowEnterAnimation">@anim/ec_pop_bottom_in</item>
        <item name="android:windowExitAnimation">@anim/ec_pop_bottom_out</item>
    </style>
    <style name="ECBottomOutWindowAnimation">
        <item name="android:windowEnterAnimation">@null</item>
        <item name="android:windowExitAnimation">@anim/ec_pop_bottom_out</item>
    </style>
    <style name="ECHalfScreenAnchorV4Anime">
        <item name="android:windowAnimationStyle">@android:style/Animation.Translucent</item>
    </style>
    <style name="ECSlideInWindowAnimation">
        <item name="android:windowEnterAnimation">@anim/ec_pop_slide_in</item>
        <item name="android:windowExitAnimation">@anim/ec_pop_slide_out</item>
    </style>
    <style name="ECSlideOutWindowAnimation">
        <item name="android:windowEnterAnimation">@null</item>
        <item name="android:windowExitAnimation">@anim/ec_pop_slide_out</item>
    </style>
    <style name="EditTextStyle">
        <item name="android:layout_marginTop">5dp</item>
        <item name="android:layout_marginRight">10dp</item>
        <item name="android:layout_marginBottom">5dp</item>
        <item name="android:layout_marginLeft">10dp</item>
        <item name="android:background">@color/tt_white</item>
    </style>
    <style name="ExpandAnimation" parent="android:Animation">
        <item name="android:windowEnterAnimation">@anim/ec_zoom_in</item>
        <item name="android:windowExitAnimation">@anim/ec_zoom_out</item>
    </style>
    <style name="LivePromotionNoAnimationStyle">
        <item name="android:windowEnterAnimation">@null</item>
        <item name="android:windowExitAnimation">@null</item>
    </style>
    <style name="ODDialogStyle" parent="Theme.AppCompat.Dialog">
        
        <item name="android:windowBackground">@android:color/transparent</item>
        
        <item name="android:windowFrame">@null</item>
        
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowFullscreen">true</item>
    </style>
    <style name="ODFullscreen" parent="android:Theme.NoTitleBar.Fullscreen">
        
    </style>
    <style name="ODTabTextStyle">
        <item name="android:textSize">18sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textAllCaps">false</item>
    </style>
    <style mce_bogus="1" name="OSETDialogAnimation" parent="android:Animation">
        <item name="android:windowEnterAnimation">@anim/oset_dialog_enter</item>
        <item name="android:windowExitAnimation">@anim/oset_dialog_exit</item>
    </style>
    <style name="OSETDialogStyle" parent="Theme.AppCompat.Dialog">
        
        <item name="android:windowBackground">@android:color/transparent</item>
        
        <item name="android:windowFrame">@null</item>
        
        <item name="android:windowNoTitle">true</item>
    </style>
    <style name="Platform.AppCompat" parent="android:Theme.Holo">
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowActionBar">false</item>

        <item name="android:buttonBarStyle">?attr/buttonBarStyle</item>
        <item name="android:buttonBarButtonStyle">?attr/buttonBarButtonStyle</item>
        <item name="android:borderlessButtonStyle">?attr/borderlessButtonStyle</item>

        
        <item name="android:colorForeground">@color/foreground_material_dark</item>
        <item name="android:colorForegroundInverse">@color/foreground_material_light</item>
        <item name="android:colorBackground">@color/background_material_dark</item>
        <item name="android:colorBackgroundCacheHint">@color/abc_background_cache_hint_selector_material_dark</item>
        <item name="android:disabledAlpha">@dimen/abc_disabled_alpha_material_dark</item>
        <item name="android:backgroundDimAmount">0.6</item>
        <item name="android:windowBackground">@color/background_material_dark</item>

        
        <item name="android:textColorPrimary">@color/abc_primary_text_material_dark</item>
        <item name="android:textColorPrimaryInverse">@color/abc_primary_text_material_light</item>
        <item name="android:textColorPrimaryDisableOnly">@color/abc_primary_text_disable_only_material_dark</item>
        <item name="android:textColorSecondary">@color/abc_secondary_text_material_dark</item>
        <item name="android:textColorSecondaryInverse">@color/abc_secondary_text_material_light</item>
        <item name="android:textColorTertiary">@color/abc_secondary_text_material_dark</item>
        <item name="android:textColorTertiaryInverse">@color/abc_secondary_text_material_light</item>
        <item name="android:textColorHint">@color/abc_hint_foreground_material_dark</item>
        <item name="android:textColorHintInverse">@color/abc_hint_foreground_material_light</item>
        <item name="android:textColorHighlight">@color/highlighted_text_material_dark</item>
        <item name="android:textColorHighlightInverse">@color/highlighted_text_material_light</item>
        <item name="android:textColorLink">?attr/colorAccent</item>
        <item name="android:textColorLinkInverse">?attr/colorAccent</item>
        <item name="android:textColorAlertDialogListItem">@color/abc_primary_text_material_dark</item>

        
        <item name="android:textAppearance">@style/TextAppearance.AppCompat</item>
        <item name="android:textAppearanceInverse">@style/TextAppearance.AppCompat.Inverse</item>
        <item name="android:textAppearanceLarge">@style/TextAppearance.AppCompat.Large</item>
        <item name="android:textAppearanceLargeInverse">@style/TextAppearance.AppCompat.Large.Inverse</item>
        <item name="android:textAppearanceMedium">@style/TextAppearance.AppCompat.Medium</item>
        <item name="android:textAppearanceMediumInverse">@style/TextAppearance.AppCompat.Medium.Inverse</item>
        <item name="android:textAppearanceSmall">@style/TextAppearance.AppCompat.Small</item>
        <item name="android:textAppearanceSmallInverse">@style/TextAppearance.AppCompat.Small.Inverse</item>

        <item name="android:listChoiceIndicatorSingle">@drawable/abc_btn_radio_material</item>
        <item name="android:listChoiceIndicatorMultiple">@drawable/abc_btn_check_material</item>

        <item name="android:listPreferredItemPaddingLeft">@dimen/abc_list_item_padding_horizontal_material</item>
        <item name="android:listPreferredItemPaddingRight">@dimen/abc_list_item_padding_horizontal_material</item>

        <item name="android:actionModeCutDrawable">?actionModeCutDrawable</item>
        <item name="android:actionModeCopyDrawable">?actionModeCopyDrawable</item>
        <item name="android:actionModePasteDrawable">?actionModePasteDrawable</item>
        <item name="android:actionModeSelectAllDrawable">?actionModeSelectAllDrawable</item>

        <item name="android:textSelectHandle">@drawable/abc_text_select_handle_middle_mtrl_dark</item>
        <item name="android:textSelectHandleLeft">@drawable/abc_text_select_handle_left_mtrl_dark</item>
        <item name="android:textSelectHandleRight">@drawable/abc_text_select_handle_right_mtrl_dark</item>
    </style>
    <style name="Platform.AppCompat.Light" parent="android:Theme.Holo.Light">
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowActionBar">false</item>

        <item name="android:buttonBarStyle">?attr/buttonBarStyle</item>
        <item name="android:buttonBarButtonStyle">?attr/buttonBarButtonStyle</item>
        <item name="android:borderlessButtonStyle">?attr/borderlessButtonStyle</item>

        
        <item name="android:colorForeground">@color/foreground_material_light</item>
        <item name="android:colorForegroundInverse">@color/foreground_material_dark</item>
        <item name="android:colorBackground">@color/background_material_light</item>
        <item name="android:colorBackgroundCacheHint">@color/abc_background_cache_hint_selector_material_light</item>
        <item name="android:disabledAlpha">@dimen/abc_disabled_alpha_material_light</item>
        <item name="android:backgroundDimAmount">0.6</item>
        <item name="android:windowBackground">@color/background_material_light</item>

        
        <item name="android:textColorPrimary">@color/abc_primary_text_material_light</item>
        <item name="android:textColorPrimaryInverse">@color/abc_primary_text_material_dark</item>
        <item name="android:textColorSecondary">@color/abc_secondary_text_material_light</item>
        <item name="android:textColorSecondaryInverse">@color/abc_secondary_text_material_dark</item>
        <item name="android:textColorTertiary">@color/abc_secondary_text_material_light</item>
        <item name="android:textColorTertiaryInverse">@color/abc_secondary_text_material_dark</item>
        <item name="android:textColorPrimaryDisableOnly">@color/abc_primary_text_disable_only_material_light</item>
        <item name="android:textColorPrimaryInverseDisableOnly">@color/abc_primary_text_disable_only_material_dark</item>
        <item name="android:textColorHint">@color/abc_hint_foreground_material_light</item>
        <item name="android:textColorHintInverse">@color/abc_hint_foreground_material_dark</item>
        <item name="android:textColorHighlight">@color/highlighted_text_material_light</item>
        <item name="android:textColorHighlightInverse">@color/highlighted_text_material_dark</item>
        <item name="android:textColorLink">?attr/colorAccent</item>
        <item name="android:textColorLinkInverse">?attr/colorAccent</item>
        <item name="android:textColorAlertDialogListItem">@color/abc_primary_text_material_light</item>

        
        <item name="android:textAppearance">@style/TextAppearance.AppCompat</item>
        <item name="android:textAppearanceInverse">@style/TextAppearance.AppCompat.Inverse</item>
        <item name="android:textAppearanceLarge">@style/TextAppearance.AppCompat.Large</item>
        <item name="android:textAppearanceLargeInverse">@style/TextAppearance.AppCompat.Large.Inverse</item>
        <item name="android:textAppearanceMedium">@style/TextAppearance.AppCompat.Medium</item>
        <item name="android:textAppearanceMediumInverse">@style/TextAppearance.AppCompat.Medium.Inverse</item>
        <item name="android:textAppearanceSmall">@style/TextAppearance.AppCompat.Small</item>
        <item name="android:textAppearanceSmallInverse">@style/TextAppearance.AppCompat.Small.Inverse</item>

        <item name="android:listChoiceIndicatorSingle">@drawable/abc_btn_radio_material</item>
        <item name="android:listChoiceIndicatorMultiple">@drawable/abc_btn_check_material</item>

        <item name="android:listPreferredItemPaddingLeft">@dimen/abc_list_item_padding_horizontal_material</item>
        <item name="android:listPreferredItemPaddingRight">@dimen/abc_list_item_padding_horizontal_material</item>

        <item name="android:actionModeCutDrawable">?actionModeCutDrawable</item>
        <item name="android:actionModeCopyDrawable">?actionModeCopyDrawable</item>
        <item name="android:actionModePasteDrawable">?actionModePasteDrawable</item>
        <item name="android:actionModeSelectAllDrawable">?actionModeSelectAllDrawable</item>

        <item name="android:textSelectHandle">@drawable/abc_text_select_handle_middle_mtrl_light</item>
        <item name="android:textSelectHandleLeft">@drawable/abc_text_select_handle_left_mtrl_light</item>
        <item name="android:textSelectHandleRight">@drawable/abc_text_select_handle_right_mtrl_light</item>
    </style>
    <style name="Platform.MaterialComponents" parent="Theme.AppCompat"/>
    <style name="Platform.MaterialComponents.Dialog" parent="Theme.AppCompat.Dialog"/>
    <style name="Platform.MaterialComponents.Light" parent="Theme.AppCompat.Light"/>
    <style name="Platform.MaterialComponents.Light.Dialog" parent="Theme.AppCompat.Light.Dialog"/>
    <style name="Platform.ThemeOverlay.AppCompat" parent=""/>
    <style name="Platform.ThemeOverlay.AppCompat.Dark">
        
        <item name="actionBarItemBackground">@drawable/abc_item_background_holo_dark</item>
        <item name="actionDropDownStyle">@style/Widget.AppCompat.Spinner.DropDown.ActionBar</item>
        <item name="selectableItemBackground">@drawable/abc_item_background_holo_dark</item>

        
        <item name="android:autoCompleteTextViewStyle">@style/Widget.AppCompat.AutoCompleteTextView</item>
        <item name="android:dropDownItemStyle">@style/Widget.AppCompat.DropDownItem.Spinner</item>
    </style>
    <style name="Platform.ThemeOverlay.AppCompat.Light">
        <item name="actionBarItemBackground">@drawable/abc_item_background_holo_light</item>
        <item name="actionDropDownStyle">@style/Widget.AppCompat.Light.Spinner.DropDown.ActionBar</item>
        <item name="selectableItemBackground">@drawable/abc_item_background_holo_light</item>

        
        <item name="android:autoCompleteTextViewStyle">@style/Widget.AppCompat.Light.AutoCompleteTextView</item>
        <item name="android:dropDownItemStyle">@style/Widget.AppCompat.DropDownItem.Spinner</item>
    </style>
    <style name="Platform.Widget.AppCompat.Spinner" parent="android:Widget.Holo.Spinner"/>
    <style name="PopupWindowFadeAnimationStyle">
        <item name="android:windowEnterAnimation">@anim/shopping_popup_fade_in</item>
        <item name="android:windowExitAnimation">@anim/shopping_popup_fade_out</item>
    </style>
    <style name="RtlOverlay.DialogWindowTitle.AppCompat" parent="Base.DialogWindowTitle.AppCompat">
    </style>
    <style name="RtlOverlay.Widget.AppCompat.ActionBar.TitleItem" parent="android:Widget">
        <item name="android:layout_gravity">center_vertical|left</item>
        <item name="android:paddingRight">8dp</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.DialogTitle.Icon" parent="android:Widget">
        <item name="android:layout_marginRight">8dp</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.PopupMenuItem" parent="android:Widget">
        <item name="android:paddingRight">16dp</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.PopupMenuItem.InternalGroup" parent="android:Widget">
        <item name="android:layout_marginLeft">16dp</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.PopupMenuItem.Shortcut" parent="android:Widget">
        <item name="android:layout_marginLeft">16dp</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.PopupMenuItem.SubmenuArrow" parent="android:Widget">
        <item name="android:layout_marginLeft">8dp</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.PopupMenuItem.Text" parent="android:Widget">
        <item name="android:layout_alignParentLeft">true</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.PopupMenuItem.Title" parent="android:Widget">
        <item name="android:layout_marginLeft">16dp</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.Search.DropDown" parent="android:Widget">
        <item name="android:paddingLeft">@dimen/abc_dropdownitem_text_padding_left</item>
        <item name="android:paddingRight">4dp</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.Search.DropDown.Icon1" parent="android:Widget">
        <item name="android:layout_alignParentLeft">true</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.Search.DropDown.Icon2" parent="android:Widget">
        <item name="android:layout_toLeftOf">@id/edit_query</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.Search.DropDown.Query" parent="android:Widget">
        <item name="android:layout_alignParentRight">true</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.Search.DropDown.Text" parent="Base.Widget.AppCompat.DropDownItem.Spinner">
        <item name="android:layout_toLeftOf">@android:id/icon2</item>
        <item name="android:layout_toRightOf">@android:id/icon1</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.SearchView.MagIcon" parent="android:Widget">
        <item name="android:layout_marginLeft">@dimen/abc_dropdownitem_text_padding_left</item>
    </style>
    <style name="RtlUnderlay.Widget.AppCompat.ActionButton" parent="android:Widget">
        <item name="android:paddingLeft">12dp</item>
        <item name="android:paddingRight">12dp</item>
    </style>
    <style name="RtlUnderlay.Widget.AppCompat.ActionButton.Overflow" parent="Base.Widget.AppCompat.ActionButton">
        <item name="android:paddingLeft">@dimen/abc_action_bar_overflow_padding_start_material</item>
        <item name="android:paddingRight">@dimen/abc_action_bar_overflow_padding_end_material</item>
    </style>
    <style name="SKUPanelDialogAnimation">
        <item name="android:windowEnterAnimation">@anim/ec_bottom_in</item>
        <item name="android:windowExitAnimation">@anim/ec_bottom_out</item>
    </style>
    <style name="SlideAnimation" parent="android:Animation">
        <item name="android:windowEnterAnimation">@anim/ec_slide_in</item>
        <item name="android:windowExitAnimation">@anim/ec_slide_out</item>
    </style>
    <style name="StoreAppBottomSheetStyle" parent="EC.Widget.Design.BottomSheet.Modal">
        <item name="android:background">@android:color/transparent</item>
        <item name="android:windowAnimationStyle">@style/SKUPanelDialogAnimation</item>
    </style>
    <style name="TextAppearance.AppCompat" parent="Base.TextAppearance.AppCompat"/>
    <style name="TextAppearance.AppCompat.Body1" parent="Base.TextAppearance.AppCompat.Body1"/>
    <style name="TextAppearance.AppCompat.Body2" parent="Base.TextAppearance.AppCompat.Body2"/>
    <style name="TextAppearance.AppCompat.Button" parent="Base.TextAppearance.AppCompat.Button"/>
    <style name="TextAppearance.AppCompat.Caption" parent="Base.TextAppearance.AppCompat.Caption"/>
    <style name="TextAppearance.AppCompat.Display1" parent="Base.TextAppearance.AppCompat.Display1"/>
    <style name="TextAppearance.AppCompat.Display2" parent="Base.TextAppearance.AppCompat.Display2"/>
    <style name="TextAppearance.AppCompat.Display3" parent="Base.TextAppearance.AppCompat.Display3"/>
    <style name="TextAppearance.AppCompat.Display4" parent="Base.TextAppearance.AppCompat.Display4"/>
    <style name="TextAppearance.AppCompat.Headline" parent="Base.TextAppearance.AppCompat.Headline"/>
    <style name="TextAppearance.AppCompat.Inverse" parent="Base.TextAppearance.AppCompat.Inverse"/>
    <style name="TextAppearance.AppCompat.Large" parent="Base.TextAppearance.AppCompat.Large"/>
    <style name="TextAppearance.AppCompat.Large.Inverse" parent="Base.TextAppearance.AppCompat.Large.Inverse"/>
    <style name="TextAppearance.AppCompat.Light.SearchResult.Subtitle" parent="TextAppearance.AppCompat.SearchResult.Subtitle"/>
    <style name="TextAppearance.AppCompat.Light.SearchResult.Title" parent="TextAppearance.AppCompat.SearchResult.Title"/>
    <style name="TextAppearance.AppCompat.Light.Widget.PopupMenu.Large" parent="TextAppearance.AppCompat.Widget.PopupMenu.Large"/>
    <style name="TextAppearance.AppCompat.Light.Widget.PopupMenu.Small" parent="TextAppearance.AppCompat.Widget.PopupMenu.Small"/>
    <style name="TextAppearance.AppCompat.Medium" parent="Base.TextAppearance.AppCompat.Medium"/>
    <style name="TextAppearance.AppCompat.Medium.Inverse" parent="Base.TextAppearance.AppCompat.Medium.Inverse"/>
    <style name="TextAppearance.AppCompat.Menu" parent="Base.TextAppearance.AppCompat.Menu"/>
    <style name="TextAppearance.AppCompat.SearchResult.Subtitle" parent="Base.TextAppearance.AppCompat.SearchResult.Subtitle">
    </style>
    <style name="TextAppearance.AppCompat.SearchResult.Title" parent="Base.TextAppearance.AppCompat.SearchResult.Title">
    </style>
    <style name="TextAppearance.AppCompat.Small" parent="Base.TextAppearance.AppCompat.Small"/>
    <style name="TextAppearance.AppCompat.Small.Inverse" parent="Base.TextAppearance.AppCompat.Small.Inverse"/>
    <style name="TextAppearance.AppCompat.Subhead" parent="Base.TextAppearance.AppCompat.Subhead"/>
    <style name="TextAppearance.AppCompat.Subhead.Inverse" parent="Base.TextAppearance.AppCompat.Subhead.Inverse"/>
    <style name="TextAppearance.AppCompat.Title" parent="Base.TextAppearance.AppCompat.Title"/>
    <style name="TextAppearance.AppCompat.Title.Inverse" parent="Base.TextAppearance.AppCompat.Title.Inverse"/>
    <style name="TextAppearance.AppCompat.Tooltip" parent="Base.TextAppearance.AppCompat.Tooltip"/>
    <style name="TextAppearance.AppCompat.Widget.ActionBar.Menu" parent="Base.TextAppearance.AppCompat.Widget.ActionBar.Menu">
    </style>
    <style name="TextAppearance.AppCompat.Widget.ActionBar.Subtitle" parent="Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle"/>
    <style name="TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse" parent="Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse">
    </style>
    <style name="TextAppearance.AppCompat.Widget.ActionBar.Title" parent="Base.TextAppearance.AppCompat.Widget.ActionBar.Title"/>
    <style name="TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse" parent="Base.TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse">
    </style>
    <style name="TextAppearance.AppCompat.Widget.ActionMode.Subtitle" parent="Base.TextAppearance.AppCompat.Widget.ActionMode.Subtitle">
    </style>
    <style name="TextAppearance.AppCompat.Widget.ActionMode.Subtitle.Inverse" parent="TextAppearance.AppCompat.Widget.ActionMode.Subtitle"/>
    <style name="TextAppearance.AppCompat.Widget.ActionMode.Title" parent="Base.TextAppearance.AppCompat.Widget.ActionMode.Title">
    </style>
    <style name="TextAppearance.AppCompat.Widget.ActionMode.Title.Inverse" parent="TextAppearance.AppCompat.Widget.ActionMode.Title"/>
    <style name="TextAppearance.AppCompat.Widget.Button" parent="Base.TextAppearance.AppCompat.Widget.Button"/>
    <style name="TextAppearance.AppCompat.Widget.Button.Borderless.Colored" parent="Base.TextAppearance.AppCompat.Widget.Button.Borderless.Colored"/>
    <style name="TextAppearance.AppCompat.Widget.Button.Colored" parent="Base.TextAppearance.AppCompat.Widget.Button.Colored"/>
    <style name="TextAppearance.AppCompat.Widget.Button.Inverse" parent="Base.TextAppearance.AppCompat.Widget.Button.Inverse"/>
    <style name="TextAppearance.AppCompat.Widget.DropDownItem" parent="Base.TextAppearance.AppCompat.Widget.DropDownItem">
    </style>
    <style name="TextAppearance.AppCompat.Widget.PopupMenu.Header" parent="Base.TextAppearance.AppCompat.Widget.PopupMenu.Header"/>
    <style name="TextAppearance.AppCompat.Widget.PopupMenu.Large" parent="Base.TextAppearance.AppCompat.Widget.PopupMenu.Large"/>
    <style name="TextAppearance.AppCompat.Widget.PopupMenu.Small" parent="Base.TextAppearance.AppCompat.Widget.PopupMenu.Small"/>
    <style name="TextAppearance.AppCompat.Widget.Switch" parent="Base.TextAppearance.AppCompat.Widget.Switch"/>
    <style name="TextAppearance.AppCompat.Widget.TextView.SpinnerItem" parent="Base.TextAppearance.AppCompat.Widget.TextView.SpinnerItem"/>
    <style name="TextAppearance.Compat.Notification" parent="@android:style/TextAppearance.StatusBar.EventContent"/>
    <style name="TextAppearance.Compat.Notification.Info">
        <item name="android:textSize">12sp</item>
        <item name="android:textColor">?android:attr/textColorSecondary</item>
    </style>
    <style name="TextAppearance.Compat.Notification.Info.Media"/>
    <style name="TextAppearance.Compat.Notification.Line2" parent="TextAppearance.Compat.Notification.Info"/>
    <style name="TextAppearance.Compat.Notification.Line2.Media" parent="TextAppearance.Compat.Notification.Info.Media"/>
    <style name="TextAppearance.Compat.Notification.Media"/>
    <style name="TextAppearance.Compat.Notification.Time">
        <item name="android:textSize">12sp</item>
        <item name="android:textColor">?android:attr/textColorSecondary</item>
    </style>
    <style name="TextAppearance.Compat.Notification.Time.Media"/>
    <style name="TextAppearance.Compat.Notification.Title" parent="@android:style/TextAppearance.StatusBar.EventContent.Title"/>
    <style name="TextAppearance.Compat.Notification.Title.Media"/>
    <style name="TextAppearance.Design.CollapsingToolbar.Expanded" parent="TextAppearance.AppCompat.Display1">
    <item name="android:textColor">?android:attr/textColorPrimary</item>
  </style>
    <style name="TextAppearance.Design.Counter" parent="TextAppearance.AppCompat.Caption"/>
    <style name="TextAppearance.Design.Counter.Overflow" parent="TextAppearance.AppCompat.Caption">
    <item name="android:textColor">@color/design_error</item>
  </style>
    <style name="TextAppearance.Design.Error" parent="TextAppearance.AppCompat.Caption">
    <item name="android:textColor">@color/design_error</item>
  </style>
    <style name="TextAppearance.Design.HelperText" parent="TextAppearance.AppCompat.Caption"/>
    <style name="TextAppearance.Design.Hint" parent="TextAppearance.AppCompat.Caption">
    <item name="android:textColor">?attr/colorControlActivated</item>
  </style>
    <style name="TextAppearance.Design.Snackbar.Message" parent="android:TextAppearance">
    <item name="android:textSize">@dimen/design_snackbar_text_size</item>
    <item name="android:textColor">?android:textColorPrimary</item>
  </style>
    <style name="TextAppearance.Design.Tab" parent="TextAppearance.AppCompat.Button">
    <item name="android:textSize">@dimen/design_tab_text_size</item>
    <item name="android:textColor">@color/mtrl_tabs_legacy_text_color_selector</item>
    <item name="textAllCaps">true</item>
  </style>
    <style name="TextAppearance.MaterialComponents.Body1" parent="TextAppearance.AppCompat.Body2">
    <item name="fontFamily">sans-serif</item>
    <item name="android:fontFamily">sans-serif</item>
    <item name="android:textStyle">normal</item>
    <item name="android:textAllCaps">false</item>
    <item name="android:textSize">16sp</item>
    <item name="android:letterSpacing">0.03125</item>
  </style>
    <style name="TextAppearance.MaterialComponents.Body2" parent="TextAppearance.AppCompat.Body1">
    <item name="fontFamily">sans-serif</item>
    <item name="android:fontFamily">sans-serif</item>
    <item name="android:textStyle">normal</item>
    <item name="android:textAllCaps">false</item>
    <item name="android:textSize">14sp</item>
    <item name="android:letterSpacing">0.0178571429</item>
  </style>
    <style name="TextAppearance.MaterialComponents.Button" parent="TextAppearance.AppCompat.Button">
    
    <item name="fontFamily">sans-serif-medium</item>
    <item name="android:fontFamily">sans-serif-medium</item>
    <item name="android:textStyle">bold</item>
    <item name="android:textAllCaps">true</item>
    <item name="android:textSize">14sp</item>
    <item name="android:letterSpacing">0.0892857143</item>
  </style>
    <style name="TextAppearance.MaterialComponents.Caption" parent="TextAppearance.AppCompat.Caption">
    <item name="fontFamily">sans-serif</item>
    <item name="android:fontFamily">sans-serif</item>
    <item name="android:textStyle">normal</item>
    <item name="android:textAllCaps">false</item>
    <item name="android:textSize">12sp</item>
    <item name="android:letterSpacing">0.0333333333</item>
  </style>
    <style name="TextAppearance.MaterialComponents.Chip" parent="TextAppearance.AppCompat">
    <item name="android:textColor">@color/mtrl_chip_text_color</item>
    <item name="android:textSize">@dimen/mtrl_chip_text_size</item>
  </style>
    <style name="TextAppearance.MaterialComponents.Headline1" parent="TextAppearance.AppCompat.Display4">
    <item name="fontFamily">sans-serif-light</item>
    <item name="android:fontFamily">sans-serif-light</item>
    <item name="android:textStyle">normal</item>
    <item name="android:textAllCaps">false</item>
    <item name="android:textSize">96sp</item>
    <item name="android:letterSpacing">-0.015625</item>
  </style>
    <style name="TextAppearance.MaterialComponents.Headline2" parent="TextAppearance.AppCompat.Display3">
    <item name="fontFamily">sans-serif-light</item>
    <item name="android:fontFamily">sans-serif-light</item>
    <item name="android:textStyle">normal</item>
    <item name="android:textAllCaps">false</item>
    <item name="android:textSize">60sp</item>
    <item name="android:letterSpacing">-0.00833333333</item>
  </style>
    <style name="TextAppearance.MaterialComponents.Headline3" parent="TextAppearance.AppCompat.Display2">
    <item name="fontFamily">sans-serif</item>
    <item name="android:fontFamily">sans-serif</item>
    <item name="android:textStyle">normal</item>
    <item name="android:textAllCaps">false</item>
    <item name="android:textSize">48sp</item>
    <item name="android:letterSpacing">0</item>
  </style>
    <style name="TextAppearance.MaterialComponents.Headline4" parent="TextAppearance.AppCompat.Display1">
    <item name="fontFamily">sans-serif</item>
    <item name="android:fontFamily">sans-serif</item>
    <item name="android:textStyle">normal</item>
    <item name="android:textAllCaps">false</item>
    <item name="android:textSize">34sp</item>
    <item name="android:letterSpacing">0.00735294118</item>
  </style>
    <style name="TextAppearance.MaterialComponents.Headline5" parent="TextAppearance.AppCompat.Headline">
    <item name="fontFamily">sans-serif</item>
    <item name="android:fontFamily">sans-serif</item>
    <item name="android:textStyle">normal</item>
    <item name="android:textAllCaps">false</item>
    <item name="android:textSize">24sp</item>
    <item name="android:letterSpacing">0</item>
  </style>
    <style name="TextAppearance.MaterialComponents.Headline6" parent="TextAppearance.AppCompat.Title">
    
    <item name="fontFamily">sans-serif-medium</item>
    <item name="android:fontFamily">sans-serif-medium</item>
    <item name="android:textStyle">bold</item>
    <item name="android:textAllCaps">false</item>
    <item name="android:textSize">20sp</item>
    <item name="android:letterSpacing">0.0125</item>
  </style>
    <style name="TextAppearance.MaterialComponents.Overline" parent="TextAppearance.AppCompat">
    
    <item name="fontFamily">sans-serif-medium</item>
    <item name="android:fontFamily">sans-serif-medium</item>
    <item name="android:textStyle">bold</item>
    <item name="android:textAllCaps">true</item>
    <item name="android:textSize">12sp</item>
    <item name="android:letterSpacing">0.166666667</item>
  </style>
    <style name="TextAppearance.MaterialComponents.Subtitle1" parent="TextAppearance.AppCompat.Subhead">
    <item name="fontFamily">sans-serif</item>
    <item name="android:fontFamily">sans-serif</item>
    <item name="android:textStyle">normal</item>
    <item name="android:textAllCaps">false</item>
    <item name="android:textSize">16sp</item>
    <item name="android:letterSpacing">0.009375</item>
  </style>
    <style name="TextAppearance.MaterialComponents.Subtitle2" parent="TextAppearance.AppCompat.Subhead">
    
    <item name="fontFamily">sans-serif-medium</item>
    <item name="android:fontFamily">sans-serif-medium</item>
    <item name="android:textStyle">bold</item>
    <item name="android:textAllCaps">false</item>
    <item name="android:textSize">14sp</item>
    <item name="android:letterSpacing">0.00714285714</item>
  </style>
    <style name="TextAppearance.MaterialComponents.Tab" parent="TextAppearance.Design.Tab">
    <item name="android:textColor">@color/mtrl_tabs_icon_color_selector</item>
  </style>
    <style name="TextAppearance.Widget.AppCompat.ExpandedMenu.Item" parent="Base.TextAppearance.Widget.AppCompat.ExpandedMenu.Item">
    </style>
    <style name="TextAppearance.Widget.AppCompat.Toolbar.Subtitle" parent="Base.TextAppearance.Widget.AppCompat.Toolbar.Subtitle">
    </style>
    <style name="TextAppearance.Widget.AppCompat.Toolbar.Title" parent="Base.TextAppearance.Widget.AppCompat.Toolbar.Title">
    </style>
    <style name="Theme.AppCompat" parent="Base.Theme.AppCompat"/>
    <style name="Theme.AppCompat.CompactMenu" parent="Base.Theme.AppCompat.CompactMenu"/>
    <style name="Theme.AppCompat.DayNight" parent="Theme.AppCompat.Light"/>
    <style name="Theme.AppCompat.DayNight.DarkActionBar" parent="Theme.AppCompat.Light.DarkActionBar"/>
    <style name="Theme.AppCompat.DayNight.Dialog" parent="Theme.AppCompat.Light.Dialog"/>
    <style name="Theme.AppCompat.DayNight.Dialog.Alert" parent="Theme.AppCompat.Light.Dialog.Alert"/>
    <style name="Theme.AppCompat.DayNight.Dialog.MinWidth" parent="Theme.AppCompat.Light.Dialog.MinWidth"/>
    <style name="Theme.AppCompat.DayNight.DialogWhenLarge" parent="Theme.AppCompat.Light.DialogWhenLarge"/>
    <style name="Theme.AppCompat.DayNight.NoActionBar" parent="Theme.AppCompat.Light.NoActionBar"/>
    <style name="Theme.AppCompat.Dialog" parent="Base.Theme.AppCompat.Dialog"/>
    <style name="Theme.AppCompat.Dialog.Alert" parent="Base.Theme.AppCompat.Dialog.Alert"/>
    <style name="Theme.AppCompat.Dialog.MinWidth" parent="Base.Theme.AppCompat.Dialog.MinWidth"/>
    <style name="Theme.AppCompat.DialogWhenLarge" parent="Base.Theme.AppCompat.DialogWhenLarge">
    </style>
    <style name="Theme.AppCompat.Light" parent="Base.Theme.AppCompat.Light"/>
    <style name="Theme.AppCompat.Light.DarkActionBar" parent="Base.Theme.AppCompat.Light.DarkActionBar"/>
    <style name="Theme.AppCompat.Light.Dialog" parent="Base.Theme.AppCompat.Light.Dialog"/>
    <style name="Theme.AppCompat.Light.Dialog.Alert" parent="Base.Theme.AppCompat.Light.Dialog.Alert"/>
    <style name="Theme.AppCompat.Light.Dialog.MinWidth" parent="Base.Theme.AppCompat.Light.Dialog.MinWidth"/>
    <style name="Theme.AppCompat.Light.DialogWhenLarge" parent="Base.Theme.AppCompat.Light.DialogWhenLarge">
    </style>
    <style name="Theme.AppCompat.Light.NoActionBar">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>
    <style name="Theme.AppCompat.NoActionBar">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>
    <style name="Theme.Design" parent="Theme.AppCompat">
  </style>
    <style name="Theme.Design.BottomSheetDialog" parent="Theme.AppCompat.Dialog">
    <item name="android:windowBackground">@android:color/transparent</item>
    <item name="android:windowAnimationStyle">@style/Animation.Design.BottomSheetDialog</item>
    <item name="bottomSheetStyle">@style/Widget.Design.BottomSheet.Modal</item>
  </style>
    <style name="Theme.Design.Light" parent="Theme.AppCompat.Light">
  </style>
    <style name="Theme.Design.Light.BottomSheetDialog" parent="Theme.AppCompat.Light.Dialog">
    <item name="android:windowBackground">@android:color/transparent</item>
    <item name="android:windowAnimationStyle">@style/Animation.Design.BottomSheetDialog</item>
    <item name="bottomSheetStyle">@style/Widget.Design.BottomSheet.Modal</item>
  </style>
    <style name="Theme.Design.Light.NoActionBar">
    <item name="windowActionBar">false</item>
    <item name="windowNoTitle">true</item>
  </style>
    <style name="Theme.Design.NoActionBar">
    <item name="windowActionBar">false</item>
    <item name="windowNoTitle">true</item>
  </style>
    <style name="Theme.Dialog.TTDownload" parent="@android:style/Theme.DeviceDefault.Light.Dialog">
        <item name="android:layout_centerInParent">true</item>
    </style>
    <style name="Theme.Dialog.TTDownloadOld" parent="@android:style/Theme.Holo.Light.Dialog">
        <item name="android:layout_centerInParent">true</item>
        <item name="android:windowFrame">@null</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:background">@android:color/transparent</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:backgroundDimEnabled">false</item>
    </style>
    <style name="Theme.MaterialComponents" parent="Base.Theme.MaterialComponents"/>
    <style name="Theme.MaterialComponents.BottomSheetDialog" parent="Theme.MaterialComponents.Dialog">
    <item name="android:windowBackground">@android:color/transparent</item>
    <item name="android:windowAnimationStyle">@style/Animation.Design.BottomSheetDialog</item>
    <item name="bottomSheetStyle">@style/Widget.Design.BottomSheet.Modal</item>
  </style>
    <style name="Theme.MaterialComponents.Bridge" parent="Base.Theme.MaterialComponents.Bridge"/>
    <style name="Theme.MaterialComponents.CompactMenu" parent="Base.Theme.MaterialComponents.CompactMenu"/>
    <style name="Theme.MaterialComponents.Dialog" parent="Base.Theme.MaterialComponents.Dialog"/>
    <style name="Theme.MaterialComponents.Dialog.Alert" parent="Base.Theme.MaterialComponents.Dialog.Alert"/>
    <style name="Theme.MaterialComponents.Dialog.MinWidth" parent="Base.Theme.MaterialComponents.Dialog.MinWidth"/>
    <style name="Theme.MaterialComponents.DialogWhenLarge" parent="Base.Theme.MaterialComponents.DialogWhenLarge">
  </style>
    <style name="Theme.MaterialComponents.Light" parent="Base.Theme.MaterialComponents.Light"/>
    <style name="Theme.MaterialComponents.Light.BottomSheetDialog" parent="Theme.MaterialComponents.Light.Dialog">
    <item name="android:windowBackground">@android:color/transparent</item>
    <item name="android:windowAnimationStyle">@style/Animation.Design.BottomSheetDialog</item>
    <item name="bottomSheetStyle">@style/Widget.Design.BottomSheet.Modal</item>
  </style>
    <style name="Theme.MaterialComponents.Light.Bridge" parent="Base.Theme.MaterialComponents.Light.Bridge"/>
    <style name="Theme.MaterialComponents.Light.DarkActionBar" parent="Base.Theme.MaterialComponents.Light.DarkActionBar"/>
    <style name="Theme.MaterialComponents.Light.DarkActionBar.Bridge" parent="Base.Theme.MaterialComponents.Light.DarkActionBar.Bridge"/>
    <style name="Theme.MaterialComponents.Light.Dialog" parent="Base.Theme.MaterialComponents.Light.Dialog"/>
    <style name="Theme.MaterialComponents.Light.Dialog.Alert" parent="Base.Theme.MaterialComponents.Light.Dialog.Alert"/>
    <style name="Theme.MaterialComponents.Light.Dialog.MinWidth" parent="Base.Theme.MaterialComponents.Light.Dialog.MinWidth"/>
    <style name="Theme.MaterialComponents.Light.DialogWhenLarge" parent="Base.Theme.MaterialComponents.Light.DialogWhenLarge">
  </style>
    <style name="Theme.MaterialComponents.Light.NoActionBar">
    <item name="windowActionBar">false</item>
    <item name="windowNoTitle">true</item>
  </style>
    <style name="Theme.MaterialComponents.Light.NoActionBar.Bridge" parent="Theme.MaterialComponents.Light.Bridge">
    <item name="windowActionBar">false</item>
    <item name="windowNoTitle">true</item>
  </style>
    <style name="Theme.MaterialComponents.NoActionBar">
    <item name="windowActionBar">false</item>
    <item name="windowNoTitle">true</item>
  </style>
    <style name="Theme.MaterialComponents.NoActionBar.Bridge" parent="Theme.MaterialComponents.Bridge">
    <item name="windowActionBar">false</item>
    <item name="windowNoTitle">true</item>
  </style>
    <style name="Theme.MountainSurvival" parent="Theme.AppCompat.Light.NoActionBar">
        
        <item name="colorPrimary">@color/primary_blue</item>
        <item name="colorPrimaryDark">@color/primary_blue_dark</item>
        <item name="colorAccent">@color/accent_orange</item>

        
        <item name="android:windowBackground">@color/background_light</item>

        
        <item name="android:textColorPrimary">@color/text_primary</item>
        <item name="android:textColorSecondary">@color/text_secondary</item>
    </style>
    <style name="ThemeOverlay.AppCompat" parent="Base.ThemeOverlay.AppCompat"/>
    <style name="ThemeOverlay.AppCompat.ActionBar" parent="Base.ThemeOverlay.AppCompat.ActionBar"/>
    <style name="ThemeOverlay.AppCompat.Dark" parent="Base.ThemeOverlay.AppCompat.Dark"/>
    <style name="ThemeOverlay.AppCompat.Dark.ActionBar" parent="Base.ThemeOverlay.AppCompat.Dark.ActionBar"/>
    <style name="ThemeOverlay.AppCompat.Dialog" parent="Base.ThemeOverlay.AppCompat.Dialog"/>
    <style name="ThemeOverlay.AppCompat.Dialog.Alert" parent="Base.ThemeOverlay.AppCompat.Dialog.Alert"/>
    <style name="ThemeOverlay.AppCompat.Light" parent="Base.ThemeOverlay.AppCompat.Light"/>
    <style name="ThemeOverlay.MaterialComponents" parent="ThemeOverlay.AppCompat"/>
    <style name="ThemeOverlay.MaterialComponents.ActionBar" parent="ThemeOverlay.AppCompat.ActionBar"/>
    <style name="ThemeOverlay.MaterialComponents.Dark" parent="ThemeOverlay.AppCompat.Dark"/>
    <style name="ThemeOverlay.MaterialComponents.Dark.ActionBar" parent="ThemeOverlay.AppCompat.Dark.ActionBar"/>
    <style name="ThemeOverlay.MaterialComponents.Dialog" parent="Base.ThemeOverlay.MaterialComponents.Dialog"/>
    <style name="ThemeOverlay.MaterialComponents.Dialog.Alert" parent="Base.ThemeOverlay.MaterialComponents.Dialog.Alert"/>
    <style name="ThemeOverlay.MaterialComponents.Light" parent="ThemeOverlay.AppCompat.Light"/>
    <style name="ThemeOverlay.MaterialComponents.TextInputEditText" parent=""/>
    <style name="ThemeOverlay.MaterialComponents.TextInputEditText.FilledBox">
    <item name="editTextStyle">@style/Widget.MaterialComponents.TextInputEditText.FilledBox</item>
  </style>
    <style name="ThemeOverlay.MaterialComponents.TextInputEditText.FilledBox.Dense">
    <item name="editTextStyle">@style/Widget.MaterialComponents.TextInputEditText.FilledBox.Dense
    </item>
  </style>
    <style name="ThemeOverlay.MaterialComponents.TextInputEditText.OutlinedBox">
    <item name="editTextStyle">@style/Widget.MaterialComponents.TextInputEditText.OutlinedBox</item>
  </style>
    <style name="ThemeOverlay.MaterialComponents.TextInputEditText.OutlinedBox.Dense">
    <item name="editTextStyle">@style/Widget.MaterialComponents.TextInputEditText.OutlinedBox.Dense
    </item>
  </style>
    <style name="TransparentDialogActivity" parent="@style/Theme.AppCompat.Dialog">
    <item name="android:windowBackground">@android:color/transparent</item>
    <item name="android:windowIsTranslucent">true</item>
    <item name="android:backgroundDimAmount">0</item>
    <item name="android:windowAnimationStyle">@android:style/Animation.Translucent</item>
  </style>
    <style name="Widget.AppCompat.ActionBar" parent="Base.Widget.AppCompat.ActionBar">
    </style>
    <style name="Widget.AppCompat.ActionBar.Solid" parent="Base.Widget.AppCompat.ActionBar.Solid">
    </style>
    <style name="Widget.AppCompat.ActionBar.TabBar" parent="Base.Widget.AppCompat.ActionBar.TabBar">
    </style>
    <style name="Widget.AppCompat.ActionBar.TabText" parent="Base.Widget.AppCompat.ActionBar.TabText">
    </style>
    <style name="Widget.AppCompat.ActionBar.TabView" parent="Base.Widget.AppCompat.ActionBar.TabView">
    </style>
    <style name="Widget.AppCompat.ActionButton" parent="Base.Widget.AppCompat.ActionButton"/>
    <style name="Widget.AppCompat.ActionButton.CloseMode" parent="Base.Widget.AppCompat.ActionButton.CloseMode"/>
    <style name="Widget.AppCompat.ActionButton.Overflow" parent="Base.Widget.AppCompat.ActionButton.Overflow"/>
    <style name="Widget.AppCompat.ActionMode" parent="Base.Widget.AppCompat.ActionMode">
    </style>
    <style name="Widget.AppCompat.ActivityChooserView" parent="Base.Widget.AppCompat.ActivityChooserView">
    </style>
    <style name="Widget.AppCompat.AutoCompleteTextView" parent="Base.Widget.AppCompat.AutoCompleteTextView">
    </style>
    <style name="Widget.AppCompat.Button" parent="Base.Widget.AppCompat.Button"/>
    <style name="Widget.AppCompat.Button.Borderless" parent="Base.Widget.AppCompat.Button.Borderless"/>
    <style name="Widget.AppCompat.Button.Borderless.Colored" parent="Base.Widget.AppCompat.Button.Borderless.Colored"/>
    <style name="Widget.AppCompat.Button.ButtonBar.AlertDialog" parent="Base.Widget.AppCompat.Button.ButtonBar.AlertDialog"/>
    <style name="Widget.AppCompat.Button.Colored" parent="Base.Widget.AppCompat.Button.Colored"/>
    <style name="Widget.AppCompat.Button.Small" parent="Base.Widget.AppCompat.Button.Small"/>
    <style name="Widget.AppCompat.ButtonBar" parent="Base.Widget.AppCompat.ButtonBar"/>
    <style name="Widget.AppCompat.ButtonBar.AlertDialog" parent="Base.Widget.AppCompat.ButtonBar.AlertDialog"/>
    <style name="Widget.AppCompat.CompoundButton.CheckBox" parent="Base.Widget.AppCompat.CompoundButton.CheckBox"/>
    <style name="Widget.AppCompat.CompoundButton.RadioButton" parent="Base.Widget.AppCompat.CompoundButton.RadioButton"/>
    <style name="Widget.AppCompat.CompoundButton.Switch" parent="Base.Widget.AppCompat.CompoundButton.Switch"/>
    <style name="Widget.AppCompat.DrawerArrowToggle" parent="Base.Widget.AppCompat.DrawerArrowToggle">
        <item name="color">?attr/colorControlNormal</item>
    </style>
    <style name="Widget.AppCompat.DropDownItem.Spinner" parent="RtlOverlay.Widget.AppCompat.Search.DropDown.Text"/>
    <style name="Widget.AppCompat.EditText" parent="Base.Widget.AppCompat.EditText"/>
    <style name="Widget.AppCompat.ImageButton" parent="Base.Widget.AppCompat.ImageButton"/>
    <style name="Widget.AppCompat.Light.ActionBar" parent="Base.Widget.AppCompat.Light.ActionBar">
    </style>
    <style name="Widget.AppCompat.Light.ActionBar.Solid" parent="Base.Widget.AppCompat.Light.ActionBar.Solid">
    </style>
    <style name="Widget.AppCompat.Light.ActionBar.Solid.Inverse"/>
    <style name="Widget.AppCompat.Light.ActionBar.TabBar" parent="Base.Widget.AppCompat.Light.ActionBar.TabBar">
    </style>
    <style name="Widget.AppCompat.Light.ActionBar.TabBar.Inverse"/>
    <style name="Widget.AppCompat.Light.ActionBar.TabText" parent="Base.Widget.AppCompat.Light.ActionBar.TabText">
    </style>
    <style name="Widget.AppCompat.Light.ActionBar.TabText.Inverse" parent="Base.Widget.AppCompat.Light.ActionBar.TabText.Inverse">
    </style>
    <style name="Widget.AppCompat.Light.ActionBar.TabView" parent="Base.Widget.AppCompat.Light.ActionBar.TabView">
    </style>
    <style name="Widget.AppCompat.Light.ActionBar.TabView.Inverse"/>
    <style name="Widget.AppCompat.Light.ActionButton" parent="Widget.AppCompat.ActionButton"/>
    <style name="Widget.AppCompat.Light.ActionButton.CloseMode" parent="Widget.AppCompat.ActionButton.CloseMode"/>
    <style name="Widget.AppCompat.Light.ActionButton.Overflow" parent="Widget.AppCompat.ActionButton.Overflow"/>
    <style name="Widget.AppCompat.Light.ActionMode.Inverse" parent="Widget.AppCompat.ActionMode"/>
    <style name="Widget.AppCompat.Light.ActivityChooserView" parent="Widget.AppCompat.ActivityChooserView"/>
    <style name="Widget.AppCompat.Light.AutoCompleteTextView" parent="Widget.AppCompat.AutoCompleteTextView"/>
    <style name="Widget.AppCompat.Light.DropDownItem.Spinner" parent="Widget.AppCompat.DropDownItem.Spinner"/>
    <style name="Widget.AppCompat.Light.ListPopupWindow" parent="Widget.AppCompat.ListPopupWindow"/>
    <style name="Widget.AppCompat.Light.ListView.DropDown" parent="Widget.AppCompat.ListView.DropDown"/>
    <style name="Widget.AppCompat.Light.PopupMenu" parent="Base.Widget.AppCompat.Light.PopupMenu"/>
    <style name="Widget.AppCompat.Light.PopupMenu.Overflow" parent="Base.Widget.AppCompat.Light.PopupMenu.Overflow">
    </style>
    <style name="Widget.AppCompat.Light.SearchView" parent="Widget.AppCompat.SearchView"/>
    <style name="Widget.AppCompat.Light.Spinner.DropDown.ActionBar" parent="Widget.AppCompat.Spinner.DropDown.ActionBar"/>
    <style name="Widget.AppCompat.ListMenuView" parent="Base.Widget.AppCompat.ListMenuView"/>
    <style name="Widget.AppCompat.ListPopupWindow" parent="Base.Widget.AppCompat.ListPopupWindow">
    </style>
    <style name="Widget.AppCompat.ListView" parent="Base.Widget.AppCompat.ListView"/>
    <style name="Widget.AppCompat.ListView.DropDown" parent="Base.Widget.AppCompat.ListView.DropDown"/>
    <style name="Widget.AppCompat.ListView.Menu" parent="Base.Widget.AppCompat.ListView.Menu"/>
    <style name="Widget.AppCompat.PopupMenu" parent="Base.Widget.AppCompat.PopupMenu"/>
    <style name="Widget.AppCompat.PopupMenu.Overflow" parent="Base.Widget.AppCompat.PopupMenu.Overflow">
    </style>
    <style name="Widget.AppCompat.PopupWindow" parent="Base.Widget.AppCompat.PopupWindow">
    </style>
    <style name="Widget.AppCompat.ProgressBar" parent="Base.Widget.AppCompat.ProgressBar">
    </style>
    <style name="Widget.AppCompat.ProgressBar.Horizontal" parent="Base.Widget.AppCompat.ProgressBar.Horizontal">
    </style>
    <style name="Widget.AppCompat.RatingBar" parent="Base.Widget.AppCompat.RatingBar"/>
    <style name="Widget.AppCompat.RatingBar.Indicator" parent="Base.Widget.AppCompat.RatingBar.Indicator"/>
    <style name="Widget.AppCompat.RatingBar.Small" parent="Base.Widget.AppCompat.RatingBar.Small"/>
    <style name="Widget.AppCompat.SearchView" parent="Base.Widget.AppCompat.SearchView"/>
    <style name="Widget.AppCompat.SearchView.ActionBar" parent="Base.Widget.AppCompat.SearchView.ActionBar"/>
    <style name="Widget.AppCompat.SeekBar" parent="Base.Widget.AppCompat.SeekBar"/>
    <style name="Widget.AppCompat.SeekBar.Discrete" parent="Base.Widget.AppCompat.SeekBar.Discrete"/>
    <style name="Widget.AppCompat.Spinner" parent="Base.Widget.AppCompat.Spinner"/>
    <style name="Widget.AppCompat.Spinner.DropDown"/>
    <style name="Widget.AppCompat.Spinner.DropDown.ActionBar"/>
    <style name="Widget.AppCompat.Spinner.Underlined" parent="Base.Widget.AppCompat.Spinner.Underlined"/>
    <style name="Widget.AppCompat.TextView.SpinnerItem" parent="Base.Widget.AppCompat.TextView.SpinnerItem"/>
    <style name="Widget.AppCompat.Toolbar" parent="Base.Widget.AppCompat.Toolbar"/>
    <style name="Widget.AppCompat.Toolbar.Button.Navigation" parent="Base.Widget.AppCompat.Toolbar.Button.Navigation"/>
    <style name="Widget.Compat.NotificationActionContainer" parent=""/>
    <style name="Widget.Compat.NotificationActionText" parent=""/>
    <style name="Widget.Design.AppBarLayout" parent="android:Widget">
    <item name="android:background">?attr/colorPrimary</item>
    <item name="android:stateListAnimator" ns2:ignore="NewApi">
      @animator/design_appbar_state_list_animator
    </item>
    <item name="android:keyboardNavigationCluster" ns2:ignore="NewApi">true</item>
    <item name="android:touchscreenBlocksFocus" ns2:ignore="NewApi">true</item>
  </style>
    <style name="Widget.Design.BottomNavigationView" parent="">
    <item name="elevation">@dimen/design_bottom_navigation_elevation</item>
    <item name="itemBackground">?attr/selectableItemBackgroundBorderless</item>
    <item name="itemHorizontalTranslationEnabled">true</item>
    <item name="itemIconSize">@dimen/design_bottom_navigation_icon_size</item>
    <item name="labelVisibilityMode">auto</item>
  </style>
    <style name="Widget.Design.BottomSheet.Modal" parent="android:Widget">
    <item name="android:background">?android:attr/colorBackground</item>
    <item name="android:elevation" ns2:ignore="NewApi">
      @dimen/design_bottom_sheet_modal_elevation
    </item>
    <item name="behavior_peekHeight">auto</item>
    <item name="behavior_hideable">true</item>
    <item name="behavior_skipCollapsed">false</item>
  </style>
    <style name="Widget.Design.CollapsingToolbar" parent="android:Widget">
    <item name="expandedTitleMargin">32dp</item>
    <item name="statusBarScrim">?attr/colorPrimaryDark</item>
  </style>
    <style name="Widget.Design.FloatingActionButton" parent="android:Widget">
    <item name="android:background">@drawable/design_fab_background</item>
    <item name="android:clickable">true</item>
    <item name="android:focusable">true</item>
    <item name="backgroundTint">?attr/colorAccent</item>
    <item name="fabSize">auto</item>
    <item name="elevation">@dimen/design_fab_elevation</item>
    <item name="hoveredFocusedTranslationZ">@dimen/design_fab_translation_z_hovered_focused</item>
    <item name="pressedTranslationZ">@dimen/design_fab_translation_z_pressed</item>
    <item name="rippleColor">?attr/colorControlHighlight</item>
    <item name="borderWidth">@dimen/design_fab_border_width</item>
    <item name="maxImageSize">@dimen/design_fab_image_size</item>
    <item name="showMotionSpec">@animator/design_fab_show_motion_spec</item>
    <item name="hideMotionSpec">@animator/design_fab_hide_motion_spec</item>
  </style>
    <style name="Widget.Design.NavigationView" parent="">
    <item name="elevation">@dimen/design_navigation_elevation</item>
    <item name="itemIconPadding">@dimen/design_navigation_item_icon_padding</item>
    <item name="itemHorizontalPadding">@dimen/design_navigation_item_horizontal_padding</item>
    <item name="android:background">?android:attr/windowBackground</item>
    <item name="android:fitsSystemWindows">true</item>
    <item name="android:maxWidth">@dimen/design_navigation_max_width</item>
  </style>
    <style name="Widget.Design.ScrimInsetsFrameLayout" parent="">
    <item name="insetForeground">#4000</item>
  </style>
    <style name="Widget.Design.Snackbar" parent="android:Widget">
    <item name="android:minWidth">@dimen/design_snackbar_min_width</item>
    <item name="android:maxWidth">@dimen/design_snackbar_max_width</item>
    <item name="android:background">@drawable/design_snackbar_background</item>
    <item name="android:paddingLeft">@dimen/design_snackbar_padding_horizontal</item>
    <item name="android:paddingRight">@dimen/design_snackbar_padding_horizontal</item>
    <item name="elevation">@dimen/design_snackbar_elevation</item>
    <item name="maxActionInlineWidth">@dimen/design_snackbar_action_inline_max_width</item>
  </style>
    <style name="Widget.Design.TabLayout" parent="Base.Widget.Design.TabLayout">
    <item name="tabGravity">fill</item>
    <item name="tabMode">fixed</item>
    <item name="tabIndicatorFullWidth">true</item>
  </style>
    <style name="Widget.Design.TextInputLayout" parent="android:Widget">
    <item name="boxBackgroundMode">none</item>
    <item name="hintTextAppearance">@style/TextAppearance.Design.Hint</item>
    <item name="helperTextTextAppearance">@style/TextAppearance.Design.HelperText</item>
    <item name="errorTextAppearance">@style/TextAppearance.Design.Error</item>
    <item name="counterTextAppearance">@style/TextAppearance.Design.Counter</item>
    <item name="counterOverflowTextAppearance">@style/TextAppearance.Design.Counter.Overflow</item>
    <item name="passwordToggleDrawable">@drawable/design_password_eye</item>
    <item name="passwordToggleTint">@color/design_tint_password_toggle</item>
    <item name="passwordToggleContentDescription">@string/password_toggle_content_description</item>
  </style>
    <style name="Widget.MaterialComponents.BottomAppBar" parent="Widget.AppCompat.Toolbar">
    <item name="backgroundTint">@android:color/white</item>
    <item name="fabCradleMargin">@dimen/mtrl_bottomappbar_fab_cradle_margin</item>
    <item name="fabCradleRoundedCornerRadius">
      @dimen/mtrl_bottomappbar_fab_cradle_rounded_corner_radius
    </item>
    <item name="fabCradleVerticalOffset">@dimen/mtrl_bottomappbar_fab_cradle_vertical_offset</item>
  </style>
    <style name="Widget.MaterialComponents.BottomAppBar.Colored" parent="Widget.MaterialComponents.BottomAppBar">
    <item name="backgroundTint">?attr/colorPrimary</item>
  </style>
    <style name="Widget.MaterialComponents.BottomNavigationView" parent="Widget.Design.BottomNavigationView">
    <item name="android:background">@android:color/white</item>
    <item name="enforceTextAppearance">true</item>
    <item name="itemHorizontalTranslationEnabled">false</item>
    <item name="itemIconTint">@color/mtrl_bottom_nav_item_tint</item>
    <item name="itemTextAppearanceInactive">?attr/textAppearanceCaption</item>
    <item name="itemTextAppearanceActive">?attr/textAppearanceCaption</item>
    <item name="itemTextColor">@color/mtrl_bottom_nav_item_tint</item>
  </style>
    <style name="Widget.MaterialComponents.BottomNavigationView.Colored">
    <item name="android:background">?attr/colorPrimary</item>
    <item name="itemIconTint">@color/mtrl_bottom_nav_colored_item_tint</item>
    <item name="itemTextAppearanceInactive">?attr/textAppearanceCaption</item>
    <item name="itemTextAppearanceActive">?attr/textAppearanceCaption</item>
    <item name="itemTextColor">@color/mtrl_bottom_nav_colored_item_tint</item>
  </style>
    <style name="Widget.MaterialComponents.BottomSheet.Modal" parent="Widget.Design.BottomSheet.Modal"/>
    <style name="Widget.MaterialComponents.Button" parent="Widget.AppCompat.Button">
    <item name="enforceTextAppearance">true</item>
    <item name="android:textAppearance">?attr/textAppearanceButton</item>
    <item name="android:textColor">@color/mtrl_btn_text_color_selector</item>
    <item name="android:paddingLeft">@dimen/mtrl_btn_padding_left</item>
    <item name="android:paddingRight">@dimen/mtrl_btn_padding_right</item>
    <item name="android:paddingTop">@dimen/mtrl_btn_padding_top</item>
    <item name="android:paddingBottom">@dimen/mtrl_btn_padding_bottom</item>
    <item name="android:insetLeft">0dp</item>
    <item name="android:insetRight">0dp</item>
    <item name="android:insetTop">@dimen/mtrl_btn_inset</item>
    <item name="android:insetBottom">@dimen/mtrl_btn_inset</item>
    <item name="android:stateListAnimator" ns2:ignore="NewApi">@animator/mtrl_btn_state_list_anim</item>
    <item name="cornerRadius">@dimen/mtrl_btn_corner_radius</item>
    <item name="iconPadding">@dimen/mtrl_btn_icon_padding</item>
    <item name="iconTint">@color/mtrl_btn_text_color_selector</item>
    <item name="rippleColor">@color/mtrl_btn_ripple_color</item>
    <item name="backgroundTint">@color/mtrl_btn_bg_color_selector</item>
  </style>
    <style name="Widget.MaterialComponents.Button.Icon">
    <item name="android:paddingLeft">@dimen/mtrl_btn_icon_btn_padding_left</item>
  </style>
    <style name="Widget.MaterialComponents.Button.OutlinedButton" parent="Widget.MaterialComponents.Button.TextButton">
    <item name="android:paddingLeft">@dimen/mtrl_btn_padding_left</item>
    <item name="android:paddingRight">@dimen/mtrl_btn_padding_right</item>
    <item name="strokeColor">@color/mtrl_btn_stroke_color_selector</item>
    <item name="strokeWidth">@dimen/mtrl_btn_stroke_size</item>
  </style>
    <style name="Widget.MaterialComponents.Button.OutlinedButton.Icon">
    <item name="android:paddingLeft">@dimen/mtrl_btn_icon_btn_padding_left</item>
  </style>
    <style name="Widget.MaterialComponents.Button.TextButton" parent="Widget.MaterialComponents.Button.UnelevatedButton">
    <item name="android:textColor">@color/mtrl_text_btn_text_color_selector</item>
    <item name="android:paddingLeft">@dimen/mtrl_btn_text_btn_padding_left</item>
    <item name="android:paddingRight">@dimen/mtrl_btn_text_btn_padding_right</item>
    <item name="iconTint">@color/mtrl_text_btn_text_color_selector</item>
    <item name="iconPadding">@dimen/mtrl_btn_text_btn_icon_padding</item>
    <item name="backgroundTint">@color/mtrl_btn_transparent_bg_color</item>
    <item name="rippleColor">@color/mtrl_btn_text_btn_ripple_color</item>
  </style>
    <style name="Widget.MaterialComponents.Button.TextButton.Dialog">
    <item name="android:minWidth">@dimen/mtrl_btn_dialog_btn_min_width</item>
  </style>
    <style name="Widget.MaterialComponents.Button.TextButton.Dialog.Icon">
    
  </style>
    <style name="Widget.MaterialComponents.Button.TextButton.Icon">
    
  </style>
    <style name="Widget.MaterialComponents.Button.UnelevatedButton">
    <item name="android:stateListAnimator" ns2:ignore="NewApi">@animator/mtrl_btn_unelevated_state_list_anim</item>
  </style>
    <style name="Widget.MaterialComponents.Button.UnelevatedButton.Icon">
    <item name="android:paddingLeft">@dimen/mtrl_btn_icon_btn_padding_left</item>
  </style>
    <style name="Widget.MaterialComponents.CardView" parent="CardView">
    <item name="cardElevation">@dimen/mtrl_card_elevation</item>
    <item name="cardBackgroundColor">?attr/colorBackgroundFloating</item>
  </style>
    <style name="Widget.MaterialComponents.Chip.Action" parent="Base.Widget.MaterialComponents.Chip">
    <item name="closeIconVisible">false</item>
  </style>
    <style name="Widget.MaterialComponents.Chip.Choice" parent="Base.Widget.MaterialComponents.Chip">
    <item name="android:checkable">true</item>

    <item name="chipIconVisible">false</item>
    <item name="checkedIconVisible">false</item>
    <item name="closeIconVisible">false</item>

    <item name="checkedIcon">@drawable/ic_mtrl_chip_checked_black</item>
  </style>
    <style name="Widget.MaterialComponents.Chip.Entry" parent="Base.Widget.MaterialComponents.Chip">
    <item name="android:checkable">true</item>
  </style>
    <style name="Widget.MaterialComponents.Chip.Filter" parent="Base.Widget.MaterialComponents.Chip">
    <item name="android:checkable">true</item>

    <item name="chipIconVisible">false</item>
    <item name="closeIconVisible">false</item>

    <item name="checkedIcon">@drawable/ic_mtrl_chip_checked_black</item>
  </style>
    <style name="Widget.MaterialComponents.ChipGroup" parent="android:Widget">
    <item name="chipSpacing">4dp</item>
    <item name="singleLine">false</item>
    <item name="singleSelection">false</item>
  </style>
    <style name="Widget.MaterialComponents.FloatingActionButton" parent="Widget.Design.FloatingActionButton">
    <item name="elevation">@dimen/mtrl_fab_elevation</item>
    <item name="hoveredFocusedTranslationZ">@dimen/mtrl_fab_translation_z_hovered_focused</item>
    <item name="pressedTranslationZ">@dimen/mtrl_fab_translation_z_pressed</item>
    <item name="rippleColor">@color/mtrl_fab_ripple_color</item>
    <item name="showMotionSpec">@animator/mtrl_fab_show_motion_spec</item>
    <item name="hideMotionSpec">@animator/mtrl_fab_hide_motion_spec</item>
  </style>
    <style name="Widget.MaterialComponents.NavigationView" parent="Widget.Design.NavigationView">
    <item name="elevation">@dimen/mtrl_navigation_elevation</item>
    <item name="itemIconPadding">@dimen/mtrl_navigation_item_icon_padding</item>
    <item name="itemHorizontalPadding">@dimen/mtrl_navigation_item_horizontal_padding</item>
  </style>
    <style name="Widget.MaterialComponents.Snackbar" parent="Widget.Design.Snackbar">
    <item name="android:background">@drawable/mtrl_snackbar_background</item>
    <item name="android:layout_margin">@dimen/mtrl_snackbar_margin</item>
  </style>
    <style name="Widget.MaterialComponents.Snackbar.FullWidth" parent="Widget.Design.Snackbar"/>
    <style name="Widget.MaterialComponents.TabLayout" parent="Widget.Design.TabLayout">
    <item name="enforceTextAppearance">true</item>
    <item name="android:background">@android:color/white</item>
    <item name="tabIconTint">@color/mtrl_tabs_icon_color_selector</item>
    <item name="tabIndicatorAnimationDuration">@integer/mtrl_tab_indicator_anim_duration_ms</item>
    <item name="tabIndicatorColor">?attr/colorAccent</item>
    <item name="tabTextAppearance">?attr/textAppearanceButton</item>
    <item name="tabTextColor">@color/mtrl_tabs_icon_color_selector</item>
    <item name="tabRippleColor">@color/mtrl_tabs_ripple_color</item>
    <item name="tabUnboundedRipple">true</item>
  </style>
    <style name="Widget.MaterialComponents.TabLayout.Colored">
    <item name="android:background">?attr/colorAccent</item>
    <item name="tabIconTint">@color/mtrl_tabs_icon_color_selector_colored</item>
    <item name="tabIndicatorColor">@android:color/white</item>
    <item name="tabTextColor">@color/mtrl_tabs_icon_color_selector_colored</item>
    <item name="tabRippleColor">@color/mtrl_tabs_colored_ripple_color</item>
  </style>
    <style name="Widget.MaterialComponents.TextInputEditText.FilledBox" parent="Base.Widget.MaterialComponents.TextInputEditText">
    <item name="android:paddingTop">20dp</item>
    <item name="android:paddingBottom">16dp</item>
  </style>
    <style name="Widget.MaterialComponents.TextInputEditText.FilledBox.Dense">
    <item name="android:paddingTop">16dp</item>
    <item name="android:paddingBottom">16dp</item>
  </style>
    <style name="Widget.MaterialComponents.TextInputEditText.OutlinedBox" parent="Base.Widget.MaterialComponents.TextInputEditText"/>
    <style name="Widget.MaterialComponents.TextInputEditText.OutlinedBox.Dense">
    <item name="android:paddingTop">12dp</item>
    <item name="android:paddingBottom">12dp</item>
  </style>
    <style name="Widget.MaterialComponents.TextInputLayout.FilledBox" parent="Base.Widget.MaterialComponents.TextInputLayout">
    <item name="android:theme">@style/ThemeOverlay.MaterialComponents.TextInputEditText.FilledBox</item>
    <item name="boxBackgroundMode">filled</item>
    <item name="boxBackgroundColor">@color/mtrl_textinput_filled_box_default_background_color</item>
    <item name="boxCollapsedPaddingTop">12dp</item>
    <item name="boxCornerRadiusBottomStart">@dimen/mtrl_textinput_box_corner_radius_small</item>
    <item name="boxCornerRadiusBottomEnd">@dimen/mtrl_textinput_box_corner_radius_small</item>
  </style>
    <style name="Widget.MaterialComponents.TextInputLayout.FilledBox.Dense">
    <item name="android:theme">@style/ThemeOverlay.MaterialComponents.TextInputEditText.FilledBox.Dense</item>
    <item name="boxCollapsedPaddingTop">8dp</item>
  </style>
    <style name="Widget.MaterialComponents.TextInputLayout.OutlinedBox" parent="Base.Widget.MaterialComponents.TextInputLayout">
    <item name="android:theme">@style/ThemeOverlay.MaterialComponents.TextInputEditText.OutlinedBox
    </item>
    <item name="boxCollapsedPaddingTop">0dp</item>
  </style>
    <style name="Widget.MaterialComponents.TextInputLayout.OutlinedBox.Dense">
    <item name="android:theme">
      @style/ThemeOverlay.MaterialComponents.TextInputEditText.OutlinedBox.Dense
    </item>
  </style>
    <style name="Widget.MaterialComponents.Toolbar" parent="Widget.AppCompat.Toolbar">
    <item name="titleTextAppearance">?attr/textAppearanceHeadline6</item>
    <item name="titleTextColor">?android:attr/textColorPrimary</item>
    <item name="subtitleTextAppearance">?attr/textAppearanceSubtitle1</item>
    <item name="subtitleTextColor">?android:attr/textColorSecondary</item>
    
    <item name="android:minHeight">@dimen/mtrl_toolbar_default_height</item>
  </style>
    <style name="Widget.Support.CoordinatorLayout" parent="android:Widget">
        <item name="statusBarBackground">#000000</item>
    </style>
    <style name="beizi_ad_custom_dialog" parent="@android:style/Theme.Dialog">
        <item name="android:windowFrame">@null</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:background">#00000000</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:backgroundDimEnabled">true</item>
    </style>
    <style name="beizi_custom_dialog" parent="@android:style/Theme.Dialog">
        <item name="android:windowFrame">@null</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:background">#00000000</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:backgroundDimEnabled">true</item>
    </style>
    <style name="bottom_sheet_anime">
        <item name="android:windowAnimationStyle">@style/SKUPanelDialogAnimation</item>
    </style>
    <style name="commerce_dialog_dim_non_enter_animation" parent="android:Theme.Dialog">
        
        <item name="android:windowEnterAnimation">@null</item>

        <item name="android:windowExitAnimation">@anim/ec_bottom_out</item>
    </style>
    <style name="ec_sku_prerender_dialog_anim">
        <item name="android:windowEnterAnimation">@anim/ec_bottom_in</item>
        <item name="android:windowExitAnimation">@anim/ec_bottom_out</item>
    </style>
    <style name="ksad_LiveSubscriberAvatar">
    <item name="android:layout_width">match_parent</item>
    <item name="android:layout_height">match_parent</item>
    <item name="android:visibility">gone</item>
  </style>
    <style name="ksad_RewardCardBtnInstall">
    <item name="android:layout_width">match_parent</item>
    <item name="android:layout_height">44dp</item>
    <item name="android:background">@drawable/ksad_reward_install_btn_bg</item>
  </style>
    <style name="ksad_RewardCardTag">
    <item name="android:background">@drawable/ksad_reward_card_tag_bg</item>
    <item name="android:paddingLeft">8dp</item>
    <item name="android:paddingRight">8dp</item>
    <item name="android:layout_width">wrap_content</item>
    <item name="android:layout_height">18dp</item>
    <item name="android:textColor">@color/ksad_reward_main_color</item>
    <item name="android:textSize">10sp</item>
    <item name="android:gravity">center</item>
  </style>
    <style name="ksad_RewardCardTagWhite">
    <item name="android:background">@drawable/ksad_reward_card_tag_white_bg</item>
    <item name="android:paddingLeft">8dp</item>
    <item name="android:paddingRight">8dp</item>
    <item name="android:layout_width">wrap_content</item>
    <item name="android:layout_height">18dp</item>
    <item name="android:textColor">@android:color/white</item>
    <item name="android:textSize">10sp</item>
    <item name="android:gravity">center</item>
  </style>
    <style name="line">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_marginTop">10dp</item>
        <item name="android:orientation">horizontal</item>
    </style>
    <style name="quick_option_dialog">
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowSoftInputMode">stateHidden</item>
        <item name="android:windowFrame">@null</item>
        
        <item name="android:windowIsFloating">true</item>
        
        <item name="android:backgroundDimEnabled">true</item>
    </style>
    <style name="sig_base_theme" parent="android:Theme.Holo.Light.NoActionBar">
        <item name="android:windowNoTitle">true</item>
        
    </style>
    <style name="sig_custom_dialog" parent="android:style/Theme.Dialog">
        
        <item name="android:windowBackground">@android:color/transparent</item>
        
        <item name="android:windowFrame">@null</item>
        
        <item name="android:windowNoTitle">true</item>
        
        <item name="android:windowIsFloating">true</item>
        
        <item name="android:backgroundDimEnabled">false</item>
        
        <item name="android:windowIsTranslucent">true</item>

        
    </style>
    <style name="sig_custom_fullscreen_dialog" parent="android:style/Theme.Dialog">
        
        <item name="android:windowBackground">@android:color/transparent</item>
        
        <item name="android:windowFrame">@null</item>
        
        <item name="android:windowNoTitle">true</item>
        
        <item name="android:windowIsFloating">true</item>
        
        <item name="android:backgroundDimEnabled">false</item>
        
        <item name="android:windowIsTranslucent">true</item>

        <item name="android:windowFullscreen">true</item>
        
        
    </style>
    <style name="sig_dialog_window_anim" parent="android:Animation">
        
        <item name="android:windowEnterAnimation">@anim/sig_dialog_slide_in_bottom</item>
        
        <item name="android:windowExitAnimation">@anim/sig_dialog_slide_out_bottom</item>
    </style>
    <style name="sig_land_theme" parent="android:Theme.Black">
        <item name="android:windowActionBar">true</item>
        
    </style>
    <style name="sig_transparent_lang" parent="sig_base_theme">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowIsTranslucent">true</item>
        
        <item name="android:backgroundDimEnabled">false</item>
    </style>
    <style name="sig_transparent_style" parent="sig_base_theme">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowAnimationStyle">@android:style/Animation.Dialog</item>
        
        <item name="android:windowFrame">@null</item>
        
        
        
        <item name="android:windowContentOverlay">@null</item>
        
        <item name="android:backgroundDimEnabled">true</item>
    </style>
    <style name="tt_Widget_ProgressBar_Horizontal">
        <item name="android:indeterminateOnly">false</item>
        <item name="android:indeterminateDrawable">@drawable/tt_ad_download_progress_bar_horizontal</item>
        <item name="android:minHeight">20dip</item>
        <item name="android:maxHeight">20dip</item>
    </style>
    <style name="tt_animation">
        <item name="android:activityOpenEnterAnimation">@null</item>
        <item name="android:activityOpenExitAnimation">@null</item>
        <item name="android:activityCloseEnterAnimation">@null</item>
        <item name="android:activityCloseExitAnimation">@null</item>
        <item name="android:taskOpenEnterAnimation">@null</item>
        <item name="android:taskOpenExitAnimation">@null</item>
        <item name="android:taskCloseEnterAnimation">@null</item>
        <item name="android:taskCloseExitAnimation">@null</item>
        <item name="android:taskToFrontEnterAnimation">@null</item>
        <item name="android:taskToFrontExitAnimation">@null</item>
        <item name="android:taskToBackEnterAnimation">@null</item>
        <item name="android:taskToBackExitAnimation">@null</item>
    </style>
    <style name="tt_appdownloader_style_detail_download_progress_bar" parent="android:Widget.ProgressBar.Horizontal">
        <item name="android:indeterminateOnly">false</item>
        <item name="android:progressDrawable">@drawable/tt_appdownloader_ad_detail_download_progress</item>
        <item name="android:minHeight">32dp</item>
        <item name="android:maxHeight">32dp</item>
    </style>
    <style name="tt_appdownloader_style_notification_text" parent="android:TextAppearance.StatusBar.EventContent"/>
    <style name="tt_appdownloader_style_notification_title" parent="android:TextAppearance.StatusBar.EventContent.Title"/>
    <style name="tt_appdownloader_style_progress_bar" parent="android:Widget.ProgressBar.Horizontal">
        <item name="android:indeterminateOnly">false</item>
        <item name="android:progressDrawable">@drawable/tt_appdownloader_download_progress_bar_horizontal</item>
        <item name="android:minHeight">3dp</item>
        <item name="android:maxHeight">6dp</item>
    </style>
    <style name="tt_appdownloader_style_progress_bar_new" parent="android:Widget.ProgressBar.Horizontal">
        <item name="android:indeterminateOnly">false</item>
        <item name="android:progressDrawable">@drawable/tt_appdownloader_download_progress_bar_horizontal_new</item>
        <item name="android:minHeight">3dp</item>
        <item name="android:maxHeight">6dp</item>
    </style>
    <style name="tt_back_view">
        <item name="android:minHeight">44dp</item>
        <item name="android:layout_marginLeft">12dp</item>
        <item name="android:text">"关闭"</item>
        
        <item name="android:background">#00000000</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
    </style>
    <style name="tt_custom_dialog" parent="android:style/Theme.Dialog">
        
        <item name="android:windowBackground">@android:color/transparent</item>
        
        <item name="android:windowNoTitle">true</item>
        
        <item name="android:windowFrame">@null</item>
        
        <item name="android:windowIsFloating">true</item>
        
        <item name="android:backgroundDimEnabled">true</item>
    </style>
    <style name="tt_dialog_full">
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowCloseOnTouchOutside">true</item>
        <item name="android:windowIsFloating">false</item>
    </style>
    <style name="tt_full_screen" parent="android:Theme">
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowIsTranslucent">false</item>
        <item name="android:windowIsFloating">false</item>
        <item name="android:windowBackground">@color/tt_full_background</item>
        <item name="android:statusBarColor">@color/tt_full_status_bar_color</item>
        <item name="android:windowTranslucentStatus">false</item>
    </style>
    <style name="tt_full_screen_interaction" parent="android:Theme.Translucent.NoTitleBar">
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowBackground">@color/tt_full_interaction_dialog_background</item>
        <item name="android:windowCloseOnTouchOutside">false</item>
        <item name="android:statusBarColor">@color/tt_full_status_bar_color</item>
        <item name="android:windowTranslucentStatus">false</item>
    </style>
    <style name="tt_full_screen_no_animation" parent="tt_full_screen">
        <item name="android:windowAnimationStyle">@style/tt_animation</item>
        <item name="android:windowBackground">@color/tt_full_interaction_dialog_background</item>
    </style>
    <style name="tt_landing_page" parent="android:Theme">
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowIsTranslucent">false</item>
        <item name="android:windowIsFloating">false</item>
        <item name="android:windowBackground">@color/tt_white</item>
        <item name="android:statusBarColor">@color/tt_full_status_bar_color</item>
        <item name="android:windowTranslucentStatus">false</item>
    </style>
    <style name="tt_ss_popup_toast_anim" parent="android:Animation">
        <item name="android:windowEnterAnimation">@android:anim/fade_in</item>
        <item name="android:windowExitAnimation">@android:anim/fade_out</item>
    </style>
    <style name="tt_wg_insert_dialog" parent="android:style/Theme.Dialog">
        
        <item name="android:windowBackground">@android:color/transparent</item>
        
        <item name="android:windowNoTitle">true</item>
        
        <item name="android:windowFrame">@null</item>
        
        <item name="android:windowIsFloating">true</item>
        
        <item name="android:backgroundDimEnabled">true</item>
    </style>
    <style name="tt_widget_gifView" parent="@android:style/Widget"/>
    <style name="ttdownloader_translucent_dialog" parent="@android:style/Theme.Dialog">
        <item name="android:windowFrame">@null</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:background">@color/ttdownloader_transparent</item>
        <item name="android:windowBackground">@color/ttdownloader_transparent</item>
        <item name="android:backgroundDimEnabled">true</item>
    </style>
    <declare-styleable name="ActionBar">
        
        <attr name="navigationMode">
            <!-- Normal static title text -->
            <enum name="normal" value="0"/>
            <!-- The action bar will use a selection list for navigation. -->
            <enum name="listMode" value="1"/>
            <!-- The action bar will use a series of horizontal tabs for navigation. -->
            <enum name="tabMode" value="2"/>
        </attr>
        
        <attr name="displayOptions">
            <flag name="none" value="0"/>
            <flag name="useLogo" value="0x1"/>
            <flag name="showHome" value="0x2"/>
            <flag name="homeAsUp" value="0x4"/>
            <flag name="showTitle" value="0x8"/>
            <flag name="showCustom" value="0x10"/>
            <flag name="disableHome" value="0x20"/>
        </attr>
        
        <attr name="title"/>
        
        <attr format="string" name="subtitle"/>
        
        <attr format="reference" name="titleTextStyle"/>
        
        <attr format="reference" name="subtitleTextStyle"/>
        
        <attr format="reference" name="icon"/>
        
        <attr format="reference" name="logo"/>
        
        <attr format="reference" name="divider"/>
        
        <attr format="reference" name="background"/>
        
        <attr format="reference|color" name="backgroundStacked"/>
        
        <attr format="reference|color" name="backgroundSplit"/>
        
        <attr format="reference" name="customNavigationLayout"/>
        
        <attr name="height"/>
        
        <attr format="reference" name="homeLayout"/>
        
        <attr format="reference" name="progressBarStyle"/>
        
        <attr format="reference" name="indeterminateProgressStyle"/>
        
        <attr format="dimension" name="progressBarPadding"/>
        
        <attr name="homeAsUpIndicator"/>
        
        <attr format="dimension" name="itemPadding"/>
        
        <attr format="boolean" name="hideOnContentScroll"/>
        
        <attr format="dimension" name="contentInsetStart"/>
        
        <attr format="dimension" name="contentInsetEnd"/>
        
        <attr format="dimension" name="contentInsetLeft"/>
        
        <attr format="dimension" name="contentInsetRight"/>
        
        <attr format="dimension" name="contentInsetStartWithNavigation"/>
        
        <attr format="dimension" name="contentInsetEndWithActions"/>
        
        <attr format="dimension" name="elevation"/>
        
        <attr format="reference" name="popupTheme"/>
    </declare-styleable>
    <declare-styleable name="ActionBarLayout">
        <attr name="android:layout_gravity"/>
    </declare-styleable>
    <declare-styleable name="ActionMenuItemView">
        <attr name="android:minWidth"/>
    </declare-styleable>
    <declare-styleable name="ActionMenuView">
        
    </declare-styleable>
    <declare-styleable name="ActionMode">
        
        <attr name="titleTextStyle"/>
        
        <attr name="subtitleTextStyle"/>
        
        <attr name="background"/>
        
        <attr name="backgroundSplit"/>
        
        <attr name="height"/>
        
        <attr format="reference" name="closeItemLayout"/>
    </declare-styleable>
    <declare-styleable name="ActivityChooserView">
        
        <attr format="string" name="initialActivityCount"/>
        
        <attr format="reference" name="expandActivityOverflowButtonDrawable"/>
    </declare-styleable>
    <declare-styleable name="AdSetSizeLimitView">
        
        <attr name="adSetMaxHeight"/>
        
        <attr name="adSetMaxWidth"/>
    </declare-styleable>
    <declare-styleable name="AdView">
        <attr name="beizi_adUnitId"/>
        <attr name="beizi_adSize"/>
        <attr name="test"/>
        <attr format="boolean" name="should_reload_on_resume"/>
        <attr format="integer" name="auto_refresh_interval"/>
        <attr format="boolean" name="opens_native_browser"/>
        <attr format="boolean" name="expands_to_fit_screen_width"/>
        <attr format="boolean" name="resize_ad_to_fit_container"/>
        <attr name="show_loading_indicator"/>
        <attr format="enum" name="transition_type">
            <enum name="none" value="0"/>
            <enum name="random" value="1"/>
            <enum name="fade" value="2"/>
            <enum name="push" value="3"/>
            <enum name="movein" value="4"/>
            <enum name="reveal" value="5"/>
        </attr>
        <attr format="enum" name="transition_direction">
            <enum name="up" value="0"/>
            <enum name="down" value="1"/>
            <enum name="right" value="2"/>
            <enum name="left" value="3"/>
        </attr>
        <attr format="integer" name="transition_duration"/>
        <attr name="load_landing_page_in_background"/>
        <attr name="video_scale_type">
            <enum name="none" value="0"/>

            <enum name="fitXY" value="1"/>
            <enum name="fitStart" value="2"/>
            <enum name="fitCenter" value="3"/>
            <enum name="fitEnd" value="4"/>

            <enum name="leftTop" value="5"/>
            <enum name="leftCenter" value="6"/>
            <enum name="leftBottom" value="7"/>
            <enum name="centerTop" value="8"/>
            <enum name="center" value="9"/>
            <enum name="centerBottom" value="10"/>
            <enum name="rightTop" value="11"/>
            <enum name="rightCenter" value="12"/>
            <enum name="rightBottom" value="13"/>

            <enum name="leftTopCrop" value="14"/>
            <enum name="leftCenterCrop" value="15"/>
            <enum name="leftBottomCrop" value="16"/>
            <enum name="centerTopCrop" value="17"/>
            <enum name="centerCrop" value="18"/>
            <enum name="centerBottomCrop" value="19"/>
            <enum name="rightTopCrop" value="20"/>
            <enum name="rightCenterCrop" value="21"/>
            <enum name="rightBottomCrop" value="22"/>

            <enum name="startInside" value="23"/>
            <enum name="centerInside" value="24"/>
            <enum name="endInside" value="25"/>
        </attr>
    </declare-styleable>
    <declare-styleable name="AlertDialog">
        <attr name="android:layout"/>
        <attr format="reference" name="buttonPanelSideLayout"/>
        <attr format="reference" name="listLayout"/>
        <attr format="reference" name="multiChoiceItemLayout"/>
        <attr format="reference" name="singleChoiceItemLayout"/>
        <attr format="reference" name="listItemLayout"/>
        <attr format="boolean" name="showTitle"/>
        <attr format="dimension" name="buttonIconDimen"/>
    </declare-styleable>
    <declare-styleable name="AnimatedStateListDrawableCompat">
        
        <attr name="android:visible"/>
        
        <attr name="android:variablePadding"/>
        
        <attr name="android:constantSize"/>
        
        <attr name="android:dither"/>
        
        <attr name="android:enterFadeDuration"/>
        
        <attr name="android:exitFadeDuration"/>
        
        
    </declare-styleable>
    <declare-styleable name="AnimatedStateListDrawableItem">
        
        <attr name="android:drawable"/>
        
        <attr name="android:id"/>
    </declare-styleable>
    <declare-styleable name="AnimatedStateListDrawableTransition">
        
        <attr name="android:fromId"/>
        
        <attr name="android:toId"/>
        
        <attr name="android:drawable"/>
        
        <attr name="android:reversible"/>
    </declare-styleable>
    <declare-styleable name="AppBarLayout"><attr name="elevation"/><attr name="android:background"/><attr format="boolean" name="expanded"/><attr name="android:keyboardNavigationCluster"/><attr name="android:touchscreenBlocksFocus"/><attr format="boolean" name="liftOnScroll"/></declare-styleable>
    <declare-styleable name="AppBarLayoutStates"><attr format="boolean" name="state_collapsed"/><attr format="boolean" name="state_collapsible"/><attr format="boolean" name="state_lifted"/><attr format="boolean" name="state_liftable"/></declare-styleable>
    <declare-styleable name="AppBarLayout_Layout"><attr name="layout_scrollFlags">
      
      <flag name="scroll" value="0x1"/>

      
      <flag name="exitUntilCollapsed" value="0x2"/>

      
      <flag name="enterAlways" value="0x4"/>

      
      <flag name="enterAlwaysCollapsed" value="0x8"/>

      
      <flag name="snap" value="0x10"/>

      
      <flag name="snapMargins" value="0x20"/>
    </attr><attr format="reference" name="layout_scrollInterpolator"/></declare-styleable>
    <declare-styleable name="AppCompatImageView">
        <attr name="android:src"/>
        
        <attr format="reference" name="srcCompat"/>

        
        <attr format="color" name="tint"/>

        
        <attr name="tintMode">
            <!-- The tint is drawn on top of the drawable.
                 [Sa + (1 - Sa)*Da, Rc = Sc + (1 - Sa)*Dc] -->
            <enum name="src_over" value="3"/>
            <!-- The tint is masked by the alpha channel of the drawable. The drawable’s
                 color channels are thrown out. [Sa * Da, Sc * Da] -->
            <enum name="src_in" value="5"/>
            <!-- The tint is drawn above the drawable, but with the drawable’s alpha
                 channel masking the result. [Da, Sc * Da + (1 - Sa) * Dc] -->
            <enum name="src_atop" value="9"/>
            <!-- Multiplies the color and alpha channels of the drawable with those of
                 the tint. [Sa * Da, Sc * Dc] -->
            <enum name="multiply" value="14"/>
            <!-- [Sa + Da - Sa * Da, Sc + Dc - Sc * Dc] -->
            <enum name="screen" value="15"/>
            <!-- Combines the tint and icon color and alpha channels, clamping the
                 result to valid color values. Saturate(S + D) -->
            <enum name="add" value="16"/>
        </attr>
    </declare-styleable>
    <declare-styleable name="AppCompatSeekBar">
        <attr name="android:thumb"/>
        
        <attr format="reference" name="tickMark"/>
        
        <attr format="color" name="tickMarkTint"/>
        
        <attr name="tickMarkTintMode">
            <!-- The tint is drawn on top of the drawable.
                 [Sa + (1 - Sa)*Da, Rc = Sc + (1 - Sa)*Dc] -->
            <enum name="src_over" value="3"/>
            <!-- The tint is masked by the alpha channel of the drawable. The drawable’s
                 color channels are thrown out. [Sa * Da, Sc * Da] -->
            <enum name="src_in" value="5"/>
            <!-- The tint is drawn above the drawable, but with the drawable’s alpha
                 channel masking the result. [Da, Sc * Da + (1 - Sa) * Dc] -->
            <enum name="src_atop" value="9"/>
            <!-- Multiplies the color and alpha channels of the drawable with those of
                 the tint. [Sa * Da, Sc * Dc] -->
            <enum name="multiply" value="14"/>
            <!-- [Sa + Da - Sa * Da, Sc + Dc - Sc * Dc] -->
            <enum name="screen" value="15"/>
            <!-- Combines the tint and drawable color and alpha channels, clamping the
                 result to valid color values. Saturate(S + D) -->
            <enum name="add" value="16"/>
        </attr>
    </declare-styleable>
    <declare-styleable name="AppCompatTextHelper">
        <attr name="android:drawableLeft"/>
        <attr name="android:drawableTop"/>
        <attr name="android:drawableRight"/>
        <attr name="android:drawableBottom"/>
        <attr name="android:drawableStart"/>
        <attr name="android:drawableEnd"/>
        <attr name="android:textAppearance"/>
    </declare-styleable>
    <declare-styleable name="AppCompatTextView">
        
        <attr format="reference|boolean" name="textAllCaps"/>
        <attr name="android:textAppearance"/>
        
        <attr format="enum" name="autoSizeTextType">
            <!-- No auto-sizing (default). -->
            <enum name="none" value="0"/>
            <!-- Uniform horizontal and vertical text size scaling to fit within the
            container. -->
            <enum name="uniform" value="1"/>
        </attr>
        
        <attr format="dimension" name="autoSizeStepGranularity"/>
        
        <attr format="reference" name="autoSizePresetSizes"/>
        
        <attr format="dimension" name="autoSizeMinTextSize"/>
        
        <attr format="dimension" name="autoSizeMaxTextSize"/>
        
        <attr format="string" name="fontFamily"/>
        
        <attr format="dimension" name="lineHeight"/>
        
        <attr format="dimension" name="firstBaselineToTopHeight"/>
        
        <attr format="dimension" name="lastBaselineToBottomHeight"/>
    </declare-styleable>
    <declare-styleable name="AppCompatTheme">

        
        
        
        <eat-comment/>

        
        <attr format="boolean" name="windowActionBar"/>

        
        <attr format="boolean" name="windowNoTitle"/>

        
        <attr format="boolean" name="windowActionBarOverlay"/>

        
        <attr format="boolean" name="windowActionModeOverlay"/>

        
        <attr format="dimension|fraction" name="windowFixedWidthMajor"/>
        
        <attr format="dimension|fraction" name="windowFixedHeightMinor"/>

        
        <attr format="dimension|fraction" name="windowFixedWidthMinor"/>
        
        <attr format="dimension|fraction" name="windowFixedHeightMajor"/>

        
        <attr format="dimension|fraction" name="windowMinWidthMajor"/>
        
        <attr format="dimension|fraction" name="windowMinWidthMinor"/>

        <attr name="android:windowIsFloating"/>
        <attr name="android:windowAnimationStyle"/>

        
        
        
        <eat-comment/>
        
        <attr format="reference" name="actionBarTabStyle"/>
        <attr format="reference" name="actionBarTabBarStyle"/>
        <attr format="reference" name="actionBarTabTextStyle"/>
        <attr format="reference" name="actionOverflowButtonStyle"/>
        <attr format="reference" name="actionOverflowMenuStyle"/>
        
        <attr format="reference" name="actionBarPopupTheme"/>
        
        <attr format="reference" name="actionBarStyle"/>
        
        <attr format="reference" name="actionBarSplitStyle"/>
        
        <attr format="reference" name="actionBarTheme"/>
        
        <attr format="reference" name="actionBarWidgetTheme"/>
        
        <attr format="dimension" name="actionBarSize">
            <enum name="wrap_content" value="0"/>
        </attr>
        
        <attr format="reference" name="actionBarDivider"/>
        
        <attr format="reference" name="actionBarItemBackground"/>
        
        <attr format="reference" name="actionMenuTextAppearance"/>
        
        
        <attr format="color|reference" name="actionMenuTextColor"/>


        
        
        
        <eat-comment/>
        <attr format="reference" name="actionModeStyle"/>
        <attr format="reference" name="actionModeCloseButtonStyle"/>
        
        <attr format="reference" name="actionModeBackground"/>
        
        <attr format="reference" name="actionModeSplitBackground"/>
        
        <attr format="reference" name="actionModeCloseDrawable"/>
        
        <attr format="reference" name="actionModeCutDrawable"/>
        
        <attr format="reference" name="actionModeCopyDrawable"/>
        
        <attr format="reference" name="actionModePasteDrawable"/>
        
        <attr format="reference" name="actionModeSelectAllDrawable"/>
        
        <attr format="reference" name="actionModeShareDrawable"/>
        
        <attr format="reference" name="actionModeFindDrawable"/>
        
        <attr format="reference" name="actionModeWebSearchDrawable"/>

        
        <attr format="reference" name="actionModePopupWindowStyle"/>


        
        
        
        <eat-comment/>
        
        <attr format="reference" name="textAppearanceLargePopupMenu"/>
        
        <attr format="reference" name="textAppearanceSmallPopupMenu"/>
        
        <attr format="reference" name="textAppearancePopupMenuHeader"/>


        
        
        
        <eat-comment/>

        
        <attr format="reference" name="dialogTheme"/>
        
        <attr format="dimension" name="dialogPreferredPadding"/>
        
        <attr format="reference" name="listDividerAlertDialog"/>
        
        <attr format="dimension" name="dialogCornerRadius"/>

        
        
        
        <eat-comment/>

        
        <attr format="reference" name="actionDropDownStyle"/>
        
        <attr format="dimension" name="dropdownListPreferredItemHeight"/>
        
        <attr format="reference" name="spinnerDropDownItemStyle"/>
        
        <attr format="reference" name="homeAsUpIndicator"/>

        
        <attr format="reference" name="actionButtonStyle"/>

        
        <attr format="reference" name="buttonBarStyle"/>
        
        <attr format="reference" name="buttonBarButtonStyle"/>
        
        <attr format="reference" name="selectableItemBackground"/>
        
        <attr format="reference" name="selectableItemBackgroundBorderless"/>
        
        <attr format="reference" name="borderlessButtonStyle"/>
        
        <attr format="reference" name="dividerVertical"/>
        
        <attr format="reference" name="dividerHorizontal"/>
        
        <attr format="reference" name="activityChooserViewStyle"/>

        
        <attr format="reference" name="toolbarStyle"/>
        
        <attr format="reference" name="toolbarNavigationButtonStyle"/>

        
        <attr format="reference" name="popupMenuStyle"/>
        
        <attr format="reference" name="popupWindowStyle"/>

        
        <attr format="reference|color" name="editTextColor"/>
        
        <attr format="reference" name="editTextBackground"/>

        
        <attr format="reference" name="imageButtonStyle"/>

        
        
        
        <eat-comment/>
        
        <attr format="reference" name="textAppearanceSearchResultTitle"/>
        
        <attr format="reference" name="textAppearanceSearchResultSubtitle"/>
        
        <attr format="reference|color" name="textColorSearchUrl"/>
        
        <attr format="reference" name="searchViewStyle"/>

        
        
        
        <eat-comment/>

        
        <attr format="dimension" name="listPreferredItemHeight"/>
        
        <attr format="dimension" name="listPreferredItemHeightSmall"/>
        
        <attr format="dimension" name="listPreferredItemHeightLarge"/>

        
        <attr format="dimension" name="listPreferredItemPaddingLeft"/>
        
        <attr format="dimension" name="listPreferredItemPaddingRight"/>

        
        <attr format="reference" name="dropDownListViewStyle"/>
        <attr format="reference" name="listPopupWindowStyle"/>

        
        <attr format="reference" name="textAppearanceListItem"/>
        
        <attr format="reference" name="textAppearanceListItemSecondary"/>
        
        <attr format="reference" name="textAppearanceListItemSmall"/>

        
        
        
        <eat-comment/>

        
        <attr format="reference" name="panelBackground"/>
        
        <attr format="dimension" name="panelMenuListWidth"/>
        
        <attr format="reference" name="panelMenuListTheme"/>
        
        <attr format="reference" name="listChoiceBackgroundIndicator"/>

        
        
        
        <eat-comment/>

        
        <attr format="color" name="colorPrimary"/>

        
        <attr format="color" name="colorPrimaryDark"/>

        
        <attr format="color" name="colorAccent"/>

        
        <attr format="color" name="colorControlNormal"/>

        
        <attr format="color" name="colorControlActivated"/>

        
        <attr format="color" name="colorControlHighlight"/>

        
        <attr format="color" name="colorButtonNormal"/>

        
        <attr format="color" name="colorSwitchThumbNormal"/>

        
        <attr format="reference" name="controlBackground"/>

        
        <attr format="color" name="colorBackgroundFloating"/>

        
        
        
        <eat-comment/>
        <attr format="reference" name="alertDialogStyle"/>
        <attr format="reference" name="alertDialogButtonGroupStyle"/>
        <attr format="boolean" name="alertDialogCenterButtons"/>
        
        <attr format="reference" name="alertDialogTheme"/>

        
        <attr format="reference|color" name="textColorAlertDialogListItem"/>

        
        <attr format="reference" name="buttonBarPositiveButtonStyle"/>

        
        <attr format="reference" name="buttonBarNegativeButtonStyle"/>

        
        <attr format="reference" name="buttonBarNeutralButtonStyle"/>

        
        
        
        <eat-comment/>

        
        <attr format="reference" name="autoCompleteTextViewStyle"/>
        
        <attr format="reference" name="buttonStyle"/>
        
        <attr format="reference" name="buttonStyleSmall"/>
        
        <attr format="reference" name="checkboxStyle"/>
        
        <attr format="reference" name="checkedTextViewStyle"/>
        
        <attr format="reference" name="editTextStyle"/>
        
        <attr format="reference" name="radioButtonStyle"/>
        
        <attr format="reference" name="ratingBarStyle"/>
        
        <attr format="reference" name="ratingBarStyleIndicator"/>
        
        <attr format="reference" name="ratingBarStyleSmall"/>
        
        <attr format="reference" name="seekBarStyle"/>
        
        <attr format="reference" name="spinnerStyle"/>
        
        <attr format="reference" name="switchStyle"/>

        
        <attr format="reference" name="listMenuViewStyle"/>

        
        
        
        <eat-comment/>

        
        <attr format="reference" name="tooltipFrameBackground"/>
        
        <attr format="reference|color" name="tooltipForegroundColor"/>

        
        <attr format="reference|color" name="colorError"/>

        <attr format="string" name="viewInflaterClass"/>
    </declare-styleable>
    <declare-styleable name="BeiZiCircleProgressViewStyle">
        <attr format="dimension" name="adScopeRadius"/>
        <attr format="dimension" name="adScopeStrokeWidth"/>
        <attr format="color" name="adScopeCircleColor"/>
        <attr format="color" name="adScopeRingColor"/>
        <attr format="color" name="adScopeTextColor"/>
        <attr format="color" name="adScopeRingBgColor"/>
    </declare-styleable>
    <declare-styleable name="BeiZi_BackArrowView">
        
        <attr format="color" name="beizi_bav_color"/>
        
        <attr format="integer|float|dimension" name="beizi_bav_stroke_width"/>
        
        <attr format="enum" name="beizi_bav_arrow_style">
            
            <enum name="beizi_material_design" value="1"/>
            
            <enum name="beizi_wechat_design" value="2"/>
        </attr>
    </declare-styleable>
    <declare-styleable name="BottomAppBar"><attr name="backgroundTint"/><attr name="fabAlignmentMode">
      
      <enum name="center" value="0"/>
      
      <enum name="end" value="1"/>
    </attr><attr format="dimension" name="fabCradleMargin"/><attr format="dimension" name="fabCradleRoundedCornerRadius"/><attr format="dimension" name="fabCradleVerticalOffset"/><attr format="boolean" name="hideOnScroll"/></declare-styleable>
    <declare-styleable name="BottomNavigationView"><attr name="menu"/><attr name="labelVisibilityMode">
      
      <enum name="auto" value="-1"/>
      
      <enum name="selected" value="0"/>
      
      <enum name="labeled" value="1"/>
      
      <enum name="unlabeled" value="2"/>
    </attr><attr name="itemBackground"/><attr format="dimension" name="itemIconSize"/><attr name="itemIconTint"/><attr format="reference" name="itemTextAppearanceInactive"/><attr format="reference" name="itemTextAppearanceActive"/><attr name="itemTextColor"/><attr format="boolean" name="itemHorizontalTranslationEnabled"/><attr name="elevation"/></declare-styleable>
    <declare-styleable name="BottomSheetBehavior_Layout"><attr format="dimension" name="behavior_peekHeight">
      
      <enum name="auto" value="-1"/>
    </attr><attr format="boolean" name="behavior_hideable"/><attr format="boolean" name="behavior_skipCollapsed"/><attr format="boolean" name="behavior_fitToContents"/></declare-styleable>
    <declare-styleable name="ButtonBarLayout">
        
        <attr format="boolean" name="allowStacking"/>
    </declare-styleable>
    <declare-styleable name="CardView">
        
        <attr format="color" name="cardBackgroundColor"/>
        
        <attr format="dimension" name="cardCornerRadius"/>
        
        <attr format="dimension" name="cardElevation"/>
        
        <attr format="dimension" name="cardMaxElevation"/>
        
        <attr format="boolean" name="cardUseCompatPadding"/>
        
        <attr format="boolean" name="cardPreventCornerOverlap"/>
        
        <attr format="dimension" name="contentPadding"/>
        
        <attr format="dimension" name="contentPaddingLeft"/>
        
        <attr format="dimension" name="contentPaddingRight"/>
        
        <attr format="dimension" name="contentPaddingTop"/>
        
        <attr format="dimension" name="contentPaddingBottom"/>
        
        <attr name="android:minWidth"/>
        
        <attr name="android:minHeight"/>
    </declare-styleable>
    <declare-styleable name="Chip"><attr format="color" name="chipBackgroundColor"/><attr format="dimension" name="chipMinHeight"/><attr format="dimension" name="chipCornerRadius"/><attr format="color" name="chipStrokeColor"/><attr format="dimension" name="chipStrokeWidth"/><attr name="rippleColor"/><attr name="android:text"/><attr name="android:textAppearance"/><attr name="android:ellipsize"/><attr name="android:maxWidth"/><attr format="boolean" name="chipIconVisible"/><attr format="boolean" name="chipIconEnabled"/><attr format="reference" name="chipIcon"/><attr format="color" name="chipIconTint"/><attr format="dimension" name="chipIconSize"/><attr format="boolean" name="closeIconVisible"/><attr format="boolean" name="closeIconEnabled"/><attr format="reference" name="closeIcon"/><attr format="color" name="closeIconTint"/><attr format="dimension" name="closeIconSize"/><attr name="android:checkable"/><attr format="boolean" name="checkedIconVisible"/><attr format="boolean" name="checkedIconEnabled"/><attr format="reference" name="checkedIcon"/><attr name="showMotionSpec"/><attr name="hideMotionSpec"/><attr format="dimension" name="chipStartPadding"/><attr format="dimension" name="iconStartPadding"/><attr format="dimension" name="iconEndPadding"/><attr format="dimension" name="textStartPadding"/><attr format="dimension" name="textEndPadding"/><attr format="dimension" name="closeIconStartPadding"/><attr format="dimension" name="closeIconEndPadding"/><attr format="dimension" name="chipEndPadding"/></declare-styleable>
    <declare-styleable name="ChipGroup"><attr format="dimension" name="chipSpacing"/><attr format="dimension" name="chipSpacingHorizontal"/><attr format="dimension" name="chipSpacingVertical"/><attr format="boolean" name="singleLine"/><attr format="boolean" name="singleSelection"/><attr format="reference" name="checkedChip"/></declare-styleable>
    <declare-styleable name="CollapsingToolbarLayout"><attr format="dimension" name="expandedTitleMargin"/><attr format="dimension" name="expandedTitleMarginStart"/><attr format="dimension" name="expandedTitleMarginTop"/><attr format="dimension" name="expandedTitleMarginEnd"/><attr format="dimension" name="expandedTitleMarginBottom"/><attr format="reference" name="expandedTitleTextAppearance"/><attr format="reference" name="collapsedTitleTextAppearance"/><attr format="color" name="contentScrim"/><attr format="color" name="statusBarScrim"/><attr format="reference" name="toolbarId"/><attr format="dimension" name="scrimVisibleHeightTrigger"/><attr format="integer" name="scrimAnimationDuration"/><attr name="collapsedTitleGravity">
      
      <flag name="top" value="0x30"/>
      
      <flag name="bottom" value="0x50"/>
      
      <flag name="left" value="0x03"/>
      
      <flag name="right" value="0x05"/>
      
      <flag name="center_vertical" value="0x10"/>
      
      <flag name="fill_vertical" value="0x70"/>
      
      <flag name="center_horizontal" value="0x01"/>
      
      <flag name="center" value="0x11"/>
      
      <flag name="start" value="0x00800003"/>
      
      <flag name="end" value="0x00800005"/>
    </attr><attr name="expandedTitleGravity">
      
      <flag name="top" value="0x30"/>
      
      <flag name="bottom" value="0x50"/>
      
      <flag name="left" value="0x03"/>
      
      <flag name="right" value="0x05"/>
      
      <flag name="center_vertical" value="0x10"/>
      
      <flag name="fill_vertical" value="0x70"/>
      
      <flag name="center_horizontal" value="0x01"/>
      
      <flag name="center" value="0x11"/>
      
      <flag name="start" value="0x00800003"/>
      
      <flag name="end" value="0x00800005"/>
    </attr><attr format="boolean" name="titleEnabled"/><attr name="title"/></declare-styleable>
    <declare-styleable name="CollapsingToolbarLayout_Layout"><attr name="layout_collapseMode">
      
      <enum name="none" value="0"/>
      
      <enum name="pin" value="1"/>
      
      <enum name="parallax" value="2"/>
    </attr><attr format="float" name="layout_collapseParallaxMultiplier"/></declare-styleable>
    <declare-styleable name="ColorStateListItem">
        
        <attr name="android:color"/>
        
        <attr format="float" name="alpha"/>
        <attr name="android:alpha"/>
    </declare-styleable>
    <declare-styleable name="CompoundButton">
        <attr name="android:button"/>
        
        <attr format="color" name="buttonTint"/>

        
        <attr name="buttonTintMode">
            <!-- The tint is drawn on top of the drawable.
                 [Sa + (1 - Sa)*Da, Rc = Sc + (1 - Sa)*Dc] -->
            <enum name="src_over" value="3"/>
            <!-- The tint is masked by the alpha channel of the drawable. The drawable’s
                 color channels are thrown out. [Sa * Da, Sc * Da] -->
            <enum name="src_in" value="5"/>
            <!-- The tint is drawn above the drawable, but with the drawable’s alpha
                 channel masking the result. [Da, Sc * Da + (1 - Sa) * Dc] -->
            <enum name="src_atop" value="9"/>
            <!-- Multiplies the color and alpha channels of the drawable with those of
                 the tint. [Sa * Da, Sc * Dc] -->
            <enum name="multiply" value="14"/>
            <!-- [Sa + Da - Sa * Da, Sc + Dc - Sc * Dc] -->
            <enum name="screen" value="15"/>
            <!-- Combines the tint and icon color and alpha channels, clamping the
                 result to valid color values. Saturate(S + D) -->
            <enum name="add" value="16"/>
        </attr>
    </declare-styleable>
    <declare-styleable name="ConstraintLayout_Layout"><attr name="android:orientation"/><attr name="android:minWidth"/><attr name="android:minHeight"/><attr name="android:maxWidth"/><attr name="android:maxHeight"/><attr name="layout_optimizationLevel"/><attr name="constraintSet"/><attr name="barrierDirection"/><attr name="barrierAllowsGoneWidgets"/><attr name="constraint_referenced_ids"/><attr name="chainUseRtl"/><attr name="layout_constraintCircle"/><attr name="layout_constraintCircleRadius"/><attr name="layout_constraintCircleAngle"/><attr name="layout_constraintGuide_begin"/><attr name="layout_constraintGuide_end"/><attr name="layout_constraintGuide_percent"/><attr name="layout_constraintLeft_toLeftOf"/><attr name="layout_constraintLeft_toRightOf"/><attr name="layout_constraintRight_toLeftOf"/><attr name="layout_constraintRight_toRightOf"/><attr name="layout_constraintTop_toTopOf"/><attr name="layout_constraintTop_toBottomOf"/><attr name="layout_constraintBottom_toTopOf"/><attr name="layout_constraintBottom_toBottomOf"/><attr name="layout_constraintBaseline_toBaselineOf"/><attr name="layout_constraintStart_toEndOf"/><attr name="layout_constraintStart_toStartOf"/><attr name="layout_constraintEnd_toStartOf"/><attr name="layout_constraintEnd_toEndOf"/><attr name="layout_goneMarginLeft"/><attr name="layout_goneMarginTop"/><attr name="layout_goneMarginRight"/><attr name="layout_goneMarginBottom"/><attr name="layout_goneMarginStart"/><attr name="layout_goneMarginEnd"/><attr name="layout_constrainedWidth"/><attr name="layout_constrainedHeight"/><attr name="layout_constraintHorizontal_bias"/><attr name="layout_constraintVertical_bias"/><attr name="layout_constraintWidth_default"/><attr name="layout_constraintHeight_default"/><attr name="layout_constraintWidth_min"/><attr name="layout_constraintWidth_max"/><attr name="layout_constraintWidth_percent"/><attr name="layout_constraintHeight_min"/><attr name="layout_constraintHeight_max"/><attr name="layout_constraintHeight_percent"/><attr name="layout_constraintLeft_creator"/><attr name="layout_constraintTop_creator"/><attr name="layout_constraintRight_creator"/><attr name="layout_constraintBottom_creator"/><attr name="layout_constraintBaseline_creator"/><attr name="layout_constraintDimensionRatio"/><attr name="layout_constraintHorizontal_weight"/><attr name="layout_constraintVertical_weight"/><attr name="layout_constraintHorizontal_chainStyle"/><attr name="layout_constraintVertical_chainStyle"/><attr name="layout_editor_absoluteX"/><attr name="layout_editor_absoluteY"/></declare-styleable>
    <declare-styleable name="ConstraintLayout_placeholder"><attr name="emptyVisibility"/><attr name="content"/></declare-styleable>
    <declare-styleable name="ConstraintSet"><attr name="android:orientation"/><attr name="android:id"/><attr name="android:visibility"/><attr name="android:alpha"/><attr name="android:elevation"/><attr name="android:rotation"/><attr name="android:rotationX"/><attr name="android:rotationY"/><attr name="android:scaleX"/><attr name="android:scaleY"/><attr name="android:transformPivotX"/><attr name="android:transformPivotY"/><attr name="android:translationX"/><attr name="android:translationY"/><attr name="android:translationZ"/><attr name="android:layout_width"/><attr name="android:layout_height"/><attr name="android:layout_marginStart"/><attr name="android:layout_marginBottom"/><attr name="android:layout_marginTop"/><attr name="android:layout_marginEnd"/><attr name="android:layout_marginLeft"/><attr name="android:layout_marginRight"/><attr name="layout_constraintCircle"/><attr name="layout_constraintCircleRadius"/><attr name="layout_constraintCircleAngle"/><attr name="layout_constraintGuide_begin"/><attr name="layout_constraintGuide_end"/><attr name="layout_constraintGuide_percent"/><attr name="layout_constraintLeft_toLeftOf"/><attr name="layout_constraintLeft_toRightOf"/><attr name="layout_constraintRight_toLeftOf"/><attr name="layout_constraintRight_toRightOf"/><attr name="layout_constraintTop_toTopOf"/><attr name="layout_constraintTop_toBottomOf"/><attr name="layout_constraintBottom_toTopOf"/><attr name="layout_constraintBottom_toBottomOf"/><attr name="layout_constraintBaseline_toBaselineOf"/><attr name="layout_constraintStart_toEndOf"/><attr name="layout_constraintStart_toStartOf"/><attr name="layout_constraintEnd_toStartOf"/><attr name="layout_constraintEnd_toEndOf"/><attr name="layout_goneMarginLeft"/><attr name="layout_goneMarginTop"/><attr name="layout_goneMarginRight"/><attr name="layout_goneMarginBottom"/><attr name="layout_goneMarginStart"/><attr name="layout_goneMarginEnd"/><attr name="layout_constrainedWidth"/><attr name="layout_constrainedHeight"/><attr name="layout_constraintHorizontal_bias"/><attr name="layout_constraintVertical_bias"/><attr name="layout_constraintWidth_default"/><attr name="layout_constraintHeight_default"/><attr name="layout_constraintWidth_min"/><attr name="layout_constraintWidth_max"/><attr name="layout_constraintWidth_percent"/><attr name="layout_constraintHeight_min"/><attr name="layout_constraintHeight_max"/><attr name="layout_constraintHeight_percent"/><attr name="layout_constraintLeft_creator"/><attr name="layout_constraintTop_creator"/><attr name="layout_constraintRight_creator"/><attr name="layout_constraintBottom_creator"/><attr name="layout_constraintBaseline_creator"/><attr name="layout_constraintDimensionRatio"/><attr name="layout_constraintHorizontal_weight"/><attr name="layout_constraintVertical_weight"/><attr name="layout_constraintHorizontal_chainStyle"/><attr name="layout_constraintVertical_chainStyle"/><attr name="layout_editor_absoluteX"/><attr name="layout_editor_absoluteY"/><attr name="barrierDirection"/><attr name="constraint_referenced_ids"/><attr name="android:maxHeight"/><attr name="android:maxWidth"/><attr name="android:minHeight"/><attr name="android:minWidth"/><attr name="barrierAllowsGoneWidgets"/><attr name="chainUseRtl"/></declare-styleable>
    <declare-styleable name="CoordinatorLayout">
        
        <attr format="reference" name="keylines"/>
        
        <attr format="color|reference" name="statusBarBackground"/>
    </declare-styleable>
    <declare-styleable name="CoordinatorLayout_Layout">
        <attr name="android:layout_gravity"/>
        
        <attr format="string" name="layout_behavior"/>
        
        <attr format="reference" name="layout_anchor"/>
        
        <attr format="integer" name="layout_keyline"/>

        
        <attr name="layout_anchorGravity">
            <!-- Push object to the top of its container, not changing its size. -->
            <flag name="top" value="0x30"/>
            <!-- Push object to the bottom of its container, not changing its size. -->
            <flag name="bottom" value="0x50"/>
            <!-- Push object to the left of its container, not changing its size. -->
            <flag name="left" value="0x03"/>
            <!-- Push object to the right of its container, not changing its size. -->
            <flag name="right" value="0x05"/>
            <!-- Place object in the vertical center of its container, not changing its size. -->
            <flag name="center_vertical" value="0x10"/>
            <!-- Grow the vertical size of the object if needed so it completely fills its container. -->
            <flag name="fill_vertical" value="0x70"/>
            <!-- Place object in the horizontal center of its container, not changing its size. -->
            <flag name="center_horizontal" value="0x01"/>
            <!-- Grow the horizontal size of the object if needed so it completely fills its container. -->
            <flag name="fill_horizontal" value="0x07"/>
            <!-- Place the object in the center of its container in both the vertical and horizontal axis, not changing its size. -->
            <flag name="center" value="0x11"/>
            <!-- Grow the horizontal and vertical size of the object if needed so it completely fills its container. -->
            <flag name="fill" value="0x77"/>
            <!-- Additional option that can be set to have the top and/or bottom edges of
                 the child clipped to its container's bounds.
                 The clip will be based on the vertical gravity: a top gravity will clip the bottom
                 edge, a bottom gravity will clip the top edge, and neither will clip both edges. -->
            <flag name="clip_vertical" value="0x80"/>
            <!-- Additional option that can be set to have the left and/or right edges of
                 the child clipped to its container's bounds.
                 The clip will be based on the horizontal gravity: a left gravity will clip the right
                 edge, a right gravity will clip the left edge, and neither will clip both edges. -->
            <flag name="clip_horizontal" value="0x08"/>
            <!-- Push object to the beginning of its container, not changing its size. -->
            <flag name="start" value="0x00800003"/>
            <!-- Push object to the end of its container, not changing its size. -->
            <flag name="end" value="0x00800005"/>
        </attr>

        
        <attr format="enum" name="layout_insetEdge">
            <!-- Don't inset. -->
            <enum name="none" value="0x0"/>
            <!-- Inset the top edge. -->
            <enum name="top" value="0x30"/>
            <!-- Inset the bottom edge. -->
            <enum name="bottom" value="0x50"/>
            <!-- Inset the left edge. -->
            <enum name="left" value="0x03"/>
            <!-- Inset the right edge. -->
            <enum name="right" value="0x05"/>
            <!-- Inset the start edge. -->
            <enum name="start" value="0x00800003"/>
            <!-- Inset the end edge. -->
            <enum name="end" value="0x00800005"/>
        </attr>
        
        <attr name="layout_dodgeInsetEdges">
            <!-- Don't dodge any edges -->
            <flag name="none" value="0x0"/>
            <!-- Dodge the top inset edge. -->
            <flag name="top" value="0x30"/>
            <!-- Dodge the bottom inset edge. -->
            <flag name="bottom" value="0x50"/>
            <!-- Dodge the left inset edge. -->
            <flag name="left" value="0x03"/>
            <!-- Dodge the right inset edge. -->
            <flag name="right" value="0x05"/>
            <!-- Dodge the start inset edge. -->
            <flag name="start" value="0x00800003"/>
            <!-- Dodge the end inset edge. -->
            <flag name="end" value="0x00800005"/>
            <!-- Dodge all the inset edges. -->
            <flag name="all" value="0x77"/>
        </attr>
    </declare-styleable>
    <declare-styleable name="DesignTheme"><attr name="bottomSheetDialogTheme"/><attr name="bottomSheetStyle"/></declare-styleable>
    <declare-styleable name="DrawerArrowToggle">
        
        <attr format="color" name="color"/>
        
        <attr format="boolean" name="spinBars"/>
        
        <attr format="dimension" name="drawableSize"/>
        
        <attr format="dimension" name="gapBetweenBars"/>
        
        <attr format="dimension" name="arrowHeadLength"/>
        
        <attr format="dimension" name="arrowShaftLength"/>
        
        <attr format="dimension" name="barLength"/>
        
        <attr format="dimension" name="thickness"/>
    </declare-styleable>
    <declare-styleable name="FloatingActionButton"><attr name="backgroundTint"/><attr name="backgroundTintMode"/><attr name="rippleColor"/><attr name="fabSize">
      
      <enum name="auto" value="-1"/>
      
      <enum name="normal" value="0"/>
      
      <enum name="mini" value="1"/>
    </attr><attr format="dimension" name="fabCustomSize"/><attr name="elevation"/><attr format="dimension" name="hoveredFocusedTranslationZ"/><attr format="dimension" name="pressedTranslationZ"/><attr format="dimension" name="borderWidth"/><attr format="boolean" name="useCompatPadding"/><attr format="dimension" name="maxImageSize"/><attr name="showMotionSpec"/><attr name="hideMotionSpec"/></declare-styleable>
    <declare-styleable name="FloatingActionButton_Behavior_Layout"><attr format="boolean" name="behavior_autoHide"/></declare-styleable>
    <declare-styleable name="FlowLayout"><attr format="dimension" name="itemSpacing"/><attr format="dimension" name="lineSpacing"/></declare-styleable>
    <declare-styleable name="FontFamily">
        
        <attr format="string" name="fontProviderAuthority"/>
        
        <attr format="string" name="fontProviderPackage"/>
        
        <attr format="string" name="fontProviderQuery"/>
        
        <attr format="reference" name="fontProviderCerts"/>
        
        <attr name="fontProviderFetchStrategy">
            <!-- The blocking font fetch works as follows.
              First, check the local cache, then if the requested font is not cached, request the
              font from the provider and wait until it is finished.  You can change the length of
              the timeout by modifying fontProviderFetchTimeout.  If the timeout happens, the
              default typeface will be used instead. -->
            <enum name="blocking" value="0"/>
            <!-- The async font fetch works as follows.
              First, check the local cache, then if the requeted font is not cached, trigger a
              request the font and continue with layout inflation. Once the font fetch succeeds, the
              target text view will be refreshed with the downloaded font data. The
              fontProviderFetchTimeout will be ignored if async loading is specified. -->
            <enum name="async" value="1"/>
        </attr>
        
        <attr format="integer" name="fontProviderFetchTimeout">
            <!-- A special value for the timeout. In this case, the blocking font fetching will not
              timeout and wait until a reply is received from the font provider. -->
            <enum name="forever" value="-1"/>
        </attr>
    </declare-styleable>
    <declare-styleable name="FontFamilyFont">
        
        <attr name="fontStyle">
            <enum name="normal" value="0"/>
            <enum name="italic" value="1"/>
        </attr>
        
        <attr format="reference" name="font"/>
        
        <attr format="integer" name="fontWeight"/>
        
        <attr format="string" name="fontVariationSettings"/>
        
        <attr format="integer" name="ttcIndex"/>
        
        <attr name="android:fontStyle"/>
        <attr name="android:font"/>
        <attr name="android:fontWeight"/>
        <attr name="android:fontVariationSettings"/>
        <attr name="android:ttcIndex"/>
    </declare-styleable>
    <declare-styleable name="ForegroundLinearLayout"><attr name="android:foreground"/><attr name="android:foregroundGravity"/><attr format="boolean" name="foregroundInsidePadding"/></declare-styleable>
    <declare-styleable name="GradientColor">
        
        <attr name="android:startColor"/>
        
        <attr name="android:centerColor"/>
        
        <attr name="android:endColor"/>
        
        <attr name="android:type"/>

        
        
        <attr name="android:gradientRadius"/>

        
        
        <attr name="android:centerX"/>
        
        <attr name="android:centerY"/>

        
        
        <attr name="android:startX"/>
        
        <attr name="android:startY"/>
        
        <attr name="android:endX"/>
        
        <attr name="android:endY"/>

        
        <attr name="android:tileMode"/>
    </declare-styleable>
    <declare-styleable name="GradientColorItem">
        
        <attr name="android:offset"/>
        
        <attr name="android:color"/>
    </declare-styleable>
    <declare-styleable name="LinearConstraintLayout"><attr name="android:orientation"/></declare-styleable>
    <declare-styleable name="LinearLayoutCompat">
        
        <attr name="android:orientation"/>
        <attr name="android:gravity"/>
        
        <attr name="android:baselineAligned"/>
        
        <attr name="android:baselineAlignedChildIndex"/>
        
        <attr name="android:weightSum"/>
        
        <attr format="boolean" name="measureWithLargestChild"/>
        
        <attr name="divider"/>
        
        <attr name="showDividers">
            <flag name="none" value="0"/>
            <flag name="beginning" value="1"/>
            <flag name="middle" value="2"/>
            <flag name="end" value="4"/>
        </attr>
        
        <attr format="dimension" name="dividerPadding"/>
    </declare-styleable>
    <declare-styleable name="LinearLayoutCompat_Layout">
        <attr name="android:layout_width"/>
        <attr name="android:layout_height"/>
        <attr name="android:layout_weight"/>
        <attr name="android:layout_gravity"/>
    </declare-styleable>
    <declare-styleable name="ListPopupWindow">
        
        <attr name="android:dropDownVerticalOffset"/>
        
        <attr name="android:dropDownHorizontalOffset"/>
    </declare-styleable>
    <declare-styleable name="MaterialButton"><attr name="android:insetLeft"/><attr name="android:insetRight"/><attr name="android:insetTop"/><attr name="android:insetBottom"/><attr name="backgroundTint"/><attr name="backgroundTintMode"/><attr format="reference" name="icon"/><attr format="dimension" name="iconSize"/><attr format="dimension" name="iconPadding"/><attr name="iconGravity">
      
      <flag name="start" value="0x1"/>
      
      <flag name="textStart" value="0x2"/>
    </attr><attr format="color" name="iconTint"/><attr name="iconTintMode"/><attr name="strokeColor"/><attr name="strokeWidth"/><attr format="dimension" name="cornerRadius"/><attr name="rippleColor"/></declare-styleable>
    <declare-styleable name="MaterialCardView"><attr name="strokeColor"/><attr name="strokeWidth"/></declare-styleable>
    <declare-styleable name="MaterialComponentsTheme"><attr name="colorAccent"/><attr name="colorPrimary"/><attr name="colorPrimaryDark"/><attr name="colorSecondary"/><attr name="scrimBackground"/><attr name="colorBackgroundFloating"/><attr name="bottomSheetDialogTheme"/><attr name="bottomSheetStyle"/><attr name="materialButtonStyle"/><attr name="chipGroupStyle"/><attr name="chipStyle"/><attr name="chipStandaloneStyle"/><attr name="editTextStyle"/><attr name="floatingActionButtonStyle"/><attr name="materialCardViewStyle"/><attr name="navigationViewStyle"/><attr name="tabStyle"/><attr name="textInputStyle"/><attr name="snackbarButtonStyle"/><attr name="textAppearanceHeadline1"/><attr name="textAppearanceHeadline2"/><attr name="textAppearanceHeadline3"/><attr name="textAppearanceHeadline4"/><attr name="textAppearanceHeadline5"/><attr name="textAppearanceHeadline6"/><attr name="textAppearanceSubtitle1"/><attr name="textAppearanceSubtitle2"/><attr name="textAppearanceBody1"/><attr name="textAppearanceBody2"/><attr name="textAppearanceCaption"/><attr name="textAppearanceButton"/><attr name="textAppearanceOverline"/></declare-styleable>
    <declare-styleable name="MenuGroup">

        
        <attr name="android:id"/>

        
        <attr name="android:menuCategory"/>

        
        <attr name="android:orderInCategory"/>

        
        <attr name="android:checkableBehavior"/>

        
        <attr name="android:visible"/>

        
        <attr name="android:enabled"/>

    </declare-styleable>
    <declare-styleable name="MenuItem">

        
        <attr name="android:id"/>

        
        <attr name="android:menuCategory"/>

        
        <attr name="android:orderInCategory"/>

        
        <attr name="android:title"/>

        
        <attr name="android:titleCondensed"/>

        
        <attr name="android:icon"/>

        
        <attr name="android:alphabeticShortcut"/>

        
        <attr name="alphabeticModifiers">
            <flag name="META" value="0x10000"/>
            <flag name="CTRL" value="0x1000"/>
            <flag name="ALT" value="0x02"/>
            <flag name="SHIFT" value="0x1"/>
            <flag name="SYM" value="0x4"/>
            <flag name="FUNCTION" value="0x8"/>
        </attr>

        
        <attr name="android:numericShortcut"/>

        
        <attr name="numericModifiers">
            <flag name="META" value="0x10000"/>
            <flag name="CTRL" value="0x1000"/>
            <flag name="ALT" value="0x02"/>
            <flag name="SHIFT" value="0x1"/>
            <flag name="SYM" value="0x4"/>
            <flag name="FUNCTION" value="0x8"/>
        </attr>

        
        <attr name="android:checkable"/>

        
        <attr name="android:checked"/>

        
        <attr name="android:visible"/>

        
        <attr name="android:enabled"/>

        
        <attr name="android:onClick"/>

        
        <attr name="showAsAction">
            <!-- Never show this item in an action bar, show it in the overflow menu instead.
                 Mutually exclusive with "ifRoom" and "always". -->
            <flag name="never" value="0"/>
            <!-- Show this item in an action bar if there is room for it as determined
                 by the system. Favor this option over "always" where possible.
                 Mutually exclusive with "never" and "always". -->
            <flag name="ifRoom" value="1"/>
            <!-- Always show this item in an actionbar, even if it would override
                 the system's limits of how much stuff to put there. This may make
                 your action bar look bad on some screens. In most cases you should
                 use "ifRoom" instead. Mutually exclusive with "ifRoom" and "never". -->
            <flag name="always" value="2"/>
            <!-- When this item is shown as an action in the action bar, show a text
                 label with it even if it has an icon representation. -->
            <flag name="withText" value="4"/>
            <!-- This item's action view collapses to a normal menu
                 item. When expanded, the action view takes over a
                 larger segment of its container. -->
            <flag name="collapseActionView" value="8"/>
        </attr>

        
        <attr format="reference" name="actionLayout"/>

        
        <attr format="string" name="actionViewClass"/>

        
        <attr format="string" name="actionProviderClass"/>

        
        <attr format="string" name="contentDescription"/>

        
        <attr format="string" name="tooltipText"/>

        
        <attr format="color" name="iconTint"/>

        
        <attr name="iconTintMode">
            <!-- The tint is drawn on top of the icon.
                 [Sa + (1 - Sa)*Da, Rc = Sc + (1 - Sa)*Dc] -->
            <enum name="src_over" value="3"/>
            <!-- The tint is masked by the alpha channel of the icon. The icon’s
                 color channels are thrown out. [Sa * Da, Sc * Da] -->
            <enum name="src_in" value="5"/>
            <!-- The tint is drawn above the icon, but with the icon’s alpha
                 channel masking the result. [Da, Sc * Da + (1 - Sa) * Dc] -->
            <enum name="src_atop" value="9"/>
            <!-- Multiplies the color and alpha channels of the icon with those of
                 the tint. [Sa * Da, Sc * Dc] -->
            <enum name="multiply" value="14"/>
            <!-- [Sa + Da - Sa * Da, Sc + Dc - Sc * Dc] -->
            <enum name="screen" value="15"/>
            <!-- Combines the tint and icon color and alpha channels, clamping the
                 result to valid color values. Saturate(S + D) -->
            <enum name="add" value="16"/>
        </attr>

    </declare-styleable>
    <declare-styleable name="MenuView">
        
        <attr name="android:itemTextAppearance"/>
        
        <attr name="android:horizontalDivider"/>
        
        <attr name="android:verticalDivider"/>
        
        <attr name="android:headerBackground"/>
        
        <attr name="android:itemBackground"/>
        
        <attr name="android:windowAnimationStyle"/>
        
        <attr name="android:itemIconDisabledAlpha"/>
        
        <attr format="boolean" name="preserveIconSpacing"/>
        
        <attr format="reference" name="subMenuArrow"/>
    </declare-styleable>
    <declare-styleable name="NavigationView"><attr name="android:background"/><attr name="android:fitsSystemWindows"/><attr name="android:maxWidth"/><attr name="elevation"/><attr format="reference" name="menu"/><attr format="color" name="itemIconTint"/><attr format="color" name="itemTextColor"/><attr format="reference" name="itemBackground"/><attr format="reference" name="itemTextAppearance"/><attr format="reference" name="headerLayout"/><attr format="dimension" name="itemHorizontalPadding"/><attr format="dimension" name="itemIconPadding"/></declare-styleable>
    <declare-styleable name="OSETCircularProgressView">
        <attr format="dimension" name="OSETbackWidth"/>    
        <attr format="dimension" name="OSETprogWidth"/>    
        <attr format="color" name="OSETbackColor"/>        
        <attr format="color" name="OSETprogColor"/>        
        <attr format="color" name="OSETprogStartColor"/>   
        <attr format="color" name="OSETprogFirstColor"/>   
        <attr format="integer" name="OSETprogress"/>       
    </declare-styleable>
    <declare-styleable name="OSETRoundedImageView">
        <attr format="dimension" name="oset_riv_corner_radius"/>
        <attr format="dimension" name="oset_riv_corner_radius_top_left"/>
        <attr format="dimension" name="oset_riv_corner_radius_top_right"/>
        <attr format="dimension" name="oset_riv_corner_radius_bottom_left"/>
        <attr format="dimension" name="oset_riv_corner_radius_bottom_right"/>
        <attr format="dimension" name="oset_riv_border_width"/>
        <attr format="color" name="oset_riv_border_color"/>
        <attr format="boolean" name="oset_riv_mutate_background"/>
        <attr format="boolean" name="oset_riv_oval"/>
        <attr name="android:scaleType"/>
        <attr name="oset_riv_tile_mode">
            <enum name="clamp" value="0"/>
            <enum name="repeat" value="1"/>
            <enum name="mirror" value="2"/>
        </attr>
        <attr name="oset_riv_tile_mode_x">
            <enum name="clamp" value="0"/>
            <enum name="repeat" value="1"/>
            <enum name="mirror" value="2"/>
        </attr>
        <attr name="oset_riv_tile_mode_y">
            <enum name="clamp" value="0"/>
            <enum name="repeat" value="1"/>
            <enum name="mirror" value="2"/>
        </attr>
    </declare-styleable>
    <declare-styleable name="PopupWindow">
        
        <attr format="boolean" name="overlapAnchor"/>
        <attr name="android:popupBackground"/>
        <attr name="android:popupAnimationStyle"/>
    </declare-styleable>
    <declare-styleable name="PopupWindowBackgroundState">
        
        <attr format="boolean" name="state_above_anchor"/>
    </declare-styleable>
    <declare-styleable name="RecycleListView">
        
        <attr format="dimension" name="paddingBottomNoButtons"/>
        
        <attr format="dimension" name="paddingTopNoTitle"/>
    </declare-styleable>
    <declare-styleable name="RecyclerView">
        
        <attr format="string" name="layoutManager"/>

        
        
        
        <eat-comment/>

        <attr name="android:orientation"/>
        <attr name="android:descendantFocusability"/>
        <attr format="integer" name="spanCount"/>
        <attr format="boolean" name="reverseLayout"/>
        <attr format="boolean" name="stackFromEnd"/>
        <attr format="boolean" name="fastScrollEnabled"/>
        <attr format="reference" name="fastScrollVerticalThumbDrawable"/>
        <attr format="reference" name="fastScrollVerticalTrackDrawable"/>
        <attr format="reference" name="fastScrollHorizontalThumbDrawable"/>
        <attr format="reference" name="fastScrollHorizontalTrackDrawable"/>
    </declare-styleable>
    <declare-styleable name="ScrimInsetsFrameLayout"><attr format="color|reference" name="insetForeground"/></declare-styleable>
    <declare-styleable name="ScrollingViewBehavior_Layout"><attr format="dimension" name="behavior_overlapTop"/></declare-styleable>
    <declare-styleable name="SearchView">
        
        <attr format="reference" name="layout"/>
        
        <attr format="boolean" name="iconifiedByDefault"/>
        
        <attr name="android:maxWidth"/>
        
        <attr format="string" name="queryHint"/>
        
        <attr format="string" name="defaultQueryHint"/>
        
        <attr name="android:imeOptions"/>
        
        <attr name="android:inputType"/>
        
        <attr format="reference" name="closeIcon"/>
        
        <attr format="reference" name="goIcon"/>
        
        <attr format="reference" name="searchIcon"/>
        
        <attr format="reference" name="searchHintIcon"/>
        
        <attr format="reference" name="voiceIcon"/>
        
        <attr format="reference" name="commitIcon"/>
        
        <attr format="reference" name="suggestionRowLayout"/>
        
        <attr format="reference" name="queryBackground"/>
        
        <attr format="reference" name="submitBackground"/>
        <attr name="android:focusable"/>
    </declare-styleable>
    <declare-styleable name="SigAdInfoView">
        <attr format="boolean" name="sig_isSmall"/>
    </declare-styleable>
    <declare-styleable name="Snackbar"><attr format="reference" name="snackbarStyle"/><attr format="reference" name="snackbarButtonStyle"/></declare-styleable>
    <declare-styleable name="SnackbarLayout"><attr name="android:maxWidth"/><attr name="elevation"/><attr format="dimension" name="maxActionInlineWidth"/></declare-styleable>
    <declare-styleable name="Spinner">
        
        <attr name="android:prompt"/>
        
        <attr name="popupTheme"/>
        
        <attr name="android:popupBackground"/>
        
        <attr name="android:dropDownWidth"/>
        
        <attr name="android:entries"/>
    </declare-styleable>
    <declare-styleable name="StateListDrawable">
        
        <attr name="android:visible"/>
        
        <attr name="android:variablePadding"/>
        
        <attr name="android:constantSize"/>
        
        <attr name="android:dither"/>
        
        <attr name="android:enterFadeDuration"/>
        
        <attr name="android:exitFadeDuration"/>
        
        
    </declare-styleable>
    <declare-styleable name="StateListDrawableItem">
        
        <attr name="android:drawable"/>
    </declare-styleable>
    <declare-styleable name="SwitchCompat">
        
        <attr name="android:thumb"/>
        
        <attr format="color" name="thumbTint"/>
        
        <attr name="thumbTintMode">
            <!-- The tint is drawn on top of the drawable.
                 [Sa + (1 - Sa)*Da, Rc = Sc + (1 - Sa)*Dc] -->
            <enum name="src_over" value="3"/>
            <!-- The tint is masked by the alpha channel of the drawable. The drawable’s
                 color channels are thrown out. [Sa * Da, Sc * Da] -->
            <enum name="src_in" value="5"/>
            <!-- The tint is drawn above the drawable, but with the drawable’s alpha
                 channel masking the result. [Da, Sc * Da + (1 - Sa) * Dc] -->
            <enum name="src_atop" value="9"/>
            <!-- Multiplies the color and alpha channels of the drawable with those of
                 the tint. [Sa * Da, Sc * Dc] -->
            <enum name="multiply" value="14"/>
            <!-- [Sa + Da - Sa * Da, Sc + Dc - Sc * Dc] -->
            <enum name="screen" value="15"/>
            <!-- Combines the tint and drawable color and alpha channels, clamping the
                 result to valid color values. Saturate(S + D) -->
            <enum name="add" value="16"/>
        </attr>
        
        <attr format="reference" name="track"/>
        
        <attr format="color" name="trackTint"/>
        
        <attr name="trackTintMode">
            <!-- The tint is drawn on top of the drawable.
                 [Sa + (1 - Sa)*Da, Rc = Sc + (1 - Sa)*Dc] -->
            <enum name="src_over" value="3"/>
            <!-- The tint is masked by the alpha channel of the drawable. The drawable’s
                 color channels are thrown out. [Sa * Da, Sc * Da] -->
            <enum name="src_in" value="5"/>
            <!-- The tint is drawn above the drawable, but with the drawable’s alpha
                 channel masking the result. [Da, Sc * Da + (1 - Sa) * Dc] -->
            <enum name="src_atop" value="9"/>
            <!-- Multiplies the color and alpha channels of the drawable with those of
                 the tint. [Sa * Da, Sc * Dc] -->
            <enum name="multiply" value="14"/>
            <!-- [Sa + Da - Sa * Da, Sc + Dc - Sc * Dc] -->
            <enum name="screen" value="15"/>
            <!-- Combines the tint and drawable color and alpha channels, clamping the
                 result to valid color values. Saturate(S + D) -->
            <enum name="add" value="16"/>
        </attr>
        
        <attr name="android:textOn"/>
        
        <attr name="android:textOff"/>
        
        <attr format="dimension" name="thumbTextPadding"/>
        
        <attr format="reference" name="switchTextAppearance"/>
        
        <attr format="dimension" name="switchMinWidth"/>
        
        <attr format="dimension" name="switchPadding"/>
        
        <attr format="boolean" name="splitTrack"/>
        
        <attr format="boolean" name="showText"/>
    </declare-styleable>
    <declare-styleable name="TabItem"><attr name="android:text"/><attr name="android:icon"/><attr name="android:layout"/></declare-styleable>
    <declare-styleable name="TabLayout"><attr format="color" name="tabIndicatorColor"/><attr format="dimension" name="tabIndicatorHeight"/><attr format="dimension" name="tabContentStart"/><attr format="reference" name="tabBackground"/><attr format="reference" name="tabIndicator"/><attr name="tabIndicatorGravity">
      
      <enum name="bottom" value="0"/>
      
      <enum name="center" value="1"/>
      
      <enum name="top" value="2"/>
      
      <enum name="stretch" value="3"/>
    </attr><attr format="integer" name="tabIndicatorAnimationDuration"/><attr format="boolean" name="tabIndicatorFullWidth"/><attr name="tabMode">
      <enum name="scrollable" value="0"/>
      <enum name="fixed" value="1"/>
    </attr><attr name="tabGravity">
      <enum name="fill" value="0"/>
      <enum name="center" value="1"/>
    </attr><attr format="boolean" name="tabInlineLabel"/><attr format="dimension" name="tabMinWidth"/><attr format="dimension" name="tabMaxWidth"/><attr format="reference" name="tabTextAppearance"/><attr format="color" name="tabTextColor"/><attr format="color" name="tabSelectedTextColor"/><attr format="dimension" name="tabPaddingStart"/><attr format="dimension" name="tabPaddingTop"/><attr format="dimension" name="tabPaddingEnd"/><attr format="dimension" name="tabPaddingBottom"/><attr format="dimension" name="tabPadding"/><attr format="color" name="tabIconTint"/><attr name="tabIconTintMode">
      <enum name="src_over" value="3"/>
      <enum name="src_in" value="5"/>
      <enum name="src_atop" value="9"/>
      <enum name="multiply" value="14"/>
      <enum name="screen" value="15"/>
      <enum name="add" value="16"/>
    </attr><attr format="color" name="tabRippleColor"/><attr format="boolean" name="tabUnboundedRipple"/></declare-styleable>
    <declare-styleable name="TextAppearance">
        <attr name="android:textSize"/>
        <attr name="android:textColor"/>
        <attr name="android:textColorHint"/>
        <attr name="android:textColorLink"/>
        <attr name="android:textStyle"/>
        <attr name="android:typeface"/>
        <attr name="android:fontFamily"/>
        <attr name="fontFamily"/>
        <attr name="textAllCaps"/>
        <attr name="android:shadowColor"/>
        <attr name="android:shadowDy"/>
        <attr name="android:shadowDx"/>
        <attr name="android:shadowRadius"/>
    </declare-styleable>
    <declare-styleable name="TextInputLayout"><attr name="android:textColorHint"/><attr name="android:hint"/><attr format="boolean" name="hintEnabled"/><attr format="boolean" name="hintAnimationEnabled"/><attr format="reference" name="hintTextAppearance"/><attr format="string" name="helperText"/><attr format="boolean" name="helperTextEnabled"/><attr format="reference" name="helperTextTextAppearance"/><attr format="boolean" name="errorEnabled"/><attr format="reference" name="errorTextAppearance"/><attr format="boolean" name="counterEnabled"/><attr format="integer" name="counterMaxLength"/><attr format="reference" name="counterTextAppearance"/><attr format="reference" name="counterOverflowTextAppearance"/><attr format="boolean" name="passwordToggleEnabled"/><attr format="reference" name="passwordToggleDrawable"/><attr format="string" name="passwordToggleContentDescription"/><attr format="color" name="passwordToggleTint"/><attr name="passwordToggleTintMode">
      
      <enum name="src_over" value="3"/>
      
      <enum name="src_in" value="5"/>
      
      <enum name="src_atop" value="9"/>
      
      <enum name="multiply" value="14"/>
      
      <enum name="screen" value="15"/>
    </attr><attr name="boxBackgroundMode">
      
      <enum name="none" value="0"/>
      
      <enum name="filled" value="1"/>
      
      <enum name="outline" value="2"/>
    </attr><attr format="dimension" name="boxCollapsedPaddingTop"/><attr format="dimension" name="boxCornerRadiusTopStart"/><attr format="dimension" name="boxCornerRadiusTopEnd"/><attr format="dimension" name="boxCornerRadiusBottomStart"/><attr format="dimension" name="boxCornerRadiusBottomEnd"/><attr format="color" name="boxStrokeColor"/><attr format="color" name="boxBackgroundColor"/><attr format="dimension" name="boxStrokeWidth"/></declare-styleable>
    <declare-styleable name="ThemeEnforcement"><attr format="boolean" name="enforceMaterialTheme"/><attr format="boolean" name="enforceTextAppearance"/><attr name="android:textAppearance"/></declare-styleable>
    <declare-styleable name="Toolbar">
        <attr format="reference" name="titleTextAppearance"/>
        <attr format="reference" name="subtitleTextAppearance"/>
        <attr name="title"/>
        <attr name="subtitle"/>
        <attr name="android:gravity"/>
        
        <attr format="dimension" name="titleMargin"/>
        
        <attr format="dimension" name="titleMarginStart"/>
        
        <attr format="dimension" name="titleMarginEnd"/>
        
        <attr format="dimension" name="titleMarginTop"/>
        
        <attr format="dimension" name="titleMarginBottom"/>
        
        <attr format="dimension" name="titleMargins"/>
        <attr name="contentInsetStart"/>
        <attr name="contentInsetEnd"/>
        <attr name="contentInsetLeft"/>
        <attr name="contentInsetRight"/>
        <attr name="contentInsetStartWithNavigation"/>
        <attr name="contentInsetEndWithActions"/>
        <attr format="dimension" name="maxButtonHeight"/>
        <attr name="buttonGravity">
            <!-- Push object to the top of its container, not changing its size. -->
            <flag name="top" value="0x30"/>
            <!-- Push object to the bottom of its container, not changing its size. -->
            <flag name="bottom" value="0x50"/>
        </attr>
        
        <attr format="reference" name="collapseIcon"/>
        
        <attr format="string" name="collapseContentDescription"/>
        
        <attr name="popupTheme"/>
        
        <attr format="reference" name="navigationIcon"/>
        
        <attr format="string" name="navigationContentDescription"/>
        
        <attr name="logo"/>
        
        <attr format="string" name="logoDescription"/>
        
        <attr format="color" name="titleTextColor"/>
        
        <attr format="color" name="subtitleTextColor"/>
        <attr name="android:minHeight"/>
    </declare-styleable>
    <declare-styleable name="View">
        
        <attr format="dimension" name="paddingStart"/>
        
        <attr format="dimension" name="paddingEnd"/>
        
        <attr name="android:focusable"/>
        
        <attr format="reference" name="theme"/>
        
        <attr name="android:theme"/>
    </declare-styleable>
    <declare-styleable name="ViewBackgroundHelper">
        <attr name="android:background"/>
        
        <attr format="color" name="backgroundTint"/>

        
        <attr name="backgroundTintMode">
            <!-- The tint is drawn on top of the drawable.
                 [Sa + (1 - Sa)*Da, Rc = Sc + (1 - Sa)*Dc] -->
            <enum name="src_over" value="3"/>
            <!-- The tint is masked by the alpha channel of the drawable. The drawable’s
                 color channels are thrown out. [Sa * Da, Sc * Da] -->
            <enum name="src_in" value="5"/>
            <!-- The tint is drawn above the drawable, but with the drawable’s alpha
                 channel masking the result. [Da, Sc * Da + (1 - Sa) * Dc] -->
            <enum name="src_atop" value="9"/>
            <!-- Multiplies the color and alpha channels of the drawable with those of
                 the tint. [Sa * Da, Sc * Dc] -->
            <enum name="multiply" value="14"/>
            <!-- [Sa + Da - Sa * Da, Sc + Dc - Sc * Dc] -->
            <enum name="screen" value="15"/>
            <!-- Combines the tint and icon color and alpha channels, clamping the
                 result to valid color values. Saturate(S + D) -->
            <enum name="add" value="16"/>
        </attr>
    </declare-styleable>
    <declare-styleable name="ViewStubCompat">
        
        <attr name="android:layout"/>
        
        <attr name="android:inflatedId"/>
        <attr name="android:id"/>
    </declare-styleable>
    <declare-styleable name="ksad_ComplianceTextView"><attr format="dimension" name="ksad_width_in_landscape"/><attr format="color|reference" name="ksad_privacy_color"/><attr format="boolean" name="ksad_show_clickable_underline"/></declare-styleable>
    <declare-styleable name="ksad_DividerView">
    <attr format="color" name="ksad_color"/>
    <attr format="dimension" name="ksad_dashLength"/>
    <attr format="dimension" name="ksad_dashGap"/>
    <attr format="dimension" name="ksad_dashThickness"/>
    <attr format="enum" name="ksad_orientation">
      <enum name="horizontal" value="0"/>
      <enum name="vertical" value="1"/>
    </attr>
  </declare-styleable>
    <declare-styleable name="ksad_DownloadProgressView">
    
    <attr format="color" name="ksad_downloadTextColor"/>
    
    <attr format="color" name="ksad_downloadLeftTextColor"/>
    
    <attr format="color" name="ksad_downloadRightTextColor"/>
    
    <attr format="reference" name="ksad_backgroundDrawable"/>
    
    <attr format="reference" name="ksad_progressDrawable"/>
    
    <attr format="dimension" name="ksad_downloadTextSize"/>
    
    <attr format="string" name="ksad_downloadingFormat"/>

  </declare-styleable>
    <declare-styleable name="ksad_JinniuCouponLayout">
    <attr format="dimension" name="ksad_outerRadius"/>
    <attr format="dimension" name="ksad_verticalRadius"/>
  </declare-styleable>
    <declare-styleable name="ksad_KSCornerImageView">
    <attr format="boolean" name="ksad_leftTopCorner"/>
    <attr format="boolean" name="ksad_topRightCorner"/>
    <attr format="boolean" name="ksad_rightBottomCorner"/>
    <attr format="boolean" name="ksad_bottomLeftCorner"/>

  </declare-styleable>
    <declare-styleable name="ksad_KSCouponLabelTextView">
    <attr format="dimension" name="ksad_labelRadius"/>
    <attr format="dimension" name="ksad_sideRadius"/>
    <attr format="color" name="ksad_strokeColor"/>
    <attr format="dimension" name="ksad_strokeSize"/>

  </declare-styleable>
    <declare-styleable name="ksad_KSLayout">
    <attr format="dimension" name="ksad_radius"/>
    <attr format="boolean" name="ksad_clipBackground"/>
    <attr format="float" name="ksad_ratio"/>
  </declare-styleable>
    <declare-styleable name="ksad_KSRatingBar" ns2:ignore="ResourceName">
    <attr format="dimension" name="ksad_starImageWidth"/>
    <attr format="dimension" name="ksad_starImageHeight"/>
    <attr format="dimension" name="ksad_starImagePadding"/>
    <attr format="integer" name="ksad_totalStarCount"/>
    <attr format="integer" name="ksad_starCount"/>
    <attr format="reference" name="ksad_starEmpty"/>
    <attr format="reference" name="ksad_starFill"/>
    <attr format="reference" name="ksad_starHalf"/>
    <attr format="boolean" name="ksad_clickable"/>
    <attr format="boolean" name="ksad_halfstart"/>
  </declare-styleable>
    <declare-styleable name="ksad_KsRadiusStrokeTextView">
    <attr format="color" name="ksad_textStrokeColor"/>
    <attr format="dimension" name="ksad_textRadius"/>
    <attr format="dimension" name="ksad_textLeftTopRadius"/>
    <attr format="dimension" name="ksad_textLeftBottomRadius"/>
    <attr format="dimension" name="ksad_textRightTopRadius"/>
    <attr format="dimension" name="ksad_textRightBottomRadius"/>
    <attr format="dimension" name="ksad_textStrokeWidth"/>
    <attr format="reference" name="ksad_textDrawable"/>
    <attr format="color" name="ksad_textNormalTextColor"/>
    <attr format="color" name="ksad_textSelectedTextColor"/>
    <attr format="boolean" name="ksad_textNoLeftStroke"/>
    <attr format="boolean" name="ksad_textNoRightStroke"/>
    <attr format="boolean" name="ksad_textNoTopStroke"/>
    <attr format="boolean" name="ksad_textNoBottomStroke"/>
    <attr format="color" name="ksad_textPressedSolidColor"/>
    <attr format="color" name="ksad_textNormalSolidColor"/>
    <attr format="boolean" name="ksad_textIsSelected"/>
  </declare-styleable>
    <declare-styleable name="ksad_KsShadowImageView">
    <attr format="dimension" name="ksad_shadowSize"/>
    <attr format="color" name="ksad_shadowColor"/>
    <attr format="boolean" name="ksad_enableLeftShadow"/>
    <attr format="boolean" name="ksad_enableRightShadow"/>
    <attr format="boolean" name="ksad_enableTopShadow"/>
    <attr format="boolean" name="ksad_enableBottomShadow"/>
  </declare-styleable>
    <declare-styleable name="ksad_KsShakeView">
    <attr format="color" name="ksad_outerStrokeColor"/>
    <attr format="dimension" name="ksad_outerStrokeWidth"/>
    <attr format="color" name="ksad_solidColor"/>

    
    <attr format="integer" name="ksad_shakeViewStyle"/>
    
    <attr format="color" name="ksad_innerCircleStrokeColor"/>
    
    <attr format="dimension" name="ksad_innerCircleStrokeWidth"/>
    
    <attr format="dimension" name="ksad_innerCirclePadding"/>
    
    <attr format="reference" name="ksad_shakeIcon"/>

  </declare-styleable>
    <declare-styleable name="ksad_KsVerticalMarqueeTextView">
    <attr format="boolean" name="ksad_autoStartMarquee"/>
    <attr format="reference|integer" name="ksad_marqueeSpeed"/>
    <attr format="reference|string" name="ksad_text"/>
    <attr format="reference|dimension" name="ksad_textSize"/>
    <attr format="reference|color" name="ksad_textColor"/>
    <attr format="reference" name="ksad_textAppearance"/>
    <attr name="ksad_textStyle">
      <enum name="normal" value="0"/>
      <enum name="bold" value="1"/>
      <enum name="italic" value="2"/>
    </attr>
    <attr name="ksad_typeface">
      <enum name="normal" value="0"/>
      <enum name="sans" value="1"/>
      <enum name="serif" value="2"/>
      <enum name="monospace" value="3"/>
    </attr>
  </declare-styleable>
    <declare-styleable name="ksad_SeekBar">
    <attr format="color" name="ksad_SeekBarBackground"/>
    <attr format="color" name="ksad_SeekBarProgress"/>
    <attr format="color" name="ksad_SeekBarSecondProgress"/>
    <attr format="boolean" name="ksad_SeekBarDisplayProgressText"/>
    <attr format="boolean" name="ksad_SeekBarLimitProgressText100"/>
    <attr format="dimension" name="ksad_SeekBarProgressTextSize"/>
    <attr format="dimension" name="ksad_SeekBarProgressTextMargin"/>
    <attr format="dimension" name="ksad_SeekBarWidth"/>
    <attr format="dimension" name="ksad_SeekBarHeight"/>
    <attr format="dimension" name="ksad_SeekBarRadius"/>
    <attr format="dimension" name="ksad_SeekBarPaddingLeft"/>
    <attr format="dimension" name="ksad_SeekBarPaddingRight"/>
    <attr format="dimension" name="ksad_SeekBarPaddingTop"/>
    <attr format="dimension" name="ksad_SeekBarPaddingBottom"/>
    <attr format="reference" name="ksad_SeekBarThumb"/>
    <attr format="reference" name="ksad_SeekBarDefaultIndicator"/>
    <attr format="reference" name="ksad_SeekBarDefaultIndicatorPass"/>
    <attr format="boolean" name="ksad_SeekBarShowProgressText"/>
    <attr format="color" name="ksad_SeekBarProgressTextColor"/>
  </declare-styleable>
    <declare-styleable name="ksad_SlideTipsView">
    <attr format="boolean" name="ksad_is_left_slide"/>
  </declare-styleable>
    <declare-styleable name="ksad_ViewPagerIndicator">
    <attr format="dimension" name="ksad_dot_distance"/>
    <attr format="dimension" name="ksad_dot_height"/>
    <attr format="dimension" name="ksad_dot_selected_width"/>
    <attr format="dimension" name="ksad_dot_unselected_width"/>
    <attr format="color" name="ksad_height_color"/>
    <attr format="color" name="ksad_default_color"/>
  </declare-styleable>
</resources>