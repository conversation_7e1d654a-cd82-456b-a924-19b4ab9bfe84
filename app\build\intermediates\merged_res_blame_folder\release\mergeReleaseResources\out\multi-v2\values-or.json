{"logs": [{"outputFile": "com.ainative.mountainsurvival.app-mergeReleaseResources-22:/values-or/values-or.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a3ae461f31008f99a87e29f3c99a3c83\\transformed\\appcompat-v7-28.0.0\\res\\values-or\\values-or.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,224,334,441,527,631,751,828,905,996,1089,1186,1281,1382,1475,1570,1666,1757,1847,1929,2039,2144,2250,2361,2464,2582,2745,2847", "endColumns": "118,109,106,85,103,119,76,76,90,92,96,94,100,92,94,95,90,89,81,109,104,105,110,102,117,162,101,88", "endOffsets": "219,329,436,522,626,746,823,900,991,1084,1181,1276,1377,1470,1565,1661,1752,1842,1924,2034,2139,2245,2356,2459,2577,2740,2842,2931"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\daefd50eed0506e326ae247974906cd2\\transformed\\support-compat-28.0.0\\res\\values-or\\values-or.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2936", "endColumns": "100", "endOffsets": "3032"}}]}]}