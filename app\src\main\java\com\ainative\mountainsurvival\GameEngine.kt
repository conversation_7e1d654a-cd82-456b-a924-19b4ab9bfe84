package com.ainative.mountainsurvival

import android.content.Context
import android.util.Log
import com.google.gson.Gson
import com.google.gson.JsonSyntaxException
import java.io.IOException

/**
 * 游戏引擎单例
 * 专门负责读取和解析 events.json 文件的模块
 */
object GameEngine {

    private const val TAG = "GameEngine"
    private const val EVENTS_FILE_NAME = "events.json"
    private const val EVENTS_FILE_NAME_EN = "events_en.json"
    private const val EVENTS_FILE_NAME_TW = "events_zh_tw.json"

    private val gson = Gson()

    /**
     * 加载游戏事件
     * 根据当前语言读取对应的事件文件并解析成事件Map
     *
     * @param context Android上下文，用于访问assets文件
     * @return 以事件ID为键的GameEvent Map，加载失败时返回空Map
     */
    fun loadEvents(context: Context): Map<String, GameEvent> {
        return try {
            Log.d(TAG, "开始加载游戏事件...")

            // 1. 根据当前语言选择事件文件
            val currentLanguage = LanguageManager.getCurrentLanguage(context)
            val eventsFileName = when (currentLanguage) {
                LanguageManager.LANGUAGE_ENGLISH -> EVENTS_FILE_NAME_EN
                LanguageManager.LANGUAGE_TRADITIONAL_CHINESE -> EVENTS_FILE_NAME_TW
                else -> EVENTS_FILE_NAME // 简体中文或其他语言使用默认文件
            }

            Log.d(TAG, "当前语言: $currentLanguage, 使用事件文件: $eventsFileName")

            // 2. 读取对应的事件文件内容
            val jsonString = readJsonFromAssets(context, eventsFileName)

            if (jsonString.isEmpty()) {
                Log.e(TAG, "JSON文件内容为空")
                return emptyMap()
            }

            Log.d(TAG, "JSON文件读取成功，长度: ${jsonString.length}")

            // 3. 使用Gson解析JSON字符串
            val eventContainer = parseJsonToEventContainer(jsonString)

            if (eventContainer?.events.isNullOrEmpty()) {
                Log.e(TAG, "解析后的事件列表为空")
                return emptyMap()
            }

            // 4. 将事件列表转换为以ID为键的Map
            val eventMap = convertEventsToMap(eventContainer.events)

            Log.d(TAG, "事件加载完成，共加载 ${eventMap.size} 个事件")
            logEventStatistics(eventMap)

            eventMap

        } catch (e: Exception) {
            Log.e(TAG, "加载游戏事件时发生错误", e)
            emptyMap()
        }
    }

    /**
     * 从assets文件夹读取JSON文件
     *
     * @param context Android上下文
     * @param fileName 文件名
     * @return JSON字符串内容
     * @throws IOException 文件读取异常
     */
    private fun readJsonFromAssets(context: Context, fileName: String): String {
        return try {
            context.assets.open(fileName).use { inputStream ->
                val size = inputStream.available()
                val buffer = ByteArray(size)
                inputStream.read(buffer)
                String(buffer, Charsets.UTF_8)
            }
        } catch (e: IOException) {
            Log.w(TAG, "读取assets文件失败: $fileName，尝试使用默认文件", e)
            // 如果指定的语言文件不存在，回退到默认的简体中文文件
            if (fileName != EVENTS_FILE_NAME) {
                Log.d(TAG, "回退到默认事件文件: $EVENTS_FILE_NAME")
                return readJsonFromAssets(context, EVENTS_FILE_NAME)
            } else {
                Log.e(TAG, "默认事件文件也无法读取: $fileName", e)
                throw e
            }
        }
    }

    /**
     * 解析JSON字符串为EventContainer对象
     *
     * @param jsonString JSON字符串
     * @return EventContainer对象
     * @throws JsonSyntaxException JSON解析异常
     */
    private fun parseJsonToEventContainer(jsonString: String): EventContainer? {
        return try {
            gson.fromJson(jsonString, EventContainer::class.java)
        } catch (e: JsonSyntaxException) {
            Log.e(TAG, "JSON解析失败", e)
            throw e
        }
    }

    /**
     * 将事件列表转换为以ID为键的Map
     *
     * @param events 事件列表
     * @return 以事件ID为键的Map
     */
    private fun convertEventsToMap(events: List<GameEvent>): Map<String, GameEvent> {
        val eventMap = mutableMapOf<String, GameEvent>()

        events.forEach { event ->
            if (event.id.isNotBlank()) {
                if (eventMap.containsKey(event.id)) {
                    Log.w(TAG, "发现重复的事件ID: ${event.id}")
                }
                eventMap[event.id] = event
            } else {
                Log.w(TAG, "发现空的事件ID，跳过该事件")
            }
        }

        return eventMap.toMap() // 返回不可变的Map
    }

    /**
     * 记录事件统计信息
     *
     * @param eventMap 事件Map
     */
    private fun logEventStatistics(eventMap: Map<String, GameEvent>) {
        val totalEvents = eventMap.size
        val totalChoices = eventMap.values.sumOf { it.choices?.size ?: 0 }
        // TODO: 随机事件统计暂时禁用
        // val eventsWithRandomChoices = eventMap.values.count { !it.randomChoices.isNullOrEmpty() }
        val eventsWithRandomChoices = 0
        val eventsWithDirectEffects = eventMap.values.count { !it.effects.isNullOrEmpty() }

        Log.d(TAG, "=== 事件统计信息 ===")
        Log.d(TAG, "总事件数: $totalEvents")
        Log.d(TAG, "总选择数: $totalChoices")
        Log.d(TAG, "平均每事件选择数: ${if (totalEvents > 0) totalChoices.toDouble() / totalEvents else 0.0}")
        Log.d(TAG, "包含随机选择的事件数: $eventsWithRandomChoices")
        Log.d(TAG, "包含直接效果的事件数: $eventsWithDirectEffects")
        Log.d(TAG, "==================")
    }

    /**
     * 验证事件Map的完整性
     * 检查事件引用是否正确
     *
     * @param eventMap 事件Map
     * @return 验证问题列表，空列表表示没有问题
     */
    fun validateEventMap(eventMap: Map<String, GameEvent>): List<String> {
        val issues = mutableListOf<String>()

        eventMap.values.forEach { event ->
            // 检查基本字段
            if (event.text.isBlank()) {
                issues.add("事件 ${event.id} 的文本为空")
            }

            // TODO: 随机事件检查暂时禁用
            if (event.choices.isEmpty() /* && event.randomChoices.isNullOrEmpty() */) {
                issues.add("事件 ${event.id} 没有任何选择")
            }

            // 检查选择的nextEventId引用
            event.choices.forEach { choice ->
                choice.nextEventId?.let { nextId ->
                    if (!eventMap.containsKey(nextId)) {
                        issues.add("事件 ${event.id} 的选择 '${choice.text}' 引用了不存在的事件: $nextId")
                    }
                }

                // 检查选择文本
                if (choice.text.isBlank()) {
                    issues.add("事件 ${event.id} 包含空的选择文本")
                }
            }

            // TODO: 随机事件检查暂时禁用
            /*
            // 检查随机选择的nextEventId引用
            event.randomChoices?.forEach { randomChoice ->
                if (!eventMap.containsKey(randomChoice.nextEventId)) {
                    issues.add("事件 ${event.id} 的随机选择引用了不存在的事件: ${randomChoice.nextEventId}")
                }
            }
            */
        }

        return issues
    }

    /**
     * 获取事件Map的统计信息
     *
     * @param eventMap 事件Map
     * @return 包含统计信息的Map
     */
    fun getEventMapStatistics(eventMap: Map<String, GameEvent>): Map<String, Any> {
        val totalEvents = eventMap.size
        val totalChoices = eventMap.values.sumOf { it.choices.size }
        // TODO: 随机事件统计暂时禁用
        // val eventsWithRandomChoices = eventMap.values.count { !it.randomChoices.isNullOrEmpty() }
        val eventsWithRandomChoices = 0
        val eventsWithDirectEffects = eventMap.values.count { !it.effects.isNullOrEmpty() }
        val eventsWithRequirements = eventMap.values.sumOf { event ->
            event.choices.count { !it.requirements.isNullOrEmpty() }
        }

        return mapOf(
            "totalEvents" to totalEvents,
            "totalChoices" to totalChoices,
            "averageChoicesPerEvent" to if (totalEvents > 0) totalChoices.toDouble() / totalEvents else 0.0,
            "eventsWithRandomChoices" to eventsWithRandomChoices,
            "eventsWithDirectEffects" to eventsWithDirectEffects,
            "choicesWithRequirements" to eventsWithRequirements
        )
    }

    /**
     * 检查特定事件是否存在
     *
     * @param eventMap 事件Map
     * @param eventId 事件ID
     * @return 是否存在
     */
    fun hasEvent(eventMap: Map<String, GameEvent>, eventId: String): Boolean {
        return eventMap.containsKey(eventId)
    }

    /**
     * 安全获取事件
     *
     * @param eventMap 事件Map
     * @param eventId 事件ID
     * @return 事件对象，不存在时返回null
     */
    fun getEvent(eventMap: Map<String, GameEvent>, eventId: String): GameEvent? {
        return eventMap[eventId]
    }
}
