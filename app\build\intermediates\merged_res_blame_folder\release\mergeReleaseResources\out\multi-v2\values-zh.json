{"logs": [{"outputFile": "com.ainative.mountainsurvival.app-mergeReleaseResources-22:/values-zh/values-zh.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\077c69690200a4c0c0ddf0630ba2f6e7\\transformed\\open_ad_sdk_6.8.4.0\\res\\values-zh\\values-zh.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,113,155,197,247,295,361,425,494,554,617,680,740,816,878", "endColumns": "57,41,41,49,47,65,63,68,59,62,62,59,75,61,60", "endOffsets": "108,150,192,242,290,356,420,489,549,612,675,735,811,873,934"}, "to": {"startLines": "13,14,15,16,17,18,19,20,21,22,23,24,25,26,27", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "615,673,715,757,807,855,921,985,1054,1114,1177,1240,1300,1376,1438", "endColumns": "57,41,41,49,47,65,63,68,59,62,62,59,75,61,60", "endOffsets": "668,710,752,802,850,916,980,1049,1109,1172,1235,1295,1371,1433,1494"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b66139be6585a43ae23903a8750be796\\transformed\\wind-sdk-4.23.0\\res\\values-zh\\values-zh.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12", "startColumns": "4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,93,133,174,227,299,348,405,456,511,563", "endColumns": "37,39,40,52,71,48,56,50,54,51,51", "endOffsets": "88,128,169,222,294,343,400,451,506,558,610"}}]}]}