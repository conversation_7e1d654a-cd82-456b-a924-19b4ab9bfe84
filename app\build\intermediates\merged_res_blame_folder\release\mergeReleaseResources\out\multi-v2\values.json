{"logs": [{"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-mergeReleaseResources-22:\\values\\values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\daefd50eed0506e326ae247974906cd2\\transformed\\support-compat-28.0.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "286,287,485,486,487,488,489,490,491,835,836,837,838,839,840,841,842,843,844,845,846,847,848,849,867,868,878,879,892,893,894,895,896,900,932,1138,3154,3155,3160,3163,3168,3563,3564", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15732,15801,28130,28200,28268,28340,28410,28471,28545,51217,51278,51339,51401,51465,51527,51588,51656,51756,51816,51882,51955,52024,52081,52133,53174,53246,53774,53809,54421,54471,54532,54589,54623,54801,56664,71271,202568,202685,202952,203245,203512,229511,229583", "endLines": "286,287,485,486,487,488,489,490,491,835,836,837,838,839,840,841,842,843,844,845,846,847,848,849,867,868,878,879,892,893,894,895,896,900,932,1138,3154,3158,3160,3166,3168,3563,3564", "endColumns": "68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,34,34,49,60,56,33,34,34,69,70,116,12,109,12,128,71,66", "endOffsets": "15796,15859,28195,28263,28335,28405,28466,28540,28613,51273,51334,51396,51460,51522,51583,51651,51751,51811,51877,51950,52019,52076,52128,52190,53241,53317,53804,53839,54466,54527,54584,54618,54653,54831,56729,71337,202680,202881,203057,203441,203636,229578,229645"}}, {"source": "E:\\Ai\\AiCode\\game\\MountainSurvival\\code\\app\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "84,83,80,79,97,86,85,-1,-1,47,67,46,68,100,43,94,34,93,56,58,66,33,32,-1,41,42,55,54,31,-1,69,50,48,51,49,-1,-1,-1,-1,-1,-1,99,70,-1,-1,73,62,65,63,64,35,-1,-1,-1,-1,-1,-1,-1,75,98,87,82,81,88,76,92,91,90,89,57,59,74,40,-1,38,-1,-1,39", "startColumns": "4,4,4,4,4,4,4,-1,-1,4,4,4,4,4,4,4,4,4,4,4,4,4,4,-1,4,4,4,4,4,-1,4,4,4,4,4,-1,-1,-1,-1,-1,-1,4,4,-1,-1,4,4,4,4,4,4,-1,-1,-1,-1,-1,-1,-1,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,-1,4,-1,-1,4", "startOffsets": "4390,4334,4144,4094,5109,4521,4465,-1,-1,2478,3559,2425,3622,5251,2346,5051,1986,5004,2953,3040,3502,1940,1884,-1,2260,2305,2842,2791,1816,-1,3684,2652,2530,2712,2589,-1,-1,-1,-1,-1,-1,5203,3744,-1,-1,3821,3205,3452,3256,3354,2033,-1,-1,-1,-1,-1,-1,-1,3948,5152,4598,4264,4205,4682,3995,4928,4873,4815,4768,2995,3125,3896,2216,-1,2115,-1,-1,2173", "endColumns": "74,55,60,49,42,76,55,-1,-1,51,62,52,61,48,59,38,46,46,41,84,56,45,55,-1,44,40,110,50,67,-1,59,59,58,59,62,-1,-1,-1,-1,-1,-1,47,58,-1,-1,74,50,49,97,97,62,-1,-1,-1,-1,-1,-1,-1,46,50,83,69,58,85,79,75,54,57,46,44,60,51,43,-1,57,-1,-1,42", "endOffsets": "4460,4385,4200,4139,5147,4593,4516,-1,-1,2525,3617,2473,3679,5295,2401,5085,2028,5046,2990,3120,3554,1981,1935,-1,2300,2341,2948,2837,1879,-1,3739,2707,2584,2767,2647,-1,-1,-1,-1,-1,-1,5246,3798,-1,-1,3891,3251,3497,3349,3447,2091,-1,-1,-1,-1,-1,-1,-1,3990,5198,4677,4329,4259,4763,4070,4999,4923,4868,4810,3035,3181,3943,2255,-1,2168,-1,-1,2211"}, "to": {"startLines": "972,973,974,975,976,977,978,979,980,998,999,1000,1001,1002,1005,1006,1007,1008,1009,1010,1011,1012,1013,1014,1017,1018,1019,1020,1021,1022,1024,1025,1026,1027,1028,1072,1073,1074,1075,1076,1077,1078,1081,1083,1084,1085,1086,1087,1088,1089,1090,1099,1100,1101,1102,1103,1104,1105,1106,1107,1108,1109,1110,1111,1112,1113,1114,1115,1116,1118,1119,1121,1136,1137,1139,1140,1312,1313", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "59492,59567,59623,59684,59734,59777,59854,59910,59952,61331,61383,61446,61499,61561,61801,61861,61900,61947,61994,62036,62121,62178,62224,62280,62627,62672,62713,62824,62875,62943,63205,63265,63325,63384,63444,66254,66294,66341,66394,66452,66513,66575,66735,66875,66918,66960,67035,67086,67136,67234,67332,68354,68406,68917,68973,69037,69094,69152,69262,69309,69360,69444,69514,69573,69659,69739,69815,69870,69928,70019,70064,70178,71183,71227,71342,71400,83556,83601", "endColumns": "74,55,60,49,42,76,55,41,56,51,62,52,61,48,59,38,46,46,41,84,56,45,55,42,44,40,110,50,67,113,59,59,58,59,62,39,46,52,57,60,61,47,58,42,41,74,50,49,97,97,62,51,510,55,63,56,57,109,46,50,83,69,58,85,79,75,54,57,46,44,60,51,43,43,57,60,44,42", "endOffsets": "59562,59618,59679,59729,59772,59849,59905,59947,60004,61378,61441,61494,61556,61605,61856,61895,61942,61989,62031,62116,62173,62219,62275,62318,62667,62708,62819,62870,62938,63052,63260,63320,63379,63439,63502,66289,66336,66389,66447,66508,66570,66618,66789,66913,66955,67030,67081,67131,67229,67327,67390,68401,68912,68968,69032,69089,69147,69257,69304,69355,69439,69509,69568,69654,69734,69810,69865,69923,69970,70059,70120,70225,71222,71266,71395,71456,83596,83639"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\62922e5f87afeea95a66affda0037661\\transformed\\beizi_fusion_sdk_5.2.1.6\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "13,14,15,147,154,158,185,186,404,982,983,984,985,986,987,988,989,990,991,992,993,994,995,996,2743,2753,2763,2766,2769,2772,2775,3841,3850", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "627,675,724,6953,7356,7558,9219,9280,22704,60154,60258,60304,60348,60393,60437,60516,60636,60742,60792,60863,60908,60970,61034,61141,175057,175678,176300,176475,176641,176798,176896,246232,246751", "endLines": "13,14,15,147,154,158,185,186,404,982,983,984,985,986,987,988,989,990,991,992,993,994,995,996,2752,2762,2765,2768,2771,2774,2782,3849,3858", "endColumns": "47,48,49,67,58,40,60,62,49,103,45,43,44,43,78,119,105,49,70,44,61,63,106,68,12,12,12,12,12,12,12,12,12", "endOffsets": "670,719,769,7016,7410,7594,9275,9338,22749,60253,60299,60343,60388,60432,60511,60631,60737,60787,60858,60903,60965,61029,61136,61205,175673,176295,176470,176636,176793,176891,177374,246746,247262"}}, {"source": "E:\\Ai\\AiCode\\game\\MountainSurvival\\code\\app\\src\\main\\res\\values\\colors.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "184,189,192,223,224,300,301,310,311,312,319,324,325,326,327,405,406", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "9171,9510,9713,11872,11921,16536,16583,17223,17270,17317,17775,18109,18154,18199,18246,22754,22801", "endColumns": "47,50,41,48,44,46,51,46,46,46,47,44,44,46,48,46,41", "endOffsets": "9214,9556,9750,11916,11961,16578,16630,17265,17312,17359,17818,18149,18194,18241,18290,22796,22838"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\0457feaf864ab549dda35ffc4a13a2e6\\transformed\\openset_sdk_6.5.2.6\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2,3,289,290,291,292,293,294,295,296,297,298,299,891,1029,1030,1031,1091,1092,1093,1117,1133,1134,1135,1327,2856,2865,2868,2873,2877,3902", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,213,15953,16004,16056,16112,16162,16214,16265,16321,16379,16432,16485,54384,63507,63544,63592,67395,67447,67501,69975,70834,70877,71044,84587,181102,181454,181553,181755,182008,249332", "endLines": "2,3,289,290,291,292,293,294,295,296,297,298,299,891,1029,1030,1031,1091,1092,1093,1117,1133,1134,1135,1329,2864,2867,2872,2876,2884,3907", "endColumns": "62,61,50,51,55,49,51,50,55,57,52,52,50,36,36,47,52,51,53,50,43,42,166,138,12,12,12,12,12,12,12", "endOffsets": "208,270,15999,16051,16107,16157,16209,16260,16316,16374,16427,16480,16531,54416,63539,63587,63640,67442,67496,67547,70014,70872,71039,71178,84678,181449,181548,181750,182003,182299,249606"}}, {"source": "E:\\Ai\\AiCode\\game\\MountainSurvival\\code\\app\\src\\main\\res\\values\\ic_launcher_background.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "229", "startColumns": "4", "startOffsets": "12246", "endColumns": "56", "endOffsets": "12298"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2144dbf5c98623e2472a5ef01095ffd5\\transformed\\GDTSDK.unionNormal.4.640.1510\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1"}, "to": {"startLines": "926,2802,2805,2808", "startColumns": "4,4,4,4", "startOffsets": "56306,178356,178487,178609", "endLines": "926,2804,2807,2810", "endColumns": "55,12,12,12", "endOffsets": "56357,178482,178604,178741"}}, {"source": "E:\\Ai\\AiCode\\game\\MountainSurvival\\code\\app\\src\\main\\res\\values\\themes.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "3421", "startColumns": "4", "startOffsets": "218745", "endLines": "3433", "endColumns": "12", "endOffsets": "219286"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\cb0f3cc3125274cec1b491c0584a407c\\transformed\\kssdk-ad-4.4.20.1-publishRelease-aa0a55f514\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,551,552,553,554,555,556,557,558,559,560,561,562,563,564,565,566,567,568,569,570,571,572,573,574,575,576,577,578,579,580,581,582,583,584,585,586,587,588,589,590,591,592,593,594,595,596,597,598,599,600,601,602,603,604,605,606,607,608,609,610,611,612,613,614,615,616,617,618,619,620,621,622,623,624,625,626,627,628,629,630,631,632,633,634,635,636,637,638,639,640,641,642,643,644,645,646,647,648,649,650,651,652,653,654,655,656,657,658,659,660,661,662,663,664,665,666,667,668,669,670,671,672,673,674,675,676,677,678,679,680,681,682,683,684,685,686,687,688,689,690,691,692,693,694,695,696,697,698,699,700,701,702,703,704,705,706,707,708,709,710,711,712,713,714,715,716,717,718,719,720,721,722,723,724,725,726,727,728,729,730,731,732,733,734,735,736,737,738,739,740,741,742,743,744,745,746,747,748,749,750,751,752,753,754,755,756,757,758,759,760,761,762,763,764,765,766,767,768,769,770,771,772,773,774,775,776,777,778,779,780,781,782,783,784,785,786,876,877,1032,1033,1034,1035,1036,1037,1038,1039,1040,1041,1042,1043,1044,1045,1046,1047,1048,1049,1050,1051,1052,1053,1054,1055,1056,1057,1058,1059,1060,1061,1062,1063,1064,1065,1066,1067,1068,1069,1070,1071,3463,3872,3877,3882,3892", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "12303,12353,12403,12453,12503,12559,12614,12669,12724,12789,12855,12923,12978,13031,13080,13145,13225,13305,13371,13443,13510,13569,13632,13693,13754,13821,13889,13960,14025,14097,14162,14234,14300,14354,14407,14452,32592,32646,32708,32766,32839,32896,32960,33025,33084,33154,33224,33296,33373,33444,33509,33596,33673,33747,33817,33885,33959,34047,34125,34189,34248,34303,34367,34432,34497,34555,34606,34659,34731,34805,34876,34949,35013,35083,35151,35214,35275,35335,35402,35470,35530,35599,35659,35727,35788,35852,35915,35973,36039,36102,36165,36228,36306,36382,36459,36528,36591,36667,36730,36804,36870,36938,37003,37073,37145,37214,37279,37340,37412,37475,37539,37609,37673,37738,37809,37886,37951,38018,38082,38156,38233,38307,38379,38455,38524,38588,38645,38711,38778,38845,38906,38962,39039,39101,39163,39236,39306,39371,39438,39502,39569,39631,39692,39761,39825,39889,39955,40018,40086,40151,40213,40276,40344,40409,40476,40540,40602,40667,40732,40803,40873,40937,41013,41096,41170,41238,41300,41364,41425,41487,41550,41614,41680,41743,41811,41878,41947,42016,42076,42159,42227,42300,42371,42446,42539,42614,42683,42751,42817,42883,42951,43024,43091,43152,43235,43308,43378,43444,43508,43578,43662,43736,43796,43859,43921,43987,44048,44119,44185,44255,44314,44367,44432,44485,44541,44594,44654,44721,44781,44852,44927,44995,45068,45135,45207,45275,45348,45410,45476,45538,45604,45671,45742,45803,45869,45939,46014,46081,46153,46214,46280,46349,46423,46490,46562,46622,46687,46753,46823,46887,46956,47029,47107,47170,47238,47301,47369,47432,47500,47564,47633,47686,47738,47800,47869,47935,47996,48060,48121,48189,48254,48314,53667,53711,63645,63712,63770,63820,63874,63931,63999,64080,64150,64222,64289,64355,64407,64465,64513,64571,64648,64725,64786,64865,64933,64994,65075,65147,65223,65286,65348,65410,65488,65545,65605,65656,65702,65766,65843,65913,65990,66065,66139,66201,221569,247870,248091,248336,248833", "endLines": "230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,551,552,553,554,555,556,557,558,559,560,561,562,563,564,565,566,567,568,569,570,571,572,573,574,575,576,577,578,579,580,581,582,583,584,585,586,587,588,589,590,591,592,593,594,595,596,597,598,599,600,601,602,603,604,605,606,607,608,609,610,611,612,613,614,615,616,617,618,619,620,621,622,623,624,625,626,627,628,629,630,631,632,633,634,635,636,637,638,639,640,641,642,643,644,645,646,647,648,649,650,651,652,653,654,655,656,657,658,659,660,661,662,663,664,665,666,667,668,669,670,671,672,673,674,675,676,677,678,679,680,681,682,683,684,685,686,687,688,689,690,691,692,693,694,695,696,697,698,699,700,701,702,703,704,705,706,707,708,709,710,711,712,713,714,715,716,717,718,719,720,721,722,723,724,725,726,727,728,729,730,731,732,733,734,735,736,737,738,739,740,741,742,743,744,745,746,747,748,749,750,751,752,753,754,755,756,757,758,759,760,761,762,763,764,765,766,767,768,769,770,771,772,773,774,775,776,777,778,779,780,781,782,783,784,785,786,876,877,1032,1033,1034,1035,1036,1037,1038,1039,1040,1041,1042,1043,1044,1045,1046,1047,1048,1049,1050,1051,1052,1053,1054,1055,1056,1057,1058,1059,1060,1061,1062,1063,1064,1065,1066,1067,1068,1069,1070,1071,3468,3876,3881,3891,3901", "endColumns": "49,49,49,49,55,54,54,54,64,65,67,54,52,48,64,79,79,65,71,66,58,62,60,60,66,67,70,64,71,64,71,65,53,52,44,55,53,61,57,72,56,63,64,58,69,69,71,76,70,64,86,76,73,69,67,73,87,77,63,58,54,63,64,64,57,50,52,71,73,70,72,63,69,67,62,60,59,66,67,59,68,59,67,60,63,62,57,65,62,62,62,77,75,76,68,62,75,62,73,65,67,64,69,71,68,64,60,71,62,63,69,63,64,70,76,64,66,63,73,76,73,71,75,68,63,56,65,66,66,60,55,76,61,61,72,69,64,66,63,66,61,60,68,63,63,65,62,67,64,61,62,67,64,66,63,61,64,64,70,69,63,75,82,73,67,61,63,60,61,62,63,65,62,67,66,68,68,59,82,67,72,70,74,92,74,68,67,65,65,67,72,66,60,82,72,69,65,63,69,83,73,59,62,61,65,60,70,65,69,58,52,64,52,55,52,59,66,59,70,74,67,72,66,71,67,72,61,65,61,65,66,70,60,65,69,74,66,71,60,65,68,73,66,71,59,64,65,69,63,68,72,77,62,67,62,67,62,67,63,68,52,51,61,68,65,60,63,60,67,64,59,52,43,62,66,57,49,53,56,67,80,69,71,66,65,51,57,47,57,76,76,60,78,67,60,80,71,75,62,61,61,77,56,59,50,45,63,76,69,76,74,73,61,52,10,10,10,10,10", "endOffsets": "12348,12398,12448,12498,12554,12609,12664,12719,12784,12850,12918,12973,13026,13075,13140,13220,13300,13366,13438,13505,13564,13627,13688,13749,13816,13884,13955,14020,14092,14157,14229,14295,14349,14402,14447,14503,32641,32703,32761,32834,32891,32955,33020,33079,33149,33219,33291,33368,33439,33504,33591,33668,33742,33812,33880,33954,34042,34120,34184,34243,34298,34362,34427,34492,34550,34601,34654,34726,34800,34871,34944,35008,35078,35146,35209,35270,35330,35397,35465,35525,35594,35654,35722,35783,35847,35910,35968,36034,36097,36160,36223,36301,36377,36454,36523,36586,36662,36725,36799,36865,36933,36998,37068,37140,37209,37274,37335,37407,37470,37534,37604,37668,37733,37804,37881,37946,38013,38077,38151,38228,38302,38374,38450,38519,38583,38640,38706,38773,38840,38901,38957,39034,39096,39158,39231,39301,39366,39433,39497,39564,39626,39687,39756,39820,39884,39950,40013,40081,40146,40208,40271,40339,40404,40471,40535,40597,40662,40727,40798,40868,40932,41008,41091,41165,41233,41295,41359,41420,41482,41545,41609,41675,41738,41806,41873,41942,42011,42071,42154,42222,42295,42366,42441,42534,42609,42678,42746,42812,42878,42946,43019,43086,43147,43230,43303,43373,43439,43503,43573,43657,43731,43791,43854,43916,43982,44043,44114,44180,44250,44309,44362,44427,44480,44536,44589,44649,44716,44776,44847,44922,44990,45063,45130,45202,45270,45343,45405,45471,45533,45599,45666,45737,45798,45864,45934,46009,46076,46148,46209,46275,46344,46418,46485,46557,46617,46682,46748,46818,46882,46951,47024,47102,47165,47233,47296,47364,47427,47495,47559,47628,47681,47733,47795,47864,47930,47991,48055,48116,48184,48249,48309,48362,53706,53769,63707,63765,63815,63869,63926,63994,64075,64145,64217,64284,64350,64402,64460,64508,64566,64643,64720,64781,64860,64928,64989,65070,65142,65218,65281,65343,65405,65483,65540,65600,65651,65697,65761,65838,65908,65985,66060,66134,66196,66249,221936,248086,248331,248828,249327"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b255ac174ecb581f0983b73e2410e4dd\\transformed\\cardview-v7-28.0.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "20,201,202,203,204,482,483,484,1351,2791,2793,2796", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1006,10396,10457,10519,10581,27960,28019,28076,86134,177874,177938,178064", "endLines": "20,201,202,203,204,482,483,484,1357,2792,2795,2798", "endColumns": "51,60,61,61,63,58,56,53,12,12,12,12", "endOffsets": "1053,10452,10514,10576,10640,28014,28071,28125,86543,177933,178059,178187"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b66139be6585a43ae23903a8750be796\\transformed\\wind-sdk-4.23.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "1122,1123,1124,1125,1126,1127,1128,1129,1130,1131,1132,3918,3922,3938,3956,3962,3966,3972", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "70230,70268,70311,70354,70409,70503,70555,70615,70668,70726,70780,250058,250215,250736,251335,251615,251757,252047", "endLines": "1122,1123,1124,1125,1126,1127,1128,1129,1130,1131,1132,3921,3937,3955,3961,3965,3971,3985", "endColumns": "37,42,42,54,93,51,59,52,57,53,53,12,12,12,12,12,12,12", "endOffsets": "70263,70306,70349,70404,70498,70550,70610,70663,70721,70775,70829,250210,250731,251330,251610,251752,252042,252629"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a7007720781ce56b641f8588ab1a8654\\transformed\\coordinatorlayout-28.0.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "29,3838", "startColumns": "4,4", "startOffsets": "1473,246087", "endLines": "29,3840", "endColumns": "60,12", "endOffsets": "1529,246227"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a3ae461f31008f99a87e29f3c99a3c83\\transformed\\appcompat-v7-28.0.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "30,36,38,173,174,175,176,178,179,180,181,182,183,187,188,190,191,193,194,195,196,197,198,199,200,216,217,218,219,221,222,225,226,227,228,266,267,268,269,270,271,272,273,274,275,276,277,302,303,304,305,306,307,308,309,313,314,315,316,317,318,320,321,322,323,328,329,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,536,537,541,542,543,544,545,546,547,854,855,856,857,858,859,860,861,869,870,871,872,874,883,884,890,915,917,918,921,922,933,934,935,936,937,938,939,940,941,942,943,944,945,946,947,948,949,950,951,952,953,954,955,956,957,958,959,960,961,962,963,964,965,966,967,968,969,970,971,1120,1314,1315,1320,1321,1322,1330,1338,1339,1343,1347,1358,1363,1369,1376,1380,1384,1389,1393,1397,1401,1405,1409,1413,1419,1423,1429,1433,1439,1443,1448,1452,1455,1459,1465,1469,1475,1479,1485,1488,1492,1496,1500,1504,1508,1509,1510,1511,1514,1517,1520,1523,1527,1528,1529,1530,1531,1534,1536,1538,1540,1545,1546,1550,1556,1560,1561,1563,1574,1575,1579,1585,1589,1643,1644,1648,1675,1679,1680,1684,1934,2101,2127,2295,2321,2352,2360,2366,2380,2402,2407,2412,2422,2431,2440,2444,2451,2459,2466,2467,2476,2479,2482,2486,2490,2494,2497,2498,2502,2506,2516,2521,2528,2534,2535,2538,2542,2547,2549,2551,2554,2557,2559,2563,2566,2573,2576,2579,2583,2585,2589,2591,2593,2595,2599,2607,2615,2627,2633,2642,2645,2656,2659,2664,2665,2885,2943,3006,3007,3017,3026,3031,3033,3037,3040,3043,3046,3049,3052,3055,3058,3062,3065,3068,3071,3075,3078,3082,3098,3099,3100,3101,3102,3103,3104,3105,3106,3107,3108,3109,3110,3111,3112,3113,3114,3115,3116,3117,3118,3120,3122,3123,3124,3125,3126,3127,3128,3129,3131,3132,3134,3135,3137,3139,3140,3142,3143,3144,3145,3146,3147,3149,3150,3151,3152,3153,3308,3310,3312,3314,3315,3316,3317,3318,3319,3320,3321,3322,3323,3324,3325,3326,3328,3329,3330,3331,3332,3333,3335,3339,3434,3435,3436,3437,3438,3439,3440,3469,3471,3473,3475,3477,3479,3480,3481,3482,3484,3486,3488,3489,3490,3491,3492,3493,3494,3495,3496,3497,3498,3499,3502,3503,3504,3505,3507,3509,3510,3512,3513,3515,3517,3519,3520,3521,3522,3523,3524,3525,3526,3527,3528,3529,3530,3532,3533,3534,3535,3537,3538,3539,3540,3541,3543,3545,3547,3549,3550,3551,3552,3553,3554,3555,3556,3557,3558,3559,3560,3561,3562", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1534,1761,1859,8444,8485,8540,8599,8723,8804,8865,8940,9016,9093,9343,9428,9561,9637,9755,9832,9910,10016,10122,10201,10281,10338,11408,11482,11557,11622,11751,11811,11966,12038,12111,12178,14508,14567,14626,14685,14744,14803,14857,14911,14964,15018,15072,15126,16635,16709,16788,16861,16935,17006,17078,17150,17364,17421,17479,17552,17626,17700,17823,17895,17968,18038,18295,18355,22843,22912,22981,23051,23125,23201,23265,23342,23418,23495,23560,23629,23706,23781,23850,23918,23995,24061,24122,24219,24284,24353,24452,24523,24582,24640,24697,24756,24820,24891,24963,25035,25107,25179,25246,25314,25382,25441,25504,25568,25658,25749,25809,25875,25942,26008,26078,26142,26195,26308,26366,26429,26494,26559,26634,26707,26779,26828,26889,26950,27011,27073,27137,27201,27265,27330,27393,27453,27514,27580,27639,27699,27761,27832,27892,31422,31508,31758,31848,31935,32023,32105,32188,32278,52407,52459,52517,52562,52628,52692,52749,52806,53322,53379,53427,53476,53567,54007,54054,54338,55663,55743,55807,55997,56057,56734,56808,56878,56956,57010,57080,57165,57213,57259,57330,57408,57486,57558,57632,57706,57780,57860,57933,58002,58074,58151,58212,58275,58341,58405,58476,58539,58604,58668,58729,58790,58842,58915,58989,59058,59133,59207,59281,59422,70125,83644,83722,84036,84124,84220,84683,85265,85354,85601,85882,86548,86833,87226,87703,87925,88147,88423,88650,88880,89110,89340,89570,89797,90216,90442,90867,91097,91525,91744,92027,92235,92366,92593,93019,93244,93671,93892,94317,94437,94713,95014,95338,95629,95943,96080,96211,96316,96558,96725,96929,97137,97408,97520,97632,97737,97854,98068,98214,98354,98440,98788,98876,99122,99540,99789,99871,99969,100561,100661,100913,101337,101592,105394,105483,105720,107744,107986,108088,108341,126358,136487,138003,148231,149759,151516,152142,152562,153623,154888,155144,155380,155927,156421,157026,157224,157804,158368,158743,158861,159399,159556,159752,160025,160281,160451,160592,160656,160938,161224,161900,162164,162502,162855,162949,163135,163441,163703,163828,163955,164194,164405,164524,164717,164894,165349,165530,165652,165911,166024,166211,166313,166420,166549,166824,167332,167828,168705,168999,169569,169718,170450,170622,170958,171050,182304,186586,191355,191417,191995,192579,192904,193017,193246,193406,193558,193729,193895,194064,194231,194394,194637,194807,194980,195151,195425,195624,195829,196854,196938,197034,197130,197228,197328,197430,197532,197634,197736,197838,197938,198034,198146,198275,198398,198529,198660,198758,198872,198966,199106,199240,199336,199448,199548,199664,199760,199872,199972,200112,200248,200412,200542,200700,200850,200991,201135,201270,201382,201532,201660,201788,201924,202056,202186,202316,202428,211042,211188,211332,211470,211536,211626,211702,211806,211896,211998,212106,212214,212314,212394,212486,212584,212694,212772,212878,212970,213074,213184,213306,213469,219291,219371,219471,219561,219671,219765,219871,221941,222041,222153,222267,222383,222499,222593,222707,222819,222921,223041,223163,223245,223349,223469,223595,223693,223787,223875,223987,224103,224225,224337,224512,224628,224714,224806,224918,225042,225109,225235,225303,225431,225575,225703,225772,225867,225982,226095,226194,226303,226414,226525,226626,226731,226831,226961,227052,227175,227269,227381,227467,227571,227667,227755,227873,227977,228081,228207,228295,228403,228503,228593,228703,228787,228889,228973,229027,229091,229197,229307,229391", "endLines": "30,36,38,173,174,175,176,178,179,180,181,182,183,187,188,190,191,193,194,195,196,197,198,199,200,216,217,218,219,221,222,225,226,227,228,266,267,268,269,270,271,272,273,274,275,276,277,302,303,304,305,306,307,308,309,313,314,315,316,317,318,320,321,322,323,328,329,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,536,537,541,542,543,544,545,546,547,854,855,856,857,858,859,860,861,869,870,871,872,874,883,884,890,915,917,918,921,922,933,934,935,936,937,938,939,940,941,942,943,944,945,946,947,948,949,950,951,952,953,954,955,956,957,958,959,960,961,962,963,964,965,966,967,968,969,970,971,1120,1314,1315,1320,1321,1322,1337,1338,1342,1346,1350,1362,1368,1375,1379,1383,1388,1392,1396,1400,1404,1408,1412,1418,1422,1428,1432,1438,1442,1447,1451,1454,1458,1464,1468,1474,1478,1484,1487,1491,1495,1499,1503,1507,1508,1509,1510,1513,1516,1519,1522,1526,1527,1528,1529,1530,1533,1535,1537,1539,1544,1545,1549,1555,1559,1560,1562,1573,1574,1578,1584,1588,1589,1643,1647,1674,1678,1679,1683,1711,2100,2126,2294,2320,2351,2359,2365,2379,2401,2406,2411,2421,2430,2439,2443,2450,2458,2465,2466,2475,2478,2481,2485,2489,2493,2496,2497,2501,2505,2515,2520,2527,2533,2534,2537,2541,2546,2548,2550,2553,2556,2558,2562,2565,2572,2575,2578,2582,2584,2588,2590,2592,2594,2598,2606,2614,2626,2632,2641,2644,2655,2658,2663,2664,2669,2942,3001,3006,3016,3025,3026,3032,3036,3039,3042,3045,3048,3051,3054,3057,3061,3064,3067,3070,3074,3077,3081,3085,3098,3099,3100,3101,3102,3103,3104,3105,3106,3107,3108,3109,3110,3111,3112,3113,3114,3115,3116,3117,3119,3121,3122,3123,3124,3125,3126,3127,3128,3130,3131,3133,3134,3136,3138,3139,3141,3142,3143,3144,3145,3146,3148,3149,3150,3151,3152,3153,3309,3311,3313,3314,3315,3316,3317,3318,3319,3320,3321,3322,3323,3324,3325,3327,3328,3329,3330,3331,3332,3334,3338,3342,3434,3435,3436,3437,3438,3439,3440,3470,3472,3474,3476,3478,3479,3480,3481,3483,3485,3487,3488,3489,3490,3491,3492,3493,3494,3495,3496,3497,3498,3501,3502,3503,3504,3506,3508,3509,3511,3512,3514,3516,3518,3519,3520,3521,3522,3523,3524,3525,3526,3527,3528,3529,3531,3532,3533,3534,3536,3537,3538,3539,3540,3542,3544,3546,3548,3549,3550,3551,3552,3553,3554,3555,3556,3557,3558,3559,3560,3561,3562", "endColumns": "54,44,48,40,54,58,61,80,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,112,57,62,64,64,74,72,71,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,70,77,77,71,73,73,73,79,72,68,71,76,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,77,105,91,103,109,12,12,12,79,99,89,109,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,109,83,119", "endOffsets": "1584,1801,1903,8480,8535,8594,8656,8799,8860,8935,9011,9088,9166,9423,9505,9632,9708,9827,9905,10011,10117,10196,10276,10333,10391,11477,11552,11617,11683,11806,11867,12033,12106,12173,12241,14562,14621,14680,14739,14798,14852,14906,14959,15013,15067,15121,15175,16704,16783,16856,16930,17001,17073,17145,17218,17416,17474,17547,17621,17695,17770,17890,17963,18033,18104,18350,18411,22907,22976,23046,23120,23196,23260,23337,23413,23490,23555,23624,23701,23776,23845,23913,23990,24056,24117,24214,24279,24348,24447,24518,24577,24635,24692,24751,24815,24886,24958,25030,25102,25174,25241,25309,25377,25436,25499,25563,25653,25744,25804,25870,25937,26003,26073,26137,26190,26303,26361,26424,26489,26554,26629,26702,26774,26823,26884,26945,27006,27068,27132,27196,27260,27325,27388,27448,27509,27575,27634,27694,27756,27827,27887,27955,31503,31590,31843,31930,32018,32100,32183,32273,32364,52454,52512,52557,52623,52687,52744,52801,52855,53374,53422,53471,53522,53596,54049,54098,54379,55690,55802,55864,56052,56109,56803,56873,56951,57005,57075,57160,57208,57254,57325,57403,57481,57553,57627,57701,57775,57855,57928,57997,58069,58146,58207,58270,58336,58400,58471,58534,58599,58663,58724,58785,58837,58910,58984,59053,59128,59202,59276,59417,59487,70173,83717,83807,84119,84215,84305,85260,85349,85596,85877,86129,86828,87221,87698,87920,88142,88418,88645,88875,89105,89335,89565,89792,90211,90437,90862,91092,91520,91739,92022,92230,92361,92588,93014,93239,93666,93887,94312,94432,94708,95009,95333,95624,95938,96075,96206,96311,96553,96720,96924,97132,97403,97515,97627,97732,97849,98063,98209,98349,98435,98783,98871,99117,99535,99784,99866,99964,100556,100656,100908,101332,101587,101681,105478,105715,107739,107981,108083,108336,110492,136482,137998,148226,149754,151511,152137,152557,153618,154883,155139,155375,155922,156416,157021,157219,157799,158363,158738,158856,159394,159551,159747,160020,160276,160446,160587,160651,160933,161219,161895,162159,162497,162850,162944,163130,163436,163698,163823,163950,164189,164400,164519,164712,164889,165344,165525,165647,165906,166019,166206,166308,166415,166544,166819,167327,167823,168700,168994,169564,169713,170445,170617,170953,171045,171323,186581,191006,191412,191990,192574,192665,193012,193241,193401,193553,193724,193890,194059,194226,194389,194632,194802,194975,195146,195420,195619,195824,196154,196933,197029,197125,197223,197323,197425,197527,197629,197731,197833,197933,198029,198141,198270,198393,198524,198655,198753,198867,198961,199101,199235,199331,199443,199543,199659,199755,199867,199967,200107,200243,200407,200537,200695,200845,200986,201130,201265,201377,201527,201655,201783,201919,202051,202181,202311,202423,202563,211183,211327,211465,211531,211621,211697,211801,211891,211993,212101,212209,212309,212389,212481,212579,212689,212767,212873,212965,213069,213179,213301,213464,213621,219366,219466,219556,219666,219760,219866,219958,222036,222148,222262,222378,222494,222588,222702,222814,222916,223036,223158,223240,223344,223464,223590,223688,223782,223870,223982,224098,224220,224332,224507,224623,224709,224801,224913,225037,225104,225230,225298,225426,225570,225698,225767,225862,225977,226090,226189,226298,226409,226520,226621,226726,226826,226956,227047,227170,227264,227376,227462,227566,227662,227750,227868,227972,228076,228202,228290,228398,228498,228588,228698,228782,228884,228968,229022,229086,229192,229302,229386,229506"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\e779f411b6970bbfe6a7d381c7d1cfc3\\transformed\\transition-28.0.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "873,882,885,886,887,901,902,903,904,905", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "53527,53964,54103,54150,54205,54836,54890,54942,54991,55052", "endColumns": "39,42,46,54,44,53,51,48,60,49", "endOffsets": "53562,54002,54145,54200,54245,54885,54937,54986,55047,55097"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\6e168fd8550fecc7fe244221549ae7ea\\transformed\\constraint-layout-1.1.3\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "4,5,21,26,27,28,31,39,40,41,42,45,46,49,52,53,54,55,56,59,62,63,64,65,70,73,76,77,78,83,84,85,88,91,92,95,98,101,104,105,108,111,112,117,118,123,126,129,130,131,132,133,134,135,136,137,138", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "275,336,1058,1314,1366,1427,1589,1908,1969,2029,2099,2232,2300,2429,2555,2617,2682,2750,2817,2940,3065,3132,3197,3262,3443,3564,3685,3751,3818,4028,4097,4163,4288,4414,4481,4607,4734,4859,4986,5051,5177,5300,5365,5573,5640,5820,5940,6060,6125,6187,6249,6311,6370,6430,6491,6552,6611", "endLines": "4,12,21,26,27,28,34,39,40,41,44,45,48,51,52,53,54,55,58,61,62,63,64,69,72,75,76,77,82,83,84,87,90,91,94,97,100,103,104,107,110,111,116,117,122,125,128,129,130,131,132,133,134,135,136,137,146", "endColumns": "60,11,47,51,60,45,11,60,59,69,11,67,11,11,61,64,67,66,11,11,66,64,64,11,11,11,65,66,11,68,65,11,11,66,11,11,11,11,64,11,11,64,11,66,11,11,11,64,61,61,61,58,59,60,60,58,11", "endOffsets": "331,622,1101,1361,1422,1468,1711,1964,2024,2094,2227,2295,2424,2550,2612,2677,2745,2812,2935,3060,3127,3192,3257,3438,3559,3680,3746,3813,4023,4092,4158,4283,4409,4476,4602,4729,4854,4981,5046,5172,5295,5360,5568,5635,5815,5935,6055,6120,6182,6244,6306,6365,6425,6486,6547,6606,6948"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\194766323b8e4d092ba620fa2362821d\\transformed\\support-media-compat-28.0.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "288,850,851,852,853,3159,3161,3162,3167,3169", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "15864,52195,52248,52301,52354,202886,203062,203184,203446,203641", "endColumns": "88,52,52,52,52,65,121,60,65,66", "endOffsets": "15948,52243,52296,52349,52402,202947,203179,203240,203507,203703"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\71b2f468c1ec12de737a4945bfcdd2ee\\transformed\\design-28.0.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "16,17,18,19,22,23,24,25,35,37,148,149,150,151,152,153,155,156,157,159,160,161,162,163,164,165,166,167,168,169,170,171,172,177,205,206,207,208,209,210,211,212,213,214,215,278,279,280,281,282,283,284,285,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522,523,524,525,526,527,528,529,530,531,532,533,534,535,787,788,789,790,791,792,793,794,795,796,797,798,799,800,801,802,803,804,805,806,807,808,809,810,811,812,813,814,815,816,817,818,819,820,821,822,823,824,825,826,827,828,829,830,831,832,833,834,880,881,888,889,897,898,899,916,919,920,923,924,925,927,928,929,930,931,981,997,1003,1004,1015,1016,1023,1082,1094,1095,1096,1097,1098,1323,1590,1591,1592,1597,1598,1602,1608,1612,1613,1614,1615,1626,1627,1628,1632,1638,1642,1712,1713,1714,1744,1764,1810,1840,1860,1880,1926,1930,2670,2684,2725,2733,3002,3003,3004,3005,3170,3173,3174,3177,3180,3181,3184,3188,3193,3201,3209,3218,3226,3230,3238,3246,3254,3262,3270,3279,3288,3296,3305,3343,3345,3350,3352,3357,3361,3378,3379,3384,3385,3386,3387,3388,3389,3391,3392,3397,3398,3399,3400,3401,3402,3403,3405,3409,3413,3417,3441,3442,3443,3444,3445,3446,3447,3448,3449,3452,3456,3459,3565,3573,3580,3589,3593,3608,3616,3619,3628,3633,3644,3652,3655,3664,3671,3672,3691,3694,3700,3703,3712,3715,3718,3721,3724,3727,3731,3734,3743,3746,3754,3759,3767,3772,3776,3777,3788,3795,3799,3803,3804,3808,3816,3820,3825,3830", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "774,830,890,951,1106,1159,1217,1265,1716,1806,7021,7079,7139,7197,7243,7303,7415,7461,7511,7599,7657,7715,7774,7834,7896,7958,8020,8082,8144,8206,8267,8329,8391,8661,10645,10719,10782,10850,10931,10995,11061,11131,11201,11271,11341,15180,15243,15308,15374,15427,15503,15569,15656,28618,28672,28751,28829,28902,28967,29030,29096,29167,29238,29300,29369,29435,29502,29569,29625,29676,29729,29781,29835,29906,29969,30028,30090,30149,30222,30289,30349,30412,30487,30559,30630,30686,30757,30814,30871,30937,31001,31072,31129,31182,31245,31297,31355,48367,48433,48499,48580,48655,48711,48764,48825,48883,48933,48982,49031,49080,49142,49194,49239,49320,49374,49427,49481,49532,49581,49632,49693,49754,49816,49866,49907,49957,50005,50067,50118,50167,50236,50297,50353,50424,50489,50558,50609,50672,50742,50811,50881,50943,51013,51083,51158,53844,53902,54250,54295,54658,54705,54750,55695,55869,55935,56114,56177,56249,56362,56419,56479,56537,56607,60009,61210,61610,61714,62323,62475,63057,66794,67552,67630,67931,68097,68252,84310,101686,101779,101886,102229,102336,102565,102974,103206,103306,103411,103530,104128,104275,104394,104629,105044,105282,110497,110618,110751,112830,114326,117560,119635,121143,122667,125897,126121,171328,172132,173892,174342,191011,191084,191171,191256,203708,203903,203995,204168,204330,204425,204594,204837,205130,205539,205953,206385,206803,207044,207474,207909,208319,208741,209151,209580,210006,210422,210860,213626,213694,214038,214118,214474,214624,215559,215643,216008,216106,216214,216312,216422,216538,216664,216760,217137,217247,217371,217509,217619,217741,217869,218007,218169,218385,218541,219963,220047,220151,220245,220359,220471,220595,220691,220771,220960,221166,221359,229650,230082,230503,230928,231125,232073,232594,232717,233354,233575,234390,234859,235042,235638,236098,236203,237464,237614,238031,238196,238876,239035,239126,239210,239406,239573,239795,239955,240332,240491,240819,241036,241611,241961,242210,242307,243013,243451,243692,243881,244015,244206,244843,245093,245396,245611", "endLines": "16,17,18,19,22,23,24,25,35,37,148,149,150,151,152,153,155,156,157,159,160,161,162,163,164,165,166,167,168,169,170,171,172,177,205,206,207,208,209,210,211,212,213,214,215,278,279,280,281,282,283,284,285,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522,523,524,525,526,527,528,529,530,531,532,533,534,535,787,788,789,790,791,792,793,794,795,796,797,798,799,800,801,802,803,804,805,806,807,808,809,810,811,812,813,814,815,816,817,818,819,820,821,822,823,824,825,826,827,828,829,830,831,832,833,834,880,881,888,889,897,898,899,916,919,920,923,924,925,927,928,929,930,931,981,997,1003,1004,1015,1016,1023,1082,1094,1095,1096,1097,1098,1326,1590,1591,1596,1597,1601,1607,1611,1612,1613,1614,1625,1626,1627,1631,1637,1641,1642,1712,1713,1743,1763,1809,1839,1859,1879,1925,1929,1933,2683,2724,2732,2742,3002,3003,3004,3005,3172,3173,3176,3179,3180,3183,3187,3192,3200,3208,3217,3225,3229,3237,3245,3253,3261,3269,3278,3287,3295,3304,3307,3344,3349,3351,3356,3360,3364,3378,3383,3384,3385,3386,3387,3388,3390,3391,3396,3397,3398,3399,3400,3401,3402,3404,3408,3412,3416,3420,3441,3442,3443,3444,3445,3446,3447,3448,3451,3455,3458,3462,3572,3579,3588,3592,3607,3615,3618,3627,3632,3643,3651,3654,3663,3670,3671,3690,3693,3699,3702,3711,3714,3717,3720,3723,3726,3730,3733,3742,3745,3753,3758,3766,3771,3775,3776,3787,3794,3798,3802,3803,3807,3815,3819,3824,3829,3837", "endColumns": "55,59,60,54,52,57,47,48,44,52,57,59,57,45,59,52,45,49,46,57,57,58,59,61,61,61,61,61,61,60,61,61,52,61,73,62,67,80,63,65,69,69,69,69,66,62,64,65,52,75,65,86,75,53,78,77,72,64,62,65,70,70,61,68,65,66,66,55,50,52,51,53,70,62,58,61,58,72,66,59,62,74,71,70,55,70,56,56,65,63,70,56,52,62,51,57,66,65,65,80,74,55,52,60,57,49,48,48,48,61,51,44,80,53,52,53,50,48,50,60,60,61,49,40,49,47,61,50,48,68,60,55,70,64,68,50,62,69,68,69,61,69,69,74,58,57,61,44,42,46,44,50,47,65,61,62,71,56,56,59,57,69,56,144,120,103,86,151,151,147,80,77,300,165,154,101,10,92,106,10,106,10,10,10,99,104,118,10,146,118,10,10,10,111,120,132,10,10,10,10,10,10,10,10,10,10,10,10,10,72,86,84,98,10,91,10,10,94,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,83,10,97,107,97,109,115,10,95,10,109,123,137,109,121,127,10,10,10,10,10,83,103,93,113,111,123,95,79,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,104,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,96,10,10,10,10,133,10,10,10,10,10,10", "endOffsets": "825,885,946,1001,1154,1212,1260,1309,1756,1854,7074,7134,7192,7238,7298,7351,7456,7506,7553,7652,7710,7769,7829,7891,7953,8015,8077,8139,8201,8262,8324,8386,8439,8718,10714,10777,10845,10926,10990,11056,11126,11196,11266,11336,11403,15238,15303,15369,15422,15498,15564,15651,15727,28667,28746,28824,28897,28962,29025,29091,29162,29233,29295,29364,29430,29497,29564,29620,29671,29724,29776,29830,29901,29964,30023,30085,30144,30217,30284,30344,30407,30482,30554,30625,30681,30752,30809,30866,30932,30996,31067,31124,31177,31240,31292,31350,31417,48428,48494,48575,48650,48706,48759,48820,48878,48928,48977,49026,49075,49137,49189,49234,49315,49369,49422,49476,49527,49576,49627,49688,49749,49811,49861,49902,49952,50000,50062,50113,50162,50231,50292,50348,50419,50484,50553,50604,50667,50737,50806,50876,50938,51008,51078,51153,51212,53897,53959,54290,54333,54700,54745,54796,55738,55930,55992,56172,56244,56301,56414,56474,56532,56602,56659,60149,61326,61709,61796,62470,62622,63200,66870,67625,67926,68092,68247,68349,84582,101774,101881,102224,102331,102560,102969,103201,103301,103406,103525,104123,104270,104389,104624,105039,105277,105389,110613,110746,112825,114321,117555,119630,121138,122662,125892,126116,126353,172127,173887,174337,175052,191079,191166,191251,191350,203898,203990,204163,204325,204420,204589,204832,205125,205534,205948,206380,206798,207039,207469,207904,208314,208736,209146,209575,210001,210417,210855,211037,213689,214033,214113,214469,214619,214763,215638,216003,216101,216209,216307,216417,216533,216659,216755,217132,217242,217366,217504,217614,217736,217864,218002,218164,218380,218536,218740,220042,220146,220240,220354,220466,220590,220686,220766,220955,221161,221354,221564,230077,230498,230923,231120,232068,232589,232712,233349,233570,234385,234854,235037,235633,236093,236198,237459,237609,238026,238191,238871,239030,239121,239205,239401,239568,239790,239950,240327,240486,240814,241031,241606,241956,242205,242302,243008,243446,243687,243876,244010,244201,244838,245088,245391,245606,246082"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\077c69690200a4c0c0ddf0630ba2f6e7\\transformed\\open_ad_sdk_6.8.4.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "220,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,538,539,540,548,549,550,862,863,864,865,866,875,906,907,908,909,910,911,912,913,914,1079,1080,1141,1142,1143,1144,1145,1146,1147,1148,1149,1150,1151,1152,1153,1154,1155,1156,1157,1158,1159,1160,1161,1162,1163,1164,1165,1166,1167,1168,1169,1170,1171,1172,1173,1174,1175,1176,1177,1178,1179,1180,1181,1182,1183,1184,1185,1186,1187,1188,1189,1190,1191,1192,1193,1194,1195,1196,1197,1198,1199,1200,1201,1202,1203,1204,1205,1206,1207,1208,1209,1210,1211,1212,1213,1214,1215,1216,1217,1218,1219,1220,1221,1222,1223,1224,1225,1226,1227,1228,1229,1230,1231,1232,1233,1234,1235,1236,1237,1238,1239,1240,1241,1242,1243,1244,1245,1246,1247,1248,1249,1250,1251,1252,1253,1254,1255,1256,1257,1258,1259,1260,1261,1262,1263,1264,1265,1266,1267,1268,1269,1270,1271,1272,1273,1274,1275,1276,1277,1278,1279,1280,1281,1282,1283,1284,1285,1286,1287,1288,1289,1290,1291,1292,1293,1294,1295,1296,1297,1298,1299,1300,1301,1302,1303,1304,1305,1306,1307,1308,1309,1310,1311,1316,2783,2787,2799,2811,2818,2822,2826,2830,2833,2837,2841,2848,2852,3027,3086,3090,3094,3365,3368,3859,3862,3868,3908,3986,3992,4006,4012,4013,4014,4020,4026,4035,4047,4053,4061,4068,4072,4080,4084,4096,4097", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "11688,18416,18471,18524,18582,18648,18706,18764,18820,18912,18990,19044,19099,19154,19208,19262,19311,19374,19433,19478,19533,19596,19663,19725,19776,19829,19900,19974,20033,20092,20143,20187,20235,20289,20350,20402,20463,20512,20573,20631,20679,20747,20804,20861,20911,20961,21009,21060,21111,21174,21237,21285,21348,21426,21474,21530,21585,21646,21707,21753,21802,21866,21930,21993,22044,22100,22151,22214,22290,22348,22417,22471,22543,22600,22643,31595,31654,31702,32369,32444,32520,52860,52927,52988,53049,53111,53601,55102,55148,55197,55273,55344,55418,55492,55561,55617,66623,66672,71461,71504,71541,71587,71636,71691,71752,71825,71897,71966,72092,72197,72276,72370,72466,72561,72644,72705,72777,72834,72907,72979,73073,73175,73274,73364,73457,73539,73619,73696,73774,73856,73935,74035,74125,74218,74295,74388,74500,74598,74687,74777,74866,74938,75010,75090,75170,75286,75363,75461,75552,75604,75842,75924,76113,76198,76255,76311,76352,76400,76449,76500,76560,76608,76671,76735,76794,76866,76939,77007,77080,77161,77218,77276,77334,77397,77446,77491,77556,77628,77705,77781,77833,77876,77934,77987,78031,78078,78121,78169,78223,78275,78327,78376,78436,78490,78548,78603,78645,78687,78764,78900,79010,79134,79242,79345,79402,79455,79527,79616,79699,79790,79844,79910,79971,80036,80091,80148,80226,80288,80342,80392,80445,80506,80545,80624,80697,80778,80844,80910,80960,81010,81079,81148,81232,81299,81356,81412,81474,81536,81596,81647,81704,81763,81865,81945,82003,82090,82150,82244,82295,82371,82430,82527,82598,82694,82760,82827,82865,82908,82957,83007,83059,83115,83168,83222,83277,83336,83397,83458,83510,83812,177379,177606,178192,178746,179146,179360,179578,179780,179932,180147,180347,180692,180915,192670,196159,196367,196591,214768,214944,247267,247406,247657,249611,252634,252970,253819,254220,254336,254459,254847,255243,255635,256074,256360,256834,257309,257568,258033,258273,258715,258784", "endLines": "220,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,538,539,540,548,549,550,862,863,864,865,866,875,906,907,908,909,910,911,912,913,914,1079,1080,1141,1142,1143,1144,1145,1146,1147,1148,1149,1150,1151,1152,1153,1154,1155,1156,1157,1158,1159,1160,1161,1162,1163,1164,1165,1166,1167,1168,1169,1170,1171,1172,1173,1174,1175,1176,1177,1178,1179,1180,1181,1182,1183,1184,1185,1186,1187,1188,1189,1190,1191,1192,1193,1194,1195,1196,1197,1198,1199,1200,1201,1202,1203,1204,1205,1206,1207,1208,1209,1210,1211,1212,1213,1214,1215,1216,1217,1218,1219,1220,1221,1222,1223,1224,1225,1226,1227,1228,1229,1230,1231,1232,1233,1234,1235,1236,1237,1238,1239,1240,1241,1242,1243,1244,1245,1246,1247,1248,1249,1250,1251,1252,1253,1254,1255,1256,1257,1258,1259,1260,1261,1262,1263,1264,1265,1266,1267,1268,1269,1270,1271,1272,1273,1274,1275,1276,1277,1278,1279,1280,1281,1282,1283,1284,1285,1286,1287,1288,1289,1290,1291,1292,1293,1294,1295,1296,1297,1298,1299,1300,1301,1302,1303,1304,1305,1306,1307,1308,1309,1310,1311,1319,2786,2790,2801,2817,2821,2825,2829,2832,2836,2840,2847,2851,2855,3030,3089,3093,3097,3367,3377,3861,3867,3871,3917,3991,4005,4011,4012,4013,4019,4025,4034,4046,4052,4060,4067,4071,4079,4083,4095,4096,4105", "endColumns": "62,54,52,57,65,57,57,55,91,77,53,54,54,53,53,48,62,58,44,54,62,66,61,50,52,70,73,58,58,50,43,47,53,60,51,60,48,60,57,47,67,56,56,49,49,47,50,50,62,62,47,62,77,47,55,54,60,60,45,48,63,63,62,50,55,50,62,75,57,68,53,71,56,42,60,58,47,55,74,75,71,66,60,60,61,62,65,45,48,75,70,73,73,68,55,45,48,62,42,36,45,48,54,60,72,71,68,125,104,78,93,95,94,82,60,71,56,72,71,93,101,98,89,92,81,79,76,77,81,78,99,89,92,76,92,111,97,88,89,88,71,71,79,79,115,76,97,90,51,237,81,188,84,56,55,40,47,48,50,59,47,62,63,58,71,72,67,72,80,56,57,57,62,48,44,64,71,76,75,51,42,57,52,43,46,42,47,53,51,51,48,59,53,57,54,41,41,76,135,109,123,107,102,56,52,71,88,82,90,53,65,60,64,54,56,77,61,53,49,52,60,38,78,72,80,65,65,49,49,68,68,83,66,56,55,61,61,59,50,56,58,101,79,57,86,59,93,50,75,58,96,70,95,65,66,37,42,48,49,51,55,52,53,54,58,60,60,51,45,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,115,122,12,12,12,12,12,12,12,12,12,12,12,68,12", "endOffsets": "11746,18466,18519,18577,18643,18701,18759,18815,18907,18985,19039,19094,19149,19203,19257,19306,19369,19428,19473,19528,19591,19658,19720,19771,19824,19895,19969,20028,20087,20138,20182,20230,20284,20345,20397,20458,20507,20568,20626,20674,20742,20799,20856,20906,20956,21004,21055,21106,21169,21232,21280,21343,21421,21469,21525,21580,21641,21702,21748,21797,21861,21925,21988,22039,22095,22146,22209,22285,22343,22412,22466,22538,22595,22638,22699,31649,31697,31753,32439,32515,32587,52922,52983,53044,53106,53169,53662,55143,55192,55268,55339,55413,55487,55556,55612,55658,66667,66730,71499,71536,71582,71631,71686,71747,71820,71892,71961,72087,72192,72271,72365,72461,72556,72639,72700,72772,72829,72902,72974,73068,73170,73269,73359,73452,73534,73614,73691,73769,73851,73930,74030,74120,74213,74290,74383,74495,74593,74682,74772,74861,74933,75005,75085,75165,75281,75358,75456,75547,75599,75837,75919,76108,76193,76250,76306,76347,76395,76444,76495,76555,76603,76666,76730,76789,76861,76934,77002,77075,77156,77213,77271,77329,77392,77441,77486,77551,77623,77700,77776,77828,77871,77929,77982,78026,78073,78116,78164,78218,78270,78322,78371,78431,78485,78543,78598,78640,78682,78759,78895,79005,79129,79237,79340,79397,79450,79522,79611,79694,79785,79839,79905,79966,80031,80086,80143,80221,80283,80337,80387,80440,80501,80540,80619,80692,80773,80839,80905,80955,81005,81074,81143,81227,81294,81351,81407,81469,81531,81591,81642,81699,81758,81860,81940,81998,82085,82145,82239,82290,82366,82425,82522,82593,82689,82755,82822,82860,82903,82952,83002,83054,83110,83163,83217,83272,83331,83392,83453,83505,83551,84031,177601,177869,178351,179141,179355,179573,179775,179927,180142,180342,180687,180910,181097,192899,196362,196586,196849,214939,215554,247401,247652,247865,250053,252965,253814,254215,254331,254454,254842,255238,255630,256069,256355,256829,257304,257563,258028,258268,258710,258779,259334"}}]}, {"outputFile": "com.ainative.mountainsurvival.app-mergeReleaseResources-22:/values/values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\daefd50eed0506e326ae247974906cd2\\transformed\\support-compat-28.0.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "286,287,485,486,487,488,489,490,491,835,836,837,838,839,840,841,842,843,844,845,846,847,848,849,867,868,878,879,892,893,894,895,896,900,932,1158,3187,3188,3193,3196,3201,3596,3597", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15732,15801,28130,28200,28268,28340,28410,28471,28545,51217,51278,51339,51401,51465,51527,51588,51656,51756,51816,51882,51955,52024,52081,52133,53174,53246,53774,53809,54421,54471,54532,54589,54623,54801,56664,72962,205852,205969,206236,206529,206796,232795,232867", "endLines": "286,287,485,486,487,488,489,490,491,835,836,837,838,839,840,841,842,843,844,845,846,847,848,849,867,868,878,879,892,893,894,895,896,900,932,1158,3187,3191,3193,3199,3201,3596,3597", "endColumns": "68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,34,34,49,60,56,33,34,34,69,70,116,12,109,12,128,71,66", "endOffsets": "15796,15859,28195,28263,28335,28405,28466,28540,28613,51273,51334,51396,51460,51522,51583,51651,51751,51811,51877,51950,52019,52076,52128,52190,53241,53317,53804,53839,54466,54527,54584,54618,54653,54831,56729,73028,205964,206165,206341,206725,206920,232862,232929"}}, {"source": "E:\\Ai\\AiCode\\game\\MountainSurvival\\code\\app\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,141,-1,-1,-1,127,128,126,124,123,131,130,121,120,135,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,144,145,-1,149,-1,148,146,147,-1,-1,-1,138,-1,-1,-1,-1,-1,-1,-1,140,139,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,116,117,115,104,105,103,134,108,109,107,112,113,111,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,-1,-1,-1,4,4,4,4,4,4,4,4,4,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,4,-1,4,-1,4,4,4,-1,-1,-1,4,-1,-1,-1,-1,-1,-1,-1,4,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,4,4,4,4,4,4,4,4,4,4,4,4,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,8221,-1,-1,-1,7438,7592,7367,7157,7083,7727,7658,6879,6806,7934,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,8292,8363,-1,8616,-1,8549,8430,8491,-1,-1,-1,8068,-1,-1,-1,-1,-1,-1,-1,8164,8109,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,6534,6711,6447,5406,5659,5319,7806,5827,6013,5742,6181,6359,6091,-1", "endColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,47,-1,-1,-1,153,64,70,208,73,59,68,202,72,111,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,70,66,-1,75,-1,66,60,57,-1,-1,-1,40,-1,-1,-1,-1,-1,-1,-1,56,54,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,176,75,86,252,81,86,127,185,76,84,177,86,89,-1", "endOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,8264,-1,-1,-1,7587,7652,7433,7361,7152,7782,7722,7077,6874,8041,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,8358,8425,-1,8687,-1,8611,8486,8544,-1,-1,-1,8104,-1,-1,-1,-1,-1,-1,-1,8216,8159,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,6706,6782,6529,5654,5736,5401,7929,6008,6085,5822,6354,6441,6176,-1"}, "to": {"startLines": "972,973,974,975,976,977,978,979,980,998,999,1000,1001,1002,1005,1006,1007,1008,1009,1010,1011,1012,1013,1014,1015,1016,1017,1018,1019,1020,1021,1022,1023,1024,1025,1028,1029,1030,1031,1032,1033,1035,1036,1037,1038,1039,1083,1084,1085,1086,1087,1088,1089,1092,1094,1095,1096,1097,1098,1099,1100,1101,1102,1103,1104,1105,1106,1107,1108,1117,1118,1119,1120,1121,1122,1123,1124,1125,1126,1127,1128,1129,1130,1131,1132,1133,1134,1135,1136,1138,1139,1141,1156,1157,1159,1160,1332,1333,1334,1335,1336,1337,1338,1339,1340,1341,1342,1343,1344,1345,1346", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "59492,59567,59623,59684,59734,59777,59854,59910,59952,61331,61383,61446,61499,61561,61801,61861,61909,61948,61995,62042,62196,62261,62332,62541,62615,62675,62744,62947,63020,63132,63174,63259,63316,63362,63418,63765,63810,63851,63962,64013,64081,64343,64403,64463,64522,64582,67392,67432,67479,67532,67590,67651,67713,67873,68013,68056,68098,68173,68244,68311,68362,68438,68488,68555,68616,68674,68772,68870,68933,69933,69985,70496,70552,70616,70673,70731,70841,70898,70953,71000,71051,71135,71205,71264,71350,71430,71506,71561,71619,71710,71755,71869,72874,72918,73033,73091,85247,85292,85469,85545,85632,85885,85967,86054,86182,86368,86445,86530,86708,86795,86885", "endColumns": "74,55,60,49,42,76,55,41,56,51,62,52,61,48,59,47,38,46,46,153,64,70,208,73,59,68,202,72,111,41,84,56,45,55,42,44,40,110,50,67,113,59,59,58,59,62,39,46,52,57,60,61,47,58,42,41,74,70,66,50,75,49,66,60,57,97,97,62,40,51,510,55,63,56,57,109,56,54,46,50,83,69,58,85,79,75,54,57,46,44,60,51,43,43,57,60,44,176,75,86,252,81,86,127,185,76,84,177,86,89,42", "endOffsets": "59562,59618,59679,59729,59772,59849,59905,59947,60004,61378,61441,61494,61556,61605,61856,61904,61943,61990,62037,62191,62256,62327,62536,62610,62670,62739,62942,63015,63127,63169,63254,63311,63357,63413,63456,63805,63846,63957,64008,64076,64190,64398,64458,64517,64577,64640,67427,67474,67527,67585,67646,67708,67756,67927,68051,68093,68168,68239,68306,68357,68433,68483,68550,68611,68669,68767,68865,68928,68969,69980,70491,70547,70611,70668,70726,70836,70893,70948,70995,71046,71130,71200,71259,71345,71425,71501,71556,71614,71661,71750,71811,71916,72913,72957,73086,73147,85287,85464,85540,85627,85880,85962,86049,86177,86363,86440,86525,86703,86790,86880,86923"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\62922e5f87afeea95a66affda0037661\\transformed\\beizi_fusion_sdk_5.2.1.6\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "13,14,15,147,154,158,185,186,404,982,983,984,985,986,987,988,989,990,991,992,993,994,995,996,2776,2786,2796,2799,2802,2805,2808,3874,3883", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "627,675,724,6953,7356,7558,9219,9280,22704,60154,60258,60304,60348,60393,60437,60516,60636,60742,60792,60863,60908,60970,61034,61141,178341,178962,179584,179759,179925,180082,180180,249516,250035", "endLines": "13,14,15,147,154,158,185,186,404,982,983,984,985,986,987,988,989,990,991,992,993,994,995,996,2785,2795,2798,2801,2804,2807,2815,3882,3891", "endColumns": "47,48,49,67,58,40,60,62,49,103,45,43,44,43,78,119,105,49,70,44,61,63,106,68,12,12,12,12,12,12,12,12,12", "endOffsets": "670,719,769,7016,7410,7594,9275,9338,22749,60253,60299,60343,60388,60432,60511,60631,60737,60787,60858,60903,60965,61029,61136,61205,178957,179579,179754,179920,180077,180175,180658,250030,250546"}}, {"source": "E:\\Ai\\AiCode\\game\\MountainSurvival\\code\\app\\src\\main\\res\\values\\colors.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "184,189,192,223,224,300,301,310,311,312,319,324,325,326,327,405,406", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "9171,9510,9713,11872,11921,16536,16583,17223,17270,17317,17775,18109,18154,18199,18246,22754,22801", "endColumns": "47,50,41,48,44,46,51,46,46,46,47,44,44,46,48,46,41", "endOffsets": "9214,9556,9750,11916,11961,16578,16630,17265,17312,17359,17818,18149,18194,18241,18290,22796,22838"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\0457feaf864ab549dda35ffc4a13a2e6\\transformed\\openset_sdk_6.5.2.6\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2,3,289,290,291,292,293,294,295,296,297,298,299,891,1040,1041,1042,1109,1110,1111,1137,1153,1154,1155,1360,2889,2898,2901,2906,2910,3935", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,213,15953,16004,16056,16112,16162,16214,16265,16321,16379,16432,16485,54384,64645,64682,64730,68974,69026,69080,71666,72525,72568,72735,87871,184386,184738,184837,185039,185292,252616", "endLines": "2,3,289,290,291,292,293,294,295,296,297,298,299,891,1040,1041,1042,1109,1110,1111,1137,1153,1154,1155,1362,2897,2900,2905,2909,2917,3940", "endColumns": "62,61,50,51,55,49,51,50,55,57,52,52,50,36,36,47,52,51,53,50,43,42,166,138,12,12,12,12,12,12,12", "endOffsets": "208,270,15999,16051,16107,16157,16209,16260,16316,16374,16427,16480,16531,54416,64677,64725,64778,69021,69075,69126,71705,72563,72730,72869,87962,184733,184832,185034,185287,185583,252890"}}, {"source": "E:\\Ai\\AiCode\\game\\MountainSurvival\\code\\app\\src\\main\\res\\values\\ic_launcher_background.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "229", "startColumns": "4", "startOffsets": "12246", "endColumns": "56", "endOffsets": "12298"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2144dbf5c98623e2472a5ef01095ffd5\\transformed\\GDTSDK.unionNormal.4.640.1510\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1"}, "to": {"startLines": "926,2835,2838,2841", "startColumns": "4,4,4,4", "startOffsets": "56306,181640,181771,181893", "endLines": "926,2837,2840,2843", "endColumns": "55,12,12,12", "endOffsets": "56357,181766,181888,182025"}}, {"source": "E:\\Ai\\AiCode\\game\\MountainSurvival\\code\\app\\src\\main\\res\\values\\themes.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "3454", "startColumns": "4", "startOffsets": "222029", "endLines": "3466", "endColumns": "12", "endOffsets": "222570"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\cb0f3cc3125274cec1b491c0584a407c\\transformed\\kssdk-ad-4.4.20.1-publishRelease-aa0a55f514\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,551,552,553,554,555,556,557,558,559,560,561,562,563,564,565,566,567,568,569,570,571,572,573,574,575,576,577,578,579,580,581,582,583,584,585,586,587,588,589,590,591,592,593,594,595,596,597,598,599,600,601,602,603,604,605,606,607,608,609,610,611,612,613,614,615,616,617,618,619,620,621,622,623,624,625,626,627,628,629,630,631,632,633,634,635,636,637,638,639,640,641,642,643,644,645,646,647,648,649,650,651,652,653,654,655,656,657,658,659,660,661,662,663,664,665,666,667,668,669,670,671,672,673,674,675,676,677,678,679,680,681,682,683,684,685,686,687,688,689,690,691,692,693,694,695,696,697,698,699,700,701,702,703,704,705,706,707,708,709,710,711,712,713,714,715,716,717,718,719,720,721,722,723,724,725,726,727,728,729,730,731,732,733,734,735,736,737,738,739,740,741,742,743,744,745,746,747,748,749,750,751,752,753,754,755,756,757,758,759,760,761,762,763,764,765,766,767,768,769,770,771,772,773,774,775,776,777,778,779,780,781,782,783,784,785,786,876,877,1043,1044,1045,1046,1047,1048,1049,1050,1051,1052,1053,1054,1055,1056,1057,1058,1059,1060,1061,1062,1063,1064,1065,1066,1067,1068,1069,1070,1071,1072,1073,1074,1075,1076,1077,1078,1079,1080,1081,1082,3496,3905,3910,3915,3925", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "12303,12353,12403,12453,12503,12559,12614,12669,12724,12789,12855,12923,12978,13031,13080,13145,13225,13305,13371,13443,13510,13569,13632,13693,13754,13821,13889,13960,14025,14097,14162,14234,14300,14354,14407,14452,32592,32646,32708,32766,32839,32896,32960,33025,33084,33154,33224,33296,33373,33444,33509,33596,33673,33747,33817,33885,33959,34047,34125,34189,34248,34303,34367,34432,34497,34555,34606,34659,34731,34805,34876,34949,35013,35083,35151,35214,35275,35335,35402,35470,35530,35599,35659,35727,35788,35852,35915,35973,36039,36102,36165,36228,36306,36382,36459,36528,36591,36667,36730,36804,36870,36938,37003,37073,37145,37214,37279,37340,37412,37475,37539,37609,37673,37738,37809,37886,37951,38018,38082,38156,38233,38307,38379,38455,38524,38588,38645,38711,38778,38845,38906,38962,39039,39101,39163,39236,39306,39371,39438,39502,39569,39631,39692,39761,39825,39889,39955,40018,40086,40151,40213,40276,40344,40409,40476,40540,40602,40667,40732,40803,40873,40937,41013,41096,41170,41238,41300,41364,41425,41487,41550,41614,41680,41743,41811,41878,41947,42016,42076,42159,42227,42300,42371,42446,42539,42614,42683,42751,42817,42883,42951,43024,43091,43152,43235,43308,43378,43444,43508,43578,43662,43736,43796,43859,43921,43987,44048,44119,44185,44255,44314,44367,44432,44485,44541,44594,44654,44721,44781,44852,44927,44995,45068,45135,45207,45275,45348,45410,45476,45538,45604,45671,45742,45803,45869,45939,46014,46081,46153,46214,46280,46349,46423,46490,46562,46622,46687,46753,46823,46887,46956,47029,47107,47170,47238,47301,47369,47432,47500,47564,47633,47686,47738,47800,47869,47935,47996,48060,48121,48189,48254,48314,53667,53711,64783,64850,64908,64958,65012,65069,65137,65218,65288,65360,65427,65493,65545,65603,65651,65709,65786,65863,65924,66003,66071,66132,66213,66285,66361,66424,66486,66548,66626,66683,66743,66794,66840,66904,66981,67051,67128,67203,67277,67339,224853,251154,251375,251620,252117", "endLines": "230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,551,552,553,554,555,556,557,558,559,560,561,562,563,564,565,566,567,568,569,570,571,572,573,574,575,576,577,578,579,580,581,582,583,584,585,586,587,588,589,590,591,592,593,594,595,596,597,598,599,600,601,602,603,604,605,606,607,608,609,610,611,612,613,614,615,616,617,618,619,620,621,622,623,624,625,626,627,628,629,630,631,632,633,634,635,636,637,638,639,640,641,642,643,644,645,646,647,648,649,650,651,652,653,654,655,656,657,658,659,660,661,662,663,664,665,666,667,668,669,670,671,672,673,674,675,676,677,678,679,680,681,682,683,684,685,686,687,688,689,690,691,692,693,694,695,696,697,698,699,700,701,702,703,704,705,706,707,708,709,710,711,712,713,714,715,716,717,718,719,720,721,722,723,724,725,726,727,728,729,730,731,732,733,734,735,736,737,738,739,740,741,742,743,744,745,746,747,748,749,750,751,752,753,754,755,756,757,758,759,760,761,762,763,764,765,766,767,768,769,770,771,772,773,774,775,776,777,778,779,780,781,782,783,784,785,786,876,877,1043,1044,1045,1046,1047,1048,1049,1050,1051,1052,1053,1054,1055,1056,1057,1058,1059,1060,1061,1062,1063,1064,1065,1066,1067,1068,1069,1070,1071,1072,1073,1074,1075,1076,1077,1078,1079,1080,1081,1082,3501,3909,3914,3924,3934", "endColumns": "49,49,49,49,55,54,54,54,64,65,67,54,52,48,64,79,79,65,71,66,58,62,60,60,66,67,70,64,71,64,71,65,53,52,44,55,53,61,57,72,56,63,64,58,69,69,71,76,70,64,86,76,73,69,67,73,87,77,63,58,54,63,64,64,57,50,52,71,73,70,72,63,69,67,62,60,59,66,67,59,68,59,67,60,63,62,57,65,62,62,62,77,75,76,68,62,75,62,73,65,67,64,69,71,68,64,60,71,62,63,69,63,64,70,76,64,66,63,73,76,73,71,75,68,63,56,65,66,66,60,55,76,61,61,72,69,64,66,63,66,61,60,68,63,63,65,62,67,64,61,62,67,64,66,63,61,64,64,70,69,63,75,82,73,67,61,63,60,61,62,63,65,62,67,66,68,68,59,82,67,72,70,74,92,74,68,67,65,65,67,72,66,60,82,72,69,65,63,69,83,73,59,62,61,65,60,70,65,69,58,52,64,52,55,52,59,66,59,70,74,67,72,66,71,67,72,61,65,61,65,66,70,60,65,69,74,66,71,60,65,68,73,66,71,59,64,65,69,63,68,72,77,62,67,62,67,62,67,63,68,52,51,61,68,65,60,63,60,67,64,59,52,43,62,66,57,49,53,56,67,80,69,71,66,65,51,57,47,57,76,76,60,78,67,60,80,71,75,62,61,61,77,56,59,50,45,63,76,69,76,74,73,61,52,10,10,10,10,10", "endOffsets": "12348,12398,12448,12498,12554,12609,12664,12719,12784,12850,12918,12973,13026,13075,13140,13220,13300,13366,13438,13505,13564,13627,13688,13749,13816,13884,13955,14020,14092,14157,14229,14295,14349,14402,14447,14503,32641,32703,32761,32834,32891,32955,33020,33079,33149,33219,33291,33368,33439,33504,33591,33668,33742,33812,33880,33954,34042,34120,34184,34243,34298,34362,34427,34492,34550,34601,34654,34726,34800,34871,34944,35008,35078,35146,35209,35270,35330,35397,35465,35525,35594,35654,35722,35783,35847,35910,35968,36034,36097,36160,36223,36301,36377,36454,36523,36586,36662,36725,36799,36865,36933,36998,37068,37140,37209,37274,37335,37407,37470,37534,37604,37668,37733,37804,37881,37946,38013,38077,38151,38228,38302,38374,38450,38519,38583,38640,38706,38773,38840,38901,38957,39034,39096,39158,39231,39301,39366,39433,39497,39564,39626,39687,39756,39820,39884,39950,40013,40081,40146,40208,40271,40339,40404,40471,40535,40597,40662,40727,40798,40868,40932,41008,41091,41165,41233,41295,41359,41420,41482,41545,41609,41675,41738,41806,41873,41942,42011,42071,42154,42222,42295,42366,42441,42534,42609,42678,42746,42812,42878,42946,43019,43086,43147,43230,43303,43373,43439,43503,43573,43657,43731,43791,43854,43916,43982,44043,44114,44180,44250,44309,44362,44427,44480,44536,44589,44649,44716,44776,44847,44922,44990,45063,45130,45202,45270,45343,45405,45471,45533,45599,45666,45737,45798,45864,45934,46009,46076,46148,46209,46275,46344,46418,46485,46557,46617,46682,46748,46818,46882,46951,47024,47102,47165,47233,47296,47364,47427,47495,47559,47628,47681,47733,47795,47864,47930,47991,48055,48116,48184,48249,48309,48362,53706,53769,64845,64903,64953,65007,65064,65132,65213,65283,65355,65422,65488,65540,65598,65646,65704,65781,65858,65919,65998,66066,66127,66208,66280,66356,66419,66481,66543,66621,66678,66738,66789,66835,66899,66976,67046,67123,67198,67272,67334,67387,225220,251370,251615,252112,252611"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b255ac174ecb581f0983b73e2410e4dd\\transformed\\cardview-v7-28.0.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "20,201,202,203,204,482,483,484,1384,2824,2826,2829", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1006,10396,10457,10519,10581,27960,28019,28076,89418,181158,181222,181348", "endLines": "20,201,202,203,204,482,483,484,1390,2825,2828,2831", "endColumns": "51,60,61,61,63,58,56,53,12,12,12,12", "endOffsets": "1053,10452,10514,10576,10640,28014,28071,28125,89827,181217,181343,181471"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b66139be6585a43ae23903a8750be796\\transformed\\wind-sdk-4.23.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "1142,1143,1144,1145,1146,1147,1148,1149,1150,1151,1152,3951,3955,3971,3989,3995,3999,4005", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "71921,71959,72002,72045,72100,72194,72246,72306,72359,72417,72471,253342,253499,254020,254619,254899,255041,255331", "endLines": "1142,1143,1144,1145,1146,1147,1148,1149,1150,1151,1152,3954,3970,3988,3994,3998,4004,4018", "endColumns": "37,42,42,54,93,51,59,52,57,53,53,12,12,12,12,12,12,12", "endOffsets": "71954,71997,72040,72095,72189,72241,72301,72354,72412,72466,72520,253494,254015,254614,254894,255036,255326,255913"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a7007720781ce56b641f8588ab1a8654\\transformed\\coordinatorlayout-28.0.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "29,3871", "startColumns": "4,4", "startOffsets": "1473,249371", "endLines": "29,3873", "endColumns": "60,12", "endOffsets": "1529,249511"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a3ae461f31008f99a87e29f3c99a3c83\\transformed\\appcompat-v7-28.0.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "30,36,38,173,174,175,176,178,179,180,181,182,183,187,188,190,191,193,194,195,196,197,198,199,200,216,217,218,219,221,222,225,226,227,228,266,267,268,269,270,271,272,273,274,275,276,277,302,303,304,305,306,307,308,309,313,314,315,316,317,318,320,321,322,323,328,329,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,536,537,541,542,543,544,545,546,547,854,855,856,857,858,859,860,861,869,870,871,872,874,883,884,890,915,917,918,921,922,933,934,935,936,937,938,939,940,941,942,943,944,945,946,947,948,949,950,951,952,953,954,955,956,957,958,959,960,961,962,963,964,965,966,967,968,969,970,971,1140,1347,1348,1353,1354,1355,1363,1371,1372,1376,1380,1391,1396,1402,1409,1413,1417,1422,1426,1430,1434,1438,1442,1446,1452,1456,1462,1466,1472,1476,1481,1485,1488,1492,1498,1502,1508,1512,1518,1521,1525,1529,1533,1537,1541,1542,1543,1544,1547,1550,1553,1556,1560,1561,1562,1563,1564,1567,1569,1571,1573,1578,1579,1583,1589,1593,1594,1596,1607,1608,1612,1618,1622,1676,1677,1681,1708,1712,1713,1717,1967,2134,2160,2328,2354,2385,2393,2399,2413,2435,2440,2445,2455,2464,2473,2477,2484,2492,2499,2500,2509,2512,2515,2519,2523,2527,2530,2531,2535,2539,2549,2554,2561,2567,2568,2571,2575,2580,2582,2584,2587,2590,2592,2596,2599,2606,2609,2612,2616,2618,2622,2624,2626,2628,2632,2640,2648,2660,2666,2675,2678,2689,2692,2697,2698,2918,2976,3039,3040,3050,3059,3064,3066,3070,3073,3076,3079,3082,3085,3088,3091,3095,3098,3101,3104,3108,3111,3115,3131,3132,3133,3134,3135,3136,3137,3138,3139,3140,3141,3142,3143,3144,3145,3146,3147,3148,3149,3150,3151,3153,3155,3156,3157,3158,3159,3160,3161,3162,3164,3165,3167,3168,3170,3172,3173,3175,3176,3177,3178,3179,3180,3182,3183,3184,3185,3186,3341,3343,3345,3347,3348,3349,3350,3351,3352,3353,3354,3355,3356,3357,3358,3359,3361,3362,3363,3364,3365,3366,3368,3372,3467,3468,3469,3470,3471,3472,3473,3502,3504,3506,3508,3510,3512,3513,3514,3515,3517,3519,3521,3522,3523,3524,3525,3526,3527,3528,3529,3530,3531,3532,3535,3536,3537,3538,3540,3542,3543,3545,3546,3548,3550,3552,3553,3554,3555,3556,3557,3558,3559,3560,3561,3562,3563,3565,3566,3567,3568,3570,3571,3572,3573,3574,3576,3578,3580,3582,3583,3584,3585,3586,3587,3588,3589,3590,3591,3592,3593,3594,3595", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1534,1761,1859,8444,8485,8540,8599,8723,8804,8865,8940,9016,9093,9343,9428,9561,9637,9755,9832,9910,10016,10122,10201,10281,10338,11408,11482,11557,11622,11751,11811,11966,12038,12111,12178,14508,14567,14626,14685,14744,14803,14857,14911,14964,15018,15072,15126,16635,16709,16788,16861,16935,17006,17078,17150,17364,17421,17479,17552,17626,17700,17823,17895,17968,18038,18295,18355,22843,22912,22981,23051,23125,23201,23265,23342,23418,23495,23560,23629,23706,23781,23850,23918,23995,24061,24122,24219,24284,24353,24452,24523,24582,24640,24697,24756,24820,24891,24963,25035,25107,25179,25246,25314,25382,25441,25504,25568,25658,25749,25809,25875,25942,26008,26078,26142,26195,26308,26366,26429,26494,26559,26634,26707,26779,26828,26889,26950,27011,27073,27137,27201,27265,27330,27393,27453,27514,27580,27639,27699,27761,27832,27892,31422,31508,31758,31848,31935,32023,32105,32188,32278,52407,52459,52517,52562,52628,52692,52749,52806,53322,53379,53427,53476,53567,54007,54054,54338,55663,55743,55807,55997,56057,56734,56808,56878,56956,57010,57080,57165,57213,57259,57330,57408,57486,57558,57632,57706,57780,57860,57933,58002,58074,58151,58212,58275,58341,58405,58476,58539,58604,58668,58729,58790,58842,58915,58989,59058,59133,59207,59281,59422,71816,86928,87006,87320,87408,87504,87967,88549,88638,88885,89166,89832,90117,90510,90987,91209,91431,91707,91934,92164,92394,92624,92854,93081,93500,93726,94151,94381,94809,95028,95311,95519,95650,95877,96303,96528,96955,97176,97601,97721,97997,98298,98622,98913,99227,99364,99495,99600,99842,100009,100213,100421,100692,100804,100916,101021,101138,101352,101498,101638,101724,102072,102160,102406,102824,103073,103155,103253,103845,103945,104197,104621,104876,108678,108767,109004,111028,111270,111372,111625,129642,139771,141287,151515,153043,154800,155426,155846,156907,158172,158428,158664,159211,159705,160310,160508,161088,161652,162027,162145,162683,162840,163036,163309,163565,163735,163876,163940,164222,164508,165184,165448,165786,166139,166233,166419,166725,166987,167112,167239,167478,167689,167808,168001,168178,168633,168814,168936,169195,169308,169495,169597,169704,169833,170108,170616,171112,171989,172283,172853,173002,173734,173906,174242,174334,185588,189870,194639,194701,195279,195863,196188,196301,196530,196690,196842,197013,197179,197348,197515,197678,197921,198091,198264,198435,198709,198908,199113,200138,200222,200318,200414,200512,200612,200714,200816,200918,201020,201122,201222,201318,201430,201559,201682,201813,201944,202042,202156,202250,202390,202524,202620,202732,202832,202948,203044,203156,203256,203396,203532,203696,203826,203984,204134,204275,204419,204554,204666,204816,204944,205072,205208,205340,205470,205600,205712,214326,214472,214616,214754,214820,214910,214986,215090,215180,215282,215390,215498,215598,215678,215770,215868,215978,216056,216162,216254,216358,216468,216590,216753,222575,222655,222755,222845,222955,223049,223155,225225,225325,225437,225551,225667,225783,225877,225991,226103,226205,226325,226447,226529,226633,226753,226879,226977,227071,227159,227271,227387,227509,227621,227796,227912,227998,228090,228202,228326,228393,228519,228587,228715,228859,228987,229056,229151,229266,229379,229478,229587,229698,229809,229910,230015,230115,230245,230336,230459,230553,230665,230751,230855,230951,231039,231157,231261,231365,231491,231579,231687,231787,231877,231987,232071,232173,232257,232311,232375,232481,232591,232675", "endLines": "30,36,38,173,174,175,176,178,179,180,181,182,183,187,188,190,191,193,194,195,196,197,198,199,200,216,217,218,219,221,222,225,226,227,228,266,267,268,269,270,271,272,273,274,275,276,277,302,303,304,305,306,307,308,309,313,314,315,316,317,318,320,321,322,323,328,329,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,536,537,541,542,543,544,545,546,547,854,855,856,857,858,859,860,861,869,870,871,872,874,883,884,890,915,917,918,921,922,933,934,935,936,937,938,939,940,941,942,943,944,945,946,947,948,949,950,951,952,953,954,955,956,957,958,959,960,961,962,963,964,965,966,967,968,969,970,971,1140,1347,1348,1353,1354,1355,1370,1371,1375,1379,1383,1395,1401,1408,1412,1416,1421,1425,1429,1433,1437,1441,1445,1451,1455,1461,1465,1471,1475,1480,1484,1487,1491,1497,1501,1507,1511,1517,1520,1524,1528,1532,1536,1540,1541,1542,1543,1546,1549,1552,1555,1559,1560,1561,1562,1563,1566,1568,1570,1572,1577,1578,1582,1588,1592,1593,1595,1606,1607,1611,1617,1621,1622,1676,1680,1707,1711,1712,1716,1744,2133,2159,2327,2353,2384,2392,2398,2412,2434,2439,2444,2454,2463,2472,2476,2483,2491,2498,2499,2508,2511,2514,2518,2522,2526,2529,2530,2534,2538,2548,2553,2560,2566,2567,2570,2574,2579,2581,2583,2586,2589,2591,2595,2598,2605,2608,2611,2615,2617,2621,2623,2625,2627,2631,2639,2647,2659,2665,2674,2677,2688,2691,2696,2697,2702,2975,3034,3039,3049,3058,3059,3065,3069,3072,3075,3078,3081,3084,3087,3090,3094,3097,3100,3103,3107,3110,3114,3118,3131,3132,3133,3134,3135,3136,3137,3138,3139,3140,3141,3142,3143,3144,3145,3146,3147,3148,3149,3150,3152,3154,3155,3156,3157,3158,3159,3160,3161,3163,3164,3166,3167,3169,3171,3172,3174,3175,3176,3177,3178,3179,3181,3182,3183,3184,3185,3186,3342,3344,3346,3347,3348,3349,3350,3351,3352,3353,3354,3355,3356,3357,3358,3360,3361,3362,3363,3364,3365,3367,3371,3375,3467,3468,3469,3470,3471,3472,3473,3503,3505,3507,3509,3511,3512,3513,3514,3516,3518,3520,3521,3522,3523,3524,3525,3526,3527,3528,3529,3530,3531,3534,3535,3536,3537,3539,3541,3542,3544,3545,3547,3549,3551,3552,3553,3554,3555,3556,3557,3558,3559,3560,3561,3562,3564,3565,3566,3567,3569,3570,3571,3572,3573,3575,3577,3579,3581,3582,3583,3584,3585,3586,3587,3588,3589,3590,3591,3592,3593,3594,3595", "endColumns": "54,44,48,40,54,58,61,80,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,112,57,62,64,64,74,72,71,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,70,77,77,71,73,73,73,79,72,68,71,76,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,77,105,91,103,109,12,12,12,79,99,89,109,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,109,83,119", "endOffsets": "1584,1801,1903,8480,8535,8594,8656,8799,8860,8935,9011,9088,9166,9423,9505,9632,9708,9827,9905,10011,10117,10196,10276,10333,10391,11477,11552,11617,11683,11806,11867,12033,12106,12173,12241,14562,14621,14680,14739,14798,14852,14906,14959,15013,15067,15121,15175,16704,16783,16856,16930,17001,17073,17145,17218,17416,17474,17547,17621,17695,17770,17890,17963,18033,18104,18350,18411,22907,22976,23046,23120,23196,23260,23337,23413,23490,23555,23624,23701,23776,23845,23913,23990,24056,24117,24214,24279,24348,24447,24518,24577,24635,24692,24751,24815,24886,24958,25030,25102,25174,25241,25309,25377,25436,25499,25563,25653,25744,25804,25870,25937,26003,26073,26137,26190,26303,26361,26424,26489,26554,26629,26702,26774,26823,26884,26945,27006,27068,27132,27196,27260,27325,27388,27448,27509,27575,27634,27694,27756,27827,27887,27955,31503,31590,31843,31930,32018,32100,32183,32273,32364,52454,52512,52557,52623,52687,52744,52801,52855,53374,53422,53471,53522,53596,54049,54098,54379,55690,55802,55864,56052,56109,56803,56873,56951,57005,57075,57160,57208,57254,57325,57403,57481,57553,57627,57701,57775,57855,57928,57997,58069,58146,58207,58270,58336,58400,58471,58534,58599,58663,58724,58785,58837,58910,58984,59053,59128,59202,59276,59417,59487,71864,87001,87091,87403,87499,87589,88544,88633,88880,89161,89413,90112,90505,90982,91204,91426,91702,91929,92159,92389,92619,92849,93076,93495,93721,94146,94376,94804,95023,95306,95514,95645,95872,96298,96523,96950,97171,97596,97716,97992,98293,98617,98908,99222,99359,99490,99595,99837,100004,100208,100416,100687,100799,100911,101016,101133,101347,101493,101633,101719,102067,102155,102401,102819,103068,103150,103248,103840,103940,104192,104616,104871,104965,108762,108999,111023,111265,111367,111620,113776,139766,141282,151510,153038,154795,155421,155841,156902,158167,158423,158659,159206,159700,160305,160503,161083,161647,162022,162140,162678,162835,163031,163304,163560,163730,163871,163935,164217,164503,165179,165443,165781,166134,166228,166414,166720,166982,167107,167234,167473,167684,167803,167996,168173,168628,168809,168931,169190,169303,169490,169592,169699,169828,170103,170611,171107,171984,172278,172848,172997,173729,173901,174237,174329,174607,189865,194290,194696,195274,195858,195949,196296,196525,196685,196837,197008,197174,197343,197510,197673,197916,198086,198259,198430,198704,198903,199108,199438,200217,200313,200409,200507,200607,200709,200811,200913,201015,201117,201217,201313,201425,201554,201677,201808,201939,202037,202151,202245,202385,202519,202615,202727,202827,202943,203039,203151,203251,203391,203527,203691,203821,203979,204129,204270,204414,204549,204661,204811,204939,205067,205203,205335,205465,205595,205707,205847,214467,214611,214749,214815,214905,214981,215085,215175,215277,215385,215493,215593,215673,215765,215863,215973,216051,216157,216249,216353,216463,216585,216748,216905,222650,222750,222840,222950,223044,223150,223242,225320,225432,225546,225662,225778,225872,225986,226098,226200,226320,226442,226524,226628,226748,226874,226972,227066,227154,227266,227382,227504,227616,227791,227907,227993,228085,228197,228321,228388,228514,228582,228710,228854,228982,229051,229146,229261,229374,229473,229582,229693,229804,229905,230010,230110,230240,230331,230454,230548,230660,230746,230850,230946,231034,231152,231256,231360,231486,231574,231682,231782,231872,231982,232066,232168,232252,232306,232370,232476,232586,232670,232790"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\e779f411b6970bbfe6a7d381c7d1cfc3\\transformed\\transition-28.0.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "873,882,885,886,887,901,902,903,904,905", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "53527,53964,54103,54150,54205,54836,54890,54942,54991,55052", "endColumns": "39,42,46,54,44,53,51,48,60,49", "endOffsets": "53562,54002,54145,54200,54245,54885,54937,54986,55047,55097"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\6e168fd8550fecc7fe244221549ae7ea\\transformed\\constraint-layout-1.1.3\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "4,5,21,26,27,28,31,39,40,41,42,45,46,49,52,53,54,55,56,59,62,63,64,65,70,73,76,77,78,83,84,85,88,91,92,95,98,101,104,105,108,111,112,117,118,123,126,129,130,131,132,133,134,135,136,137,138", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "275,336,1058,1314,1366,1427,1589,1908,1969,2029,2099,2232,2300,2429,2555,2617,2682,2750,2817,2940,3065,3132,3197,3262,3443,3564,3685,3751,3818,4028,4097,4163,4288,4414,4481,4607,4734,4859,4986,5051,5177,5300,5365,5573,5640,5820,5940,6060,6125,6187,6249,6311,6370,6430,6491,6552,6611", "endLines": "4,12,21,26,27,28,34,39,40,41,44,45,48,51,52,53,54,55,58,61,62,63,64,69,72,75,76,77,82,83,84,87,90,91,94,97,100,103,104,107,110,111,116,117,122,125,128,129,130,131,132,133,134,135,136,137,146", "endColumns": "60,11,47,51,60,45,11,60,59,69,11,67,11,11,61,64,67,66,11,11,66,64,64,11,11,11,65,66,11,68,65,11,11,66,11,11,11,11,64,11,11,64,11,66,11,11,11,64,61,61,61,58,59,60,60,58,11", "endOffsets": "331,622,1101,1361,1422,1468,1711,1964,2024,2094,2227,2295,2424,2550,2612,2677,2745,2812,2935,3060,3127,3192,3257,3438,3559,3680,3746,3813,4023,4092,4158,4283,4409,4476,4602,4729,4854,4981,5046,5172,5295,5360,5568,5635,5815,5935,6055,6120,6182,6244,6306,6365,6425,6486,6547,6606,6948"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\194766323b8e4d092ba620fa2362821d\\transformed\\support-media-compat-28.0.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "288,850,851,852,853,3192,3194,3195,3200,3202", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "15864,52195,52248,52301,52354,206170,206346,206468,206730,206925", "endColumns": "88,52,52,52,52,65,121,60,65,66", "endOffsets": "15948,52243,52296,52349,52402,206231,206463,206524,206791,206987"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\71b2f468c1ec12de737a4945bfcdd2ee\\transformed\\design-28.0.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "16,17,18,19,22,23,24,25,35,37,148,149,150,151,152,153,155,156,157,159,160,161,162,163,164,165,166,167,168,169,170,171,172,177,205,206,207,208,209,210,211,212,213,214,215,278,279,280,281,282,283,284,285,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522,523,524,525,526,527,528,529,530,531,532,533,534,535,787,788,789,790,791,792,793,794,795,796,797,798,799,800,801,802,803,804,805,806,807,808,809,810,811,812,813,814,815,816,817,818,819,820,821,822,823,824,825,826,827,828,829,830,831,832,833,834,880,881,888,889,897,898,899,916,919,920,923,924,925,927,928,929,930,931,981,997,1003,1004,1026,1027,1034,1093,1112,1113,1114,1115,1116,1356,1623,1624,1625,1630,1631,1635,1641,1645,1646,1647,1648,1659,1660,1661,1665,1671,1675,1745,1746,1747,1777,1797,1843,1873,1893,1913,1959,1963,2703,2717,2758,2766,3035,3036,3037,3038,3203,3206,3207,3210,3213,3214,3217,3221,3226,3234,3242,3251,3259,3263,3271,3279,3287,3295,3303,3312,3321,3329,3338,3376,3378,3383,3385,3390,3394,3411,3412,3417,3418,3419,3420,3421,3422,3424,3425,3430,3431,3432,3433,3434,3435,3436,3438,3442,3446,3450,3474,3475,3476,3477,3478,3479,3480,3481,3482,3485,3489,3492,3598,3606,3613,3622,3626,3641,3649,3652,3661,3666,3677,3685,3688,3697,3704,3705,3724,3727,3733,3736,3745,3748,3751,3754,3757,3760,3764,3767,3776,3779,3787,3792,3800,3805,3809,3810,3821,3828,3832,3836,3837,3841,3849,3853,3858,3863", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "774,830,890,951,1106,1159,1217,1265,1716,1806,7021,7079,7139,7197,7243,7303,7415,7461,7511,7599,7657,7715,7774,7834,7896,7958,8020,8082,8144,8206,8267,8329,8391,8661,10645,10719,10782,10850,10931,10995,11061,11131,11201,11271,11341,15180,15243,15308,15374,15427,15503,15569,15656,28618,28672,28751,28829,28902,28967,29030,29096,29167,29238,29300,29369,29435,29502,29569,29625,29676,29729,29781,29835,29906,29969,30028,30090,30149,30222,30289,30349,30412,30487,30559,30630,30686,30757,30814,30871,30937,31001,31072,31129,31182,31245,31297,31355,48367,48433,48499,48580,48655,48711,48764,48825,48883,48933,48982,49031,49080,49142,49194,49239,49320,49374,49427,49481,49532,49581,49632,49693,49754,49816,49866,49907,49957,50005,50067,50118,50167,50236,50297,50353,50424,50489,50558,50609,50672,50742,50811,50881,50943,51013,51083,51158,53844,53902,54250,54295,54658,54705,54750,55695,55869,55935,56114,56177,56249,56362,56419,56479,56537,56607,60009,61210,61610,61714,63461,63613,64195,67932,69131,69209,69510,69676,69831,87594,104970,105063,105170,105513,105620,105849,106258,106490,106590,106695,106814,107412,107559,107678,107913,108328,108566,113781,113902,114035,116114,117610,120844,122919,124427,125951,129181,129405,174612,175416,177176,177626,194295,194368,194455,194540,206992,207187,207279,207452,207614,207709,207878,208121,208414,208823,209237,209669,210087,210328,210758,211193,211603,212025,212435,212864,213290,213706,214144,216910,216978,217322,217402,217758,217908,218843,218927,219292,219390,219498,219596,219706,219822,219948,220044,220421,220531,220655,220793,220903,221025,221153,221291,221453,221669,221825,223247,223331,223435,223529,223643,223755,223879,223975,224055,224244,224450,224643,232934,233366,233787,234212,234409,235357,235878,236001,236638,236859,237674,238143,238326,238922,239382,239487,240748,240898,241315,241480,242160,242319,242410,242494,242690,242857,243079,243239,243616,243775,244103,244320,244895,245245,245494,245591,246297,246735,246976,247165,247299,247490,248127,248377,248680,248895", "endLines": "16,17,18,19,22,23,24,25,35,37,148,149,150,151,152,153,155,156,157,159,160,161,162,163,164,165,166,167,168,169,170,171,172,177,205,206,207,208,209,210,211,212,213,214,215,278,279,280,281,282,283,284,285,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522,523,524,525,526,527,528,529,530,531,532,533,534,535,787,788,789,790,791,792,793,794,795,796,797,798,799,800,801,802,803,804,805,806,807,808,809,810,811,812,813,814,815,816,817,818,819,820,821,822,823,824,825,826,827,828,829,830,831,832,833,834,880,881,888,889,897,898,899,916,919,920,923,924,925,927,928,929,930,931,981,997,1003,1004,1026,1027,1034,1093,1112,1113,1114,1115,1116,1359,1623,1624,1629,1630,1634,1640,1644,1645,1646,1647,1658,1659,1660,1664,1670,1674,1675,1745,1746,1776,1796,1842,1872,1892,1912,1958,1962,1966,2716,2757,2765,2775,3035,3036,3037,3038,3205,3206,3209,3212,3213,3216,3220,3225,3233,3241,3250,3258,3262,3270,3278,3286,3294,3302,3311,3320,3328,3337,3340,3377,3382,3384,3389,3393,3397,3411,3416,3417,3418,3419,3420,3421,3423,3424,3429,3430,3431,3432,3433,3434,3435,3437,3441,3445,3449,3453,3474,3475,3476,3477,3478,3479,3480,3481,3484,3488,3491,3495,3605,3612,3621,3625,3640,3648,3651,3660,3665,3676,3684,3687,3696,3703,3704,3723,3726,3732,3735,3744,3747,3750,3753,3756,3759,3763,3766,3775,3778,3786,3791,3799,3804,3808,3809,3820,3827,3831,3835,3836,3840,3848,3852,3857,3862,3870", "endColumns": "55,59,60,54,52,57,47,48,44,52,57,59,57,45,59,52,45,49,46,57,57,58,59,61,61,61,61,61,61,60,61,61,52,61,73,62,67,80,63,65,69,69,69,69,66,62,64,65,52,75,65,86,75,53,78,77,72,64,62,65,70,70,61,68,65,66,66,55,50,52,51,53,70,62,58,61,58,72,66,59,62,74,71,70,55,70,56,56,65,63,70,56,52,62,51,57,66,65,65,80,74,55,52,60,57,49,48,48,48,61,51,44,80,53,52,53,50,48,50,60,60,61,49,40,49,47,61,50,48,68,60,55,70,64,68,50,62,69,68,69,61,69,69,74,58,57,61,44,42,46,44,50,47,65,61,62,71,56,56,59,57,69,56,144,120,103,86,151,151,147,80,77,300,165,154,101,10,92,106,10,106,10,10,10,99,104,118,10,146,118,10,10,10,111,120,132,10,10,10,10,10,10,10,10,10,10,10,10,10,72,86,84,98,10,91,10,10,94,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,83,10,97,107,97,109,115,10,95,10,109,123,137,109,121,127,10,10,10,10,10,83,103,93,113,111,123,95,79,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,104,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,96,10,10,10,10,133,10,10,10,10,10,10", "endOffsets": "825,885,946,1001,1154,1212,1260,1309,1756,1854,7074,7134,7192,7238,7298,7351,7456,7506,7553,7652,7710,7769,7829,7891,7953,8015,8077,8139,8201,8262,8324,8386,8439,8718,10714,10777,10845,10926,10990,11056,11126,11196,11266,11336,11403,15238,15303,15369,15422,15498,15564,15651,15727,28667,28746,28824,28897,28962,29025,29091,29162,29233,29295,29364,29430,29497,29564,29620,29671,29724,29776,29830,29901,29964,30023,30085,30144,30217,30284,30344,30407,30482,30554,30625,30681,30752,30809,30866,30932,30996,31067,31124,31177,31240,31292,31350,31417,48428,48494,48575,48650,48706,48759,48820,48878,48928,48977,49026,49075,49137,49189,49234,49315,49369,49422,49476,49527,49576,49627,49688,49749,49811,49861,49902,49952,50000,50062,50113,50162,50231,50292,50348,50419,50484,50553,50604,50667,50737,50806,50876,50938,51008,51078,51153,51212,53897,53959,54290,54333,54700,54745,54796,55738,55930,55992,56172,56244,56301,56414,56474,56532,56602,56659,60149,61326,61709,61796,63608,63760,64338,68008,69204,69505,69671,69826,69928,87866,105058,105165,105508,105615,105844,106253,106485,106585,106690,106809,107407,107554,107673,107908,108323,108561,108673,113897,114030,116109,117605,120839,122914,124422,125946,129176,129400,129637,175411,177171,177621,178336,194363,194450,194535,194634,207182,207274,207447,207609,207704,207873,208116,208409,208818,209232,209664,210082,210323,210753,211188,211598,212020,212430,212859,213285,213701,214139,214321,216973,217317,217397,217753,217903,218047,218922,219287,219385,219493,219591,219701,219817,219943,220039,220416,220526,220650,220788,220898,221020,221148,221286,221448,221664,221820,222024,223326,223430,223524,223638,223750,223874,223970,224050,224239,224445,224638,224848,233361,233782,234207,234404,235352,235873,235996,236633,236854,237669,238138,238321,238917,239377,239482,240743,240893,241310,241475,242155,242314,242405,242489,242685,242852,243074,243234,243611,243770,244098,244315,244890,245240,245489,245586,246292,246730,246971,247160,247294,247485,248122,248372,248675,248890,249366"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\077c69690200a4c0c0ddf0630ba2f6e7\\transformed\\open_ad_sdk_6.8.4.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "220,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,538,539,540,548,549,550,862,863,864,865,866,875,906,907,908,909,910,911,912,913,914,1090,1091,1161,1162,1163,1164,1165,1166,1167,1168,1169,1170,1171,1172,1173,1174,1175,1176,1177,1178,1179,1180,1181,1182,1183,1184,1185,1186,1187,1188,1189,1190,1191,1192,1193,1194,1195,1196,1197,1198,1199,1200,1201,1202,1203,1204,1205,1206,1207,1208,1209,1210,1211,1212,1213,1214,1215,1216,1217,1218,1219,1220,1221,1222,1223,1224,1225,1226,1227,1228,1229,1230,1231,1232,1233,1234,1235,1236,1237,1238,1239,1240,1241,1242,1243,1244,1245,1246,1247,1248,1249,1250,1251,1252,1253,1254,1255,1256,1257,1258,1259,1260,1261,1262,1263,1264,1265,1266,1267,1268,1269,1270,1271,1272,1273,1274,1275,1276,1277,1278,1279,1280,1281,1282,1283,1284,1285,1286,1287,1288,1289,1290,1291,1292,1293,1294,1295,1296,1297,1298,1299,1300,1301,1302,1303,1304,1305,1306,1307,1308,1309,1310,1311,1312,1313,1314,1315,1316,1317,1318,1319,1320,1321,1322,1323,1324,1325,1326,1327,1328,1329,1330,1331,1349,2816,2820,2832,2844,2851,2855,2859,2863,2866,2870,2874,2881,2885,3060,3119,3123,3127,3398,3401,3892,3895,3901,3941,4019,4025,4039,4045,4046,4047,4053,4059,4068,4080,4086,4094,4101,4105,4113,4117,4129,4130", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "11688,18416,18471,18524,18582,18648,18706,18764,18820,18912,18990,19044,19099,19154,19208,19262,19311,19374,19433,19478,19533,19596,19663,19725,19776,19829,19900,19974,20033,20092,20143,20187,20235,20289,20350,20402,20463,20512,20573,20631,20679,20747,20804,20861,20911,20961,21009,21060,21111,21174,21237,21285,21348,21426,21474,21530,21585,21646,21707,21753,21802,21866,21930,21993,22044,22100,22151,22214,22290,22348,22417,22471,22543,22600,22643,31595,31654,31702,32369,32444,32520,52860,52927,52988,53049,53111,53601,55102,55148,55197,55273,55344,55418,55492,55561,55617,67761,67810,73152,73195,73232,73278,73327,73382,73443,73516,73588,73657,73783,73888,73967,74061,74157,74252,74335,74396,74468,74525,74598,74670,74764,74866,74965,75055,75148,75230,75310,75387,75465,75547,75626,75726,75816,75909,75986,76079,76191,76289,76378,76468,76557,76629,76701,76781,76861,76977,77054,77152,77243,77295,77533,77615,77804,77889,77946,78002,78043,78091,78140,78191,78251,78299,78362,78426,78485,78557,78630,78698,78771,78852,78909,78967,79025,79088,79137,79182,79247,79319,79396,79472,79524,79567,79625,79678,79722,79769,79812,79860,79914,79966,80018,80067,80127,80181,80239,80294,80336,80378,80455,80591,80701,80825,80933,81036,81093,81146,81218,81307,81390,81481,81535,81601,81662,81727,81782,81839,81917,81979,82033,82083,82136,82197,82236,82315,82388,82469,82535,82601,82651,82701,82770,82839,82923,82990,83047,83103,83165,83227,83287,83338,83395,83454,83556,83636,83694,83781,83841,83935,83986,84062,84121,84218,84289,84385,84451,84518,84556,84599,84648,84698,84750,84806,84859,84913,84968,85027,85088,85149,85201,87096,180663,180890,181476,182030,182430,182644,182862,183064,183216,183431,183631,183976,184199,195954,199443,199651,199875,218052,218228,250551,250690,250941,252895,255918,256254,257103,257504,257620,257743,258131,258527,258919,259358,259644,260118,260593,260852,261317,261557,261999,262068", "endLines": "220,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,538,539,540,548,549,550,862,863,864,865,866,875,906,907,908,909,910,911,912,913,914,1090,1091,1161,1162,1163,1164,1165,1166,1167,1168,1169,1170,1171,1172,1173,1174,1175,1176,1177,1178,1179,1180,1181,1182,1183,1184,1185,1186,1187,1188,1189,1190,1191,1192,1193,1194,1195,1196,1197,1198,1199,1200,1201,1202,1203,1204,1205,1206,1207,1208,1209,1210,1211,1212,1213,1214,1215,1216,1217,1218,1219,1220,1221,1222,1223,1224,1225,1226,1227,1228,1229,1230,1231,1232,1233,1234,1235,1236,1237,1238,1239,1240,1241,1242,1243,1244,1245,1246,1247,1248,1249,1250,1251,1252,1253,1254,1255,1256,1257,1258,1259,1260,1261,1262,1263,1264,1265,1266,1267,1268,1269,1270,1271,1272,1273,1274,1275,1276,1277,1278,1279,1280,1281,1282,1283,1284,1285,1286,1287,1288,1289,1290,1291,1292,1293,1294,1295,1296,1297,1298,1299,1300,1301,1302,1303,1304,1305,1306,1307,1308,1309,1310,1311,1312,1313,1314,1315,1316,1317,1318,1319,1320,1321,1322,1323,1324,1325,1326,1327,1328,1329,1330,1331,1352,2819,2823,2834,2850,2854,2858,2862,2865,2869,2873,2880,2884,2888,3063,3122,3126,3130,3400,3410,3894,3900,3904,3950,4024,4038,4044,4045,4046,4052,4058,4067,4079,4085,4093,4100,4104,4112,4116,4128,4129,4138", "endColumns": "62,54,52,57,65,57,57,55,91,77,53,54,54,53,53,48,62,58,44,54,62,66,61,50,52,70,73,58,58,50,43,47,53,60,51,60,48,60,57,47,67,56,56,49,49,47,50,50,62,62,47,62,77,47,55,54,60,60,45,48,63,63,62,50,55,50,62,75,57,68,53,71,56,42,60,58,47,55,74,75,71,66,60,60,61,62,65,45,48,75,70,73,73,68,55,45,48,62,42,36,45,48,54,60,72,71,68,125,104,78,93,95,94,82,60,71,56,72,71,93,101,98,89,92,81,79,76,77,81,78,99,89,92,76,92,111,97,88,89,88,71,71,79,79,115,76,97,90,51,237,81,188,84,56,55,40,47,48,50,59,47,62,63,58,71,72,67,72,80,56,57,57,62,48,44,64,71,76,75,51,42,57,52,43,46,42,47,53,51,51,48,59,53,57,54,41,41,76,135,109,123,107,102,56,52,71,88,82,90,53,65,60,64,54,56,77,61,53,49,52,60,38,78,72,80,65,65,49,49,68,68,83,66,56,55,61,61,59,50,56,58,101,79,57,86,59,93,50,75,58,96,70,95,65,66,37,42,48,49,51,55,52,53,54,58,60,60,51,45,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,115,122,12,12,12,12,12,12,12,12,12,12,12,68,12", "endOffsets": "11746,18466,18519,18577,18643,18701,18759,18815,18907,18985,19039,19094,19149,19203,19257,19306,19369,19428,19473,19528,19591,19658,19720,19771,19824,19895,19969,20028,20087,20138,20182,20230,20284,20345,20397,20458,20507,20568,20626,20674,20742,20799,20856,20906,20956,21004,21055,21106,21169,21232,21280,21343,21421,21469,21525,21580,21641,21702,21748,21797,21861,21925,21988,22039,22095,22146,22209,22285,22343,22412,22466,22538,22595,22638,22699,31649,31697,31753,32439,32515,32587,52922,52983,53044,53106,53169,53662,55143,55192,55268,55339,55413,55487,55556,55612,55658,67805,67868,73190,73227,73273,73322,73377,73438,73511,73583,73652,73778,73883,73962,74056,74152,74247,74330,74391,74463,74520,74593,74665,74759,74861,74960,75050,75143,75225,75305,75382,75460,75542,75621,75721,75811,75904,75981,76074,76186,76284,76373,76463,76552,76624,76696,76776,76856,76972,77049,77147,77238,77290,77528,77610,77799,77884,77941,77997,78038,78086,78135,78186,78246,78294,78357,78421,78480,78552,78625,78693,78766,78847,78904,78962,79020,79083,79132,79177,79242,79314,79391,79467,79519,79562,79620,79673,79717,79764,79807,79855,79909,79961,80013,80062,80122,80176,80234,80289,80331,80373,80450,80586,80696,80820,80928,81031,81088,81141,81213,81302,81385,81476,81530,81596,81657,81722,81777,81834,81912,81974,82028,82078,82131,82192,82231,82310,82383,82464,82530,82596,82646,82696,82765,82834,82918,82985,83042,83098,83160,83222,83282,83333,83390,83449,83551,83631,83689,83776,83836,83930,83981,84057,84116,84213,84284,84380,84446,84513,84551,84594,84643,84693,84745,84801,84854,84908,84963,85022,85083,85144,85196,85242,87315,180885,181153,181635,182425,182639,182857,183059,183211,183426,183626,183971,184194,184381,196183,199646,199870,200133,218223,218838,250685,250936,251149,253337,256249,257098,257499,257615,257738,258126,258522,258914,259353,259639,260113,260588,260847,261312,261552,261994,262063,262618"}}]}]}