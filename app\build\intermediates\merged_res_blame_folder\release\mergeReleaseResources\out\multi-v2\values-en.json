{"logs": [{"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-mergeReleaseResources-22:\\values-en\\values-en.xml", "map": [{"source": "E:\\Ai\\AiCode\\game\\MountainSurvival\\code\\app\\src\\main\\res\\values-en\\strings.xml", "from": {"startLines": "1,2,6,11,9,19,17,15,16,18,8,7,26,24,27,25,23,22,28,5,12,10", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "16,70,204,444,348,1083,968,838,907,1021,299,252,2918,1314,2973,2832,1245,1163,3034,154,727,394", "endColumns": "53,56,47,282,45,50,52,68,60,61,48,46,54,1517,60,85,68,81,109,49,77,49", "endOffsets": "65,122,247,722,389,1129,1016,902,963,1078,343,294,2968,2827,3029,2913,1309,1240,3139,199,800,439"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,96", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,109,166,214,497,543,594,647,716,777,839,888,935,990,2508,2569,2655,2724,2806,2916,2966,8154", "endColumns": "53,56,47,282,45,50,52,68,60,61,48,46,54,1517,60,85,68,81,109,49,77,49", "endOffsets": "104,161,209,492,538,589,642,711,772,834,883,930,985,2503,2564,2650,2719,2801,2911,2961,3039,8199"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\077c69690200a4c0c0ddf0630ba2f6e7\\transformed\\open_ad_sdk_6.8.4.0\\res\\values-en\\values-en.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,98,135,181,236,307,367,412,473,527,593,649,730,807,878,1001,1090,1214,1347,1484,1545,1607,1678,1754,1807,1860,1984,2033,2118,2169,2220,2263,2305,2347,2439,2512,2586,2662,2722,2787,2872,2989,3034,3118,3207,3295,3351,3409,3460,3562,3642,3729,3823,3899,3960,4057,4128,4224,4290,4357,4397,4442,4493,4543,4595,4661,4717,4773,4832,4898,4984,5057,5116", "endColumns": "42,36,45,54,70,59,44,60,53,65,55,80,76,70,122,88,123,132,136,60,61,70,75,52,52,123,48,84,50,50,42,41,41,91,72,73,75,59,64,84,116,44,83,88,87,55,57,50,101,79,86,93,75,60,96,70,95,65,66,39,44,50,49,51,65,55,55,58,65,85,72,58,48", "endOffsets": "93,130,176,231,302,362,407,468,522,588,644,725,802,873,996,1085,1209,1342,1479,1540,1602,1673,1749,1802,1855,1979,2028,2113,2164,2215,2258,2300,2342,2434,2507,2581,2657,2717,2782,2867,2984,3029,3113,3202,3290,3346,3404,3455,3557,3637,3724,3818,3894,3955,4052,4123,4219,4285,4352,4392,4437,4488,4538,4590,4656,4712,4768,4827,4893,4979,5052,5111,5160"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3044,3087,3124,3170,3225,3296,3356,3401,3462,3516,3582,3638,3719,3796,3867,3990,4079,4203,4336,4473,4534,4596,4667,4743,4796,4849,4973,5022,5107,5158,5209,5252,5294,5336,5428,5501,5575,5651,5711,5776,5861,5978,6023,6107,6196,6284,6340,6398,6449,6551,6631,6718,6812,6888,6949,7046,7117,7213,7279,7346,7386,7431,7482,7532,7584,7650,7706,7762,7821,7887,7973,8046,8105", "endColumns": "42,36,45,54,70,59,44,60,53,65,55,80,76,70,122,88,123,132,136,60,61,70,75,52,52,123,48,84,50,50,42,41,41,91,72,73,75,59,64,84,116,44,83,88,87,55,57,50,101,79,86,93,75,60,96,70,95,65,66,39,44,50,49,51,65,55,55,58,65,85,72,58,48", "endOffsets": "3082,3119,3165,3220,3291,3351,3396,3457,3511,3577,3633,3714,3791,3862,3985,4074,4198,4331,4468,4529,4591,4662,4738,4791,4844,4968,5017,5102,5153,5204,5247,5289,5331,5423,5496,5570,5646,5706,5771,5856,5973,6018,6102,6191,6279,6335,6393,6444,6546,6626,6713,6807,6883,6944,7041,7112,7208,7274,7341,7381,7426,7477,7527,7579,7645,7701,7757,7816,7882,7968,8041,8100,8149"}}]}, {"outputFile": "com.ainative.mountainsurvival.app-mergeReleaseResources-22:/values-en/values-en.xml", "map": [{"source": "E:\\Ai\\AiCode\\game\\MountainSurvival\\code\\app\\src\\main\\res\\values-en\\strings.xml", "from": {"startLines": "84,83,80,79,97,86,85,-1,-1,47,67,46,68,100,43,94,34,93,56,58,66,33,32,-1,41,42,55,54,31,-1,69,50,48,51,49,-1,-1,-1,-1,-1,-1,99,70,-1,-1,73,62,65,63,64,35,-1,-1,-1,-1,-1,-1,-1,75,98,87,82,81,88,76,92,91,90,89,57,59,74,40,-1,38,-1,-1,39", "startColumns": "4,4,4,4,4,4,4,-1,-1,4,4,4,4,4,4,4,4,4,4,4,4,4,4,-1,4,4,4,4,4,-1,4,4,4,4,4,-1,-1,-1,-1,-1,-1,4,4,-1,-1,4,4,4,4,4,4,-1,-1,-1,-1,-1,-1,-1,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,-1,4,-1,-1,4", "startOffsets": "6838,6774,6542,6486,7824,7007,6943,-1,-1,4038,5710,3962,5808,7994,3847,7761,3413,7705,4737,4835,5635,3359,3289,-1,3753,3804,4521,4459,3181,-1,5904,4277,4115,4352,4194,-1,-1,-1,-1,-1,-1,7936,5989,-1,-1,6085,5124,5573,5182,5383,3466,-1,-1,-1,-1,-1,-1,-1,6274,7872,7113,6686,6616,7232,6328,7558,7490,7410,7352,4783,5013,6198,3704,-1,3588,-1,-1,3657", "endColumns": "104,63,73,55,47,105,63,-1,-1,76,97,75,95,59,85,38,52,55,45,177,74,53,69,-1,50,42,215,61,107,-1,84,74,78,81,82,-1,-1,-1,-1,-1,-1,57,73,-1,-1,112,57,61,200,189,92,-1,-1,-1,-1,-1,-1,-1,53,63,118,87,69,119,132,146,67,79,57,51,84,75,48,-1,68,-1,-1,46", "endOffsets": "6938,6833,6611,6537,7867,7108,7002,-1,-1,4110,5803,4033,5899,8049,3928,7795,3461,7756,4778,5008,5705,3408,3354,-1,3799,3842,4732,4516,3284,-1,5984,4347,4189,4429,4272,-1,-1,-1,-1,-1,-1,7989,6058,-1,-1,6193,5177,5630,5378,5568,3554,-1,-1,-1,-1,-1,-1,-1,6323,7931,7227,6769,6681,7347,6456,7700,7553,7485,7405,4830,5093,6269,3748,-1,3652,-1,-1,3699"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,151,152", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,160,224,298,354,402,508,572,626,683,760,858,934,1030,1090,1176,1215,1268,1324,1370,1548,1623,1677,1747,1795,1846,1889,2105,2167,2275,2558,2643,2718,2797,2879,2962,3008,3059,3112,3181,3242,3304,3362,3436,3485,3532,3645,3703,3765,3966,4156,4249,4304,5822,5883,5969,6038,6120,6230,6284,6348,6467,6555,6625,6745,6878,7025,7093,7173,7231,7283,7368,7444,7493,7543,7612,12800,12850", "endColumns": "104,63,73,55,47,105,63,53,56,76,97,75,95,59,85,38,52,55,45,177,74,53,69,47,50,42,215,61,107,282,84,74,78,81,82,45,50,52,68,60,61,57,73,48,46,112,57,61,200,189,92,54,1517,60,85,68,81,109,53,63,118,87,69,119,132,146,67,79,57,51,84,75,48,49,68,77,49,46", "endOffsets": "155,219,293,349,397,503,567,621,678,755,853,929,1025,1085,1171,1210,1263,1319,1365,1543,1618,1672,1742,1790,1841,1884,2100,2162,2270,2553,2638,2713,2792,2874,2957,3003,3054,3107,3176,3237,3299,3357,3431,3480,3527,3640,3698,3760,3961,4151,4244,4299,5817,5878,5964,6033,6115,6225,6279,6343,6462,6550,6620,6740,6873,7020,7088,7168,7226,7278,7363,7439,7488,7538,7607,7685,12845,12892"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\077c69690200a4c0c0ddf0630ba2f6e7\\transformed\\open_ad_sdk_6.8.4.0\\res\\values-en\\values-en.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7690,7733,7770,7816,7871,7942,8002,8047,8108,8162,8228,8284,8365,8442,8513,8636,8725,8849,8982,9119,9180,9242,9313,9389,9442,9495,9619,9668,9753,9804,9855,9898,9940,9982,10074,10147,10221,10297,10357,10422,10507,10624,10669,10753,10842,10930,10986,11044,11095,11197,11277,11364,11458,11534,11595,11692,11763,11859,11925,11992,12032,12077,12128,12178,12230,12296,12352,12408,12467,12533,12619,12692,12751", "endColumns": "42,36,45,54,70,59,44,60,53,65,55,80,76,70,122,88,123,132,136,60,61,70,75,52,52,123,48,84,50,50,42,41,41,91,72,73,75,59,64,84,116,44,83,88,87,55,57,50,101,79,86,93,75,60,96,70,95,65,66,39,44,50,49,51,65,55,55,58,65,85,72,58,48", "endOffsets": "7728,7765,7811,7866,7937,7997,8042,8103,8157,8223,8279,8360,8437,8508,8631,8720,8844,8977,9114,9175,9237,9308,9384,9437,9490,9614,9663,9748,9799,9850,9893,9935,9977,10069,10142,10216,10292,10352,10417,10502,10619,10664,10748,10837,10925,10981,11039,11090,11192,11272,11359,11453,11529,11590,11687,11758,11854,11920,11987,12027,12072,12123,12173,12225,12291,12347,12403,12462,12528,12614,12687,12746,12795"}}]}]}