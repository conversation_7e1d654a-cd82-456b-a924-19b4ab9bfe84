{"logs": [{"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-mergeReleaseResources-22:\\values-en\\values-en.xml", "map": [{"source": "E:\\Ai\\AiCode\\game\\MountainSurvival\\code\\app\\src\\main\\res\\values-en\\strings.xml", "from": {"startLines": "84,83,80,79,97,86,85,-1,-1,47,67,46,68,100,43,94,34,93,56,58,66,33,32,-1,41,42,55,54,31,-1,69,50,48,51,49,-1,-1,-1,-1,-1,-1,99,70,-1,-1,73,62,65,63,64,35,-1,-1,-1,-1,-1,-1,-1,75,98,87,82,81,88,76,92,91,90,89,57,59,74,40,-1,38,-1,-1,39", "startColumns": "4,4,4,4,4,4,4,-1,-1,4,4,4,4,4,4,4,4,4,4,4,4,4,4,-1,4,4,4,4,4,-1,4,4,4,4,4,-1,-1,-1,-1,-1,-1,4,4,-1,-1,4,4,4,4,4,4,-1,-1,-1,-1,-1,-1,-1,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,-1,4,-1,-1,4", "startOffsets": "6838,6774,6542,6486,7824,7007,6943,-1,-1,4038,5710,3962,5808,7994,3847,7761,3413,7705,4737,4835,5635,3359,3289,-1,3753,3804,4521,4459,3181,-1,5904,4277,4115,4352,4194,-1,-1,-1,-1,-1,-1,7936,5989,-1,-1,6085,5124,5573,5182,5383,3466,-1,-1,-1,-1,-1,-1,-1,6274,7872,7113,6686,6616,7232,6328,7558,7490,7410,7352,4783,5013,6198,3704,-1,3588,-1,-1,3657", "endColumns": "104,63,73,55,47,105,63,-1,-1,76,97,75,95,59,85,38,52,55,45,177,74,53,69,-1,50,42,215,61,107,-1,84,74,78,81,82,-1,-1,-1,-1,-1,-1,57,73,-1,-1,112,57,61,200,189,92,-1,-1,-1,-1,-1,-1,-1,53,63,118,87,69,119,132,146,67,79,57,51,84,75,48,-1,68,-1,-1,46", "endOffsets": "6938,6833,6611,6537,7867,7108,7002,-1,-1,4110,5803,4033,5899,8049,3928,7795,3461,7756,4778,5008,5705,3408,3354,-1,3799,3842,4732,4516,3284,-1,5984,4347,4189,4429,4272,-1,-1,-1,-1,-1,-1,7989,6058,-1,-1,6193,5177,5630,5378,5568,3554,-1,-1,-1,-1,-1,-1,-1,6323,7931,7227,6769,6681,7347,6456,7700,7553,7485,7405,4830,5093,6269,3748,-1,3652,-1,-1,3699"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,151,152", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,160,224,298,354,402,508,572,626,683,760,858,934,1030,1090,1176,1215,1268,1324,1370,1548,1623,1677,1747,1795,1846,1889,2105,2167,2275,2558,2643,2718,2797,2879,2962,3008,3059,3112,3181,3242,3304,3362,3436,3485,3532,3645,3703,3765,3966,4156,4249,4304,5822,5883,5969,6038,6120,6230,6284,6348,6467,6555,6625,6745,6878,7025,7093,7173,7231,7283,7368,7444,7493,7543,7612,12800,12850", "endColumns": "104,63,73,55,47,105,63,53,56,76,97,75,95,59,85,38,52,55,45,177,74,53,69,47,50,42,215,61,107,282,84,74,78,81,82,45,50,52,68,60,61,57,73,48,46,112,57,61,200,189,92,54,1517,60,85,68,81,109,53,63,118,87,69,119,132,146,67,79,57,51,84,75,48,49,68,77,49,46", "endOffsets": "155,219,293,349,397,503,567,621,678,755,853,929,1025,1085,1171,1210,1263,1319,1365,1543,1618,1672,1742,1790,1841,1884,2100,2162,2270,2553,2638,2713,2792,2874,2957,3003,3054,3107,3176,3237,3299,3357,3431,3480,3527,3640,3698,3760,3961,4151,4244,4299,5817,5878,5964,6033,6115,6225,6279,6343,6462,6550,6620,6740,6873,7020,7088,7168,7226,7278,7363,7439,7488,7538,7607,7685,12845,12892"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\077c69690200a4c0c0ddf0630ba2f6e7\\transformed\\open_ad_sdk_6.8.4.0\\res\\values-en\\values-en.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7690,7733,7770,7816,7871,7942,8002,8047,8108,8162,8228,8284,8365,8442,8513,8636,8725,8849,8982,9119,9180,9242,9313,9389,9442,9495,9619,9668,9753,9804,9855,9898,9940,9982,10074,10147,10221,10297,10357,10422,10507,10624,10669,10753,10842,10930,10986,11044,11095,11197,11277,11364,11458,11534,11595,11692,11763,11859,11925,11992,12032,12077,12128,12178,12230,12296,12352,12408,12467,12533,12619,12692,12751", "endColumns": "42,36,45,54,70,59,44,60,53,65,55,80,76,70,122,88,123,132,136,60,61,70,75,52,52,123,48,84,50,50,42,41,41,91,72,73,75,59,64,84,116,44,83,88,87,55,57,50,101,79,86,93,75,60,96,70,95,65,66,39,44,50,49,51,65,55,55,58,65,85,72,58,48", "endOffsets": "7728,7765,7811,7866,7937,7997,8042,8103,8157,8223,8279,8360,8437,8508,8631,8720,8844,8977,9114,9175,9237,9308,9384,9437,9490,9614,9663,9748,9799,9850,9893,9935,9977,10069,10142,10216,10292,10352,10417,10502,10619,10664,10748,10837,10925,10981,11039,11090,11192,11272,11359,11453,11529,11590,11687,11758,11854,11920,11987,12027,12072,12123,12173,12225,12291,12347,12403,12462,12528,12614,12687,12746,12795"}}]}, {"outputFile": "com.ainative.mountainsurvival.app-mergeReleaseResources-22:/values-en/values-en.xml", "map": [{"source": "E:\\Ai\\AiCode\\game\\MountainSurvival\\code\\app\\src\\main\\res\\values-en\\strings.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,141,-1,-1,-1,127,128,126,124,123,131,130,121,120,135,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,144,145,-1,149,-1,148,146,147,-1,-1,-1,138,-1,-1,-1,-1,-1,-1,-1,140,139,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,116,117,115,104,105,103,134,108,109,107,112,113,111,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,-1,-1,-1,4,4,4,4,4,4,4,4,4,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,4,-1,4,-1,4,4,4,-1,-1,-1,4,-1,-1,-1,-1,-1,-1,-1,4,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,4,4,4,4,4,4,4,4,4,4,4,4,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,13665,-1,-1,-1,12403,12729,12297,11813,11704,12941,12846,11193,11084,13266,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,13769,13891,-1,14305,-1,14193,14005,14104,-1,-1,-1,13484,-1,-1,-1,-1,-1,-1,-1,13593,13525,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,10569,10948,10442,8233,8881,8081,13051,9152,9596,9015,9875,10290,9719,-1", "endColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,58,-1,-1,-1,325,115,105,482,108,84,94,509,108,182,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,121,113,-1,155,-1,111,98,88,-1,-1,-1,40,-1,-1,-1,-1,-1,-1,-1,71,67,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,378,109,126,647,132,151,214,443,121,136,414,150,155,-1", "endOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,13719,-1,-1,-1,12724,12840,12398,12291,11808,13021,12936,11698,11188,13444,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,13886,14000,-1,14456,-1,14300,14099,14188,-1,-1,-1,13520,-1,-1,-1,-1,-1,-1,-1,13660,13588,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,10943,11053,10564,8876,9009,8228,13261,9591,9713,9147,10285,10436,9870,-1"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,160,224,298,354,402,508,572,626,683,760,858,934,1030,1090,1176,1235,1274,1327,1383,1709,1825,1931,2414,2523,2608,2703,3213,3322,3505,3551,3729,3804,3858,3928,3976,4027,4070,4286,4348,4456,4739,4824,4899,4978,5060,5143,5189,5240,5293,5362,5423,5485,5543,5617,5666,5713,5826,5948,6062,6120,6276,6338,6450,6549,6638,6839,7029,7122,7163,7218,8736,8797,8883,8952,9034,9144,9216,9284,9338,9402,9521,9609,9679,9799,9932,10079,10147,10227,10285,10337,10422,10498,10547,10597,10666,15854,15904,16283,16393,16520,17168,17301,17453,17668,18112,18234,18371,18786,18937,19093", "endColumns": "104,63,73,55,47,105,63,53,56,76,97,75,95,59,85,58,38,52,55,325,115,105,482,108,84,94,509,108,182,45,177,74,53,69,47,50,42,215,61,107,282,84,74,78,81,82,45,50,52,68,60,61,57,73,48,46,112,121,113,57,155,61,111,98,88,200,189,92,40,54,1517,60,85,68,81,109,71,67,53,63,118,87,69,119,132,146,67,79,57,51,84,75,48,49,68,77,49,378,109,126,647,132,151,214,443,121,136,414,150,155,46", "endOffsets": "155,219,293,349,397,503,567,621,678,755,853,929,1025,1085,1171,1230,1269,1322,1378,1704,1820,1926,2409,2518,2603,2698,3208,3317,3500,3546,3724,3799,3853,3923,3971,4022,4065,4281,4343,4451,4734,4819,4894,4973,5055,5138,5184,5235,5288,5357,5418,5480,5538,5612,5661,5708,5821,5943,6057,6115,6271,6333,6445,6544,6633,6834,7024,7117,7158,7213,8731,8792,8878,8947,9029,9139,9211,9279,9333,9397,9516,9604,9674,9794,9927,10074,10142,10222,10280,10332,10417,10493,10542,10592,10661,10739,15899,16278,16388,16515,17163,17296,17448,17663,18107,18229,18366,18781,18932,19088,19135"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\077c69690200a4c0c0ddf0630ba2f6e7\\transformed\\open_ad_sdk_6.8.4.0\\res\\values-en\\values-en.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "10744,10787,10824,10870,10925,10996,11056,11101,11162,11216,11282,11338,11419,11496,11567,11690,11779,11903,12036,12173,12234,12296,12367,12443,12496,12549,12673,12722,12807,12858,12909,12952,12994,13036,13128,13201,13275,13351,13411,13476,13561,13678,13723,13807,13896,13984,14040,14098,14149,14251,14331,14418,14512,14588,14649,14746,14817,14913,14979,15046,15086,15131,15182,15232,15284,15350,15406,15462,15521,15587,15673,15746,15805", "endColumns": "42,36,45,54,70,59,44,60,53,65,55,80,76,70,122,88,123,132,136,60,61,70,75,52,52,123,48,84,50,50,42,41,41,91,72,73,75,59,64,84,116,44,83,88,87,55,57,50,101,79,86,93,75,60,96,70,95,65,66,39,44,50,49,51,65,55,55,58,65,85,72,58,48", "endOffsets": "10782,10819,10865,10920,10991,11051,11096,11157,11211,11277,11333,11414,11491,11562,11685,11774,11898,12031,12168,12229,12291,12362,12438,12491,12544,12668,12717,12802,12853,12904,12947,12989,13031,13123,13196,13270,13346,13406,13471,13556,13673,13718,13802,13891,13979,14035,14093,14144,14246,14326,14413,14507,14583,14644,14741,14812,14908,14974,15041,15081,15126,15177,15227,15279,15345,15401,15457,15516,15582,15668,15741,15800,15849"}}]}]}