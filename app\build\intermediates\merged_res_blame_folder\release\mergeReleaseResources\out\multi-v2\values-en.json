{"logs": [{"outputFile": "com.ainative.mountainsurvival.app-mergeReleaseResources-22:/values-en/values-en.xml", "map": [{"source": "E:\\Ai\\AiCode\\game\\MountainSurvival\\code\\app\\src\\main\\res\\values-en\\strings.xml", "from": {"startLines": "1,2,6,11,9,19,17,15,16,18,8,7,26,24,27,25,23,22,28,5,12,10", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "16,70,204,444,348,1083,968,838,907,1021,299,252,2918,1314,2973,2832,1245,1163,3034,154,727,394", "endColumns": "53,56,47,282,45,50,52,68,60,61,48,46,54,1517,60,85,68,81,109,49,77,49", "endOffsets": "65,122,247,722,389,1129,1016,902,963,1078,343,294,2968,2827,3029,2913,1309,1240,3139,199,800,439"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,96", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,109,166,214,497,543,594,647,716,777,839,888,935,990,2508,2569,2655,2724,2806,2916,2966,8154", "endColumns": "53,56,47,282,45,50,52,68,60,61,48,46,54,1517,60,85,68,81,109,49,77,49", "endOffsets": "104,161,209,492,538,589,642,711,772,834,883,930,985,2503,2564,2650,2719,2801,2911,2961,3039,8199"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\077c69690200a4c0c0ddf0630ba2f6e7\\transformed\\open_ad_sdk_6.8.4.0\\res\\values-en\\values-en.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,98,135,181,236,307,367,412,473,527,593,649,730,807,878,1001,1090,1214,1347,1484,1545,1607,1678,1754,1807,1860,1984,2033,2118,2169,2220,2263,2305,2347,2439,2512,2586,2662,2722,2787,2872,2989,3034,3118,3207,3295,3351,3409,3460,3562,3642,3729,3823,3899,3960,4057,4128,4224,4290,4357,4397,4442,4493,4543,4595,4661,4717,4773,4832,4898,4984,5057,5116", "endColumns": "42,36,45,54,70,59,44,60,53,65,55,80,76,70,122,88,123,132,136,60,61,70,75,52,52,123,48,84,50,50,42,41,41,91,72,73,75,59,64,84,116,44,83,88,87,55,57,50,101,79,86,93,75,60,96,70,95,65,66,39,44,50,49,51,65,55,55,58,65,85,72,58,48", "endOffsets": "93,130,176,231,302,362,407,468,522,588,644,725,802,873,996,1085,1209,1342,1479,1540,1602,1673,1749,1802,1855,1979,2028,2113,2164,2215,2258,2300,2342,2434,2507,2581,2657,2717,2782,2867,2984,3029,3113,3202,3290,3346,3404,3455,3557,3637,3724,3818,3894,3955,4052,4123,4219,4285,4352,4392,4437,4488,4538,4590,4656,4712,4768,4827,4893,4979,5052,5111,5160"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3044,3087,3124,3170,3225,3296,3356,3401,3462,3516,3582,3638,3719,3796,3867,3990,4079,4203,4336,4473,4534,4596,4667,4743,4796,4849,4973,5022,5107,5158,5209,5252,5294,5336,5428,5501,5575,5651,5711,5776,5861,5978,6023,6107,6196,6284,6340,6398,6449,6551,6631,6718,6812,6888,6949,7046,7117,7213,7279,7346,7386,7431,7482,7532,7584,7650,7706,7762,7821,7887,7973,8046,8105", "endColumns": "42,36,45,54,70,59,44,60,53,65,55,80,76,70,122,88,123,132,136,60,61,70,75,52,52,123,48,84,50,50,42,41,41,91,72,73,75,59,64,84,116,44,83,88,87,55,57,50,101,79,86,93,75,60,96,70,95,65,66,39,44,50,49,51,65,55,55,58,65,85,72,58,48", "endOffsets": "3082,3119,3165,3220,3291,3351,3396,3457,3511,3577,3633,3714,3791,3862,3985,4074,4198,4331,4468,4529,4591,4662,4738,4791,4844,4968,5017,5102,5153,5204,5247,5289,5331,5423,5496,5570,5646,5706,5771,5856,5973,6018,6102,6191,6279,6335,6393,6444,6546,6626,6713,6807,6883,6944,7041,7112,7208,7274,7341,7381,7426,7477,7527,7579,7645,7701,7757,7816,7882,7968,8041,8100,8149"}}]}]}