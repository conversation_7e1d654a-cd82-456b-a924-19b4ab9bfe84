{"logs": [{"outputFile": "com.ainative.mountainsurvival.app-mergeReleaseResources-22:/values-ka/values-ka.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a3ae461f31008f99a87e29f3c99a3c83\\transformed\\appcompat-v7-28.0.0\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,318,429,517,622,735,819,901,992,1085,1180,1276,1377,1470,1565,1659,1750,1841,1924,2037,2144,2242,2355,2459,2563,2720,2818", "endColumns": "108,103,110,87,104,112,83,81,90,92,94,95,100,92,94,93,90,90,82,112,106,97,112,103,103,156,97,80", "endOffsets": "209,313,424,512,617,730,814,896,987,1080,1175,1271,1372,1465,1560,1654,1745,1836,1919,2032,2139,2237,2350,2454,2558,2715,2813,2894"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\daefd50eed0506e326ae247974906cd2\\transformed\\support-compat-28.0.0\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2899", "endColumns": "100", "endOffsets": "2995"}}]}]}