{"logs": [{"outputFile": "com.ainative.mountainsurvival.app-mergeReleaseResources-22:/values-zh-rTW/values-zh-rTW.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a3ae461f31008f99a87e29f3c99a3c83\\transformed\\appcompat-v7-28.0.0\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,293,393,475,572,680,756,832,924,1018,1116,1212,1308,1402,1498,1590,1682,1774,1852,1948,2044,2139,2236,2331,2431,2581,2675", "endColumns": "94,92,99,81,96,107,75,75,91,93,97,95,95,93,95,91,91,91,77,95,95,94,96,94,99,149,93,77", "endOffsets": "195,288,388,470,567,675,751,827,919,1013,1111,1207,1303,1397,1493,1585,1677,1769,1847,1943,2039,2134,2231,2326,2426,2576,2670,2748"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,48", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,293,393,475,572,680,756,832,924,1018,1116,1212,1308,1402,1498,1590,1682,1774,1852,1948,2044,2139,2236,2331,2431,2581,4245", "endColumns": "94,92,99,81,96,107,75,75,91,93,97,95,95,93,95,91,91,91,77,95,95,94,96,94,99,149,93,77", "endOffsets": "195,288,388,470,567,675,751,827,919,1013,1111,1207,1303,1397,1493,1585,1677,1769,1847,1943,2039,2134,2231,2326,2426,2576,2670,4318"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\daefd50eed0506e326ae247974906cd2\\transformed\\support-compat-28.0.0\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "50", "startColumns": "4", "startOffsets": "4367", "endColumns": "100", "endOffsets": "4463"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\077c69690200a4c0c0ddf0630ba2f6e7\\transformed\\open_ad_sdk_6.8.4.0\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,98,135,181,236,297,353,394,443,494,554,602,665,729,788,860,933,1001,1074,1155,1212,1270,1328,1391,1440,1485,1550,1593,1651,1695,1742,1785,1839,1905,1966,2031,2086,2143,2221,2283,2322,2401,2474,2555,2605,2661,2720,2758,2801,2850,2900,2952,3008,3061,3115,3170,3229,3290,3351,3403", "endColumns": "42,36,45,54,60,55,40,48,50,59,47,62,63,58,71,72,67,72,80,56,57,57,62,48,44,64,42,57,43,46,42,53,65,60,64,54,56,77,61,38,78,72,80,49,55,58,37,42,48,49,51,55,52,53,54,58,60,60,51,45", "endOffsets": "93,130,176,231,292,348,389,438,489,549,597,660,724,783,855,928,996,1069,1150,1207,1265,1323,1386,1435,1480,1545,1588,1646,1690,1737,1780,1834,1900,1961,2026,2081,2138,2216,2278,2317,2396,2469,2550,2600,2656,2715,2753,2796,2845,2895,2947,3003,3056,3110,3165,3224,3285,3346,3398,3444"}, "to": {"startLines": "52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4529,4572,4609,4655,4710,4771,4827,4868,4917,4968,5028,5076,5139,5203,5262,5334,5407,5475,5548,5629,5686,5744,5802,5865,5914,5959,6024,6067,6125,6169,6216,6259,6313,6379,6440,6505,6560,6617,6695,6757,6796,6875,6948,7029,7079,7135,7194,7232,7275,7324,7374,7426,7482,7535,7589,7644,7703,7764,7825,7877", "endColumns": "42,36,45,54,60,55,40,48,50,59,47,62,63,58,71,72,67,72,80,56,57,57,62,48,44,64,42,57,43,46,42,53,65,60,64,54,56,77,61,38,78,72,80,49,55,58,37,42,48,49,51,55,52,53,54,58,60,60,51,45", "endOffsets": "4567,4604,4650,4705,4766,4822,4863,4912,4963,5023,5071,5134,5198,5257,5329,5402,5470,5543,5624,5681,5739,5797,5860,5909,5954,6019,6062,6120,6164,6211,6254,6308,6374,6435,6500,6555,6612,6690,6752,6791,6870,6943,7024,7074,7130,7189,7227,7270,7319,7369,7421,7477,7530,7584,7639,7698,7759,7820,7872,7918"}}, {"source": "E:\\Ai\\AiCode\\game\\MountainSurvival\\code\\app\\src\\main\\res\\values-zh-rTW\\strings.xml", "from": {"startLines": "1,2,6,11,9,19,17,15,16,18,8,7,26,24,27,25,23,22,28,5,12,10", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "16,58,178,391,306,819,704,585,643,757,263,221,1577,1002,1629,1513,945,887,1685,134,505,346", "endColumns": "41,56,42,113,39,46,52,57,60,61,42,41,51,510,55,63,56,57,109,43,60,44", "endOffsets": "53,110,216,500,341,861,752,638,699,814,301,258,1624,1508,1680,1572,997,940,1790,173,561,386"}, "to": {"startLines": "29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,49,51,112", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2675,2717,2774,2817,2931,2971,3018,3071,3129,3190,3252,3295,3337,3389,3900,3956,4020,4077,4135,4323,4468,7923", "endColumns": "41,56,42,113,39,46,52,57,60,61,42,41,51,510,55,63,56,57,109,43,60,44", "endOffsets": "2712,2769,2812,2926,2966,3013,3066,3124,3185,3247,3290,3332,3384,3895,3951,4015,4072,4130,4240,4362,4524,7963"}}]}]}