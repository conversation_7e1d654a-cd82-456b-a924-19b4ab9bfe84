{"logs": [{"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-mergeReleaseResources-22:\\values-zh-rTW\\values-zh-rTW.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a3ae461f31008f99a87e29f3c99a3c83\\transformed\\appcompat-v7-28.0.0\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,293,393,475,572,680,756,832,924,1018,1116,1212,1308,1402,1498,1590,1682,1774,1852,1948,2044,2139,2236,2331,2431,2581,2675", "endColumns": "94,92,99,81,96,107,75,75,91,93,97,95,95,93,95,91,91,91,77,95,95,94,96,94,99,149,93,77", "endOffsets": "195,288,388,470,567,675,751,827,919,1013,1111,1207,1303,1397,1493,1585,1677,1769,1847,1943,2039,2134,2231,2326,2426,2576,2670,2748"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,48", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,293,393,475,572,680,756,832,924,1018,1116,1212,1308,1402,1498,1590,1682,1774,1852,1948,2044,2139,2236,2331,2431,2581,4245", "endColumns": "94,92,99,81,96,107,75,75,91,93,97,95,95,93,95,91,91,91,77,95,95,94,96,94,99,149,93,77", "endOffsets": "195,288,388,470,567,675,751,827,919,1013,1111,1207,1303,1397,1493,1585,1677,1769,1847,1943,2039,2134,2231,2326,2426,2576,2670,4318"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\daefd50eed0506e326ae247974906cd2\\transformed\\support-compat-28.0.0\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "50", "startColumns": "4", "startOffsets": "4367", "endColumns": "100", "endOffsets": "4463"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\077c69690200a4c0c0ddf0630ba2f6e7\\transformed\\open_ad_sdk_6.8.4.0\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,98,135,181,236,297,353,394,443,494,554,602,665,729,788,860,933,1001,1074,1155,1212,1270,1328,1391,1440,1485,1550,1593,1651,1695,1742,1785,1839,1905,1966,2031,2086,2143,2221,2283,2322,2401,2474,2555,2605,2661,2720,2758,2801,2850,2900,2952,3008,3061,3115,3170,3229,3290,3351,3403", "endColumns": "42,36,45,54,60,55,40,48,50,59,47,62,63,58,71,72,67,72,80,56,57,57,62,48,44,64,42,57,43,46,42,53,65,60,64,54,56,77,61,38,78,72,80,49,55,58,37,42,48,49,51,55,52,53,54,58,60,60,51,45", "endOffsets": "93,130,176,231,292,348,389,438,489,549,597,660,724,783,855,928,996,1069,1150,1207,1265,1323,1386,1435,1480,1545,1588,1646,1690,1737,1780,1834,1900,1961,2026,2081,2138,2216,2278,2317,2396,2469,2550,2600,2656,2715,2753,2796,2845,2895,2947,3003,3056,3110,3165,3224,3285,3346,3398,3444"}, "to": {"startLines": "52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4529,4572,4609,4655,4710,4771,4827,4868,4917,4968,5028,5076,5139,5203,5262,5334,5407,5475,5548,5629,5686,5744,5802,5865,5914,5959,6024,6067,6125,6169,6216,6259,6313,6379,6440,6505,6560,6617,6695,6757,6796,6875,6948,7029,7079,7135,7194,7232,7275,7324,7374,7426,7482,7535,7589,7644,7703,7764,7825,7877", "endColumns": "42,36,45,54,60,55,40,48,50,59,47,62,63,58,71,72,67,72,80,56,57,57,62,48,44,64,42,57,43,46,42,53,65,60,64,54,56,77,61,38,78,72,80,49,55,58,37,42,48,49,51,55,52,53,54,58,60,60,51,45", "endOffsets": "4567,4604,4650,4705,4766,4822,4863,4912,4963,5023,5071,5134,5198,5257,5329,5402,5470,5543,5624,5681,5739,5797,5860,5909,5954,6019,6062,6120,6164,6211,6254,6308,6374,6435,6500,6555,6612,6690,6752,6791,6870,6943,7024,7074,7130,7189,7227,7270,7319,7369,7421,7477,7530,7584,7639,7698,7759,7820,7872,7918"}}, {"source": "E:\\Ai\\AiCode\\game\\MountainSurvival\\code\\app\\src\\main\\res\\values-zh-rTW\\strings.xml", "from": {"startLines": "1,2,6,11,9,19,17,15,16,18,8,7,26,24,27,25,23,22,28,5,12,10", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "16,58,178,391,306,819,704,585,643,757,263,221,1577,1002,1629,1513,945,887,1685,134,505,346", "endColumns": "41,56,42,113,39,46,52,57,60,61,42,41,51,510,55,63,56,57,109,43,60,44", "endOffsets": "53,110,216,500,341,861,752,638,699,814,301,258,1624,1508,1680,1572,997,940,1790,173,561,386"}, "to": {"startLines": "29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,49,51,112", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2675,2717,2774,2817,2931,2971,3018,3071,3129,3190,3252,3295,3337,3389,3900,3956,4020,4077,4135,4323,4468,7923", "endColumns": "41,56,42,113,39,46,52,57,60,61,42,41,51,510,55,63,56,57,109,43,60,44", "endOffsets": "2712,2769,2812,2926,2966,3013,3066,3124,3185,3247,3290,3332,3384,3895,3951,4015,4072,4130,4240,4362,4524,7963"}}]}, {"outputFile": "com.ainative.mountainsurvival.app-mergeReleaseResources-22:/values-zh-rTW/values-zh-rTW.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a3ae461f31008f99a87e29f3c99a3c83\\transformed\\appcompat-v7-28.0.0\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,100", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,293,393,475,572,680,756,832,924,1018,1116,1212,1308,1402,1498,1590,1682,1774,1852,1948,2044,2139,2236,2331,2431,2581,7400", "endColumns": "94,92,99,81,96,107,75,75,91,93,97,95,95,93,95,91,91,91,77,95,95,94,96,94,99,149,93,77", "endOffsets": "195,288,388,470,567,675,751,827,919,1013,1111,1207,1303,1397,1493,1585,1677,1769,1847,1943,2039,2134,2231,2326,2426,2576,2670,7473"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\daefd50eed0506e326ae247974906cd2\\transformed\\support-compat-28.0.0\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "104", "startColumns": "4", "startOffsets": "7618", "endColumns": "100", "endOffsets": "7714"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\077c69690200a4c0c0ddf0630ba2f6e7\\transformed\\open_ad_sdk_6.8.4.0\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7838,7881,7918,7964,8019,8080,8136,8177,8226,8277,8337,8385,8448,8512,8571,8643,8716,8784,8857,8938,8995,9053,9111,9174,9223,9268,9333,9376,9434,9478,9525,9568,9622,9688,9749,9814,9869,9926,10004,10066,10105,10184,10257,10338,10388,10444,10503,10541,10584,10633,10683,10735,10791,10844,10898,10953,11012,11073,11134,11186", "endColumns": "42,36,45,54,60,55,40,48,50,59,47,62,63,58,71,72,67,72,80,56,57,57,62,48,44,64,42,57,43,46,42,53,65,60,64,54,56,77,61,38,78,72,80,49,55,58,37,42,48,49,51,55,52,53,54,58,60,60,51,45", "endOffsets": "7876,7913,7959,8014,8075,8131,8172,8221,8272,8332,8380,8443,8507,8566,8638,8711,8779,8852,8933,8990,9048,9106,9169,9218,9263,9328,9371,9429,9473,9520,9563,9617,9683,9744,9809,9864,9921,9999,10061,10100,10179,10252,10333,10383,10439,10498,10536,10579,10628,10678,10730,10786,10839,10893,10948,11007,11068,11129,11181,11227"}}, {"source": "E:\\Ai\\AiCode\\game\\MountainSurvival\\code\\app\\src\\main\\res\\values-zh-rTW\\strings.xml", "from": {"startLines": "84,83,80,79,97,86,85,-1,-1,47,67,46,68,100,43,94,34,93,56,58,66,33,32,-1,41,42,55,54,31,-1,69,50,48,51,49,-1,-1,-1,-1,-1,-1,99,70,-1,-1,73,62,65,63,64,35,-1,-1,-1,-1,-1,-1,-1,75,98,87,82,81,88,76,92,91,90,89,57,59,74,40,-1,38,-1,-1,39", "startColumns": "4,4,4,4,4,4,4,-1,-1,4,4,4,4,4,4,4,4,4,4,4,4,4,4,-1,4,4,4,4,4,-1,4,4,4,4,4,-1,-1,-1,-1,-1,-1,4,4,-1,-1,4,4,4,4,4,4,-1,-1,-1,-1,-1,-1,-1,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,-1,4,-1,-1,4", "startOffsets": "4390,4334,4144,4094,5109,4521,4465,-1,-1,2478,3559,2425,3622,5251,2346,5051,1986,5004,2953,3040,3502,1940,1884,-1,2260,2305,2842,2791,1816,-1,3684,2652,2530,2712,2589,-1,-1,-1,-1,-1,-1,5203,3744,-1,-1,3821,3205,3452,3256,3354,2033,-1,-1,-1,-1,-1,-1,-1,3948,5152,4598,4264,4205,4682,3995,4928,4873,4815,4768,2995,3125,3896,2216,-1,2115,-1,-1,2173", "endColumns": "74,55,60,49,42,76,55,-1,-1,51,62,52,61,48,59,38,46,46,41,84,56,45,55,-1,44,40,110,50,67,-1,59,59,58,59,62,-1,-1,-1,-1,-1,-1,47,58,-1,-1,74,50,49,97,97,62,-1,-1,-1,-1,-1,-1,-1,46,50,83,69,58,85,79,75,54,57,46,44,60,51,43,-1,57,-1,-1,42", "endOffsets": "4460,4385,4200,4139,5147,4593,4516,-1,-1,2525,3617,2473,3679,5295,2401,5085,2028,5046,2990,3120,3554,1981,1935,-1,2300,2341,2948,2837,1879,-1,3739,2707,2584,2767,2647,-1,-1,-1,-1,-1,-1,5246,3798,-1,-1,3891,3251,3497,3349,3447,2091,-1,-1,-1,-1,-1,-1,-1,3990,5198,4677,4329,4259,4763,4070,4999,4923,4868,4810,3035,3181,3943,2255,-1,2168,-1,-1,2211"}, "to": {"startLines": "29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,101,102,103,105,106,167,168", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2675,2750,2806,2867,2917,2960,3037,3093,3135,3192,3244,3307,3360,3422,3471,3531,3570,3617,3664,3706,3791,3848,3894,3950,3993,4038,4079,4190,4241,4309,4423,4483,4543,4602,4662,4725,4765,4812,4865,4923,4984,5046,5094,5153,5196,5238,5313,5364,5414,5512,5610,5673,5725,6236,6292,6356,6413,6471,6581,6628,6679,6763,6833,6892,6978,7058,7134,7189,7247,7294,7339,7478,7530,7574,7719,7777,11232,11277", "endColumns": "74,55,60,49,42,76,55,41,56,51,62,52,61,48,59,38,46,46,41,84,56,45,55,42,44,40,110,50,67,113,59,59,58,59,62,39,46,52,57,60,61,47,58,42,41,74,50,49,97,97,62,51,510,55,63,56,57,109,46,50,83,69,58,85,79,75,54,57,46,44,60,51,43,43,57,60,44,42", "endOffsets": "2745,2801,2862,2912,2955,3032,3088,3130,3187,3239,3302,3355,3417,3466,3526,3565,3612,3659,3701,3786,3843,3889,3945,3988,4033,4074,4185,4236,4304,4418,4478,4538,4597,4657,4720,4760,4807,4860,4918,4979,5041,5089,5148,5191,5233,5308,5359,5409,5507,5605,5668,5720,6231,6287,6351,6408,6466,6576,6623,6674,6758,6828,6887,6973,7053,7129,7184,7242,7289,7334,7395,7525,7569,7613,7772,7833,11272,11315"}}]}]}