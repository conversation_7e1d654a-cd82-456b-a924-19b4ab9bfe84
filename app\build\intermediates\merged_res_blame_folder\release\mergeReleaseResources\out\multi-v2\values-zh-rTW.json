{"logs": [{"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-mergeReleaseResources-22:\\values-zh-rTW\\values-zh-rTW.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a3ae461f31008f99a87e29f3c99a3c83\\transformed\\appcompat-v7-28.0.0\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,100", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,293,393,475,572,680,756,832,924,1018,1116,1212,1308,1402,1498,1590,1682,1774,1852,1948,2044,2139,2236,2331,2431,2581,7400", "endColumns": "94,92,99,81,96,107,75,75,91,93,97,95,95,93,95,91,91,91,77,95,95,94,96,94,99,149,93,77", "endOffsets": "195,288,388,470,567,675,751,827,919,1013,1111,1207,1303,1397,1493,1585,1677,1769,1847,1943,2039,2134,2231,2326,2426,2576,2670,7473"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\daefd50eed0506e326ae247974906cd2\\transformed\\support-compat-28.0.0\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "104", "startColumns": "4", "startOffsets": "7618", "endColumns": "100", "endOffsets": "7714"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\077c69690200a4c0c0ddf0630ba2f6e7\\transformed\\open_ad_sdk_6.8.4.0\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7838,7881,7918,7964,8019,8080,8136,8177,8226,8277,8337,8385,8448,8512,8571,8643,8716,8784,8857,8938,8995,9053,9111,9174,9223,9268,9333,9376,9434,9478,9525,9568,9622,9688,9749,9814,9869,9926,10004,10066,10105,10184,10257,10338,10388,10444,10503,10541,10584,10633,10683,10735,10791,10844,10898,10953,11012,11073,11134,11186", "endColumns": "42,36,45,54,60,55,40,48,50,59,47,62,63,58,71,72,67,72,80,56,57,57,62,48,44,64,42,57,43,46,42,53,65,60,64,54,56,77,61,38,78,72,80,49,55,58,37,42,48,49,51,55,52,53,54,58,60,60,51,45", "endOffsets": "7876,7913,7959,8014,8075,8131,8172,8221,8272,8332,8380,8443,8507,8566,8638,8711,8779,8852,8933,8990,9048,9106,9169,9218,9263,9328,9371,9429,9473,9520,9563,9617,9683,9744,9809,9864,9921,9999,10061,10100,10179,10252,10333,10383,10439,10498,10536,10579,10628,10678,10730,10786,10839,10893,10948,11007,11068,11129,11181,11227"}}, {"source": "E:\\Ai\\AiCode\\game\\MountainSurvival\\code\\app\\src\\main\\res\\values-zh-rTW\\strings.xml", "from": {"startLines": "84,83,80,79,97,86,85,-1,-1,47,67,46,68,100,43,94,34,93,56,58,66,33,32,-1,41,42,55,54,31,-1,69,50,48,51,49,-1,-1,-1,-1,-1,-1,99,70,-1,-1,73,62,65,63,64,35,-1,-1,-1,-1,-1,-1,-1,75,98,87,82,81,88,76,92,91,90,89,57,59,74,40,-1,38,-1,-1,39", "startColumns": "4,4,4,4,4,4,4,-1,-1,4,4,4,4,4,4,4,4,4,4,4,4,4,4,-1,4,4,4,4,4,-1,4,4,4,4,4,-1,-1,-1,-1,-1,-1,4,4,-1,-1,4,4,4,4,4,4,-1,-1,-1,-1,-1,-1,-1,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,-1,4,-1,-1,4", "startOffsets": "4390,4334,4144,4094,5109,4521,4465,-1,-1,2478,3559,2425,3622,5251,2346,5051,1986,5004,2953,3040,3502,1940,1884,-1,2260,2305,2842,2791,1816,-1,3684,2652,2530,2712,2589,-1,-1,-1,-1,-1,-1,5203,3744,-1,-1,3821,3205,3452,3256,3354,2033,-1,-1,-1,-1,-1,-1,-1,3948,5152,4598,4264,4205,4682,3995,4928,4873,4815,4768,2995,3125,3896,2216,-1,2115,-1,-1,2173", "endColumns": "74,55,60,49,42,76,55,-1,-1,51,62,52,61,48,59,38,46,46,41,84,56,45,55,-1,44,40,110,50,67,-1,59,59,58,59,62,-1,-1,-1,-1,-1,-1,47,58,-1,-1,74,50,49,97,97,62,-1,-1,-1,-1,-1,-1,-1,46,50,83,69,58,85,79,75,54,57,46,44,60,51,43,-1,57,-1,-1,42", "endOffsets": "4460,4385,4200,4139,5147,4593,4516,-1,-1,2525,3617,2473,3679,5295,2401,5085,2028,5046,2990,3120,3554,1981,1935,-1,2300,2341,2948,2837,1879,-1,3739,2707,2584,2767,2647,-1,-1,-1,-1,-1,-1,5246,3798,-1,-1,3891,3251,3497,3349,3447,2091,-1,-1,-1,-1,-1,-1,-1,3990,5198,4677,4329,4259,4763,4070,4999,4923,4868,4810,3035,3181,3943,2255,-1,2168,-1,-1,2211"}, "to": {"startLines": "29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,101,102,103,105,106,167,168", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2675,2750,2806,2867,2917,2960,3037,3093,3135,3192,3244,3307,3360,3422,3471,3531,3570,3617,3664,3706,3791,3848,3894,3950,3993,4038,4079,4190,4241,4309,4423,4483,4543,4602,4662,4725,4765,4812,4865,4923,4984,5046,5094,5153,5196,5238,5313,5364,5414,5512,5610,5673,5725,6236,6292,6356,6413,6471,6581,6628,6679,6763,6833,6892,6978,7058,7134,7189,7247,7294,7339,7478,7530,7574,7719,7777,11232,11277", "endColumns": "74,55,60,49,42,76,55,41,56,51,62,52,61,48,59,38,46,46,41,84,56,45,55,42,44,40,110,50,67,113,59,59,58,59,62,39,46,52,57,60,61,47,58,42,41,74,50,49,97,97,62,51,510,55,63,56,57,109,46,50,83,69,58,85,79,75,54,57,46,44,60,51,43,43,57,60,44,42", "endOffsets": "2745,2801,2862,2912,2955,3032,3088,3130,3187,3239,3302,3355,3417,3466,3526,3565,3612,3659,3701,3786,3843,3889,3945,3988,4033,4074,4185,4236,4304,4418,4478,4538,4597,4657,4720,4760,4807,4860,4918,4979,5041,5089,5148,5191,5233,5308,5359,5409,5507,5605,5668,5720,6231,6287,6351,6408,6466,6576,6623,6674,6758,6828,6887,6973,7053,7129,7184,7242,7289,7334,7395,7525,7569,7613,7772,7833,11272,11315"}}]}, {"outputFile": "com.ainative.mountainsurvival.app-mergeReleaseResources-22:/values-zh-rTW/values-zh-rTW.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a3ae461f31008f99a87e29f3c99a3c83\\transformed\\appcompat-v7-28.0.0\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,120", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,293,393,475,572,680,756,832,924,1018,1116,1212,1308,1402,1498,1590,1682,1774,1852,1948,2044,2139,2236,2331,2431,2581,9091", "endColumns": "94,92,99,81,96,107,75,75,91,93,97,95,95,93,95,91,91,91,77,95,95,94,96,94,99,149,93,77", "endOffsets": "195,288,388,470,567,675,751,827,919,1013,1111,1207,1303,1397,1493,1585,1677,1769,1847,1943,2039,2134,2231,2326,2426,2576,2670,9164"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\daefd50eed0506e326ae247974906cd2\\transformed\\support-compat-28.0.0\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "124", "startColumns": "4", "startOffsets": "9309", "endColumns": "100", "endOffsets": "9405"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\077c69690200a4c0c0ddf0630ba2f6e7\\transformed\\open_ad_sdk_6.8.4.0\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "9529,9572,9609,9655,9710,9771,9827,9868,9917,9968,10028,10076,10139,10203,10262,10334,10407,10475,10548,10629,10686,10744,10802,10865,10914,10959,11024,11067,11125,11169,11216,11259,11313,11379,11440,11505,11560,11617,11695,11757,11796,11875,11948,12029,12079,12135,12194,12232,12275,12324,12374,12426,12482,12535,12589,12644,12703,12764,12825,12877", "endColumns": "42,36,45,54,60,55,40,48,50,59,47,62,63,58,71,72,67,72,80,56,57,57,62,48,44,64,42,57,43,46,42,53,65,60,64,54,56,77,61,38,78,72,80,49,55,58,37,42,48,49,51,55,52,53,54,58,60,60,51,45", "endOffsets": "9567,9604,9650,9705,9766,9822,9863,9912,9963,10023,10071,10134,10198,10257,10329,10402,10470,10543,10624,10681,10739,10797,10860,10909,10954,11019,11062,11120,11164,11211,11254,11308,11374,11435,11500,11555,11612,11690,11752,11791,11870,11943,12024,12074,12130,12189,12227,12270,12319,12369,12421,12477,12530,12584,12639,12698,12759,12820,12872,12918"}}, {"source": "E:\\Ai\\AiCode\\game\\MountainSurvival\\code\\app\\src\\main\\res\\values-zh-rTW\\strings.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,141,-1,-1,-1,127,128,126,124,123,131,130,121,120,135,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,144,145,-1,149,-1,148,146,147,-1,-1,-1,138,-1,-1,-1,-1,-1,-1,-1,140,139,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,116,117,115,104,105,103,134,108,109,107,112,113,111,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,-1,-1,-1,4,4,4,4,4,4,4,4,4,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,4,-1,4,-1,4,4,4,-1,-1,-1,4,-1,-1,-1,-1,-1,-1,-1,4,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,4,4,4,4,4,4,4,4,4,4,4,4,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,8221,-1,-1,-1,7438,7592,7367,7157,7083,7727,7658,6879,6806,7934,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,8292,8363,-1,8616,-1,8549,8430,8491,-1,-1,-1,8068,-1,-1,-1,-1,-1,-1,-1,8164,8109,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,6534,6711,6447,5406,5659,5319,7806,5827,6013,5742,6181,6359,6091,-1", "endColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,47,-1,-1,-1,153,64,70,208,73,59,68,202,72,111,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,70,66,-1,75,-1,66,60,57,-1,-1,-1,40,-1,-1,-1,-1,-1,-1,-1,56,54,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,176,75,86,252,81,86,127,185,76,84,177,86,89,-1", "endOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,8264,-1,-1,-1,7587,7652,7433,7361,7152,7782,7722,7077,6874,8041,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,8358,8425,-1,8687,-1,8611,8486,8544,-1,-1,-1,8104,-1,-1,-1,-1,-1,-1,-1,8216,8159,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,6706,6782,6529,5654,5736,5401,7929,6008,6085,5822,6354,6441,6176,-1"}, "to": {"startLines": "29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,121,122,123,125,126,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2675,2750,2806,2867,2917,2960,3037,3093,3135,3192,3244,3307,3360,3422,3471,3531,3579,3618,3665,3712,3866,3931,4002,4211,4285,4345,4414,4617,4690,4802,4844,4929,4986,5032,5088,5131,5176,5217,5328,5379,5447,5561,5621,5681,5740,5800,5863,5903,5950,6003,6061,6122,6184,6232,6291,6334,6376,6451,6522,6589,6640,6716,6766,6833,6894,6952,7050,7148,7211,7252,7304,7815,7871,7935,7992,8050,8160,8217,8272,8319,8370,8454,8524,8583,8669,8749,8825,8880,8938,8985,9030,9169,9221,9265,9410,9468,12923,12968,13145,13221,13308,13561,13643,13730,13858,14044,14121,14206,14384,14471,14561", "endColumns": "74,55,60,49,42,76,55,41,56,51,62,52,61,48,59,47,38,46,46,153,64,70,208,73,59,68,202,72,111,41,84,56,45,55,42,44,40,110,50,67,113,59,59,58,59,62,39,46,52,57,60,61,47,58,42,41,74,70,66,50,75,49,66,60,57,97,97,62,40,51,510,55,63,56,57,109,56,54,46,50,83,69,58,85,79,75,54,57,46,44,60,51,43,43,57,60,44,176,75,86,252,81,86,127,185,76,84,177,86,89,42", "endOffsets": "2745,2801,2862,2912,2955,3032,3088,3130,3187,3239,3302,3355,3417,3466,3526,3574,3613,3660,3707,3861,3926,3997,4206,4280,4340,4409,4612,4685,4797,4839,4924,4981,5027,5083,5126,5171,5212,5323,5374,5442,5556,5616,5676,5735,5795,5858,5898,5945,5998,6056,6117,6179,6227,6286,6329,6371,6446,6517,6584,6635,6711,6761,6828,6889,6947,7045,7143,7206,7247,7299,7810,7866,7930,7987,8045,8155,8212,8267,8314,8365,8449,8519,8578,8664,8744,8820,8875,8933,8980,9025,9086,9216,9260,9304,9463,9524,12963,13140,13216,13303,13556,13638,13725,13853,14039,14116,14201,14379,14466,14556,14599"}}]}]}