{"logs": [{"outputFile": "com.ainative.mountainsurvival.app-release-24:/values-en_values-en.arsc.flat", "map": [{"source": "E:\\Ai\\AiCode\\game\\MountainSurvival\\code\\app\\src\\main\\res\\values-en\\strings.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,141,-1,-1,-1,127,128,126,124,123,131,130,121,120,135,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,144,145,-1,149,-1,148,146,147,-1,-1,-1,138,-1,-1,-1,-1,-1,-1,-1,140,139,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,116,117,115,104,105,103,134,108,109,107,112,113,111,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,-1,-1,-1,4,4,4,4,4,4,4,4,4,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,4,-1,4,-1,4,4,4,-1,-1,-1,4,-1,-1,-1,-1,-1,-1,-1,4,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,4,4,4,4,4,4,4,4,4,4,4,4,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,13665,-1,-1,-1,12403,12729,12297,11813,11704,12941,12846,11193,11084,13266,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,13769,13891,-1,14305,-1,14193,14005,14104,-1,-1,-1,13484,-1,-1,-1,-1,-1,-1,-1,13593,13525,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,10569,10948,10442,8233,8881,8081,13051,9152,9596,9015,9875,10290,9719,-1", "endColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,58,-1,-1,-1,325,115,105,482,108,84,94,509,108,182,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,121,113,-1,155,-1,111,98,88,-1,-1,-1,40,-1,-1,-1,-1,-1,-1,-1,71,67,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,378,109,126,647,132,151,214,443,121,136,414,150,155,-1", "endOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,13719,-1,-1,-1,12724,12840,12398,12291,11808,13021,12936,11698,11188,13444,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,13886,14000,-1,14456,-1,14300,14099,14188,-1,-1,-1,13520,-1,-1,-1,-1,-1,-1,-1,13660,13588,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,10943,11053,10564,8876,9009,8228,13261,9591,9713,9147,10285,10436,9870,-1"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,160,224,298,354,402,508,572,626,683,760,858,934,1030,1090,1176,1235,1274,1327,1383,1709,1825,1931,2414,2523,2608,2703,3213,3322,3505,3551,3729,3804,3858,3928,3976,4027,4070,4286,4348,4456,4739,4824,4899,4978,5060,5143,5189,5240,5293,5362,5423,5485,5543,5617,5666,5713,5826,5948,6062,6120,6276,6338,6450,6549,6638,6839,7029,7122,7163,7218,8736,8797,8883,8952,9034,9144,9216,9284,9338,9402,9521,9609,9679,9799,9932,10079,10147,10227,10285,10337,10422,10498,10547,10597,10666,15854,15904,16283,16393,16520,17168,17301,17453,17668,18112,18234,18371,18786,18937,19093", "endColumns": "104,63,73,55,47,105,63,53,56,76,97,75,95,59,85,58,38,52,55,325,115,105,482,108,84,94,509,108,182,45,177,74,53,69,47,50,42,215,61,107,282,84,74,78,81,82,45,50,52,68,60,61,57,73,48,46,112,121,113,57,155,61,111,98,88,200,189,92,40,54,1517,60,85,68,81,109,71,67,53,63,118,87,69,119,132,146,67,79,57,51,84,75,48,49,68,77,49,378,109,126,647,132,151,214,443,121,136,414,150,155,46", "endOffsets": "155,219,293,349,397,503,567,621,678,755,853,929,1025,1085,1171,1230,1269,1322,1378,1704,1820,1926,2409,2518,2603,2698,3208,3317,3500,3546,3724,3799,3853,3923,3971,4022,4065,4281,4343,4451,4734,4819,4894,4973,5055,5138,5184,5235,5288,5357,5418,5480,5538,5612,5661,5708,5821,5943,6057,6115,6271,6333,6445,6544,6633,6834,7024,7117,7158,7213,8731,8792,8878,8947,9029,9139,9211,9279,9333,9397,9516,9604,9674,9794,9927,10074,10142,10222,10280,10332,10417,10493,10542,10592,10661,10739,15899,16278,16388,16515,17163,17296,17448,17663,18107,18229,18366,18781,18932,19088,19135"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\077c69690200a4c0c0ddf0630ba2f6e7\\transformed\\open_ad_sdk_6.8.4.0\\res\\values-en\\values-en.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "10744,10787,10824,10870,10925,10996,11056,11101,11162,11216,11282,11338,11419,11496,11567,11690,11779,11903,12036,12173,12234,12296,12367,12443,12496,12549,12673,12722,12807,12858,12909,12952,12994,13036,13128,13201,13275,13351,13411,13476,13561,13678,13723,13807,13896,13984,14040,14098,14149,14251,14331,14418,14512,14588,14649,14746,14817,14913,14979,15046,15086,15131,15182,15232,15284,15350,15406,15462,15521,15587,15673,15746,15805", "endColumns": "42,36,45,54,70,59,44,60,53,65,55,80,76,70,122,88,123,132,136,60,61,70,75,52,52,123,48,84,50,50,42,41,41,91,72,73,75,59,64,84,116,44,83,88,87,55,57,50,101,79,86,93,75,60,96,70,95,65,66,39,44,50,49,51,65,55,55,58,65,85,72,58,48", "endOffsets": "10782,10819,10865,10920,10991,11051,11096,11157,11211,11277,11333,11414,11491,11562,11685,11774,11898,12031,12168,12229,12291,12362,12438,12491,12544,12668,12717,12802,12853,12904,12947,12989,13031,13123,13196,13270,13346,13406,13471,13556,13673,13718,13802,13891,13979,14035,14093,14144,14246,14326,14413,14507,14583,14644,14741,14812,14908,14974,15041,15081,15126,15177,15227,15279,15345,15401,15457,15516,15582,15668,15741,15800,15849"}}]}, {"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\values-ru_values-ru.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a3ae461f31008f99a87e29f3c99a3c83\\transformed\\appcompat-v7-28.0.0\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,322,434,520,625,745,824,902,994,1088,1184,1277,1373,1467,1563,1658,1750,1842,1922,2028,2136,2234,2343,2449,2557,2732,2832", "endColumns": "114,101,111,85,104,119,78,77,91,93,95,92,95,93,95,94,91,91,79,105,107,97,108,105,107,174,99,80", "endOffsets": "215,317,429,515,620,740,819,897,989,1083,1179,1272,1368,1462,1558,1653,1745,1837,1917,2023,2131,2229,2338,2444,2552,2727,2827,2908"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\daefd50eed0506e326ae247974906cd2\\transformed\\support-compat-28.0.0\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2913", "endColumns": "100", "endOffsets": "3009"}}]}, {"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\values-ko_values-ko.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\daefd50eed0506e326ae247974906cd2\\transformed\\support-compat-28.0.0\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2756", "endColumns": "100", "endOffsets": "2852"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a3ae461f31008f99a87e29f3c99a3c83\\transformed\\appcompat-v7-28.0.0\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,294,396,478,576,682,761,837,928,1021,1113,1204,1305,1398,1493,1587,1678,1769,1849,1947,2042,2137,2237,2333,2432,2584,2678", "endColumns": "94,93,101,81,97,105,78,75,90,92,91,90,100,92,94,93,90,90,79,97,94,94,99,95,98,151,93,77", "endOffsets": "195,289,391,473,571,677,756,832,923,1016,1108,1199,1300,1393,1488,1582,1673,1764,1844,1942,2037,2132,2232,2328,2427,2579,2673,2751"}}]}, {"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\values-hdpi-v4_values-hdpi-v4.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a3ae461f31008f99a87e29f3c99a3c83\\transformed\\appcompat-v7-28.0.0\\res\\values-hdpi-v4\\values-hdpi-v4.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endLines": "6", "endColumns": "13", "endOffsets": "327"}}]}, {"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\values-v21_values-v21.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\194766323b8e4d092ba620fa2362821d\\transformed\\support-media-compat-28.0.0\\res\\values-v21\\values-v21.xml", "from": {"startLines": "2,5,8,11", "startColumns": "4,4,4,4", "startOffsets": "55,223,386,554", "endLines": "4,7,10,13", "endColumns": "12,12,12,12", "endOffsets": "218,381,549,716"}, "to": {"startLines": "265,268,272,276", "startColumns": "4,4,4,4", "startOffsets": "18875,19043,19332,19628", "endLines": "267,270,274,278", "endColumns": "12,12,12,12", "endOffsets": "19038,19201,19495,19790"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\077c69690200a4c0c0ddf0630ba2f6e7\\transformed\\open_ad_sdk_6.8.4.0\\res\\values-v21\\values-v21.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,177", "endColumns": "121,128", "endOffsets": "172,301"}, "to": {"startLines": "319,320", "startColumns": "4,4", "startOffsets": "22018,22140", "endColumns": "121,128", "endOffsets": "22135,22264"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\71b2f468c1ec12de737a4945bfcdd2ee\\transformed\\design-28.0.0\\res\\values-v21\\values-v21.xml", "from": {"startLines": "2,10,18,26", "startColumns": "4,4,4,4", "startOffsets": "55,484,910,1333", "endLines": "9,17,25,33", "endColumns": "10,10,10,10", "endOffsets": "479,905,1328,1763"}, "to": {"startLines": "279,287,295,303", "startColumns": "4,4,4,4", "startOffsets": "19795,20224,20650,21073", "endLines": "286,294,302,310", "endColumns": "10,10,10,10", "endOffsets": "20219,20645,21068,21503"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a3ae461f31008f99a87e29f3c99a3c83\\transformed\\appcompat-v7-28.0.0\\res\\values-v21\\values-v21.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,17,19,20,21,22,24,26,27,28,29,30,32,34,36,38,40,42,43,48,50,52,53,54,56,58,59,60,61,62,63,107,110,154,157,160,162,164,166,169,171,174,175,176,179,180,181,182,183,184,187,188,190,192,194,196,200,202,203,204,205,207,211,213,215,216,217,218,219,221,222,223,233,234,235,247", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,146,249,352,457,564,673,782,891,1000,1109,1216,1319,1438,1593,1748,1853,1974,2075,2222,2363,2466,2585,2692,2795,2950,3121,3270,3435,3592,3743,3862,4234,4383,4532,4644,4791,4944,5091,5166,5255,5342,5443,5546,8614,8799,11879,12076,12275,12398,12521,12634,12817,12948,13149,13238,13349,13582,13683,13778,13901,14030,14147,14324,14423,14558,14701,14836,14955,15156,15275,15368,15479,15535,15642,15837,15948,16081,16176,16267,16358,16475,16614,16685,16768,17448,17505,17563,18257", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,16,18,19,20,21,23,25,26,27,28,29,31,33,35,37,39,41,42,47,49,51,52,53,55,57,58,59,60,61,62,106,109,153,156,159,161,163,165,168,170,173,174,175,178,179,180,181,182,183,186,187,189,191,193,195,199,201,202,203,204,206,210,212,214,215,216,217,218,220,221,222,232,233,234,246,258", "endColumns": "90,102,102,104,106,108,108,108,108,108,106,102,118,12,12,104,120,100,12,12,102,118,106,102,12,12,12,12,12,12,118,12,12,12,111,146,12,12,74,88,86,100,102,12,12,12,12,12,12,12,12,12,12,12,88,110,12,100,94,122,128,116,12,98,12,12,12,12,12,12,92,110,55,12,12,12,12,94,90,90,116,12,70,82,12,56,57,12,12", "endOffsets": "141,244,347,452,559,668,777,886,995,1104,1211,1314,1433,1588,1743,1848,1969,2070,2217,2358,2461,2580,2687,2790,2945,3116,3265,3430,3587,3738,3857,4229,4378,4527,4639,4786,4939,5086,5161,5250,5337,5438,5541,8609,8794,11874,12071,12270,12393,12516,12629,12812,12943,13144,13233,13344,13577,13678,13773,13896,14025,14142,14319,14418,14553,14696,14831,14950,15151,15270,15363,15474,15530,15637,15832,15943,16076,16171,16262,16353,16470,16609,16680,16763,17443,17500,17558,18252,18958"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,21,23,24,25,26,28,30,31,32,33,34,36,38,40,42,44,46,47,52,54,56,57,58,60,62,63,64,65,66,67,111,114,158,161,164,166,168,170,173,175,178,179,180,183,184,185,186,187,188,191,192,194,196,198,200,204,206,207,208,209,211,215,217,219,220,221,222,223,225,226,227,237,238,239,251", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "354,445,548,651,756,863,972,1081,1190,1299,1408,1515,1618,1737,1892,2047,2152,2273,2374,2521,2662,2765,2884,2991,3094,3249,3420,3569,3734,3891,4042,4161,4533,4682,4831,4943,5090,5243,5390,5465,5554,5641,5742,5845,8697,8882,11746,11943,12142,12265,12388,12501,12684,12815,13016,13105,13216,13449,13550,13645,13768,13897,14014,14191,14290,14425,14568,14703,14822,15023,15142,15235,15346,15402,15509,15704,15815,15948,16043,16134,16225,16342,16481,16552,16635,17258,17315,17373,17997", "endLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,20,22,23,24,25,27,29,30,31,32,33,35,37,39,41,43,45,46,51,53,55,56,57,59,61,62,63,64,65,66,110,113,157,160,163,165,167,169,172,174,177,178,179,182,183,184,185,186,187,190,191,193,195,197,199,203,205,206,207,208,210,214,216,218,219,220,221,222,224,225,226,236,237,238,250,262", "endColumns": "90,102,102,104,106,108,108,108,108,108,106,102,118,12,12,104,120,100,12,12,102,118,106,102,12,12,12,12,12,12,118,12,12,12,111,146,12,12,74,88,86,100,102,12,12,12,12,12,12,12,12,12,12,12,88,110,12,100,94,122,128,116,12,98,12,12,12,12,12,12,92,110,55,12,12,12,12,94,90,90,116,12,70,82,12,56,57,12,12", "endOffsets": "440,543,646,751,858,967,1076,1185,1294,1403,1510,1613,1732,1887,2042,2147,2268,2369,2516,2657,2760,2879,2986,3089,3244,3415,3564,3729,3886,4037,4156,4528,4677,4826,4938,5085,5238,5385,5460,5549,5636,5737,5840,8692,8877,11741,11938,12137,12260,12383,12496,12679,12810,13011,13100,13211,13444,13545,13640,13763,13892,14009,14186,14285,14420,14563,14698,14817,15018,15137,15230,15341,15397,15504,15699,15810,15943,16038,16129,16220,16337,16476,16547,16630,17253,17310,17368,17992,18628"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\daefd50eed0506e326ae247974906cd2\\transformed\\support-compat-28.0.0\\res\\values-v21\\values-v21.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,13", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,159,223,290,354,470,596,722,850,1022", "endLines": "2,3,4,5,6,7,8,9,12,17", "endColumns": "103,63,66,63,115,125,125,127,12,12", "endOffsets": "154,218,285,349,465,591,717,845,1017,1355"}, "to": {"startLines": "2,3,4,5,263,264,271,275,311,314", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,159,223,290,18633,18749,19206,19500,21508,21680", "endLines": "2,3,4,5,263,264,271,275,313,318", "endColumns": "103,63,66,63,115,125,125,127,12,12", "endOffsets": "154,218,285,349,18744,18870,19327,19623,21675,22013"}}]}, {"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\values-th_values-th.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\daefd50eed0506e326ae247974906cd2\\transformed\\support-compat-28.0.0\\res\\values-th\\values-th.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2845", "endColumns": "100", "endOffsets": "2941"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a3ae461f31008f99a87e29f3c99a3c83\\transformed\\appcompat-v7-28.0.0\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,308,416,505,607,717,794,872,963,1056,1152,1246,1347,1440,1535,1629,1720,1811,1892,2000,2104,2202,2310,2415,2516,2669,2764", "endColumns": "104,97,107,88,101,109,76,77,90,92,95,93,100,92,94,93,90,90,80,107,103,97,107,104,100,152,94,80", "endOffsets": "205,303,411,500,602,712,789,867,958,1051,1147,1241,1342,1435,1530,1624,1715,1806,1887,1995,2099,2197,2305,2410,2511,2664,2759,2840"}}]}, {"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\values-xlarge-v4_values-xlarge-v4.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a3ae461f31008f99a87e29f3c99a3c83\\transformed\\appcompat-v7-28.0.0\\res\\values-xlarge-v4\\values-xlarge-v4.xml", "from": {"startLines": "2,3,4,5,6,7", "startColumns": "4,4,4,4,4,4", "startOffsets": "55,126,197,267,337,405", "endColumns": "70,70,69,69,67,67", "endOffsets": "121,192,262,332,400,468"}}]}, {"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\values-lo_values-lo.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a3ae461f31008f99a87e29f3c99a3c83\\transformed\\appcompat-v7-28.0.0\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,305,412,497,602,714,791,869,960,1053,1149,1243,1344,1437,1532,1626,1717,1808,1888,1995,2092,2190,2295,2398,2502,2659,2755", "endColumns": "102,96,106,84,104,111,76,77,90,92,95,93,100,92,94,93,90,90,79,106,96,97,104,102,103,156,95,80", "endOffsets": "203,300,407,492,597,709,786,864,955,1048,1144,1238,1339,1432,1527,1621,1712,1803,1883,1990,2087,2185,2290,2393,2497,2654,2750,2831"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\daefd50eed0506e326ae247974906cd2\\transformed\\support-compat-28.0.0\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2836", "endColumns": "100", "endOffsets": "2932"}}]}, {"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\values-mk_values-mk.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\daefd50eed0506e326ae247974906cd2\\transformed\\support-compat-28.0.0\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2945", "endColumns": "100", "endOffsets": "3041"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a3ae461f31008f99a87e29f3c99a3c83\\transformed\\appcompat-v7-28.0.0\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,317,425,511,616,735,818,900,997,1096,1193,1293,1400,1499,1600,1696,1793,1884,1971,2077,2184,2285,2392,2503,2607,2763,2861", "endColumns": "107,103,107,85,104,118,82,81,96,98,96,99,106,98,100,95,96,90,86,105,106,100,106,110,103,155,97,83", "endOffsets": "208,312,420,506,611,730,813,895,992,1091,1188,1288,1395,1494,1595,1691,1788,1879,1966,2072,2179,2280,2387,2498,2602,2758,2856,2940"}}]}, {"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\values-sl_values-sl.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a3ae461f31008f99a87e29f3c99a3c83\\transformed\\appcompat-v7-28.0.0\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,319,427,514,617,736,821,905,997,1091,1187,1281,1377,1471,1567,1667,1759,1851,1935,2043,2152,2252,2365,2472,2576,2756,2853", "endColumns": "106,106,107,86,102,118,84,83,91,93,95,93,95,93,95,99,91,91,83,107,108,99,112,106,103,179,96,82", "endOffsets": "207,314,422,509,612,731,816,900,992,1086,1182,1276,1372,1466,1562,1662,1754,1846,1930,2038,2147,2247,2360,2467,2571,2751,2848,2931"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\daefd50eed0506e326ae247974906cd2\\transformed\\support-compat-28.0.0\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2936", "endColumns": "100", "endOffsets": "3032"}}]}, {"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\values-zh-rTW_values-zh-rTW.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a3ae461f31008f99a87e29f3c99a3c83\\transformed\\appcompat-v7-28.0.0\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,100", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,293,393,475,572,680,756,832,924,1018,1116,1212,1308,1402,1498,1590,1682,1774,1852,1948,2044,2139,2236,2331,2431,2581,7400", "endColumns": "94,92,99,81,96,107,75,75,91,93,97,95,95,93,95,91,91,91,77,95,95,94,96,94,99,149,93,77", "endOffsets": "195,288,388,470,567,675,751,827,919,1013,1111,1207,1303,1397,1493,1585,1677,1769,1847,1943,2039,2134,2231,2326,2426,2576,2670,7473"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\daefd50eed0506e326ae247974906cd2\\transformed\\support-compat-28.0.0\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "104", "startColumns": "4", "startOffsets": "7618", "endColumns": "100", "endOffsets": "7714"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\077c69690200a4c0c0ddf0630ba2f6e7\\transformed\\open_ad_sdk_6.8.4.0\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7838,7881,7918,7964,8019,8080,8136,8177,8226,8277,8337,8385,8448,8512,8571,8643,8716,8784,8857,8938,8995,9053,9111,9174,9223,9268,9333,9376,9434,9478,9525,9568,9622,9688,9749,9814,9869,9926,10004,10066,10105,10184,10257,10338,10388,10444,10503,10541,10584,10633,10683,10735,10791,10844,10898,10953,11012,11073,11134,11186", "endColumns": "42,36,45,54,60,55,40,48,50,59,47,62,63,58,71,72,67,72,80,56,57,57,62,48,44,64,42,57,43,46,42,53,65,60,64,54,56,77,61,38,78,72,80,49,55,58,37,42,48,49,51,55,52,53,54,58,60,60,51,45", "endOffsets": "7876,7913,7959,8014,8075,8131,8172,8221,8272,8332,8380,8443,8507,8566,8638,8711,8779,8852,8933,8990,9048,9106,9169,9218,9263,9328,9371,9429,9473,9520,9563,9617,9683,9744,9809,9864,9921,9999,10061,10100,10179,10252,10333,10383,10439,10498,10536,10579,10628,10678,10730,10786,10839,10893,10948,11007,11068,11129,11181,11227"}}, {"source": "E:\\Ai\\AiCode\\game\\MountainSurvival\\code\\app\\src\\main\\res\\values-zh-rTW\\strings.xml", "from": {"startLines": "84,83,80,79,97,86,85,-1,-1,47,67,46,68,100,43,94,34,93,56,58,66,33,32,-1,41,42,55,54,31,-1,69,50,48,51,49,-1,-1,-1,-1,-1,-1,99,70,-1,-1,73,62,65,63,64,35,-1,-1,-1,-1,-1,-1,-1,75,98,87,82,81,88,76,92,91,90,89,57,59,74,40,-1,38,-1,-1,39", "startColumns": "4,4,4,4,4,4,4,-1,-1,4,4,4,4,4,4,4,4,4,4,4,4,4,4,-1,4,4,4,4,4,-1,4,4,4,4,4,-1,-1,-1,-1,-1,-1,4,4,-1,-1,4,4,4,4,4,4,-1,-1,-1,-1,-1,-1,-1,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,-1,4,-1,-1,4", "startOffsets": "4390,4334,4144,4094,5109,4521,4465,-1,-1,2478,3559,2425,3622,5251,2346,5051,1986,5004,2953,3040,3502,1940,1884,-1,2260,2305,2842,2791,1816,-1,3684,2652,2530,2712,2589,-1,-1,-1,-1,-1,-1,5203,3744,-1,-1,3821,3205,3452,3256,3354,2033,-1,-1,-1,-1,-1,-1,-1,3948,5152,4598,4264,4205,4682,3995,4928,4873,4815,4768,2995,3125,3896,2216,-1,2115,-1,-1,2173", "endColumns": "74,55,60,49,42,76,55,-1,-1,51,62,52,61,48,59,38,46,46,41,84,56,45,55,-1,44,40,110,50,67,-1,59,59,58,59,62,-1,-1,-1,-1,-1,-1,47,58,-1,-1,74,50,49,97,97,62,-1,-1,-1,-1,-1,-1,-1,46,50,83,69,58,85,79,75,54,57,46,44,60,51,43,-1,57,-1,-1,42", "endOffsets": "4460,4385,4200,4139,5147,4593,4516,-1,-1,2525,3617,2473,3679,5295,2401,5085,2028,5046,2990,3120,3554,1981,1935,-1,2300,2341,2948,2837,1879,-1,3739,2707,2584,2767,2647,-1,-1,-1,-1,-1,-1,5246,3798,-1,-1,3891,3251,3497,3349,3447,2091,-1,-1,-1,-1,-1,-1,-1,3990,5198,4677,4329,4259,4763,4070,4999,4923,4868,4810,3035,3181,3943,2255,-1,2168,-1,-1,2211"}, "to": {"startLines": "29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,101,102,103,105,106,167,168", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2675,2750,2806,2867,2917,2960,3037,3093,3135,3192,3244,3307,3360,3422,3471,3531,3570,3617,3664,3706,3791,3848,3894,3950,3993,4038,4079,4190,4241,4309,4423,4483,4543,4602,4662,4725,4765,4812,4865,4923,4984,5046,5094,5153,5196,5238,5313,5364,5414,5512,5610,5673,5725,6236,6292,6356,6413,6471,6581,6628,6679,6763,6833,6892,6978,7058,7134,7189,7247,7294,7339,7478,7530,7574,7719,7777,11232,11277", "endColumns": "74,55,60,49,42,76,55,41,56,51,62,52,61,48,59,38,46,46,41,84,56,45,55,42,44,40,110,50,67,113,59,59,58,59,62,39,46,52,57,60,61,47,58,42,41,74,50,49,97,97,62,51,510,55,63,56,57,109,46,50,83,69,58,85,79,75,54,57,46,44,60,51,43,43,57,60,44,42", "endOffsets": "2745,2801,2862,2912,2955,3032,3088,3130,3187,3239,3302,3355,3417,3466,3526,3565,3612,3659,3701,3786,3843,3889,3945,3988,4033,4074,4185,4236,4304,4418,4478,4538,4597,4657,4720,4760,4807,4860,4918,4979,5041,5089,5148,5191,5233,5308,5359,5409,5507,5605,5668,5720,6231,6287,6351,6408,6466,6576,6623,6674,6758,6828,6887,6973,7053,7129,7184,7242,7289,7334,7395,7525,7569,7613,7772,7833,11272,11315"}}]}, {"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\values-iw_values-iw.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\daefd50eed0506e326ae247974906cd2\\transformed\\support-compat-28.0.0\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2832", "endColumns": "100", "endOffsets": "2928"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a3ae461f31008f99a87e29f3c99a3c83\\transformed\\appcompat-v7-28.0.0\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,308,416,500,600,714,792,870,961,1055,1151,1245,1346,1439,1534,1631,1722,1814,1895,1997,2101,2199,2302,2403,2503,2655,2751", "endColumns": "103,98,107,83,99,113,77,77,90,93,95,93,100,92,94,96,90,91,80,101,103,97,102,100,99,151,95,80", "endOffsets": "204,303,411,495,595,709,787,865,956,1050,1146,1240,1341,1434,1529,1626,1717,1809,1890,1992,2096,2194,2297,2398,2498,2650,2746,2827"}}]}, {"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\values-fr_values-fr.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a3ae461f31008f99a87e29f3c99a3c83\\transformed\\appcompat-v7-28.0.0\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,331,442,524,630,760,843,923,1014,1107,1206,1301,1402,1495,1588,1683,1774,1865,1951,2061,2173,2276,2387,2494,2601,2760,2859", "endColumns": "110,114,110,81,105,129,82,79,90,92,98,94,100,92,92,94,90,90,85,109,111,102,110,106,106,158,98,85", "endOffsets": "211,326,437,519,625,755,838,918,1009,1102,1201,1296,1397,1490,1583,1678,1769,1860,1946,2056,2168,2271,2382,2489,2596,2755,2854,2940"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\daefd50eed0506e326ae247974906cd2\\transformed\\support-compat-28.0.0\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2945", "endColumns": "100", "endOffsets": "3041"}}]}, {"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\values-lt_values-lt.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\daefd50eed0506e326ae247974906cd2\\transformed\\support-compat-28.0.0\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2978", "endColumns": "100", "endOffsets": "3074"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a3ae461f31008f99a87e29f3c99a3c83\\transformed\\appcompat-v7-28.0.0\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,221,322,435,522,631,752,834,915,1009,1105,1203,1299,1403,1499,1597,1700,1794,1888,1973,2082,2191,2291,2401,2505,2618,2794,2895", "endColumns": "115,100,112,86,108,120,81,80,93,95,97,95,103,95,97,102,93,93,84,108,108,99,109,103,112,175,100,82", "endOffsets": "216,317,430,517,626,747,829,910,1004,1100,1198,1294,1398,1494,1592,1695,1789,1883,1968,2077,2186,2286,2396,2500,2613,2789,2890,2973"}}]}, {"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\values-v25_values-v25.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a3ae461f31008f99a87e29f3c99a3c83\\transformed\\appcompat-v7-28.0.0\\res\\values-v25\\values-v25.xml", "from": {"startLines": "2,3,4,6", "startColumns": "4,4,4,4", "startOffsets": "55,126,209,308", "endLines": "2,3,5,7", "endColumns": "70,82,12,12", "endOffsets": "121,204,303,414"}}]}, {"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\values-en-rAU_values-en-rAU.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\daefd50eed0506e326ae247974906cd2\\transformed\\support-compat-28.0.0\\res\\values-en-rAU\\values-en-rAU.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2846", "endColumns": "100", "endOffsets": "2942"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a3ae461f31008f99a87e29f3c99a3c83\\transformed\\appcompat-v7-28.0.0\\res\\values-en-rAU\\values-en-rAU.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,309,417,501,601,716,793,869,960,1053,1149,1243,1344,1437,1532,1626,1717,1808,1890,1993,2097,2196,2301,2404,2508,2664,2764", "endColumns": "103,99,107,83,99,114,76,75,90,92,95,93,100,92,94,93,90,90,81,102,103,98,104,102,103,155,99,81", "endOffsets": "204,304,412,496,596,711,788,864,955,1048,1144,1238,1339,1432,1527,1621,1712,1803,1885,1988,2092,2191,2296,2399,2503,2659,2759,2841"}}]}, {"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\values-kk_values-kk.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a3ae461f31008f99a87e29f3c99a3c83\\transformed\\appcompat-v7-28.0.0\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,217,320,430,515,621,740,821,901,992,1085,1181,1275,1376,1469,1564,1661,1752,1844,1925,2028,2133,2231,2338,2447,2547,2713,2812", "endColumns": "111,102,109,84,105,118,80,79,90,92,95,93,100,92,94,96,90,91,80,102,104,97,106,108,99,165,98,80", "endOffsets": "212,315,425,510,616,735,816,896,987,1080,1176,1270,1371,1464,1559,1656,1747,1839,1920,2023,2128,2226,2333,2442,2542,2708,2807,2888"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\daefd50eed0506e326ae247974906cd2\\transformed\\support-compat-28.0.0\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2893", "endColumns": "100", "endOffsets": "2989"}}]}, {"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\values-tl_values-tl.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a3ae461f31008f99a87e29f3c99a3c83\\transformed\\appcompat-v7-28.0.0\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,224,332,449,537,643,764,843,921,1012,1105,1201,1295,1396,1489,1584,1678,1769,1860,1944,2053,2164,2265,2375,2492,2600,2763,2865", "endColumns": "118,107,116,87,105,120,78,77,90,92,95,93,100,92,94,93,90,90,83,108,110,100,109,116,107,162,101,83", "endOffsets": "219,327,444,532,638,759,838,916,1007,1100,1196,1290,1391,1484,1579,1673,1764,1855,1939,2048,2159,2260,2370,2487,2595,2758,2860,2944"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\daefd50eed0506e326ae247974906cd2\\transformed\\support-compat-28.0.0\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2949", "endColumns": "100", "endOffsets": "3045"}}]}, {"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\values-hr_values-hr.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a3ae461f31008f99a87e29f3c99a3c83\\transformed\\appcompat-v7-28.0.0\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,307,417,503,607,726,810,893,984,1077,1173,1267,1368,1461,1556,1655,1746,1837,1923,2027,2140,2246,2351,2464,2571,2740,2837", "endColumns": "104,96,109,85,103,118,83,82,90,92,95,93,100,92,94,98,90,90,85,103,112,105,104,112,106,168,96,88", "endOffsets": "205,302,412,498,602,721,805,888,979,1072,1168,1262,1363,1456,1551,1650,1741,1832,1918,2022,2135,2241,2346,2459,2566,2735,2832,2921"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\daefd50eed0506e326ae247974906cd2\\transformed\\support-compat-28.0.0\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2926", "endColumns": "100", "endOffsets": "3022"}}]}, {"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\values-pl_values-pl.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a3ae461f31008f99a87e29f3c99a3c83\\transformed\\appcompat-v7-28.0.0\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,221,323,431,517,626,745,823,900,991,1084,1180,1274,1376,1469,1564,1659,1750,1841,1923,2032,2141,2240,2349,2460,2568,2731,2827", "endColumns": "115,101,107,85,108,118,77,76,90,92,95,93,101,92,94,94,90,90,81,108,108,98,108,110,107,162,95,81", "endOffsets": "216,318,426,512,621,740,818,895,986,1079,1175,1269,1371,1464,1559,1654,1745,1836,1918,2027,2136,2235,2344,2455,2563,2726,2822,2904"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\daefd50eed0506e326ae247974906cd2\\transformed\\support-compat-28.0.0\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2909", "endColumns": "100", "endOffsets": "3005"}}]}, {"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\values-ka_values-ka.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a3ae461f31008f99a87e29f3c99a3c83\\transformed\\appcompat-v7-28.0.0\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,318,429,517,622,735,819,901,992,1085,1180,1276,1377,1470,1565,1659,1750,1841,1924,2037,2144,2242,2355,2459,2563,2720,2818", "endColumns": "108,103,110,87,104,112,83,81,90,92,94,95,100,92,94,93,90,90,82,112,106,97,112,103,103,156,97,80", "endOffsets": "209,313,424,512,617,730,814,896,987,1080,1175,1271,1372,1465,1560,1654,1745,1836,1919,2032,2139,2237,2350,2454,2558,2715,2813,2894"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\daefd50eed0506e326ae247974906cd2\\transformed\\support-compat-28.0.0\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2899", "endColumns": "100", "endOffsets": "2995"}}]}, {"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\values-ja_values-ja.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\daefd50eed0506e326ae247974906cd2\\transformed\\support-compat-28.0.0\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2771", "endColumns": "100", "endOffsets": "2867"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a3ae461f31008f99a87e29f3c99a3c83\\transformed\\appcompat-v7-28.0.0\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,295,400,482,580,688,765,841,932,1025,1121,1215,1316,1409,1504,1598,1689,1780,1858,1960,2059,2154,2257,2352,2448,2596,2693", "endColumns": "96,92,104,81,97,107,76,75,90,92,95,93,100,92,94,93,90,90,77,101,98,94,102,94,95,147,96,77", "endOffsets": "197,290,395,477,575,683,760,836,927,1020,1116,1210,1311,1404,1499,1593,1684,1775,1853,1955,2054,2149,2252,2347,2443,2591,2688,2766"}}]}, {"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\values-sv_values-sv.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a3ae461f31008f99a87e29f3c99a3c83\\transformed\\appcompat-v7-28.0.0\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,211,314,425,510,612,725,801,877,970,1065,1161,1255,1358,1453,1550,1648,1744,1837,1917,2023,2123,2219,2324,2426,2528,2682,2784", "endColumns": "105,102,110,84,101,112,75,75,92,94,95,93,102,94,96,97,95,92,79,105,99,95,104,101,101,153,101,78", "endOffsets": "206,309,420,505,607,720,796,872,965,1060,1156,1250,1353,1448,1545,1643,1739,1832,1912,2018,2118,2214,2319,2421,2523,2677,2779,2858"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\daefd50eed0506e326ae247974906cd2\\transformed\\support-compat-28.0.0\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2863", "endColumns": "100", "endOffsets": "2959"}}]}, {"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\values-nb_values-nb.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\daefd50eed0506e326ae247974906cd2\\transformed\\support-compat-28.0.0\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2826", "endColumns": "100", "endOffsets": "2922"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a3ae461f31008f99a87e29f3c99a3c83\\transformed\\appcompat-v7-28.0.0\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,308,422,508,608,721,797,873,964,1057,1153,1247,1348,1441,1536,1634,1725,1816,1896,1999,2098,2194,2298,2396,2497,2650,2747", "endColumns": "107,94,113,85,99,112,75,75,90,92,95,93,100,92,94,97,90,90,79,102,98,95,103,97,100,152,96,78", "endOffsets": "208,303,417,503,603,716,792,868,959,1052,1148,1242,1343,1436,1531,1629,1720,1811,1891,1994,2093,2189,2293,2391,2492,2645,2742,2821"}}]}, {"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\values-sq_values-sq.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a3ae461f31008f99a87e29f3c99a3c83\\transformed\\appcompat-v7-28.0.0\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,319,431,518,628,751,832,911,1002,1095,1191,1285,1387,1480,1575,1672,1763,1856,1939,2045,2150,2248,2354,2457,2573,2727,2826", "endColumns": "113,99,111,86,109,122,80,78,90,92,95,93,101,92,94,96,90,92,82,105,104,97,105,102,115,153,98,80", "endOffsets": "214,314,426,513,623,746,827,906,997,1090,1186,1280,1382,1475,1570,1667,1758,1851,1934,2040,2145,2243,2349,2452,2568,2722,2821,2902"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\daefd50eed0506e326ae247974906cd2\\transformed\\support-compat-28.0.0\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2907", "endColumns": "100", "endOffsets": "3003"}}]}, {"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\values-v16_values-v16.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\daefd50eed0506e326ae247974906cd2\\transformed\\support-compat-28.0.0\\res\\values-v16\\values-v16.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "65", "endOffsets": "116"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a3ae461f31008f99a87e29f3c99a3c83\\transformed\\appcompat-v7-28.0.0\\res\\values-v16\\values-v16.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endLines": "5", "endColumns": "12", "endOffsets": "223"}, "to": {"startLines": "3", "startColumns": "4", "startOffsets": "121", "endLines": "6", "endColumns": "12", "endOffsets": "289"}}]}, {"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\values-de_values-de.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a3ae461f31008f99a87e29f3c99a3c83\\transformed\\appcompat-v7-28.0.0\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,308,420,506,611,726,803,879,971,1065,1162,1263,1371,1471,1575,1675,1773,1870,1952,2063,2166,2265,2376,2478,2585,2741,2843", "endColumns": "104,97,111,85,104,114,76,75,91,93,96,100,107,99,103,99,97,96,81,110,102,98,110,101,106,155,101,81", "endOffsets": "205,303,415,501,606,721,798,874,966,1060,1157,1258,1366,1466,1570,1670,1768,1865,1947,2058,2161,2260,2371,2473,2580,2736,2838,2920"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\daefd50eed0506e326ae247974906cd2\\transformed\\support-compat-28.0.0\\res\\values-de\\values-de.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2925", "endColumns": "100", "endOffsets": "3021"}}]}, {"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\values-az_values-az.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a3ae461f31008f99a87e29f3c99a3c83\\transformed\\appcompat-v7-28.0.0\\res\\values-az\\values-az.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,312,424,512,619,733,814,893,984,1077,1172,1271,1372,1465,1560,1655,1746,1838,1923,2030,2137,2237,2346,2450,2560,2718,2820", "endColumns": "107,98,111,87,106,113,80,78,90,92,94,98,100,92,94,94,90,91,84,106,106,99,108,103,109,157,101,82", "endOffsets": "208,307,419,507,614,728,809,888,979,1072,1167,1266,1367,1460,1555,1650,1741,1833,1918,2025,2132,2232,2341,2445,2555,2713,2815,2898"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\daefd50eed0506e326ae247974906cd2\\transformed\\support-compat-28.0.0\\res\\values-az\\values-az.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2903", "endColumns": "100", "endOffsets": "2999"}}]}, {"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\values-v22_values-v22.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a3ae461f31008f99a87e29f3c99a3c83\\transformed\\appcompat-v7-28.0.0\\res\\values-v22\\values-v22.xml", "from": {"startLines": "2,3,4,9", "startColumns": "4,4,4,4", "startOffsets": "55,130,217,553", "endLines": "2,3,8,13", "endColumns": "74,86,12,12", "endOffsets": "125,212,548,896"}, "to": {"startLines": "2,3,4,9", "startColumns": "4,4,4,4", "startOffsets": "55,130,217,487", "endLines": "2,3,8,13", "endColumns": "74,86,12,12", "endOffsets": "125,212,482,764"}}]}, {"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\values-it_values-it.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\daefd50eed0506e326ae247974906cd2\\transformed\\support-compat-28.0.0\\res\\values-it\\values-it.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2906", "endColumns": "100", "endOffsets": "3002"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a3ae461f31008f99a87e29f3c99a3c83\\transformed\\appcompat-v7-28.0.0\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,314,423,507,616,741,818,894,986,1080,1174,1268,1370,1464,1561,1667,1759,1851,1932,2038,2146,2244,2348,2453,2560,2723,2823", "endColumns": "108,99,108,83,108,124,76,75,91,93,93,93,101,93,96,105,91,91,80,105,107,97,103,104,106,162,99,82", "endOffsets": "209,309,418,502,611,736,813,889,981,1075,1169,1263,1365,1459,1556,1662,1754,1846,1927,2033,2141,2239,2343,2448,2555,2718,2818,2901"}}]}, {"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\values-es_values-es.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\daefd50eed0506e326ae247974906cd2\\transformed\\support-compat-28.0.0\\res\\values-es\\values-es.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2924", "endColumns": "100", "endOffsets": "3020"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a3ae461f31008f99a87e29f3c99a3c83\\transformed\\appcompat-v7-28.0.0\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,222,335,443,528,629,757,842,924,1016,1110,1208,1302,1403,1497,1593,1689,1781,1873,1955,2062,2162,2261,2369,2476,2583,2742,2842", "endColumns": "116,112,107,84,100,127,84,81,91,93,97,93,100,93,95,95,91,91,81,106,99,98,107,106,106,158,99,81", "endOffsets": "217,330,438,523,624,752,837,919,1011,1105,1203,1297,1398,1492,1588,1684,1776,1868,1950,2057,2157,2256,2364,2471,2578,2737,2837,2919"}}]}, {"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\values-be_values-be.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a3ae461f31008f99a87e29f3c99a3c83\\transformed\\appcompat-v7-28.0.0\\res\\values-be\\values-be.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,328,444,530,638,756,835,913,1005,1099,1195,1289,1385,1479,1575,1670,1762,1854,1937,2043,2149,2247,2355,2460,2565,2734,2834", "endColumns": "119,102,115,85,107,117,78,77,91,93,95,93,95,93,95,94,91,91,82,105,105,97,107,104,104,168,99,80", "endOffsets": "220,323,439,525,633,751,830,908,1000,1094,1190,1284,1380,1474,1570,1665,1757,1849,1932,2038,2144,2242,2350,2455,2560,2729,2829,2910"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\daefd50eed0506e326ae247974906cd2\\transformed\\support-compat-28.0.0\\res\\values-be\\values-be.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2915", "endColumns": "100", "endOffsets": "3011"}}]}, {"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\values-en-rXC_values-en-rXC.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\daefd50eed0506e326ae247974906cd2\\transformed\\support-compat-28.0.0\\res\\values-en-rXC\\values-en-rXC.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "202", "endOffsets": "253"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "5682", "endColumns": "202", "endOffsets": "5880"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a3ae461f31008f99a87e29f3c99a3c83\\transformed\\appcompat-v7-28.0.0\\res\\values-en-rXC\\values-en-rXC.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,310,510,719,904,1106,1321,1494,1671,1862,2055,2253,2449,2652,2847,3044,3239,3432,3623,3807,4011,4216,4417,4624,4826,5031,5303,5503", "endColumns": "204,199,208,184,201,214,172,176,190,192,197,195,202,194,196,194,192,190,183,203,204,200,206,201,204,271,199,178", "endOffsets": "305,505,714,899,1101,1316,1489,1666,1857,2050,2248,2444,2647,2842,3039,3234,3427,3618,3802,4006,4211,4412,4619,4821,5026,5298,5498,5677"}}]}, {"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\values-v24_values-v24.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a3ae461f31008f99a87e29f3c99a3c83\\transformed\\appcompat-v7-28.0.0\\res\\values-v24\\values-v24.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,212", "endColumns": "156,134", "endOffsets": "207,342"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\194766323b8e4d092ba620fa2362821d\\transformed\\support-media-compat-28.0.0\\res\\values-v24\\values-v24.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,121,182,248", "endColumns": "65,60,65,66", "endOffsets": "116,177,243,310"}, "to": {"startLines": "4,5,6,7", "startColumns": "4,4,4,4", "startOffsets": "347,413,474,540", "endColumns": "65,60,65,66", "endOffsets": "408,469,535,602"}}]}, {"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\values-ar_values-ar.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a3ae461f31008f99a87e29f3c99a3c83\\transformed\\appcompat-v7-28.0.0\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,222,326,435,517,618,732,811,890,981,1074,1170,1264,1365,1458,1553,1647,1738,1832,1911,2016,2117,2213,2321,2424,2527,2682,2779", "endColumns": "116,103,108,81,100,113,78,78,90,92,95,93,100,92,94,93,90,93,78,104,100,95,107,102,102,154,96,80", "endOffsets": "217,321,430,512,613,727,806,885,976,1069,1165,1259,1360,1453,1548,1642,1733,1827,1906,2011,2112,2208,2316,2419,2522,2677,2774,2855"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\daefd50eed0506e326ae247974906cd2\\transformed\\support-compat-28.0.0\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2860", "endColumns": "100", "endOffsets": "2956"}}]}, {"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\values-ky_values-ky.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\daefd50eed0506e326ae247974906cd2\\transformed\\support-compat-28.0.0\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2883", "endColumns": "100", "endOffsets": "2979"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a3ae461f31008f99a87e29f3c99a3c83\\transformed\\appcompat-v7-28.0.0\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,304,423,507,614,731,809,888,979,1072,1168,1262,1363,1456,1551,1646,1737,1828,1909,2019,2126,2224,2330,2437,2538,2699,2802", "endColumns": "103,94,118,83,106,116,77,78,90,92,95,93,100,92,94,94,90,90,80,109,106,97,105,106,100,160,102,80", "endOffsets": "204,299,418,502,609,726,804,883,974,1067,1163,1257,1358,1451,1546,1641,1732,1823,1904,2014,2121,2219,2325,2432,2533,2694,2797,2878"}}]}, {"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\values-da_values-da.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\daefd50eed0506e326ae247974906cd2\\transformed\\support-compat-28.0.0\\res\\values-da\\values-da.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2844", "endColumns": "100", "endOffsets": "2940"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a3ae461f31008f99a87e29f3c99a3c83\\transformed\\appcompat-v7-28.0.0\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,307,419,502,602,715,792,869,960,1053,1149,1243,1338,1431,1526,1624,1715,1806,1885,1994,2102,2198,2312,2414,2515,2668,2765", "endColumns": "102,98,111,82,99,112,76,76,90,92,95,93,94,92,94,97,90,90,78,108,107,95,113,101,100,152,96,78", "endOffsets": "203,302,414,497,597,710,787,864,955,1048,1144,1238,1333,1426,1521,1619,1710,1801,1880,1989,2097,2193,2307,2409,2510,2663,2760,2839"}}]}, {"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\values-en-rIN_values-en-rIN.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a3ae461f31008f99a87e29f3c99a3c83\\transformed\\appcompat-v7-28.0.0\\res\\values-en-rIN\\values-en-rIN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,309,417,501,601,716,793,869,960,1053,1149,1243,1344,1437,1532,1626,1717,1808,1890,1993,2097,2196,2301,2404,2508,2664,2764", "endColumns": "103,99,107,83,99,114,76,75,90,92,95,93,100,92,94,93,90,90,81,102,103,98,104,102,103,155,99,81", "endOffsets": "204,304,412,496,596,711,788,864,955,1048,1144,1238,1339,1432,1527,1621,1712,1803,1885,1988,2092,2191,2296,2399,2503,2659,2759,2841"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\daefd50eed0506e326ae247974906cd2\\transformed\\support-compat-28.0.0\\res\\values-en-rIN\\values-en-rIN.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2846", "endColumns": "100", "endOffsets": "2942"}}]}, {"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\values-port_values-port.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a3ae461f31008f99a87e29f3c99a3c83\\transformed\\appcompat-v7-28.0.0\\res\\values-port\\values-port.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "55", "endOffsets": "106"}}]}, {"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\values-mn_values-mn.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\daefd50eed0506e326ae247974906cd2\\transformed\\support-compat-28.0.0\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2876", "endColumns": "100", "endOffsets": "2972"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a3ae461f31008f99a87e29f3c99a3c83\\transformed\\appcompat-v7-28.0.0\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,218,318,431,518,624,736,818,900,991,1084,1180,1276,1374,1467,1562,1654,1745,1835,1917,2026,2130,2227,2335,2436,2539,2698,2795", "endColumns": "112,99,112,86,105,111,81,81,90,92,95,95,97,92,94,91,90,89,81,108,103,96,107,100,102,158,96,80", "endOffsets": "213,313,426,513,619,731,813,895,986,1079,1175,1271,1369,1462,1557,1649,1740,1830,1912,2021,2125,2222,2330,2431,2534,2693,2790,2871"}}]}, {"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\values-te_values-te.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\daefd50eed0506e326ae247974906cd2\\transformed\\support-compat-28.0.0\\res\\values-te\\values-te.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2946", "endColumns": "100", "endOffsets": "3042"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a3ae461f31008f99a87e29f3c99a3c83\\transformed\\appcompat-v7-28.0.0\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,328,439,529,634,759,841,923,1014,1107,1203,1297,1398,1491,1586,1681,1772,1863,1947,2060,2168,2267,2378,2480,2597,2763,2864", "endColumns": "113,108,110,89,104,124,81,81,90,92,95,93,100,92,94,94,90,90,83,112,107,98,110,101,116,165,100,81", "endOffsets": "214,323,434,524,629,754,836,918,1009,1102,1198,1292,1393,1486,1581,1676,1767,1858,1942,2055,2163,2262,2373,2475,2592,2758,2859,2941"}}]}, {"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\values-kn_values-kn.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\daefd50eed0506e326ae247974906cd2\\transformed\\support-compat-28.0.0\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2948", "endColumns": "100", "endOffsets": "3044"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a3ae461f31008f99a87e29f3c99a3c83\\transformed\\appcompat-v7-28.0.0\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,223,335,448,536,643,770,847,924,1015,1108,1204,1298,1399,1492,1587,1681,1772,1863,1945,2061,2172,2271,2384,2488,2602,2766,2866", "endColumns": "117,111,112,87,106,126,76,76,90,92,95,93,100,92,94,93,90,90,81,115,110,98,112,103,113,163,99,81", "endOffsets": "218,330,443,531,638,765,842,919,1010,1103,1199,1293,1394,1487,1582,1676,1767,1858,1940,2056,2167,2266,2379,2483,2597,2761,2861,2943"}}]}, {"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\values-ms_values-ms.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a3ae461f31008f99a87e29f3c99a3c83\\transformed\\appcompat-v7-28.0.0\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,321,429,516,620,731,809,888,979,1072,1167,1261,1360,1453,1548,1642,1733,1824,1904,2016,2125,2222,2331,2434,2541,2700,2801", "endColumns": "110,104,107,86,103,110,77,78,90,92,94,93,98,92,94,93,90,90,79,111,108,96,108,102,106,158,100,79", "endOffsets": "211,316,424,511,615,726,804,883,974,1067,1162,1256,1355,1448,1543,1637,1728,1819,1899,2011,2120,2217,2326,2429,2536,2695,2796,2876"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\daefd50eed0506e326ae247974906cd2\\transformed\\support-compat-28.0.0\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2881", "endColumns": "100", "endOffsets": "2977"}}]}, {"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\values-nl_values-nl.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a3ae461f31008f99a87e29f3c99a3c83\\transformed\\appcompat-v7-28.0.0\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,223,328,435,521,629,749,826,903,995,1089,1190,1284,1385,1479,1575,1670,1762,1854,1936,2047,2151,2250,2365,2478,2581,2736,2839", "endColumns": "117,104,106,85,107,119,76,76,91,93,100,93,100,93,95,94,91,91,81,110,103,98,114,112,102,154,102,81", "endOffsets": "218,323,430,516,624,744,821,898,990,1084,1185,1279,1380,1474,1570,1665,1757,1849,1931,2042,2146,2245,2360,2473,2576,2731,2834,2916"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\daefd50eed0506e326ae247974906cd2\\transformed\\support-compat-28.0.0\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2921", "endColumns": "100", "endOffsets": "3017"}}]}, {"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\values-v28_values-v28.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a3ae461f31008f99a87e29f3c99a3c83\\transformed\\appcompat-v7-28.0.0\\res\\values-v28\\values-v28.xml", "from": {"startLines": "2,3,4,8", "startColumns": "4,4,4,4", "startOffsets": "55,130,217,447", "endLines": "2,3,7,11", "endColumns": "74,86,12,12", "endOffsets": "125,212,442,684"}, "to": {"startLines": "2,3,4,8", "startColumns": "4,4,4,4", "startOffsets": "55,130,217,397", "endLines": "2,3,7,11", "endColumns": "74,86,12,12", "endOffsets": "125,212,392,584"}}]}, {"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\values-sw600dp-v13_values-sw600dp-v13.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a3ae461f31008f99a87e29f3c99a3c83\\transformed\\appcompat-v7-28.0.0\\res\\values-sw600dp-v13\\values-sw600dp-v13.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,124,193,263,337,413,472,543", "endColumns": "68,68,69,73,75,58,70,67", "endOffsets": "119,188,258,332,408,467,538,606"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\71b2f468c1ec12de737a4945bfcdd2ee\\transformed\\design-28.0.0\\res\\values-sw600dp-v13\\values-sw600dp-v13.xml", "from": {"startLines": "2,3,4,5,6,7,8,10,11,12", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,185,256,328,386,444,553,617,680", "endLines": "2,3,4,5,6,7,9,10,11,15", "endColumns": "59,69,70,71,57,57,10,63,62,10", "endOffsets": "110,180,251,323,381,439,548,612,675,847"}, "to": {"startLines": "10,11,12,13,14,15,16,18,19,21", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "611,671,741,812,884,942,1000,1109,1173,1292", "endLines": "10,11,12,13,14,15,17,18,19,24", "endColumns": "59,69,70,71,57,57,10,63,62,10", "endOffsets": "666,736,807,879,937,995,1104,1168,1231,1459"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2144dbf5c98623e2472a5ef01095ffd5\\transformed\\GDTSDK.unionNormal.4.640.1510\\res\\values-sw600dp-v13\\values-sw600dp-v13.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "55", "endOffsets": "106"}, "to": {"startLines": "20", "startColumns": "4", "startOffsets": "1236", "endColumns": "55", "endOffsets": "1287"}}]}, {"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\values-my_values-my.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a3ae461f31008f99a87e29f3c99a3c83\\transformed\\appcompat-v7-28.0.0\\res\\values-my\\values-my.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,319,436,529,641,769,847,926,1017,1110,1206,1300,1401,1494,1589,1683,1774,1865,1951,2074,2186,2288,2414,2525,2635,2795,2895", "endColumns": "108,104,116,92,111,127,77,78,90,92,95,93,100,92,94,93,90,90,85,122,111,101,125,110,109,159,99,83", "endOffsets": "209,314,431,524,636,764,842,921,1012,1105,1201,1295,1396,1489,1584,1678,1769,1860,1946,2069,2181,2283,2409,2520,2630,2790,2890,2974"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\daefd50eed0506e326ae247974906cd2\\transformed\\support-compat-28.0.0\\res\\values-my\\values-my.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2979", "endColumns": "100", "endOffsets": "3075"}}]}, {"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\values-fa_values-fa.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\daefd50eed0506e326ae247974906cd2\\transformed\\support-compat-28.0.0\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2903", "endColumns": "100", "endOffsets": "2999"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a3ae461f31008f99a87e29f3c99a3c83\\transformed\\appcompat-v7-28.0.0\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,215,318,429,513,616,731,810,888,981,1076,1172,1266,1369,1464,1561,1660,1753,1843,1924,2036,2139,2237,2347,2451,2560,2721,2822", "endColumns": "109,102,110,83,102,114,78,77,92,94,95,93,102,94,96,98,92,89,80,111,102,97,109,103,108,160,100,80", "endOffsets": "210,313,424,508,611,726,805,883,976,1071,1167,1261,1364,1459,1556,1655,1748,1838,1919,2031,2134,2232,2342,2446,2555,2716,2817,2898"}}]}, {"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\values-tr_values-tr.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\daefd50eed0506e326ae247974906cd2\\transformed\\support-compat-28.0.0\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2865", "endColumns": "100", "endOffsets": "2961"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a3ae461f31008f99a87e29f3c99a3c83\\transformed\\appcompat-v7-28.0.0\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,309,421,506,612,732,811,887,978,1071,1164,1258,1356,1449,1551,1646,1737,1828,1907,2014,2119,2215,2322,2424,2532,2688,2786", "endColumns": "104,98,111,84,105,119,78,75,90,92,92,93,97,92,101,94,90,90,78,106,104,95,106,101,107,155,97,78", "endOffsets": "205,304,416,501,607,727,806,882,973,1066,1159,1253,1351,1444,1546,1641,1732,1823,1902,2009,2114,2210,2317,2419,2527,2683,2781,2860"}}]}, {"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\values-v26_values-v26.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a3ae461f31008f99a87e29f3c99a3c83\\transformed\\appcompat-v7-28.0.0\\res\\values-v26\\values-v26.xml", "from": {"startLines": "2,3,4,8,12,16", "startColumns": "4,4,4,4,4,4", "startOffsets": "55,130,217,431,657,896", "endLines": "2,3,7,11,15,16", "endColumns": "74,86,12,12,12,92", "endOffsets": "125,212,426,652,891,984"}, "to": {"startLines": "2,3,4,8,12,16", "startColumns": "4,4,4,4,4,4", "startOffsets": "55,130,217,381,557,796", "endLines": "2,3,7,11,15,16", "endColumns": "74,86,12,12,12,92", "endOffsets": "125,212,376,552,791,884"}}]}, {"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\values-watch-v20_values-watch-v20.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a3ae461f31008f99a87e29f3c99a3c83\\transformed\\appcompat-v7-28.0.0\\res\\values-watch-v20\\values-watch-v20.xml", "from": {"startLines": "2,5,8", "startColumns": "4,4,4", "startOffsets": "55,214,385", "endLines": "4,7,10", "endColumns": "12,12,12", "endOffsets": "209,380,553"}}]}, {"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\values-en_values-en.arsc.flat", "map": [{"source": "E:\\Ai\\AiCode\\game\\MountainSurvival\\code\\app\\src\\main\\res\\values-en\\strings.xml", "from": {"startLines": "1,2,6,11,9,19,17,15,16,18,8,7,26,24,27,25,23,22,28,5,12,10", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "16,70,204,444,348,1083,968,838,907,1021,299,252,2918,1314,2973,2832,1245,1163,3034,154,727,394", "endColumns": "53,56,47,282,45,50,52,68,60,61,48,46,54,1517,60,85,68,81,109,49,77,49", "endOffsets": "65,122,247,722,389,1129,1016,902,963,1078,343,294,2968,2827,3029,2913,1309,1240,3139,199,800,439"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,96", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,109,166,214,497,543,594,647,716,777,839,888,935,990,2508,2569,2655,2724,2806,2916,2966,8154", "endColumns": "53,56,47,282,45,50,52,68,60,61,48,46,54,1517,60,85,68,81,109,49,77,49", "endOffsets": "104,161,209,492,538,589,642,711,772,834,883,930,985,2503,2564,2650,2719,2801,2911,2961,3039,8199"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\077c69690200a4c0c0ddf0630ba2f6e7\\transformed\\open_ad_sdk_6.8.4.0\\res\\values-en\\values-en.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,98,135,181,236,307,367,412,473,527,593,649,730,807,878,1001,1090,1214,1347,1484,1545,1607,1678,1754,1807,1860,1984,2033,2118,2169,2220,2263,2305,2347,2439,2512,2586,2662,2722,2787,2872,2989,3034,3118,3207,3295,3351,3409,3460,3562,3642,3729,3823,3899,3960,4057,4128,4224,4290,4357,4397,4442,4493,4543,4595,4661,4717,4773,4832,4898,4984,5057,5116", "endColumns": "42,36,45,54,70,59,44,60,53,65,55,80,76,70,122,88,123,132,136,60,61,70,75,52,52,123,48,84,50,50,42,41,41,91,72,73,75,59,64,84,116,44,83,88,87,55,57,50,101,79,86,93,75,60,96,70,95,65,66,39,44,50,49,51,65,55,55,58,65,85,72,58,48", "endOffsets": "93,130,176,231,302,362,407,468,522,588,644,725,802,873,996,1085,1209,1342,1479,1540,1602,1673,1749,1802,1855,1979,2028,2113,2164,2215,2258,2300,2342,2434,2507,2581,2657,2717,2782,2867,2984,3029,3113,3202,3290,3346,3404,3455,3557,3637,3724,3818,3894,3955,4052,4123,4219,4285,4352,4392,4437,4488,4538,4590,4656,4712,4768,4827,4893,4979,5052,5111,5160"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3044,3087,3124,3170,3225,3296,3356,3401,3462,3516,3582,3638,3719,3796,3867,3990,4079,4203,4336,4473,4534,4596,4667,4743,4796,4849,4973,5022,5107,5158,5209,5252,5294,5336,5428,5501,5575,5651,5711,5776,5861,5978,6023,6107,6196,6284,6340,6398,6449,6551,6631,6718,6812,6888,6949,7046,7117,7213,7279,7346,7386,7431,7482,7532,7584,7650,7706,7762,7821,7887,7973,8046,8105", "endColumns": "42,36,45,54,70,59,44,60,53,65,55,80,76,70,122,88,123,132,136,60,61,70,75,52,52,123,48,84,50,50,42,41,41,91,72,73,75,59,64,84,116,44,83,88,87,55,57,50,101,79,86,93,75,60,96,70,95,65,66,39,44,50,49,51,65,55,55,58,65,85,72,58,48", "endOffsets": "3082,3119,3165,3220,3291,3351,3396,3457,3511,3577,3633,3714,3791,3862,3985,4074,4198,4331,4468,4529,4591,4662,4738,4791,4844,4968,5017,5102,5153,5204,5247,5289,5331,5423,5496,5570,5646,5706,5771,5856,5973,6018,6102,6191,6279,6335,6393,6444,6546,6626,6713,6807,6883,6944,7041,7112,7208,7274,7341,7381,7426,7477,7527,7579,7645,7701,7757,7816,7882,7968,8041,8100,8149"}}]}, {"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\values-uk_values-uk.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\daefd50eed0506e326ae247974906cd2\\transformed\\support-compat-28.0.0\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2900", "endColumns": "100", "endOffsets": "2996"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a3ae461f31008f99a87e29f3c99a3c83\\transformed\\appcompat-v7-28.0.0\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,215,317,423,509,617,735,814,894,985,1078,1174,1268,1369,1462,1557,1652,1743,1834,1915,2021,2128,2226,2334,2440,2549,2719,2819", "endColumns": "109,101,105,85,107,117,78,79,90,92,95,93,100,92,94,94,90,90,80,105,106,97,107,105,108,169,99,80", "endOffsets": "210,312,418,504,612,730,809,889,980,1073,1169,1263,1364,1457,1552,1647,1738,1829,1910,2016,2123,2221,2329,2435,2544,2714,2814,2895"}}]}, {"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\values-en-rCA_values-en-rCA.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a3ae461f31008f99a87e29f3c99a3c83\\transformed\\appcompat-v7-28.0.0\\res\\values-en-rCA\\values-en-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,309,417,501,601,716,793,869,960,1053,1149,1243,1344,1437,1532,1626,1717,1808,1890,1993,2097,2196,2301,2404,2508,2664,2764", "endColumns": "103,99,107,83,99,114,76,75,90,92,95,93,100,92,94,93,90,90,81,102,103,98,104,102,103,155,99,81", "endOffsets": "204,304,412,496,596,711,788,864,955,1048,1144,1238,1339,1432,1527,1621,1712,1803,1885,1988,2092,2191,2296,2399,2503,2659,2759,2841"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\daefd50eed0506e326ae247974906cd2\\transformed\\support-compat-28.0.0\\res\\values-en-rCA\\values-en-rCA.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2846", "endColumns": "100", "endOffsets": "2942"}}]}, {"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\values-in_values-in.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\daefd50eed0506e326ae247974906cd2\\transformed\\support-compat-28.0.0\\res\\values-in\\values-in.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2887", "endColumns": "100", "endOffsets": "2983"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a3ae461f31008f99a87e29f3c99a3c83\\transformed\\appcompat-v7-28.0.0\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,215,317,422,509,613,729,811,890,981,1074,1170,1264,1365,1458,1553,1647,1738,1829,1915,2018,2127,2228,2332,2440,2548,2704,2803", "endColumns": "109,101,104,86,103,115,81,78,90,92,95,93,100,92,94,93,90,90,85,102,108,100,103,107,107,155,98,83", "endOffsets": "210,312,417,504,608,724,806,885,976,1069,1165,1259,1360,1453,1548,1642,1733,1824,1910,2013,2122,2223,2327,2435,2543,2699,2798,2882"}}]}, {"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\values-am_values-am.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\daefd50eed0506e326ae247974906cd2\\transformed\\support-compat-28.0.0\\res\\values-am\\values-am.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2817", "endColumns": "100", "endOffsets": "2913"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a3ae461f31008f99a87e29f3c99a3c83\\transformed\\appcompat-v7-28.0.0\\res\\values-am\\values-am.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,207,306,412,498,601,714,792,870,961,1054,1147,1241,1342,1435,1530,1624,1715,1805,1884,1984,2084,2180,2283,2382,2489,2642,2738", "endColumns": "101,98,105,85,102,112,77,77,90,92,92,93,100,92,94,93,90,89,78,99,99,95,102,98,106,152,95,78", "endOffsets": "202,301,407,493,596,709,787,865,956,1049,1142,1236,1337,1430,1525,1619,1710,1800,1879,1979,2079,2175,2278,2377,2484,2637,2733,2812"}}]}, {"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\values-es-rUS_values-es-rUS.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\daefd50eed0506e326ae247974906cd2\\transformed\\support-compat-28.0.0\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2920", "endColumns": "100", "endOffsets": "3016"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a3ae461f31008f99a87e29f3c99a3c83\\transformed\\appcompat-v7-28.0.0\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,334,442,527,628,751,836,918,1009,1102,1198,1292,1392,1485,1584,1680,1771,1862,1944,2056,2156,2257,2365,2472,2579,2738,2838", "endColumns": "119,108,107,84,100,122,84,81,90,92,95,93,99,92,98,95,90,90,81,111,99,100,107,106,106,158,99,81", "endOffsets": "220,329,437,522,623,746,831,913,1004,1097,1193,1287,1387,1480,1579,1675,1766,1857,1939,2051,2151,2252,2360,2467,2574,2733,2833,2915"}}]}, {"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\values-vi_values-vi.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a3ae461f31008f99a87e29f3c99a3c83\\transformed\\appcompat-v7-28.0.0\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,327,436,520,623,742,819,896,987,1080,1176,1270,1371,1464,1559,1657,1748,1839,1923,2027,2136,2237,2342,2456,2561,2718,2817", "endColumns": "113,107,108,83,102,118,76,76,90,92,95,93,100,92,94,97,90,90,83,103,108,100,104,113,104,156,98,83", "endOffsets": "214,322,431,515,618,737,814,891,982,1075,1171,1265,1366,1459,1554,1652,1743,1834,1918,2022,2131,2232,2337,2451,2556,2713,2812,2896"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\daefd50eed0506e326ae247974906cd2\\transformed\\support-compat-28.0.0\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2901", "endColumns": "100", "endOffsets": "2997"}}]}, {"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\values-watch-v21_values-watch-v21.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a3ae461f31008f99a87e29f3c99a3c83\\transformed\\appcompat-v7-28.0.0\\res\\values-watch-v21\\values-watch-v21.xml", "from": {"startLines": "2,6,10", "startColumns": "4,4,4", "startOffsets": "55,271,499", "endLines": "5,9,13", "endColumns": "12,12,12", "endOffsets": "266,494,724"}}]}, {"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\values-lv_values-lv.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\daefd50eed0506e326ae247974906cd2\\transformed\\support-compat-28.0.0\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "3076", "endColumns": "100", "endOffsets": "3172"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a3ae461f31008f99a87e29f3c99a3c83\\transformed\\appcompat-v7-28.0.0\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,333,442,528,632,754,836,918,1028,1136,1243,1352,1464,1567,1679,1786,1891,1991,2076,2185,2297,2396,2507,2616,2721,2895,2994", "endColumns": "119,107,108,85,103,121,81,81,109,107,106,108,111,102,111,106,104,99,84,108,111,98,110,108,104,173,98,81", "endOffsets": "220,328,437,523,627,749,831,913,1023,1131,1238,1347,1459,1562,1674,1781,1886,1986,2071,2180,2292,2391,2502,2611,2716,2890,2989,3071"}}]}, {"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\values-sk_values-sk.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a3ae461f31008f99a87e29f3c99a3c83\\transformed\\appcompat-v7-28.0.0\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,312,423,509,617,735,816,897,988,1081,1180,1274,1375,1468,1563,1661,1752,1843,1927,2032,2141,2240,2346,2457,2566,2732,2830", "endColumns": "106,99,110,85,107,117,80,80,90,92,98,93,100,92,94,97,90,90,83,104,108,98,105,110,108,165,97,87", "endOffsets": "207,307,418,504,612,730,811,892,983,1076,1175,1269,1370,1463,1558,1656,1747,1838,1922,2027,2136,2235,2341,2452,2561,2727,2825,2913"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\daefd50eed0506e326ae247974906cd2\\transformed\\support-compat-28.0.0\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2918", "endColumns": "100", "endOffsets": "3014"}}]}, {"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\values-is_values-is.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a3ae461f31008f99a87e29f3c99a3c83\\transformed\\appcompat-v7-28.0.0\\res\\values-is\\values-is.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,205,302,414,499,600,714,794,874,965,1058,1154,1248,1355,1448,1543,1638,1729,1823,1904,2014,2122,2220,2329,2428,2531,2686,2784", "endColumns": "99,96,111,84,100,113,79,79,90,92,95,93,106,92,94,94,90,93,80,109,107,97,108,98,102,154,97,80", "endOffsets": "200,297,409,494,595,709,789,869,960,1053,1149,1243,1350,1443,1538,1633,1724,1818,1899,2009,2117,2215,2324,2423,2526,2681,2779,2860"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\daefd50eed0506e326ae247974906cd2\\transformed\\support-compat-28.0.0\\res\\values-is\\values-is.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2865", "endColumns": "100", "endOffsets": "2961"}}]}, {"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\values-uz_values-uz.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\daefd50eed0506e326ae247974906cd2\\transformed\\support-compat-28.0.0\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2889", "endColumns": "100", "endOffsets": "2985"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a3ae461f31008f99a87e29f3c99a3c83\\transformed\\appcompat-v7-28.0.0\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,314,428,514,614,730,810,889,980,1073,1169,1263,1358,1451,1546,1641,1732,1824,1908,2017,2124,2225,2333,2438,2545,2706,2805", "endColumns": "104,103,113,85,99,115,79,78,90,92,95,93,94,92,94,94,90,91,83,108,106,100,107,104,106,160,98,83", "endOffsets": "205,309,423,509,609,725,805,884,975,1068,1164,1258,1353,1446,1541,1636,1727,1819,1903,2012,2119,2220,2328,2433,2540,2701,2800,2884"}}]}, {"outputFile": "com.ainative.mountainsurvival.app-release-24:/values-zh-rTW_values-zh-rTW.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a3ae461f31008f99a87e29f3c99a3c83\\transformed\\appcompat-v7-28.0.0\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,120", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,293,393,475,572,680,756,832,924,1018,1116,1212,1308,1402,1498,1590,1682,1774,1852,1948,2044,2139,2236,2331,2431,2581,9091", "endColumns": "94,92,99,81,96,107,75,75,91,93,97,95,95,93,95,91,91,91,77,95,95,94,96,94,99,149,93,77", "endOffsets": "195,288,388,470,567,675,751,827,919,1013,1111,1207,1303,1397,1493,1585,1677,1769,1847,1943,2039,2134,2231,2326,2426,2576,2670,9164"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\daefd50eed0506e326ae247974906cd2\\transformed\\support-compat-28.0.0\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "124", "startColumns": "4", "startOffsets": "9309", "endColumns": "100", "endOffsets": "9405"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\077c69690200a4c0c0ddf0630ba2f6e7\\transformed\\open_ad_sdk_6.8.4.0\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "9529,9572,9609,9655,9710,9771,9827,9868,9917,9968,10028,10076,10139,10203,10262,10334,10407,10475,10548,10629,10686,10744,10802,10865,10914,10959,11024,11067,11125,11169,11216,11259,11313,11379,11440,11505,11560,11617,11695,11757,11796,11875,11948,12029,12079,12135,12194,12232,12275,12324,12374,12426,12482,12535,12589,12644,12703,12764,12825,12877", "endColumns": "42,36,45,54,60,55,40,48,50,59,47,62,63,58,71,72,67,72,80,56,57,57,62,48,44,64,42,57,43,46,42,53,65,60,64,54,56,77,61,38,78,72,80,49,55,58,37,42,48,49,51,55,52,53,54,58,60,60,51,45", "endOffsets": "9567,9604,9650,9705,9766,9822,9863,9912,9963,10023,10071,10134,10198,10257,10329,10402,10470,10543,10624,10681,10739,10797,10860,10909,10954,11019,11062,11120,11164,11211,11254,11308,11374,11435,11500,11555,11612,11690,11752,11791,11870,11943,12024,12074,12130,12189,12227,12270,12319,12369,12421,12477,12530,12584,12639,12698,12759,12820,12872,12918"}}, {"source": "E:\\Ai\\AiCode\\game\\MountainSurvival\\code\\app\\src\\main\\res\\values-zh-rTW\\strings.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,141,-1,-1,-1,127,128,126,124,123,131,130,121,120,135,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,144,145,-1,149,-1,148,146,147,-1,-1,-1,138,-1,-1,-1,-1,-1,-1,-1,140,139,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,116,117,115,104,105,103,134,108,109,107,112,113,111,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,-1,-1,-1,4,4,4,4,4,4,4,4,4,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,4,-1,4,-1,4,4,4,-1,-1,-1,4,-1,-1,-1,-1,-1,-1,-1,4,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,4,4,4,4,4,4,4,4,4,4,4,4,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,8221,-1,-1,-1,7438,7592,7367,7157,7083,7727,7658,6879,6806,7934,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,8292,8363,-1,8616,-1,8549,8430,8491,-1,-1,-1,8068,-1,-1,-1,-1,-1,-1,-1,8164,8109,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,6534,6711,6447,5406,5659,5319,7806,5827,6013,5742,6181,6359,6091,-1", "endColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,47,-1,-1,-1,153,64,70,208,73,59,68,202,72,111,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,70,66,-1,75,-1,66,60,57,-1,-1,-1,40,-1,-1,-1,-1,-1,-1,-1,56,54,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,176,75,86,252,81,86,127,185,76,84,177,86,89,-1", "endOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,8264,-1,-1,-1,7587,7652,7433,7361,7152,7782,7722,7077,6874,8041,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,8358,8425,-1,8687,-1,8611,8486,8544,-1,-1,-1,8104,-1,-1,-1,-1,-1,-1,-1,8216,8159,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,6706,6782,6529,5654,5736,5401,7929,6008,6085,5822,6354,6441,6176,-1"}, "to": {"startLines": "29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,121,122,123,125,126,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2675,2750,2806,2867,2917,2960,3037,3093,3135,3192,3244,3307,3360,3422,3471,3531,3579,3618,3665,3712,3866,3931,4002,4211,4285,4345,4414,4617,4690,4802,4844,4929,4986,5032,5088,5131,5176,5217,5328,5379,5447,5561,5621,5681,5740,5800,5863,5903,5950,6003,6061,6122,6184,6232,6291,6334,6376,6451,6522,6589,6640,6716,6766,6833,6894,6952,7050,7148,7211,7252,7304,7815,7871,7935,7992,8050,8160,8217,8272,8319,8370,8454,8524,8583,8669,8749,8825,8880,8938,8985,9030,9169,9221,9265,9410,9468,12923,12968,13145,13221,13308,13561,13643,13730,13858,14044,14121,14206,14384,14471,14561", "endColumns": "74,55,60,49,42,76,55,41,56,51,62,52,61,48,59,47,38,46,46,153,64,70,208,73,59,68,202,72,111,41,84,56,45,55,42,44,40,110,50,67,113,59,59,58,59,62,39,46,52,57,60,61,47,58,42,41,74,70,66,50,75,49,66,60,57,97,97,62,40,51,510,55,63,56,57,109,56,54,46,50,83,69,58,85,79,75,54,57,46,44,60,51,43,43,57,60,44,176,75,86,252,81,86,127,185,76,84,177,86,89,42", "endOffsets": "2745,2801,2862,2912,2955,3032,3088,3130,3187,3239,3302,3355,3417,3466,3526,3574,3613,3660,3707,3861,3926,3997,4206,4280,4340,4409,4612,4685,4797,4839,4924,4981,5027,5083,5126,5171,5212,5323,5374,5442,5556,5616,5676,5735,5795,5858,5898,5945,5998,6056,6117,6179,6227,6286,6329,6371,6446,6517,6584,6635,6711,6761,6828,6889,6947,7045,7143,7206,7247,7299,7810,7866,7930,7987,8045,8155,8212,8267,8314,8365,8449,8519,8578,8664,8744,8820,8875,8933,8980,9025,9086,9216,9260,9304,9463,9524,12963,13140,13216,13303,13556,13638,13725,13853,14039,14116,14201,14379,14466,14556,14599"}}]}, {"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\values-pt-rPT_values-pt-rPT.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a3ae461f31008f99a87e29f3c99a3c83\\transformed\\appcompat-v7-28.0.0\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,331,438,527,628,752,836,917,1009,1103,1201,1295,1395,1489,1585,1680,1772,1864,1951,2058,2170,2272,2380,2487,2594,2765,2864", "endColumns": "119,105,106,88,100,123,83,80,91,93,97,93,99,93,95,94,91,91,86,106,111,101,107,106,106,170,98,84", "endOffsets": "220,326,433,522,623,747,831,912,1004,1098,1196,1290,1390,1484,1580,1675,1767,1859,1946,2053,2165,2267,2375,2482,2589,2760,2859,2944"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\daefd50eed0506e326ae247974906cd2\\transformed\\support-compat-28.0.0\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2949", "endColumns": "100", "endOffsets": "3045"}}]}, {"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\values-or_values-or.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a3ae461f31008f99a87e29f3c99a3c83\\transformed\\appcompat-v7-28.0.0\\res\\values-or\\values-or.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,224,334,441,527,631,751,828,905,996,1089,1186,1281,1382,1475,1570,1666,1757,1847,1929,2039,2144,2250,2361,2464,2582,2745,2847", "endColumns": "118,109,106,85,103,119,76,76,90,92,96,94,100,92,94,95,90,89,81,109,104,105,110,102,117,162,101,88", "endOffsets": "219,329,436,522,626,746,823,900,991,1084,1181,1276,1377,1470,1565,1661,1752,1842,1924,2034,2139,2245,2356,2459,2577,2740,2842,2931"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\daefd50eed0506e326ae247974906cd2\\transformed\\support-compat-28.0.0\\res\\values-or\\values-or.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2936", "endColumns": "100", "endOffsets": "3032"}}]}, {"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\values-v23_values-v23.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b255ac174ecb581f0983b73e2410e4dd\\transformed\\cardview-v7-28.0.0\\res\\values-v23\\values-v23.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endLines": "4", "endColumns": "12", "endOffsets": "200"}, "to": {"startLines": "37", "startColumns": "4", "startOffsets": "2354", "endLines": "39", "endColumns": "12", "endOffsets": "2499"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a3ae461f31008f99a87e29f3c99a3c83\\transformed\\appcompat-v7-28.0.0\\res\\values-v23\\values-v23.xml", "from": {"startLines": "2,3,4,5,6,19,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,190,325,400,487,1277,2079,2206,2311,2426,2533", "endLines": "2,3,4,5,18,31,32,33,34,35,36", "endColumns": "134,134,74,86,12,12,126,104,114,106,112", "endOffsets": "185,320,395,482,1272,2074,2201,2306,2421,2528,2641"}, "to": {"startLines": "2,3,4,5,6,19,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,190,325,400,487,1131,1787,1914,2019,2134,2241", "endLines": "2,3,4,5,18,31,32,33,34,35,36", "endColumns": "134,134,74,86,12,12,126,104,114,106,112", "endOffsets": "185,320,395,482,1126,1782,1909,2014,2129,2236,2349"}}]}, {"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\values-large-v4_values-large-v4.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\71b2f468c1ec12de737a4945bfcdd2ee\\transformed\\design-28.0.0\\res\\values-large-v4\\values-large-v4.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,177", "endColumns": "121,133", "endOffsets": "172,306"}, "to": {"startLines": "11,12", "startColumns": "4,4", "startOffsets": "752,874", "endColumns": "121,133", "endOffsets": "869,1003"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a3ae461f31008f99a87e29f3c99a3c83\\transformed\\appcompat-v7-28.0.0\\res\\values-large-v4\\values-large-v4.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,114,185,256,326,396,464,532,636", "endColumns": "58,70,70,69,69,67,67,103,115", "endOffsets": "109,180,251,321,391,459,527,631,747"}}]}, {"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\values-b+sr+Latn_values-b+sr+Latn.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\daefd50eed0506e326ae247974906cd2\\transformed\\support-compat-28.0.0\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2912", "endColumns": "100", "endOffsets": "3008"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a3ae461f31008f99a87e29f3c99a3c83\\transformed\\appcompat-v7-28.0.0\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,318,424,510,614,732,814,895,986,1079,1175,1269,1370,1463,1558,1663,1754,1845,1933,2039,2147,2248,2353,2461,2562,2731,2828", "endColumns": "108,103,105,85,103,117,81,80,90,92,95,93,100,92,94,104,90,90,87,105,107,100,104,107,100,168,96,83", "endOffsets": "209,313,419,505,609,727,809,890,981,1074,1170,1264,1365,1458,1553,1658,1749,1840,1928,2034,2142,2243,2348,2456,2557,2726,2823,2907"}}]}, {"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\values-en-rGB_values-en-rGB.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\daefd50eed0506e326ae247974906cd2\\transformed\\support-compat-28.0.0\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2846", "endColumns": "100", "endOffsets": "2942"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a3ae461f31008f99a87e29f3c99a3c83\\transformed\\appcompat-v7-28.0.0\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,309,417,501,601,716,793,869,960,1053,1149,1243,1344,1437,1532,1626,1717,1808,1890,1993,2097,2196,2301,2404,2508,2664,2764", "endColumns": "103,99,107,83,99,114,76,75,90,92,95,93,100,92,94,93,90,90,81,102,103,98,104,102,103,155,99,81", "endOffsets": "204,304,412,496,596,711,788,864,955,1048,1144,1238,1339,1432,1527,1621,1712,1803,1885,1988,2092,2191,2296,2399,2503,2659,2759,2841"}}]}, {"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\values-hu_values-hu.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a3ae461f31008f99a87e29f3c99a3c83\\transformed\\appcompat-v7-28.0.0\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,318,433,517,629,759,835,911,1002,1095,1191,1285,1386,1479,1574,1669,1760,1851,1934,2044,2155,2255,2366,2474,2593,2775,2878", "endColumns": "107,104,114,83,111,129,75,75,90,92,95,93,100,92,94,94,90,90,82,109,110,99,110,107,118,181,102,82", "endOffsets": "208,313,428,512,624,754,830,906,997,1090,1186,1280,1381,1474,1569,1664,1755,1846,1929,2039,2150,2250,2361,2469,2588,2770,2873,2956"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\daefd50eed0506e326ae247974906cd2\\transformed\\support-compat-28.0.0\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2961", "endColumns": "100", "endOffsets": "3057"}}]}, {"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\values-af_values-af.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\daefd50eed0506e326ae247974906cd2\\transformed\\support-compat-28.0.0\\res\\values-af\\values-af.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2856", "endColumns": "100", "endOffsets": "2952"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a3ae461f31008f99a87e29f3c99a3c83\\transformed\\appcompat-v7-28.0.0\\res\\values-af\\values-af.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,309,415,500,603,721,797,874,965,1058,1154,1248,1348,1441,1536,1635,1730,1824,1905,2012,2115,2212,2320,2422,2524,2678,2776", "endColumns": "103,99,105,84,102,117,75,76,90,92,95,93,99,92,94,98,94,93,80,106,102,96,107,101,101,153,97,79", "endOffsets": "204,304,410,495,598,716,792,869,960,1053,1149,1243,1343,1436,1531,1630,1725,1819,1900,2007,2110,2207,2315,2417,2519,2673,2771,2851"}}]}, {"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\values-sw720dp-v17_values-sw720dp-v17.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2144dbf5c98623e2472a5ef01095ffd5\\transformed\\GDTSDK.unionNormal.4.640.1510\\res\\values-sw720dp-v17\\values-sw720dp-v17.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "55", "endOffsets": "106"}}]}, {"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\values-gl_values-gl.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a3ae461f31008f99a87e29f3c99a3c83\\transformed\\appcompat-v7-28.0.0\\res\\values-gl\\values-gl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,217,329,437,522,624,750,834,915,1007,1101,1199,1293,1394,1488,1584,1679,1771,1863,1945,2052,2161,2260,2368,2472,2579,2738,2838", "endColumns": "111,111,107,84,101,125,83,80,91,93,97,93,100,93,95,94,91,91,81,106,108,98,107,103,106,158,99,81", "endOffsets": "212,324,432,517,619,745,829,910,1002,1096,1194,1288,1389,1483,1579,1674,1766,1858,1940,2047,2156,2255,2363,2467,2574,2733,2833,2915"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\daefd50eed0506e326ae247974906cd2\\transformed\\support-compat-28.0.0\\res\\values-gl\\values-gl.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2920", "endColumns": "100", "endOffsets": "3016"}}]}, {"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\values-ldltr-v21_values-ldltr-v21.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a3ae461f31008f99a87e29f3c99a3c83\\transformed\\appcompat-v7-28.0.0\\res\\values-ldltr-v21\\values-ldltr-v21.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "112", "endOffsets": "163"}}]}, {"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\values-ta_values-ta.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a3ae461f31008f99a87e29f3c99a3c83\\transformed\\appcompat-v7-28.0.0\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,317,432,521,628,754,832,909,1009,1114,1210,1305,1412,1514,1618,1713,1815,1913,1995,2097,2201,2298,2408,2510,2617,2774,2874", "endColumns": "113,97,114,88,106,125,77,76,99,104,95,94,106,101,103,94,101,97,81,101,103,96,109,101,106,156,99,79", "endOffsets": "214,312,427,516,623,749,827,904,1004,1109,1205,1300,1407,1509,1613,1708,1810,1908,1990,2092,2196,2293,2403,2505,2612,2769,2869,2949"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\daefd50eed0506e326ae247974906cd2\\transformed\\support-compat-28.0.0\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2954", "endColumns": "100", "endOffsets": "3050"}}]}, {"outputFile": "com.ainative.mountainsurvival.app-release-24:/values_values.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\daefd50eed0506e326ae247974906cd2\\transformed\\support-compat-28.0.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "286,287,485,486,487,488,489,490,491,835,836,837,838,839,840,841,842,843,844,845,846,847,848,849,867,868,878,879,892,893,894,895,896,900,932,1158,3187,3188,3193,3196,3201,3596,3597", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15732,15801,28130,28200,28268,28340,28410,28471,28545,51217,51278,51339,51401,51465,51527,51588,51656,51756,51816,51882,51955,52024,52081,52133,53174,53246,53774,53809,54421,54471,54532,54589,54623,54801,56664,72962,205852,205969,206236,206529,206796,232795,232867", "endLines": "286,287,485,486,487,488,489,490,491,835,836,837,838,839,840,841,842,843,844,845,846,847,848,849,867,868,878,879,892,893,894,895,896,900,932,1158,3187,3191,3193,3199,3201,3596,3597", "endColumns": "68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,34,34,49,60,56,33,34,34,69,70,116,12,109,12,128,71,66", "endOffsets": "15796,15859,28195,28263,28335,28405,28466,28540,28613,51273,51334,51396,51460,51522,51583,51651,51751,51811,51877,51950,52019,52076,52128,52190,53241,53317,53804,53839,54466,54527,54584,54618,54653,54831,56729,73028,205964,206165,206341,206725,206920,232862,232929"}}, {"source": "E:\\Ai\\AiCode\\game\\MountainSurvival\\code\\app\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,141,-1,-1,-1,127,128,126,124,123,131,130,121,120,135,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,144,145,-1,149,-1,148,146,147,-1,-1,-1,138,-1,-1,-1,-1,-1,-1,-1,140,139,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,116,117,115,104,105,103,134,108,109,107,112,113,111,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,-1,-1,-1,4,4,4,4,4,4,4,4,4,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,4,-1,4,-1,4,4,4,-1,-1,-1,4,-1,-1,-1,-1,-1,-1,-1,4,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,4,4,4,4,4,4,4,4,4,4,4,4,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,8221,-1,-1,-1,7438,7592,7367,7157,7083,7727,7658,6879,6806,7934,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,8292,8363,-1,8616,-1,8549,8430,8491,-1,-1,-1,8068,-1,-1,-1,-1,-1,-1,-1,8164,8109,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,6534,6711,6447,5406,5659,5319,7806,5827,6013,5742,6181,6359,6091,-1", "endColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,47,-1,-1,-1,153,64,70,208,73,59,68,202,72,111,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,70,66,-1,75,-1,66,60,57,-1,-1,-1,40,-1,-1,-1,-1,-1,-1,-1,56,54,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,176,75,86,252,81,86,127,185,76,84,177,86,89,-1", "endOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,8264,-1,-1,-1,7587,7652,7433,7361,7152,7782,7722,7077,6874,8041,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,8358,8425,-1,8687,-1,8611,8486,8544,-1,-1,-1,8104,-1,-1,-1,-1,-1,-1,-1,8216,8159,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,6706,6782,6529,5654,5736,5401,7929,6008,6085,5822,6354,6441,6176,-1"}, "to": {"startLines": "972,973,974,975,976,977,978,979,980,998,999,1000,1001,1002,1005,1006,1007,1008,1009,1010,1011,1012,1013,1014,1015,1016,1017,1018,1019,1020,1021,1022,1023,1024,1025,1028,1029,1030,1031,1032,1033,1035,1036,1037,1038,1039,1083,1084,1085,1086,1087,1088,1089,1092,1094,1095,1096,1097,1098,1099,1100,1101,1102,1103,1104,1105,1106,1107,1108,1117,1118,1119,1120,1121,1122,1123,1124,1125,1126,1127,1128,1129,1130,1131,1132,1133,1134,1135,1136,1138,1139,1141,1156,1157,1159,1160,1332,1333,1334,1335,1336,1337,1338,1339,1340,1341,1342,1343,1344,1345,1346", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "59492,59567,59623,59684,59734,59777,59854,59910,59952,61331,61383,61446,61499,61561,61801,61861,61909,61948,61995,62042,62196,62261,62332,62541,62615,62675,62744,62947,63020,63132,63174,63259,63316,63362,63418,63765,63810,63851,63962,64013,64081,64343,64403,64463,64522,64582,67392,67432,67479,67532,67590,67651,67713,67873,68013,68056,68098,68173,68244,68311,68362,68438,68488,68555,68616,68674,68772,68870,68933,69933,69985,70496,70552,70616,70673,70731,70841,70898,70953,71000,71051,71135,71205,71264,71350,71430,71506,71561,71619,71710,71755,71869,72874,72918,73033,73091,85247,85292,85469,85545,85632,85885,85967,86054,86182,86368,86445,86530,86708,86795,86885", "endColumns": "74,55,60,49,42,76,55,41,56,51,62,52,61,48,59,47,38,46,46,153,64,70,208,73,59,68,202,72,111,41,84,56,45,55,42,44,40,110,50,67,113,59,59,58,59,62,39,46,52,57,60,61,47,58,42,41,74,70,66,50,75,49,66,60,57,97,97,62,40,51,510,55,63,56,57,109,56,54,46,50,83,69,58,85,79,75,54,57,46,44,60,51,43,43,57,60,44,176,75,86,252,81,86,127,185,76,84,177,86,89,42", "endOffsets": "59562,59618,59679,59729,59772,59849,59905,59947,60004,61378,61441,61494,61556,61605,61856,61904,61943,61990,62037,62191,62256,62327,62536,62610,62670,62739,62942,63015,63127,63169,63254,63311,63357,63413,63456,63805,63846,63957,64008,64076,64190,64398,64458,64517,64577,64640,67427,67474,67527,67585,67646,67708,67756,67927,68051,68093,68168,68239,68306,68357,68433,68483,68550,68611,68669,68767,68865,68928,68969,69980,70491,70547,70611,70668,70726,70836,70893,70948,70995,71046,71130,71200,71259,71345,71425,71501,71556,71614,71661,71750,71811,71916,72913,72957,73086,73147,85287,85464,85540,85627,85880,85962,86049,86177,86363,86440,86525,86703,86790,86880,86923"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\62922e5f87afeea95a66affda0037661\\transformed\\beizi_fusion_sdk_5.2.1.6\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "13,14,15,147,154,158,185,186,404,982,983,984,985,986,987,988,989,990,991,992,993,994,995,996,2776,2786,2796,2799,2802,2805,2808,3874,3883", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "627,675,724,6953,7356,7558,9219,9280,22704,60154,60258,60304,60348,60393,60437,60516,60636,60742,60792,60863,60908,60970,61034,61141,178341,178962,179584,179759,179925,180082,180180,249516,250035", "endLines": "13,14,15,147,154,158,185,186,404,982,983,984,985,986,987,988,989,990,991,992,993,994,995,996,2785,2795,2798,2801,2804,2807,2815,3882,3891", "endColumns": "47,48,49,67,58,40,60,62,49,103,45,43,44,43,78,119,105,49,70,44,61,63,106,68,12,12,12,12,12,12,12,12,12", "endOffsets": "670,719,769,7016,7410,7594,9275,9338,22749,60253,60299,60343,60388,60432,60511,60631,60737,60787,60858,60903,60965,61029,61136,61205,178957,179579,179754,179920,180077,180175,180658,250030,250546"}}, {"source": "E:\\Ai\\AiCode\\game\\MountainSurvival\\code\\app\\src\\main\\res\\values\\colors.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "184,189,192,223,224,300,301,310,311,312,319,324,325,326,327,405,406", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "9171,9510,9713,11872,11921,16536,16583,17223,17270,17317,17775,18109,18154,18199,18246,22754,22801", "endColumns": "47,50,41,48,44,46,51,46,46,46,47,44,44,46,48,46,41", "endOffsets": "9214,9556,9750,11916,11961,16578,16630,17265,17312,17359,17818,18149,18194,18241,18290,22796,22838"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\0457feaf864ab549dda35ffc4a13a2e6\\transformed\\openset_sdk_6.5.2.6\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2,3,289,290,291,292,293,294,295,296,297,298,299,891,1040,1041,1042,1109,1110,1111,1137,1153,1154,1155,1360,2889,2898,2901,2906,2910,3935", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,213,15953,16004,16056,16112,16162,16214,16265,16321,16379,16432,16485,54384,64645,64682,64730,68974,69026,69080,71666,72525,72568,72735,87871,184386,184738,184837,185039,185292,252616", "endLines": "2,3,289,290,291,292,293,294,295,296,297,298,299,891,1040,1041,1042,1109,1110,1111,1137,1153,1154,1155,1362,2897,2900,2905,2909,2917,3940", "endColumns": "62,61,50,51,55,49,51,50,55,57,52,52,50,36,36,47,52,51,53,50,43,42,166,138,12,12,12,12,12,12,12", "endOffsets": "208,270,15999,16051,16107,16157,16209,16260,16316,16374,16427,16480,16531,54416,64677,64725,64778,69021,69075,69126,71705,72563,72730,72869,87962,184733,184832,185034,185287,185583,252890"}}, {"source": "E:\\Ai\\AiCode\\game\\MountainSurvival\\code\\app\\src\\main\\res\\values\\ic_launcher_background.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "229", "startColumns": "4", "startOffsets": "12246", "endColumns": "56", "endOffsets": "12298"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2144dbf5c98623e2472a5ef01095ffd5\\transformed\\GDTSDK.unionNormal.4.640.1510\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1"}, "to": {"startLines": "926,2835,2838,2841", "startColumns": "4,4,4,4", "startOffsets": "56306,181640,181771,181893", "endLines": "926,2837,2840,2843", "endColumns": "55,12,12,12", "endOffsets": "56357,181766,181888,182025"}}, {"source": "E:\\Ai\\AiCode\\game\\MountainSurvival\\code\\app\\src\\main\\res\\values\\themes.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "3454", "startColumns": "4", "startOffsets": "222029", "endLines": "3466", "endColumns": "12", "endOffsets": "222570"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\cb0f3cc3125274cec1b491c0584a407c\\transformed\\kssdk-ad-4.4.20.1-publishRelease-aa0a55f514\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,551,552,553,554,555,556,557,558,559,560,561,562,563,564,565,566,567,568,569,570,571,572,573,574,575,576,577,578,579,580,581,582,583,584,585,586,587,588,589,590,591,592,593,594,595,596,597,598,599,600,601,602,603,604,605,606,607,608,609,610,611,612,613,614,615,616,617,618,619,620,621,622,623,624,625,626,627,628,629,630,631,632,633,634,635,636,637,638,639,640,641,642,643,644,645,646,647,648,649,650,651,652,653,654,655,656,657,658,659,660,661,662,663,664,665,666,667,668,669,670,671,672,673,674,675,676,677,678,679,680,681,682,683,684,685,686,687,688,689,690,691,692,693,694,695,696,697,698,699,700,701,702,703,704,705,706,707,708,709,710,711,712,713,714,715,716,717,718,719,720,721,722,723,724,725,726,727,728,729,730,731,732,733,734,735,736,737,738,739,740,741,742,743,744,745,746,747,748,749,750,751,752,753,754,755,756,757,758,759,760,761,762,763,764,765,766,767,768,769,770,771,772,773,774,775,776,777,778,779,780,781,782,783,784,785,786,876,877,1043,1044,1045,1046,1047,1048,1049,1050,1051,1052,1053,1054,1055,1056,1057,1058,1059,1060,1061,1062,1063,1064,1065,1066,1067,1068,1069,1070,1071,1072,1073,1074,1075,1076,1077,1078,1079,1080,1081,1082,3496,3905,3910,3915,3925", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "12303,12353,12403,12453,12503,12559,12614,12669,12724,12789,12855,12923,12978,13031,13080,13145,13225,13305,13371,13443,13510,13569,13632,13693,13754,13821,13889,13960,14025,14097,14162,14234,14300,14354,14407,14452,32592,32646,32708,32766,32839,32896,32960,33025,33084,33154,33224,33296,33373,33444,33509,33596,33673,33747,33817,33885,33959,34047,34125,34189,34248,34303,34367,34432,34497,34555,34606,34659,34731,34805,34876,34949,35013,35083,35151,35214,35275,35335,35402,35470,35530,35599,35659,35727,35788,35852,35915,35973,36039,36102,36165,36228,36306,36382,36459,36528,36591,36667,36730,36804,36870,36938,37003,37073,37145,37214,37279,37340,37412,37475,37539,37609,37673,37738,37809,37886,37951,38018,38082,38156,38233,38307,38379,38455,38524,38588,38645,38711,38778,38845,38906,38962,39039,39101,39163,39236,39306,39371,39438,39502,39569,39631,39692,39761,39825,39889,39955,40018,40086,40151,40213,40276,40344,40409,40476,40540,40602,40667,40732,40803,40873,40937,41013,41096,41170,41238,41300,41364,41425,41487,41550,41614,41680,41743,41811,41878,41947,42016,42076,42159,42227,42300,42371,42446,42539,42614,42683,42751,42817,42883,42951,43024,43091,43152,43235,43308,43378,43444,43508,43578,43662,43736,43796,43859,43921,43987,44048,44119,44185,44255,44314,44367,44432,44485,44541,44594,44654,44721,44781,44852,44927,44995,45068,45135,45207,45275,45348,45410,45476,45538,45604,45671,45742,45803,45869,45939,46014,46081,46153,46214,46280,46349,46423,46490,46562,46622,46687,46753,46823,46887,46956,47029,47107,47170,47238,47301,47369,47432,47500,47564,47633,47686,47738,47800,47869,47935,47996,48060,48121,48189,48254,48314,53667,53711,64783,64850,64908,64958,65012,65069,65137,65218,65288,65360,65427,65493,65545,65603,65651,65709,65786,65863,65924,66003,66071,66132,66213,66285,66361,66424,66486,66548,66626,66683,66743,66794,66840,66904,66981,67051,67128,67203,67277,67339,224853,251154,251375,251620,252117", "endLines": "230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,551,552,553,554,555,556,557,558,559,560,561,562,563,564,565,566,567,568,569,570,571,572,573,574,575,576,577,578,579,580,581,582,583,584,585,586,587,588,589,590,591,592,593,594,595,596,597,598,599,600,601,602,603,604,605,606,607,608,609,610,611,612,613,614,615,616,617,618,619,620,621,622,623,624,625,626,627,628,629,630,631,632,633,634,635,636,637,638,639,640,641,642,643,644,645,646,647,648,649,650,651,652,653,654,655,656,657,658,659,660,661,662,663,664,665,666,667,668,669,670,671,672,673,674,675,676,677,678,679,680,681,682,683,684,685,686,687,688,689,690,691,692,693,694,695,696,697,698,699,700,701,702,703,704,705,706,707,708,709,710,711,712,713,714,715,716,717,718,719,720,721,722,723,724,725,726,727,728,729,730,731,732,733,734,735,736,737,738,739,740,741,742,743,744,745,746,747,748,749,750,751,752,753,754,755,756,757,758,759,760,761,762,763,764,765,766,767,768,769,770,771,772,773,774,775,776,777,778,779,780,781,782,783,784,785,786,876,877,1043,1044,1045,1046,1047,1048,1049,1050,1051,1052,1053,1054,1055,1056,1057,1058,1059,1060,1061,1062,1063,1064,1065,1066,1067,1068,1069,1070,1071,1072,1073,1074,1075,1076,1077,1078,1079,1080,1081,1082,3501,3909,3914,3924,3934", "endColumns": "49,49,49,49,55,54,54,54,64,65,67,54,52,48,64,79,79,65,71,66,58,62,60,60,66,67,70,64,71,64,71,65,53,52,44,55,53,61,57,72,56,63,64,58,69,69,71,76,70,64,86,76,73,69,67,73,87,77,63,58,54,63,64,64,57,50,52,71,73,70,72,63,69,67,62,60,59,66,67,59,68,59,67,60,63,62,57,65,62,62,62,77,75,76,68,62,75,62,73,65,67,64,69,71,68,64,60,71,62,63,69,63,64,70,76,64,66,63,73,76,73,71,75,68,63,56,65,66,66,60,55,76,61,61,72,69,64,66,63,66,61,60,68,63,63,65,62,67,64,61,62,67,64,66,63,61,64,64,70,69,63,75,82,73,67,61,63,60,61,62,63,65,62,67,66,68,68,59,82,67,72,70,74,92,74,68,67,65,65,67,72,66,60,82,72,69,65,63,69,83,73,59,62,61,65,60,70,65,69,58,52,64,52,55,52,59,66,59,70,74,67,72,66,71,67,72,61,65,61,65,66,70,60,65,69,74,66,71,60,65,68,73,66,71,59,64,65,69,63,68,72,77,62,67,62,67,62,67,63,68,52,51,61,68,65,60,63,60,67,64,59,52,43,62,66,57,49,53,56,67,80,69,71,66,65,51,57,47,57,76,76,60,78,67,60,80,71,75,62,61,61,77,56,59,50,45,63,76,69,76,74,73,61,52,10,10,10,10,10", "endOffsets": "12348,12398,12448,12498,12554,12609,12664,12719,12784,12850,12918,12973,13026,13075,13140,13220,13300,13366,13438,13505,13564,13627,13688,13749,13816,13884,13955,14020,14092,14157,14229,14295,14349,14402,14447,14503,32641,32703,32761,32834,32891,32955,33020,33079,33149,33219,33291,33368,33439,33504,33591,33668,33742,33812,33880,33954,34042,34120,34184,34243,34298,34362,34427,34492,34550,34601,34654,34726,34800,34871,34944,35008,35078,35146,35209,35270,35330,35397,35465,35525,35594,35654,35722,35783,35847,35910,35968,36034,36097,36160,36223,36301,36377,36454,36523,36586,36662,36725,36799,36865,36933,36998,37068,37140,37209,37274,37335,37407,37470,37534,37604,37668,37733,37804,37881,37946,38013,38077,38151,38228,38302,38374,38450,38519,38583,38640,38706,38773,38840,38901,38957,39034,39096,39158,39231,39301,39366,39433,39497,39564,39626,39687,39756,39820,39884,39950,40013,40081,40146,40208,40271,40339,40404,40471,40535,40597,40662,40727,40798,40868,40932,41008,41091,41165,41233,41295,41359,41420,41482,41545,41609,41675,41738,41806,41873,41942,42011,42071,42154,42222,42295,42366,42441,42534,42609,42678,42746,42812,42878,42946,43019,43086,43147,43230,43303,43373,43439,43503,43573,43657,43731,43791,43854,43916,43982,44043,44114,44180,44250,44309,44362,44427,44480,44536,44589,44649,44716,44776,44847,44922,44990,45063,45130,45202,45270,45343,45405,45471,45533,45599,45666,45737,45798,45864,45934,46009,46076,46148,46209,46275,46344,46418,46485,46557,46617,46682,46748,46818,46882,46951,47024,47102,47165,47233,47296,47364,47427,47495,47559,47628,47681,47733,47795,47864,47930,47991,48055,48116,48184,48249,48309,48362,53706,53769,64845,64903,64953,65007,65064,65132,65213,65283,65355,65422,65488,65540,65598,65646,65704,65781,65858,65919,65998,66066,66127,66208,66280,66356,66419,66481,66543,66621,66678,66738,66789,66835,66899,66976,67046,67123,67198,67272,67334,67387,225220,251370,251615,252112,252611"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b255ac174ecb581f0983b73e2410e4dd\\transformed\\cardview-v7-28.0.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "20,201,202,203,204,482,483,484,1384,2824,2826,2829", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1006,10396,10457,10519,10581,27960,28019,28076,89418,181158,181222,181348", "endLines": "20,201,202,203,204,482,483,484,1390,2825,2828,2831", "endColumns": "51,60,61,61,63,58,56,53,12,12,12,12", "endOffsets": "1053,10452,10514,10576,10640,28014,28071,28125,89827,181217,181343,181471"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b66139be6585a43ae23903a8750be796\\transformed\\wind-sdk-4.23.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "1142,1143,1144,1145,1146,1147,1148,1149,1150,1151,1152,3951,3955,3971,3989,3995,3999,4005", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "71921,71959,72002,72045,72100,72194,72246,72306,72359,72417,72471,253342,253499,254020,254619,254899,255041,255331", "endLines": "1142,1143,1144,1145,1146,1147,1148,1149,1150,1151,1152,3954,3970,3988,3994,3998,4004,4018", "endColumns": "37,42,42,54,93,51,59,52,57,53,53,12,12,12,12,12,12,12", "endOffsets": "71954,71997,72040,72095,72189,72241,72301,72354,72412,72466,72520,253494,254015,254614,254894,255036,255326,255913"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a7007720781ce56b641f8588ab1a8654\\transformed\\coordinatorlayout-28.0.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "29,3871", "startColumns": "4,4", "startOffsets": "1473,249371", "endLines": "29,3873", "endColumns": "60,12", "endOffsets": "1529,249511"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a3ae461f31008f99a87e29f3c99a3c83\\transformed\\appcompat-v7-28.0.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "30,36,38,173,174,175,176,178,179,180,181,182,183,187,188,190,191,193,194,195,196,197,198,199,200,216,217,218,219,221,222,225,226,227,228,266,267,268,269,270,271,272,273,274,275,276,277,302,303,304,305,306,307,308,309,313,314,315,316,317,318,320,321,322,323,328,329,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,536,537,541,542,543,544,545,546,547,854,855,856,857,858,859,860,861,869,870,871,872,874,883,884,890,915,917,918,921,922,933,934,935,936,937,938,939,940,941,942,943,944,945,946,947,948,949,950,951,952,953,954,955,956,957,958,959,960,961,962,963,964,965,966,967,968,969,970,971,1140,1347,1348,1353,1354,1355,1363,1371,1372,1376,1380,1391,1396,1402,1409,1413,1417,1422,1426,1430,1434,1438,1442,1446,1452,1456,1462,1466,1472,1476,1481,1485,1488,1492,1498,1502,1508,1512,1518,1521,1525,1529,1533,1537,1541,1542,1543,1544,1547,1550,1553,1556,1560,1561,1562,1563,1564,1567,1569,1571,1573,1578,1579,1583,1589,1593,1594,1596,1607,1608,1612,1618,1622,1676,1677,1681,1708,1712,1713,1717,1967,2134,2160,2328,2354,2385,2393,2399,2413,2435,2440,2445,2455,2464,2473,2477,2484,2492,2499,2500,2509,2512,2515,2519,2523,2527,2530,2531,2535,2539,2549,2554,2561,2567,2568,2571,2575,2580,2582,2584,2587,2590,2592,2596,2599,2606,2609,2612,2616,2618,2622,2624,2626,2628,2632,2640,2648,2660,2666,2675,2678,2689,2692,2697,2698,2918,2976,3039,3040,3050,3059,3064,3066,3070,3073,3076,3079,3082,3085,3088,3091,3095,3098,3101,3104,3108,3111,3115,3131,3132,3133,3134,3135,3136,3137,3138,3139,3140,3141,3142,3143,3144,3145,3146,3147,3148,3149,3150,3151,3153,3155,3156,3157,3158,3159,3160,3161,3162,3164,3165,3167,3168,3170,3172,3173,3175,3176,3177,3178,3179,3180,3182,3183,3184,3185,3186,3341,3343,3345,3347,3348,3349,3350,3351,3352,3353,3354,3355,3356,3357,3358,3359,3361,3362,3363,3364,3365,3366,3368,3372,3467,3468,3469,3470,3471,3472,3473,3502,3504,3506,3508,3510,3512,3513,3514,3515,3517,3519,3521,3522,3523,3524,3525,3526,3527,3528,3529,3530,3531,3532,3535,3536,3537,3538,3540,3542,3543,3545,3546,3548,3550,3552,3553,3554,3555,3556,3557,3558,3559,3560,3561,3562,3563,3565,3566,3567,3568,3570,3571,3572,3573,3574,3576,3578,3580,3582,3583,3584,3585,3586,3587,3588,3589,3590,3591,3592,3593,3594,3595", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1534,1761,1859,8444,8485,8540,8599,8723,8804,8865,8940,9016,9093,9343,9428,9561,9637,9755,9832,9910,10016,10122,10201,10281,10338,11408,11482,11557,11622,11751,11811,11966,12038,12111,12178,14508,14567,14626,14685,14744,14803,14857,14911,14964,15018,15072,15126,16635,16709,16788,16861,16935,17006,17078,17150,17364,17421,17479,17552,17626,17700,17823,17895,17968,18038,18295,18355,22843,22912,22981,23051,23125,23201,23265,23342,23418,23495,23560,23629,23706,23781,23850,23918,23995,24061,24122,24219,24284,24353,24452,24523,24582,24640,24697,24756,24820,24891,24963,25035,25107,25179,25246,25314,25382,25441,25504,25568,25658,25749,25809,25875,25942,26008,26078,26142,26195,26308,26366,26429,26494,26559,26634,26707,26779,26828,26889,26950,27011,27073,27137,27201,27265,27330,27393,27453,27514,27580,27639,27699,27761,27832,27892,31422,31508,31758,31848,31935,32023,32105,32188,32278,52407,52459,52517,52562,52628,52692,52749,52806,53322,53379,53427,53476,53567,54007,54054,54338,55663,55743,55807,55997,56057,56734,56808,56878,56956,57010,57080,57165,57213,57259,57330,57408,57486,57558,57632,57706,57780,57860,57933,58002,58074,58151,58212,58275,58341,58405,58476,58539,58604,58668,58729,58790,58842,58915,58989,59058,59133,59207,59281,59422,71816,86928,87006,87320,87408,87504,87967,88549,88638,88885,89166,89832,90117,90510,90987,91209,91431,91707,91934,92164,92394,92624,92854,93081,93500,93726,94151,94381,94809,95028,95311,95519,95650,95877,96303,96528,96955,97176,97601,97721,97997,98298,98622,98913,99227,99364,99495,99600,99842,100009,100213,100421,100692,100804,100916,101021,101138,101352,101498,101638,101724,102072,102160,102406,102824,103073,103155,103253,103845,103945,104197,104621,104876,108678,108767,109004,111028,111270,111372,111625,129642,139771,141287,151515,153043,154800,155426,155846,156907,158172,158428,158664,159211,159705,160310,160508,161088,161652,162027,162145,162683,162840,163036,163309,163565,163735,163876,163940,164222,164508,165184,165448,165786,166139,166233,166419,166725,166987,167112,167239,167478,167689,167808,168001,168178,168633,168814,168936,169195,169308,169495,169597,169704,169833,170108,170616,171112,171989,172283,172853,173002,173734,173906,174242,174334,185588,189870,194639,194701,195279,195863,196188,196301,196530,196690,196842,197013,197179,197348,197515,197678,197921,198091,198264,198435,198709,198908,199113,200138,200222,200318,200414,200512,200612,200714,200816,200918,201020,201122,201222,201318,201430,201559,201682,201813,201944,202042,202156,202250,202390,202524,202620,202732,202832,202948,203044,203156,203256,203396,203532,203696,203826,203984,204134,204275,204419,204554,204666,204816,204944,205072,205208,205340,205470,205600,205712,214326,214472,214616,214754,214820,214910,214986,215090,215180,215282,215390,215498,215598,215678,215770,215868,215978,216056,216162,216254,216358,216468,216590,216753,222575,222655,222755,222845,222955,223049,223155,225225,225325,225437,225551,225667,225783,225877,225991,226103,226205,226325,226447,226529,226633,226753,226879,226977,227071,227159,227271,227387,227509,227621,227796,227912,227998,228090,228202,228326,228393,228519,228587,228715,228859,228987,229056,229151,229266,229379,229478,229587,229698,229809,229910,230015,230115,230245,230336,230459,230553,230665,230751,230855,230951,231039,231157,231261,231365,231491,231579,231687,231787,231877,231987,232071,232173,232257,232311,232375,232481,232591,232675", "endLines": "30,36,38,173,174,175,176,178,179,180,181,182,183,187,188,190,191,193,194,195,196,197,198,199,200,216,217,218,219,221,222,225,226,227,228,266,267,268,269,270,271,272,273,274,275,276,277,302,303,304,305,306,307,308,309,313,314,315,316,317,318,320,321,322,323,328,329,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,536,537,541,542,543,544,545,546,547,854,855,856,857,858,859,860,861,869,870,871,872,874,883,884,890,915,917,918,921,922,933,934,935,936,937,938,939,940,941,942,943,944,945,946,947,948,949,950,951,952,953,954,955,956,957,958,959,960,961,962,963,964,965,966,967,968,969,970,971,1140,1347,1348,1353,1354,1355,1370,1371,1375,1379,1383,1395,1401,1408,1412,1416,1421,1425,1429,1433,1437,1441,1445,1451,1455,1461,1465,1471,1475,1480,1484,1487,1491,1497,1501,1507,1511,1517,1520,1524,1528,1532,1536,1540,1541,1542,1543,1546,1549,1552,1555,1559,1560,1561,1562,1563,1566,1568,1570,1572,1577,1578,1582,1588,1592,1593,1595,1606,1607,1611,1617,1621,1622,1676,1680,1707,1711,1712,1716,1744,2133,2159,2327,2353,2384,2392,2398,2412,2434,2439,2444,2454,2463,2472,2476,2483,2491,2498,2499,2508,2511,2514,2518,2522,2526,2529,2530,2534,2538,2548,2553,2560,2566,2567,2570,2574,2579,2581,2583,2586,2589,2591,2595,2598,2605,2608,2611,2615,2617,2621,2623,2625,2627,2631,2639,2647,2659,2665,2674,2677,2688,2691,2696,2697,2702,2975,3034,3039,3049,3058,3059,3065,3069,3072,3075,3078,3081,3084,3087,3090,3094,3097,3100,3103,3107,3110,3114,3118,3131,3132,3133,3134,3135,3136,3137,3138,3139,3140,3141,3142,3143,3144,3145,3146,3147,3148,3149,3150,3152,3154,3155,3156,3157,3158,3159,3160,3161,3163,3164,3166,3167,3169,3171,3172,3174,3175,3176,3177,3178,3179,3181,3182,3183,3184,3185,3186,3342,3344,3346,3347,3348,3349,3350,3351,3352,3353,3354,3355,3356,3357,3358,3360,3361,3362,3363,3364,3365,3367,3371,3375,3467,3468,3469,3470,3471,3472,3473,3503,3505,3507,3509,3511,3512,3513,3514,3516,3518,3520,3521,3522,3523,3524,3525,3526,3527,3528,3529,3530,3531,3534,3535,3536,3537,3539,3541,3542,3544,3545,3547,3549,3551,3552,3553,3554,3555,3556,3557,3558,3559,3560,3561,3562,3564,3565,3566,3567,3569,3570,3571,3572,3573,3575,3577,3579,3581,3582,3583,3584,3585,3586,3587,3588,3589,3590,3591,3592,3593,3594,3595", "endColumns": "54,44,48,40,54,58,61,80,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,112,57,62,64,64,74,72,71,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,70,77,77,71,73,73,73,79,72,68,71,76,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,77,105,91,103,109,12,12,12,79,99,89,109,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,109,83,119", "endOffsets": "1584,1801,1903,8480,8535,8594,8656,8799,8860,8935,9011,9088,9166,9423,9505,9632,9708,9827,9905,10011,10117,10196,10276,10333,10391,11477,11552,11617,11683,11806,11867,12033,12106,12173,12241,14562,14621,14680,14739,14798,14852,14906,14959,15013,15067,15121,15175,16704,16783,16856,16930,17001,17073,17145,17218,17416,17474,17547,17621,17695,17770,17890,17963,18033,18104,18350,18411,22907,22976,23046,23120,23196,23260,23337,23413,23490,23555,23624,23701,23776,23845,23913,23990,24056,24117,24214,24279,24348,24447,24518,24577,24635,24692,24751,24815,24886,24958,25030,25102,25174,25241,25309,25377,25436,25499,25563,25653,25744,25804,25870,25937,26003,26073,26137,26190,26303,26361,26424,26489,26554,26629,26702,26774,26823,26884,26945,27006,27068,27132,27196,27260,27325,27388,27448,27509,27575,27634,27694,27756,27827,27887,27955,31503,31590,31843,31930,32018,32100,32183,32273,32364,52454,52512,52557,52623,52687,52744,52801,52855,53374,53422,53471,53522,53596,54049,54098,54379,55690,55802,55864,56052,56109,56803,56873,56951,57005,57075,57160,57208,57254,57325,57403,57481,57553,57627,57701,57775,57855,57928,57997,58069,58146,58207,58270,58336,58400,58471,58534,58599,58663,58724,58785,58837,58910,58984,59053,59128,59202,59276,59417,59487,71864,87001,87091,87403,87499,87589,88544,88633,88880,89161,89413,90112,90505,90982,91204,91426,91702,91929,92159,92389,92619,92849,93076,93495,93721,94146,94376,94804,95023,95306,95514,95645,95872,96298,96523,96950,97171,97596,97716,97992,98293,98617,98908,99222,99359,99490,99595,99837,100004,100208,100416,100687,100799,100911,101016,101133,101347,101493,101633,101719,102067,102155,102401,102819,103068,103150,103248,103840,103940,104192,104616,104871,104965,108762,108999,111023,111265,111367,111620,113776,139766,141282,151510,153038,154795,155421,155841,156902,158167,158423,158659,159206,159700,160305,160503,161083,161647,162022,162140,162678,162835,163031,163304,163560,163730,163871,163935,164217,164503,165179,165443,165781,166134,166228,166414,166720,166982,167107,167234,167473,167684,167803,167996,168173,168628,168809,168931,169190,169303,169490,169592,169699,169828,170103,170611,171107,171984,172278,172848,172997,173729,173901,174237,174329,174607,189865,194290,194696,195274,195858,195949,196296,196525,196685,196837,197008,197174,197343,197510,197673,197916,198086,198259,198430,198704,198903,199108,199438,200217,200313,200409,200507,200607,200709,200811,200913,201015,201117,201217,201313,201425,201554,201677,201808,201939,202037,202151,202245,202385,202519,202615,202727,202827,202943,203039,203151,203251,203391,203527,203691,203821,203979,204129,204270,204414,204549,204661,204811,204939,205067,205203,205335,205465,205595,205707,205847,214467,214611,214749,214815,214905,214981,215085,215175,215277,215385,215493,215593,215673,215765,215863,215973,216051,216157,216249,216353,216463,216585,216748,216905,222650,222750,222840,222950,223044,223150,223242,225320,225432,225546,225662,225778,225872,225986,226098,226200,226320,226442,226524,226628,226748,226874,226972,227066,227154,227266,227382,227504,227616,227791,227907,227993,228085,228197,228321,228388,228514,228582,228710,228854,228982,229051,229146,229261,229374,229473,229582,229693,229804,229905,230010,230110,230240,230331,230454,230548,230660,230746,230850,230946,231034,231152,231256,231360,231486,231574,231682,231782,231872,231982,232066,232168,232252,232306,232370,232476,232586,232670,232790"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\e779f411b6970bbfe6a7d381c7d1cfc3\\transformed\\transition-28.0.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "873,882,885,886,887,901,902,903,904,905", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "53527,53964,54103,54150,54205,54836,54890,54942,54991,55052", "endColumns": "39,42,46,54,44,53,51,48,60,49", "endOffsets": "53562,54002,54145,54200,54245,54885,54937,54986,55047,55097"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\6e168fd8550fecc7fe244221549ae7ea\\transformed\\constraint-layout-1.1.3\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "4,5,21,26,27,28,31,39,40,41,42,45,46,49,52,53,54,55,56,59,62,63,64,65,70,73,76,77,78,83,84,85,88,91,92,95,98,101,104,105,108,111,112,117,118,123,126,129,130,131,132,133,134,135,136,137,138", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "275,336,1058,1314,1366,1427,1589,1908,1969,2029,2099,2232,2300,2429,2555,2617,2682,2750,2817,2940,3065,3132,3197,3262,3443,3564,3685,3751,3818,4028,4097,4163,4288,4414,4481,4607,4734,4859,4986,5051,5177,5300,5365,5573,5640,5820,5940,6060,6125,6187,6249,6311,6370,6430,6491,6552,6611", "endLines": "4,12,21,26,27,28,34,39,40,41,44,45,48,51,52,53,54,55,58,61,62,63,64,69,72,75,76,77,82,83,84,87,90,91,94,97,100,103,104,107,110,111,116,117,122,125,128,129,130,131,132,133,134,135,136,137,146", "endColumns": "60,11,47,51,60,45,11,60,59,69,11,67,11,11,61,64,67,66,11,11,66,64,64,11,11,11,65,66,11,68,65,11,11,66,11,11,11,11,64,11,11,64,11,66,11,11,11,64,61,61,61,58,59,60,60,58,11", "endOffsets": "331,622,1101,1361,1422,1468,1711,1964,2024,2094,2227,2295,2424,2550,2612,2677,2745,2812,2935,3060,3127,3192,3257,3438,3559,3680,3746,3813,4023,4092,4158,4283,4409,4476,4602,4729,4854,4981,5046,5172,5295,5360,5568,5635,5815,5935,6055,6120,6182,6244,6306,6365,6425,6486,6547,6606,6948"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\194766323b8e4d092ba620fa2362821d\\transformed\\support-media-compat-28.0.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "288,850,851,852,853,3192,3194,3195,3200,3202", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "15864,52195,52248,52301,52354,206170,206346,206468,206730,206925", "endColumns": "88,52,52,52,52,65,121,60,65,66", "endOffsets": "15948,52243,52296,52349,52402,206231,206463,206524,206791,206987"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\71b2f468c1ec12de737a4945bfcdd2ee\\transformed\\design-28.0.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "16,17,18,19,22,23,24,25,35,37,148,149,150,151,152,153,155,156,157,159,160,161,162,163,164,165,166,167,168,169,170,171,172,177,205,206,207,208,209,210,211,212,213,214,215,278,279,280,281,282,283,284,285,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522,523,524,525,526,527,528,529,530,531,532,533,534,535,787,788,789,790,791,792,793,794,795,796,797,798,799,800,801,802,803,804,805,806,807,808,809,810,811,812,813,814,815,816,817,818,819,820,821,822,823,824,825,826,827,828,829,830,831,832,833,834,880,881,888,889,897,898,899,916,919,920,923,924,925,927,928,929,930,931,981,997,1003,1004,1026,1027,1034,1093,1112,1113,1114,1115,1116,1356,1623,1624,1625,1630,1631,1635,1641,1645,1646,1647,1648,1659,1660,1661,1665,1671,1675,1745,1746,1747,1777,1797,1843,1873,1893,1913,1959,1963,2703,2717,2758,2766,3035,3036,3037,3038,3203,3206,3207,3210,3213,3214,3217,3221,3226,3234,3242,3251,3259,3263,3271,3279,3287,3295,3303,3312,3321,3329,3338,3376,3378,3383,3385,3390,3394,3411,3412,3417,3418,3419,3420,3421,3422,3424,3425,3430,3431,3432,3433,3434,3435,3436,3438,3442,3446,3450,3474,3475,3476,3477,3478,3479,3480,3481,3482,3485,3489,3492,3598,3606,3613,3622,3626,3641,3649,3652,3661,3666,3677,3685,3688,3697,3704,3705,3724,3727,3733,3736,3745,3748,3751,3754,3757,3760,3764,3767,3776,3779,3787,3792,3800,3805,3809,3810,3821,3828,3832,3836,3837,3841,3849,3853,3858,3863", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "774,830,890,951,1106,1159,1217,1265,1716,1806,7021,7079,7139,7197,7243,7303,7415,7461,7511,7599,7657,7715,7774,7834,7896,7958,8020,8082,8144,8206,8267,8329,8391,8661,10645,10719,10782,10850,10931,10995,11061,11131,11201,11271,11341,15180,15243,15308,15374,15427,15503,15569,15656,28618,28672,28751,28829,28902,28967,29030,29096,29167,29238,29300,29369,29435,29502,29569,29625,29676,29729,29781,29835,29906,29969,30028,30090,30149,30222,30289,30349,30412,30487,30559,30630,30686,30757,30814,30871,30937,31001,31072,31129,31182,31245,31297,31355,48367,48433,48499,48580,48655,48711,48764,48825,48883,48933,48982,49031,49080,49142,49194,49239,49320,49374,49427,49481,49532,49581,49632,49693,49754,49816,49866,49907,49957,50005,50067,50118,50167,50236,50297,50353,50424,50489,50558,50609,50672,50742,50811,50881,50943,51013,51083,51158,53844,53902,54250,54295,54658,54705,54750,55695,55869,55935,56114,56177,56249,56362,56419,56479,56537,56607,60009,61210,61610,61714,63461,63613,64195,67932,69131,69209,69510,69676,69831,87594,104970,105063,105170,105513,105620,105849,106258,106490,106590,106695,106814,107412,107559,107678,107913,108328,108566,113781,113902,114035,116114,117610,120844,122919,124427,125951,129181,129405,174612,175416,177176,177626,194295,194368,194455,194540,206992,207187,207279,207452,207614,207709,207878,208121,208414,208823,209237,209669,210087,210328,210758,211193,211603,212025,212435,212864,213290,213706,214144,216910,216978,217322,217402,217758,217908,218843,218927,219292,219390,219498,219596,219706,219822,219948,220044,220421,220531,220655,220793,220903,221025,221153,221291,221453,221669,221825,223247,223331,223435,223529,223643,223755,223879,223975,224055,224244,224450,224643,232934,233366,233787,234212,234409,235357,235878,236001,236638,236859,237674,238143,238326,238922,239382,239487,240748,240898,241315,241480,242160,242319,242410,242494,242690,242857,243079,243239,243616,243775,244103,244320,244895,245245,245494,245591,246297,246735,246976,247165,247299,247490,248127,248377,248680,248895", "endLines": "16,17,18,19,22,23,24,25,35,37,148,149,150,151,152,153,155,156,157,159,160,161,162,163,164,165,166,167,168,169,170,171,172,177,205,206,207,208,209,210,211,212,213,214,215,278,279,280,281,282,283,284,285,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522,523,524,525,526,527,528,529,530,531,532,533,534,535,787,788,789,790,791,792,793,794,795,796,797,798,799,800,801,802,803,804,805,806,807,808,809,810,811,812,813,814,815,816,817,818,819,820,821,822,823,824,825,826,827,828,829,830,831,832,833,834,880,881,888,889,897,898,899,916,919,920,923,924,925,927,928,929,930,931,981,997,1003,1004,1026,1027,1034,1093,1112,1113,1114,1115,1116,1359,1623,1624,1629,1630,1634,1640,1644,1645,1646,1647,1658,1659,1660,1664,1670,1674,1675,1745,1746,1776,1796,1842,1872,1892,1912,1958,1962,1966,2716,2757,2765,2775,3035,3036,3037,3038,3205,3206,3209,3212,3213,3216,3220,3225,3233,3241,3250,3258,3262,3270,3278,3286,3294,3302,3311,3320,3328,3337,3340,3377,3382,3384,3389,3393,3397,3411,3416,3417,3418,3419,3420,3421,3423,3424,3429,3430,3431,3432,3433,3434,3435,3437,3441,3445,3449,3453,3474,3475,3476,3477,3478,3479,3480,3481,3484,3488,3491,3495,3605,3612,3621,3625,3640,3648,3651,3660,3665,3676,3684,3687,3696,3703,3704,3723,3726,3732,3735,3744,3747,3750,3753,3756,3759,3763,3766,3775,3778,3786,3791,3799,3804,3808,3809,3820,3827,3831,3835,3836,3840,3848,3852,3857,3862,3870", "endColumns": "55,59,60,54,52,57,47,48,44,52,57,59,57,45,59,52,45,49,46,57,57,58,59,61,61,61,61,61,61,60,61,61,52,61,73,62,67,80,63,65,69,69,69,69,66,62,64,65,52,75,65,86,75,53,78,77,72,64,62,65,70,70,61,68,65,66,66,55,50,52,51,53,70,62,58,61,58,72,66,59,62,74,71,70,55,70,56,56,65,63,70,56,52,62,51,57,66,65,65,80,74,55,52,60,57,49,48,48,48,61,51,44,80,53,52,53,50,48,50,60,60,61,49,40,49,47,61,50,48,68,60,55,70,64,68,50,62,69,68,69,61,69,69,74,58,57,61,44,42,46,44,50,47,65,61,62,71,56,56,59,57,69,56,144,120,103,86,151,151,147,80,77,300,165,154,101,10,92,106,10,106,10,10,10,99,104,118,10,146,118,10,10,10,111,120,132,10,10,10,10,10,10,10,10,10,10,10,10,10,72,86,84,98,10,91,10,10,94,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,83,10,97,107,97,109,115,10,95,10,109,123,137,109,121,127,10,10,10,10,10,83,103,93,113,111,123,95,79,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,104,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,96,10,10,10,10,133,10,10,10,10,10,10", "endOffsets": "825,885,946,1001,1154,1212,1260,1309,1756,1854,7074,7134,7192,7238,7298,7351,7456,7506,7553,7652,7710,7769,7829,7891,7953,8015,8077,8139,8201,8262,8324,8386,8439,8718,10714,10777,10845,10926,10990,11056,11126,11196,11266,11336,11403,15238,15303,15369,15422,15498,15564,15651,15727,28667,28746,28824,28897,28962,29025,29091,29162,29233,29295,29364,29430,29497,29564,29620,29671,29724,29776,29830,29901,29964,30023,30085,30144,30217,30284,30344,30407,30482,30554,30625,30681,30752,30809,30866,30932,30996,31067,31124,31177,31240,31292,31350,31417,48428,48494,48575,48650,48706,48759,48820,48878,48928,48977,49026,49075,49137,49189,49234,49315,49369,49422,49476,49527,49576,49627,49688,49749,49811,49861,49902,49952,50000,50062,50113,50162,50231,50292,50348,50419,50484,50553,50604,50667,50737,50806,50876,50938,51008,51078,51153,51212,53897,53959,54290,54333,54700,54745,54796,55738,55930,55992,56172,56244,56301,56414,56474,56532,56602,56659,60149,61326,61709,61796,63608,63760,64338,68008,69204,69505,69671,69826,69928,87866,105058,105165,105508,105615,105844,106253,106485,106585,106690,106809,107407,107554,107673,107908,108323,108561,108673,113897,114030,116109,117605,120839,122914,124422,125946,129176,129400,129637,175411,177171,177621,178336,194363,194450,194535,194634,207182,207274,207447,207609,207704,207873,208116,208409,208818,209232,209664,210082,210323,210753,211188,211598,212020,212430,212859,213285,213701,214139,214321,216973,217317,217397,217753,217903,218047,218922,219287,219385,219493,219591,219701,219817,219943,220039,220416,220526,220650,220788,220898,221020,221148,221286,221448,221664,221820,222024,223326,223430,223524,223638,223750,223874,223970,224050,224239,224445,224638,224848,233361,233782,234207,234404,235352,235873,235996,236633,236854,237669,238138,238321,238917,239377,239482,240743,240893,241310,241475,242155,242314,242405,242489,242685,242852,243074,243234,243611,243770,244098,244315,244890,245240,245489,245586,246292,246730,246971,247160,247294,247485,248122,248372,248675,248890,249366"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\077c69690200a4c0c0ddf0630ba2f6e7\\transformed\\open_ad_sdk_6.8.4.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "220,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,538,539,540,548,549,550,862,863,864,865,866,875,906,907,908,909,910,911,912,913,914,1090,1091,1161,1162,1163,1164,1165,1166,1167,1168,1169,1170,1171,1172,1173,1174,1175,1176,1177,1178,1179,1180,1181,1182,1183,1184,1185,1186,1187,1188,1189,1190,1191,1192,1193,1194,1195,1196,1197,1198,1199,1200,1201,1202,1203,1204,1205,1206,1207,1208,1209,1210,1211,1212,1213,1214,1215,1216,1217,1218,1219,1220,1221,1222,1223,1224,1225,1226,1227,1228,1229,1230,1231,1232,1233,1234,1235,1236,1237,1238,1239,1240,1241,1242,1243,1244,1245,1246,1247,1248,1249,1250,1251,1252,1253,1254,1255,1256,1257,1258,1259,1260,1261,1262,1263,1264,1265,1266,1267,1268,1269,1270,1271,1272,1273,1274,1275,1276,1277,1278,1279,1280,1281,1282,1283,1284,1285,1286,1287,1288,1289,1290,1291,1292,1293,1294,1295,1296,1297,1298,1299,1300,1301,1302,1303,1304,1305,1306,1307,1308,1309,1310,1311,1312,1313,1314,1315,1316,1317,1318,1319,1320,1321,1322,1323,1324,1325,1326,1327,1328,1329,1330,1331,1349,2816,2820,2832,2844,2851,2855,2859,2863,2866,2870,2874,2881,2885,3060,3119,3123,3127,3398,3401,3892,3895,3901,3941,4019,4025,4039,4045,4046,4047,4053,4059,4068,4080,4086,4094,4101,4105,4113,4117,4129,4130", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "11688,18416,18471,18524,18582,18648,18706,18764,18820,18912,18990,19044,19099,19154,19208,19262,19311,19374,19433,19478,19533,19596,19663,19725,19776,19829,19900,19974,20033,20092,20143,20187,20235,20289,20350,20402,20463,20512,20573,20631,20679,20747,20804,20861,20911,20961,21009,21060,21111,21174,21237,21285,21348,21426,21474,21530,21585,21646,21707,21753,21802,21866,21930,21993,22044,22100,22151,22214,22290,22348,22417,22471,22543,22600,22643,31595,31654,31702,32369,32444,32520,52860,52927,52988,53049,53111,53601,55102,55148,55197,55273,55344,55418,55492,55561,55617,67761,67810,73152,73195,73232,73278,73327,73382,73443,73516,73588,73657,73783,73888,73967,74061,74157,74252,74335,74396,74468,74525,74598,74670,74764,74866,74965,75055,75148,75230,75310,75387,75465,75547,75626,75726,75816,75909,75986,76079,76191,76289,76378,76468,76557,76629,76701,76781,76861,76977,77054,77152,77243,77295,77533,77615,77804,77889,77946,78002,78043,78091,78140,78191,78251,78299,78362,78426,78485,78557,78630,78698,78771,78852,78909,78967,79025,79088,79137,79182,79247,79319,79396,79472,79524,79567,79625,79678,79722,79769,79812,79860,79914,79966,80018,80067,80127,80181,80239,80294,80336,80378,80455,80591,80701,80825,80933,81036,81093,81146,81218,81307,81390,81481,81535,81601,81662,81727,81782,81839,81917,81979,82033,82083,82136,82197,82236,82315,82388,82469,82535,82601,82651,82701,82770,82839,82923,82990,83047,83103,83165,83227,83287,83338,83395,83454,83556,83636,83694,83781,83841,83935,83986,84062,84121,84218,84289,84385,84451,84518,84556,84599,84648,84698,84750,84806,84859,84913,84968,85027,85088,85149,85201,87096,180663,180890,181476,182030,182430,182644,182862,183064,183216,183431,183631,183976,184199,195954,199443,199651,199875,218052,218228,250551,250690,250941,252895,255918,256254,257103,257504,257620,257743,258131,258527,258919,259358,259644,260118,260593,260852,261317,261557,261999,262068", "endLines": "220,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,538,539,540,548,549,550,862,863,864,865,866,875,906,907,908,909,910,911,912,913,914,1090,1091,1161,1162,1163,1164,1165,1166,1167,1168,1169,1170,1171,1172,1173,1174,1175,1176,1177,1178,1179,1180,1181,1182,1183,1184,1185,1186,1187,1188,1189,1190,1191,1192,1193,1194,1195,1196,1197,1198,1199,1200,1201,1202,1203,1204,1205,1206,1207,1208,1209,1210,1211,1212,1213,1214,1215,1216,1217,1218,1219,1220,1221,1222,1223,1224,1225,1226,1227,1228,1229,1230,1231,1232,1233,1234,1235,1236,1237,1238,1239,1240,1241,1242,1243,1244,1245,1246,1247,1248,1249,1250,1251,1252,1253,1254,1255,1256,1257,1258,1259,1260,1261,1262,1263,1264,1265,1266,1267,1268,1269,1270,1271,1272,1273,1274,1275,1276,1277,1278,1279,1280,1281,1282,1283,1284,1285,1286,1287,1288,1289,1290,1291,1292,1293,1294,1295,1296,1297,1298,1299,1300,1301,1302,1303,1304,1305,1306,1307,1308,1309,1310,1311,1312,1313,1314,1315,1316,1317,1318,1319,1320,1321,1322,1323,1324,1325,1326,1327,1328,1329,1330,1331,1352,2819,2823,2834,2850,2854,2858,2862,2865,2869,2873,2880,2884,2888,3063,3122,3126,3130,3400,3410,3894,3900,3904,3950,4024,4038,4044,4045,4046,4052,4058,4067,4079,4085,4093,4100,4104,4112,4116,4128,4129,4138", "endColumns": "62,54,52,57,65,57,57,55,91,77,53,54,54,53,53,48,62,58,44,54,62,66,61,50,52,70,73,58,58,50,43,47,53,60,51,60,48,60,57,47,67,56,56,49,49,47,50,50,62,62,47,62,77,47,55,54,60,60,45,48,63,63,62,50,55,50,62,75,57,68,53,71,56,42,60,58,47,55,74,75,71,66,60,60,61,62,65,45,48,75,70,73,73,68,55,45,48,62,42,36,45,48,54,60,72,71,68,125,104,78,93,95,94,82,60,71,56,72,71,93,101,98,89,92,81,79,76,77,81,78,99,89,92,76,92,111,97,88,89,88,71,71,79,79,115,76,97,90,51,237,81,188,84,56,55,40,47,48,50,59,47,62,63,58,71,72,67,72,80,56,57,57,62,48,44,64,71,76,75,51,42,57,52,43,46,42,47,53,51,51,48,59,53,57,54,41,41,76,135,109,123,107,102,56,52,71,88,82,90,53,65,60,64,54,56,77,61,53,49,52,60,38,78,72,80,65,65,49,49,68,68,83,66,56,55,61,61,59,50,56,58,101,79,57,86,59,93,50,75,58,96,70,95,65,66,37,42,48,49,51,55,52,53,54,58,60,60,51,45,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,115,122,12,12,12,12,12,12,12,12,12,12,12,68,12", "endOffsets": "11746,18466,18519,18577,18643,18701,18759,18815,18907,18985,19039,19094,19149,19203,19257,19306,19369,19428,19473,19528,19591,19658,19720,19771,19824,19895,19969,20028,20087,20138,20182,20230,20284,20345,20397,20458,20507,20568,20626,20674,20742,20799,20856,20906,20956,21004,21055,21106,21169,21232,21280,21343,21421,21469,21525,21580,21641,21702,21748,21797,21861,21925,21988,22039,22095,22146,22209,22285,22343,22412,22466,22538,22595,22638,22699,31649,31697,31753,32439,32515,32587,52922,52983,53044,53106,53169,53662,55143,55192,55268,55339,55413,55487,55556,55612,55658,67805,67868,73190,73227,73273,73322,73377,73438,73511,73583,73652,73778,73883,73962,74056,74152,74247,74330,74391,74463,74520,74593,74665,74759,74861,74960,75050,75143,75225,75305,75382,75460,75542,75621,75721,75811,75904,75981,76074,76186,76284,76373,76463,76552,76624,76696,76776,76856,76972,77049,77147,77238,77290,77528,77610,77799,77884,77941,77997,78038,78086,78135,78186,78246,78294,78357,78421,78480,78552,78625,78693,78766,78847,78904,78962,79020,79083,79132,79177,79242,79314,79391,79467,79519,79562,79620,79673,79717,79764,79807,79855,79909,79961,80013,80062,80122,80176,80234,80289,80331,80373,80450,80586,80696,80820,80928,81031,81088,81141,81213,81302,81385,81476,81530,81596,81657,81722,81777,81834,81912,81974,82028,82078,82131,82192,82231,82310,82383,82464,82530,82596,82646,82696,82765,82834,82918,82985,83042,83098,83160,83222,83282,83333,83390,83449,83551,83631,83689,83776,83836,83930,83981,84057,84116,84213,84284,84380,84446,84513,84551,84594,84643,84693,84745,84801,84854,84908,84963,85022,85083,85144,85196,85242,87315,180885,181153,181635,182425,182639,182857,183059,183211,183426,183626,183971,184194,184381,196183,199646,199870,200133,218223,218838,250685,250936,251149,253337,256249,257098,257499,257615,257738,258126,258522,258914,259353,259639,260113,260588,260847,261312,261552,261994,262063,262618"}}]}, {"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\values-pt-rBR_values-pt-rBR.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a3ae461f31008f99a87e29f3c99a3c83\\transformed\\appcompat-v7-28.0.0\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,331,438,527,628,746,829,909,1000,1093,1189,1283,1384,1477,1572,1667,1758,1849,1936,2043,2155,2257,2365,2472,2582,2744,2844", "endColumns": "119,105,106,88,100,117,82,79,90,92,95,93,100,92,94,94,90,90,86,106,111,101,107,106,109,161,99,84", "endOffsets": "220,326,433,522,623,741,824,904,995,1088,1184,1278,1379,1472,1567,1662,1753,1844,1931,2038,2150,2252,2360,2467,2577,2739,2839,2924"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\daefd50eed0506e326ae247974906cd2\\transformed\\support-compat-28.0.0\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2929", "endColumns": "100", "endOffsets": "3025"}}]}, {"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\values-fr-rCA_values-fr-rCA.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\daefd50eed0506e326ae247974906cd2\\transformed\\support-compat-28.0.0\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2965", "endColumns": "100", "endOffsets": "3061"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a3ae461f31008f99a87e29f3c99a3c83\\transformed\\appcompat-v7-28.0.0\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,331,442,529,645,775,858,938,1029,1122,1221,1316,1417,1510,1603,1698,1789,1880,1976,2086,2198,2301,2412,2519,2621,2780,2879", "endColumns": "110,114,110,86,115,129,82,79,90,92,98,94,100,92,92,94,90,90,95,109,111,102,110,106,101,158,98,85", "endOffsets": "211,326,437,524,640,770,853,933,1024,1117,1216,1311,1412,1505,1598,1693,1784,1875,1971,2081,2193,2296,2407,2514,2616,2775,2874,2960"}}]}, {"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\values-mr_values-mr.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a3ae461f31008f99a87e29f3c99a3c83\\transformed\\appcompat-v7-28.0.0\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,322,429,519,620,735,812,890,981,1074,1168,1265,1366,1459,1554,1651,1742,1833,1913,2025,2127,2223,2332,2433,2545,2702,2807", "endColumns": "110,105,106,89,100,114,76,77,90,92,93,96,100,92,94,96,90,90,79,111,101,95,108,100,111,156,104,79", "endOffsets": "211,317,424,514,615,730,807,885,976,1069,1163,1260,1361,1454,1549,1646,1737,1828,1908,2020,2122,2218,2327,2428,2540,2697,2802,2882"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\daefd50eed0506e326ae247974906cd2\\transformed\\support-compat-28.0.0\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2887", "endColumns": "100", "endOffsets": "2983"}}]}, {"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\values-et_values-et.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a3ae461f31008f99a87e29f3c99a3c83\\transformed\\appcompat-v7-28.0.0\\res\\values-et\\values-et.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,222,329,440,526,628,745,825,903,995,1089,1194,1296,1406,1500,1601,1695,1787,1880,1963,2074,2179,2278,2388,2489,2592,2758,2860", "endColumns": "116,106,110,85,101,116,79,77,91,93,104,101,109,93,100,93,91,92,82,110,104,98,109,100,102,165,101,81", "endOffsets": "217,324,435,521,623,740,820,898,990,1084,1189,1291,1401,1495,1596,1690,1782,1875,1958,2069,2174,2273,2383,2484,2587,2753,2855,2937"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\daefd50eed0506e326ae247974906cd2\\transformed\\support-compat-28.0.0\\res\\values-et\\values-et.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2942", "endColumns": "100", "endOffsets": "3038"}}]}, {"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\values-zh-rHK_values-zh-rHK.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\daefd50eed0506e326ae247974906cd2\\transformed\\support-compat-28.0.0\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2746", "endColumns": "100", "endOffsets": "2842"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a3ae461f31008f99a87e29f3c99a3c83\\transformed\\appcompat-v7-28.0.0\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,293,393,475,572,680,756,832,924,1018,1110,1206,1302,1396,1492,1584,1676,1768,1846,1942,2038,2133,2230,2325,2423,2574,2668", "endColumns": "94,92,99,81,96,107,75,75,91,93,91,95,95,93,95,91,91,91,77,95,95,94,96,94,97,150,93,77", "endOffsets": "195,288,388,470,567,675,751,827,919,1013,1105,1201,1297,1391,1487,1579,1671,1763,1841,1937,2033,2128,2225,2320,2418,2569,2663,2741"}}]}, {"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\values-zu_values-zu.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\daefd50eed0506e326ae247974906cd2\\transformed\\support-compat-28.0.0\\res\\values-zu\\values-zu.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2898", "endColumns": "100", "endOffsets": "2994"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a3ae461f31008f99a87e29f3c99a3c83\\transformed\\appcompat-v7-28.0.0\\res\\values-zu\\values-zu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,320,434,522,625,752,832,912,1003,1096,1190,1284,1385,1478,1573,1667,1758,1851,1937,2041,2147,2245,2352,2458,2564,2721,2817", "endColumns": "107,106,113,87,102,126,79,79,90,92,93,93,100,92,94,93,90,92,85,103,105,97,106,105,105,156,95,80", "endOffsets": "208,315,429,517,620,747,827,907,998,1091,1185,1279,1380,1473,1568,1662,1753,1846,1932,2036,2142,2240,2347,2453,2559,2716,2812,2893"}}]}, {"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\values-km_values-km.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\daefd50eed0506e326ae247974906cd2\\transformed\\support-compat-28.0.0\\res\\values-km\\values-km.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2882", "endColumns": "100", "endOffsets": "2978"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a3ae461f31008f99a87e29f3c99a3c83\\transformed\\appcompat-v7-28.0.0\\res\\values-km\\values-km.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,308,420,507,611,729,806,883,974,1067,1163,1257,1358,1451,1546,1640,1731,1822,1905,2009,2114,2214,2324,2431,2539,2701,2799", "endColumns": "102,99,111,86,103,117,76,76,90,92,95,93,100,92,94,93,90,90,82,103,104,99,109,106,107,161,97,82", "endOffsets": "203,303,415,502,606,724,801,878,969,1062,1158,1252,1353,1446,1541,1635,1726,1817,1900,2004,2109,2209,2319,2426,2534,2696,2794,2877"}}]}, {"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\values-el_values-el.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a3ae461f31008f99a87e29f3c99a3c83\\transformed\\appcompat-v7-28.0.0\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,223,334,451,536,641,767,855,941,1032,1125,1221,1315,1416,1509,1604,1701,1792,1883,1968,2079,2189,2291,2402,2511,2619,2779,2879", "endColumns": "117,110,116,84,104,125,87,85,90,92,95,93,100,92,94,96,90,90,84,110,109,101,110,108,107,159,99,84", "endOffsets": "218,329,446,531,636,762,850,936,1027,1120,1216,1310,1411,1504,1599,1696,1787,1878,1963,2074,2184,2286,2397,2506,2614,2774,2874,2959"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\daefd50eed0506e326ae247974906cd2\\transformed\\support-compat-28.0.0\\res\\values-el\\values-el.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2964", "endColumns": "100", "endOffsets": "3060"}}]}, {"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\values-land_values-land.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\71b2f468c1ec12de737a4945bfcdd2ee\\transformed\\design-28.0.0\\res\\values-land\\values-land.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endLines": "5", "endColumns": "10", "endOffsets": "222"}, "to": {"startLines": "5", "startColumns": "4", "startOffsets": "264", "endLines": "8", "endColumns": "10", "endOffsets": "431"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a3ae461f31008f99a87e29f3c99a3c83\\transformed\\appcompat-v7-28.0.0\\res\\values-land\\values-land.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,125,196", "endColumns": "69,70,67", "endOffsets": "120,191,259"}}]}, {"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\values-as_values-as.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a3ae461f31008f99a87e29f3c99a3c83\\transformed\\appcompat-v7-28.0.0\\res\\values-as\\values-as.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,312,419,515,617,736,816,896,987,1080,1173,1268,1368,1461,1558,1652,1743,1834,1923,2025,2140,2243,2352,2471,2591,2758,2861", "endColumns": "107,98,106,95,101,118,79,79,90,92,92,94,99,92,96,93,90,90,88,101,114,102,108,118,119,166,102,88", "endOffsets": "208,307,414,510,612,731,811,891,982,1075,1168,1263,1363,1456,1553,1647,1738,1829,1918,2020,2135,2238,2347,2466,2586,2753,2856,2945"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\daefd50eed0506e326ae247974906cd2\\transformed\\support-compat-28.0.0\\res\\values-as\\values-as.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2950", "endColumns": "100", "endOffsets": "3046"}}]}, {"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\values-hy_values-hy.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\daefd50eed0506e326ae247974906cd2\\transformed\\support-compat-28.0.0\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2884", "endColumns": "100", "endOffsets": "2980"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a3ae461f31008f99a87e29f3c99a3c83\\transformed\\appcompat-v7-28.0.0\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,309,419,508,614,729,811,892,983,1076,1172,1266,1367,1460,1555,1649,1740,1831,1916,2023,2130,2229,2339,2446,2546,2703,2802", "endColumns": "102,100,109,88,105,114,81,80,90,92,95,93,100,92,94,93,90,90,84,106,106,98,109,106,99,156,98,81", "endOffsets": "203,304,414,503,609,724,806,887,978,1071,1167,1261,1362,1455,1550,1644,1735,1826,1911,2018,2125,2224,2334,2441,2541,2698,2797,2879"}}]}, {"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\values-cs_values-cs.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\daefd50eed0506e326ae247974906cd2\\transformed\\support-compat-28.0.0\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2891", "endColumns": "100", "endOffsets": "2987"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a3ae461f31008f99a87e29f3c99a3c83\\transformed\\appcompat-v7-28.0.0\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,314,423,509,614,731,812,893,984,1077,1173,1267,1362,1455,1550,1647,1738,1829,1913,2017,2126,2225,2331,2441,2548,2711,2809", "endColumns": "106,101,108,85,104,116,80,80,90,92,95,93,94,92,94,96,90,90,83,103,108,98,105,109,106,162,97,81", "endOffsets": "207,309,418,504,609,726,807,888,979,1072,1168,1262,1357,1450,1545,1642,1733,1824,1908,2012,2121,2220,2326,2436,2543,2706,2804,2886"}}]}, {"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\values-v17_values-v17.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a3ae461f31008f99a87e29f3c99a3c83\\transformed\\appcompat-v7-28.0.0\\res\\values-v17\\values-v17.xml", "from": {"startLines": "2,5,9,12,15,18,22,25,29,33,37,40,43,46,50,53,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,228,456,614,764,936,1161,1331,1559,1783,2025,2196,2370,2539,2812,3012,3216", "endLines": "4,8,11,14,17,21,24,28,32,36,39,42,45,49,52,56,60", "endColumns": "12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12", "endOffsets": "223,451,609,759,931,1156,1326,1554,1778,2020,2191,2365,2534,2807,3007,3211,3540"}}]}, {"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\values-bg_values-bg.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\daefd50eed0506e326ae247974906cd2\\transformed\\support-compat-28.0.0\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2930", "endColumns": "100", "endOffsets": "3026"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a3ae461f31008f99a87e29f3c99a3c83\\transformed\\appcompat-v7-28.0.0\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,327,432,518,623,744,823,901,992,1085,1181,1275,1376,1469,1564,1672,1763,1854,1937,2051,2160,2260,2374,2480,2588,2748,2847", "endColumns": "114,106,104,85,104,120,78,77,90,92,95,93,100,92,94,107,90,90,82,113,108,99,113,105,107,159,98,82", "endOffsets": "215,322,427,513,618,739,818,896,987,1080,1176,1270,1371,1464,1559,1667,1758,1849,1932,2046,2155,2255,2369,2475,2583,2743,2842,2925"}}]}, {"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\values-bs_values-bs.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\daefd50eed0506e326ae247974906cd2\\transformed\\support-compat-28.0.0\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2926", "endColumns": "100", "endOffsets": "3022"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a3ae461f31008f99a87e29f3c99a3c83\\transformed\\appcompat-v7-28.0.0\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,224,333,440,526,630,752,834,915,1006,1099,1195,1289,1390,1483,1578,1677,1768,1859,1945,2048,2153,2251,2356,2469,2572,2745,2842", "endColumns": "118,108,106,85,103,121,81,80,90,92,95,93,100,92,94,98,90,90,85,102,104,97,104,112,102,172,96,83", "endOffsets": "219,328,435,521,625,747,829,910,1001,1094,1190,1284,1385,1478,1573,1672,1763,1854,1940,2043,2148,2246,2351,2464,2567,2740,2837,2921"}}]}, {"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\values-v18_values-v18.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a3ae461f31008f99a87e29f3c99a3c83\\transformed\\appcompat-v7-28.0.0\\res\\values-v18\\values-v18.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "48", "endOffsets": "99"}}]}, {"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\values-ml_values-ml.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a3ae461f31008f99a87e29f3c99a3c83\\transformed\\appcompat-v7-28.0.0\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,224,342,457,550,655,787,864,940,1031,1124,1226,1320,1421,1515,1610,1709,1800,1891,1973,2084,2190,2288,2402,2502,2613,2772,2873", "endColumns": "118,117,114,92,104,131,76,75,90,92,101,93,100,93,94,98,90,90,81,110,105,97,113,99,110,158,100,81", "endOffsets": "219,337,452,545,650,782,859,935,1026,1119,1221,1315,1416,1510,1605,1704,1795,1886,1968,2079,2185,2283,2397,2497,2608,2767,2868,2950"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\daefd50eed0506e326ae247974906cd2\\transformed\\support-compat-28.0.0\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2955", "endColumns": "100", "endOffsets": "3051"}}]}, {"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\values-bn_values-bn.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\daefd50eed0506e326ae247974906cd2\\transformed\\support-compat-28.0.0\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2939", "endColumns": "100", "endOffsets": "3035"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a3ae461f31008f99a87e29f3c99a3c83\\transformed\\appcompat-v7-28.0.0\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,326,432,526,631,760,838,916,1007,1100,1195,1289,1390,1483,1578,1672,1763,1854,1941,2051,2159,2258,2368,2474,2587,2752,2857", "endColumns": "108,111,105,93,104,128,77,77,90,92,94,93,100,92,94,93,90,90,86,109,107,98,109,105,112,164,104,81", "endOffsets": "209,321,427,521,626,755,833,911,1002,1095,1190,1284,1385,1478,1573,1667,1758,1849,1936,2046,2154,2253,2363,2469,2582,2747,2852,2934"}}]}, {"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\values-sr_values-sr.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a3ae461f31008f99a87e29f3c99a3c83\\transformed\\appcompat-v7-28.0.0\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,317,423,509,613,731,812,892,983,1076,1172,1266,1367,1460,1555,1660,1751,1842,1930,2035,2143,2244,2348,2456,2557,2724,2821", "endColumns": "108,102,105,85,103,117,80,79,90,92,95,93,100,92,94,104,90,90,87,104,107,100,103,107,100,166,96,83", "endOffsets": "209,312,418,504,608,726,807,887,978,1071,1167,1261,1362,1455,1550,1655,1746,1837,1925,2030,2138,2239,2343,2451,2552,2719,2816,2900"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\daefd50eed0506e326ae247974906cd2\\transformed\\support-compat-28.0.0\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2905", "endColumns": "100", "endOffsets": "3001"}}]}, {"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\values-si_values-si.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\daefd50eed0506e326ae247974906cd2\\transformed\\support-compat-28.0.0\\res\\values-si\\values-si.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2903", "endColumns": "100", "endOffsets": "2999"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a3ae461f31008f99a87e29f3c99a3c83\\transformed\\appcompat-v7-28.0.0\\res\\values-si\\values-si.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,215,322,429,517,622,738,827,914,1005,1098,1193,1287,1388,1481,1576,1670,1761,1852,1936,2045,2150,2248,2358,2457,2563,2722,2821", "endColumns": "109,106,106,87,104,115,88,86,90,92,94,93,100,92,94,93,90,90,83,108,104,97,109,98,105,158,98,81", "endOffsets": "210,317,424,512,617,733,822,909,1000,1093,1188,1282,1383,1476,1571,1665,1756,1847,1931,2040,2145,2243,2353,2452,2558,2717,2816,2898"}}]}, {"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\values-eu_values-eu.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\daefd50eed0506e326ae247974906cd2\\transformed\\support-compat-28.0.0\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2938", "endColumns": "100", "endOffsets": "3034"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a3ae461f31008f99a87e29f3c99a3c83\\transformed\\appcompat-v7-28.0.0\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,312,422,508,614,738,825,909,1001,1095,1192,1286,1388,1482,1578,1675,1767,1860,1942,2051,2161,2260,2369,2475,2586,2757,2856", "endColumns": "108,97,109,85,105,123,86,83,91,93,96,93,101,93,95,96,91,92,81,108,109,98,108,105,110,170,98,81", "endOffsets": "209,307,417,503,609,733,820,904,996,1090,1187,1281,1383,1477,1573,1670,1762,1855,1937,2046,2156,2255,2364,2470,2581,2752,2851,2933"}}]}, {"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\values-ur_values-ur.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\daefd50eed0506e326ae247974906cd2\\transformed\\support-compat-28.0.0\\res\\values-ur\\values-ur.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2904", "endColumns": "100", "endOffsets": "3000"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a3ae461f31008f99a87e29f3c99a3c83\\transformed\\appcompat-v7-28.0.0\\res\\values-ur\\values-ur.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,215,321,430,516,620,740,816,892,984,1078,1174,1268,1370,1464,1560,1654,1746,1838,1923,2031,2140,2242,2353,2453,2561,2726,2824", "endColumns": "109,105,108,85,103,119,75,75,91,93,95,93,101,93,95,93,91,91,84,107,108,101,110,99,107,164,97,79", "endOffsets": "210,316,425,511,615,735,811,887,979,1073,1169,1263,1365,1459,1555,1649,1741,1833,1918,2026,2135,2237,2348,2448,2556,2721,2819,2899"}}]}, {"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\values_values.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\daefd50eed0506e326ae247974906cd2\\transformed\\support-compat-28.0.0\\res\\values\\values.xml", "from": {"startLines": "2,3,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,171,172,176,177,178,6,13,56,88,125", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,7725,7795,7863,7935,8005,8066,8140,8213,8274,8335,8397,8461,8523,8584,8652,8752,8812,8878,8951,9020,9077,9129,9191,9263,9339,9374,9409,9459,9520,9577,9611,9646,9681,9751,9822,9939,10140,10250,10451,10580,10652,319,617,3523,5588,7348", "endLines": "2,3,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,170,171,175,176,177,178,12,55,87,124,131", "endColumns": "68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,34,34,49,60,56,33,34,34,69,70,116,12,109,12,128,71,66,24,24,24,24,24", "endOffsets": "119,182,7790,7858,7930,8000,8061,8135,8208,8269,8330,8392,8456,8518,8579,8647,8747,8807,8873,8946,9015,9072,9124,9186,9258,9334,9369,9404,9454,9515,9572,9606,9641,9676,9746,9817,9934,10135,10245,10446,10575,10647,10714,612,3518,5583,7343,7720"}, "to": {"startLines": "286,287,485,486,487,488,489,490,491,835,836,837,838,839,840,841,842,843,844,845,846,847,848,849,867,868,878,879,892,893,894,895,896,900,932,1084,3098,3099,3104,3107,3112,3507,3508,4845,5000,5031,5053,5086", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15732,15801,28130,28200,28268,28340,28410,28471,28545,51217,51278,51339,51401,51465,51527,51588,51656,51756,51816,51882,51955,52024,52081,52133,53174,53246,53774,53809,54421,54471,54532,54589,54623,54801,56664,68020,199216,199333,199600,199893,200160,226159,226231,285844,299745,301399,302282,302964", "endLines": "286,287,485,486,487,488,489,490,491,835,836,837,838,839,840,841,842,843,844,845,846,847,848,849,867,868,878,879,892,893,894,895,896,900,932,1084,3098,3102,3104,3110,3112,3507,3508,4851,5030,5051,5085,5091", "endColumns": "68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,34,34,49,60,56,33,34,34,69,70,116,12,109,12,128,71,66,24,24,24,24,24", "endOffsets": "15796,15859,28195,28263,28335,28405,28466,28540,28613,51273,51334,51396,51460,51522,51583,51651,51751,51811,51877,51950,52019,52076,52128,52190,53241,53317,53804,53839,54466,54527,54584,54618,54653,54831,56729,68086,199328,199529,199705,200089,200284,226226,226293,286050,301394,302075,302959,303126"}}, {"source": "E:\\Ai\\AiCode\\game\\MountainSurvival\\code\\app\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "1,2,6,11,9,19,17,15,16,18,8,7,26,24,27,25,23,22,28,5,12,10", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "16,58,178,391,306,819,704,585,643,757,263,221,1577,1002,1629,1513,945,887,1685,134,505,346", "endColumns": "41,56,42,113,39,46,52,57,60,61,42,41,51,510,55,63,56,57,109,43,60,44", "endOffsets": "53,110,216,500,341,861,752,638,699,814,301,258,1624,1508,1680,1572,997,940,1790,173,561,386"}, "to": {"startLines": "972,973,993,996,1041,1042,1043,1044,1045,1046,1050,1051,1060,1061,1062,1063,1064,1065,1066,1083,1085,1257", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "59492,59534,61104,61451,64460,64500,64547,64600,64658,64719,64974,65017,66018,66070,66581,66637,66701,66758,66816,67976,68091,80247", "endColumns": "41,56,42,113,39,46,52,57,60,61,42,41,51,510,55,63,56,57,109,43,60,44", "endOffsets": "59529,59586,61142,61560,64495,64542,64595,64653,64714,64776,65012,65054,66065,66576,66632,66696,66753,66811,66921,68015,68147,80287"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\62922e5f87afeea95a66affda0037661\\transformed\\beizi_fusion_sdk_5.2.1.6\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,36,46,49,52,55,58,66,75,84,143,151", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,103,152,202,270,329,370,431,494,544,648,694,738,783,827,906,1026,1132,1182,1253,1298,1360,1424,1531,1600,2221,2843,3018,3184,3341,3474,3957,4476,4992,7537,7963", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,35,45,48,51,54,57,65,74,83,142,150,163", "endColumns": "47,48,49,67,58,40,60,62,49,103,45,43,44,43,78,119,105,49,70,44,61,63,106,68,12,12,12,12,12,12,12,12,12,24,24,24", "endOffsets": "98,147,197,265,324,365,426,489,539,643,689,733,778,822,901,1021,1127,1177,1248,1293,1355,1419,1526,1595,2216,2838,3013,3179,3336,3469,3952,4471,4987,7532,7958,8410"}, "to": {"startLines": "13,14,15,147,154,158,185,186,404,975,976,977,978,979,980,981,982,983,984,985,986,987,988,989,2687,2697,2707,2710,2713,2716,2719,3785,3794,4160,4719,4727", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "627,675,724,6953,7356,7558,9219,9280,22704,59736,59840,59886,59930,59975,60019,60098,60218,60324,60374,60445,60490,60552,60616,60723,171705,172326,172948,173123,173289,173446,173544,242880,243399,259678,278367,278793", "endLines": "13,14,15,147,154,158,185,186,404,975,976,977,978,979,980,981,982,983,984,985,986,987,988,989,2696,2706,2709,2712,2715,2718,2726,3793,3802,4218,4726,4739", "endColumns": "47,48,49,67,58,40,60,62,49,103,45,43,44,43,78,119,105,49,70,44,61,63,106,68,12,12,12,12,12,12,12,12,12,24,24,24", "endOffsets": "670,719,769,7016,7410,7594,9275,9338,22749,59835,59881,59925,59970,60014,60093,60213,60319,60369,60440,60485,60547,60611,60718,60787,172321,172943,173118,173284,173441,173539,174022,243394,243910,262218,278788,279240"}}, {"source": "E:\\Ai\\AiCode\\game\\MountainSurvival\\code\\app\\src\\main\\res\\values\\colors.xml", "from": {"startLines": "17,20,8,29,30,13,14,3,4,5,28,6,7,23,24,27,9", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "565,634,306,914,963,436,483,75,122,169,866,216,261,704,751,819,348", "endColumns": "47,50,41,48,44,46,51,46,46,46,47,44,44,46,48,46,41", "endOffsets": "608,680,343,958,1003,478,530,117,164,211,909,256,301,746,795,861,385"}, "to": {"startLines": "184,189,192,223,224,300,301,310,311,312,319,324,325,326,327,405,406", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "9171,9510,9713,11872,11921,16536,16583,17223,17270,17317,17775,18109,18154,18199,18246,22754,22801", "endColumns": "47,50,41,48,44,46,51,46,46,46,47,44,44,46,48,46,41", "endOffsets": "9214,9556,9750,11916,11961,16578,16630,17265,17312,17359,17818,18149,18194,18241,18290,22796,22838"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\0457feaf864ab549dda35ffc4a13a2e6\\transformed\\openset_sdk_6.5.2.6\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,29,38,41,46,50,58,64,70,79", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,118,180,231,283,339,389,441,492,548,606,659,712,763,800,837,885,938,990,1044,1095,1139,1182,1349,1488,1619,2015,2149,2351,2604,2944,3223,3413,4007", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,28,37,40,45,49,57,63,69,78,105", "endColumns": "62,61,50,51,55,49,51,50,55,57,52,52,50,36,36,47,52,51,53,50,43,42,166,138,12,12,12,12,12,12,12,24,24,24", "endOffsets": "113,175,226,278,334,384,436,487,543,601,654,707,758,795,832,880,933,985,1039,1090,1134,1177,1344,1483,1614,2010,2144,2346,2599,2939,3218,3408,4002,5302"}, "to": {"startLines": "2,3,289,290,291,292,293,294,295,296,297,298,299,891,998,999,1000,1052,1053,1054,1067,1080,1081,1082,1271,2800,2809,2812,2817,2821,3846,4154,5303,5312", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,213,15953,16004,16056,16112,16162,16214,16265,16321,16379,16432,16485,54384,61713,61750,61798,65059,65111,65165,66926,67627,67670,67837,81235,177750,178102,178201,178403,178656,245980,259510,312256,312757", "endLines": "2,3,289,290,291,292,293,294,295,296,297,298,299,891,998,999,1000,1052,1053,1054,1067,1080,1081,1082,1273,2808,2811,2816,2820,2828,3851,4159,5311,5338", "endColumns": "62,61,50,51,55,49,51,50,55,57,52,52,50,36,36,47,52,51,53,50,43,42,166,138,12,12,12,12,12,12,12,24,24,24", "endOffsets": "208,270,15999,16051,16107,16157,16209,16260,16316,16374,16427,16480,16531,54416,61745,61793,61846,65106,65160,65211,66965,67665,67832,67971,81326,178097,178196,178398,178651,178947,246254,259673,312752,314052"}}, {"source": "E:\\Ai\\AiCode\\game\\MountainSurvival\\code\\app\\src\\main\\res\\values\\ic_launcher_background.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "57", "endColumns": "56", "endOffsets": "109"}, "to": {"startLines": "229", "startColumns": "4", "startOffsets": "12246", "endColumns": "56", "endOffsets": "12298"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2144dbf5c98623e2472a5ef01095ffd5\\transformed\\GDTSDK.unionNormal.4.640.1510\\res\\values\\values.xml", "from": {"startLines": "2,4,7,10", "startColumns": "4,4,4,4", "startOffsets": "55,159,290,412", "endLines": "2,6,9,12", "endColumns": "55,12,12,12", "endOffsets": "106,285,407,544"}, "to": {"startLines": "926,2746,2749,2752", "startColumns": "4,4,4,4", "startOffsets": "56306,175004,175135,175257", "endLines": "926,2748,2751,2754", "endColumns": "55,12,12,12", "endOffsets": "56357,175130,175252,175389"}}, {"source": "E:\\Ai\\AiCode\\game\\MountainSurvival\\code\\app\\src\\main\\res\\values\\themes.xml", "from": {"startLines": "3", "startColumns": "4", "startOffsets": "56", "endLines": "15", "endColumns": "12", "endOffsets": "637"}, "to": {"startLines": "3365", "startColumns": "4", "startOffsets": "215393", "endLines": "3377", "endColumns": "12", "endOffsets": "215934"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\cb0f3cc3125274cec1b491c0584a407c\\transformed\\kssdk-ad-4.4.20.1-publishRelease-aa0a55f514\\res\\values\\values.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,330,335,340,350,360,361,371,388,392,399,406,411,423,442,450,467,486,507,510", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "142,192,242,292,342,398,453,508,563,628,694,762,817,870,919,984,1064,1144,1210,1282,1349,1408,1471,1532,1593,1660,1728,1799,1864,1936,2001,2073,2139,2193,2246,2291,2620,2674,2736,2794,2867,2924,2988,3053,3112,3182,3252,3324,3401,3472,3537,3624,3701,3775,3845,3913,3987,4075,4153,4217,4276,4331,4395,4460,4525,4583,4634,4687,4759,4833,4904,4977,5041,5111,5179,5242,5303,5363,5430,5498,5558,5627,5687,5755,5816,5880,5943,6001,6067,6130,6193,6256,6334,6410,6487,6556,6619,6695,6758,6832,6898,6966,7031,7101,7173,7242,7307,7368,7440,7503,7567,7637,7701,7766,7837,7914,7979,8046,8110,8184,8261,8335,8407,8483,8552,8616,8673,8739,8806,8873,8934,8990,9067,9129,9191,9264,9334,9399,9466,9530,9597,9659,9720,9789,9853,9917,9983,10046,10114,10179,10241,10304,10372,10437,10504,10568,10630,10695,10760,10831,10901,10965,11041,11124,11198,11266,11328,11392,11453,11515,11578,11642,11708,11771,11839,11906,11975,12044,12104,12187,12255,12328,12399,12474,12567,12642,12711,12779,12845,12911,12979,13052,13119,13180,13263,13336,13406,13472,13536,13606,13690,13764,13824,13887,13949,14015,14076,14147,14213,14283,14342,14395,14460,14513,14569,14622,14682,14749,14809,14880,14955,15023,15096,15163,15235,15303,15376,15438,15504,15566,15632,15699,15770,15831,15897,15967,16042,16109,16181,16242,16308,16377,16451,16518,16590,16650,16715,16781,16851,16915,16984,17057,17135,17198,17266,17329,17397,17460,17528,17592,17661,17714,17766,17828,17897,17963,18024,18088,18149,18217,18282,18342,18395,18439,18546,18613,18671,18721,18775,18832,18900,18981,19051,19123,19190,19256,19308,19366,19414,19472,19549,19626,19687,19766,19834,19895,19976,20048,20124,20187,20249,20311,20389,20446,20506,20557,20603,20667,20744,20814,20891,20966,21040,21102,21155,21527,21748,21993,22490,22989,23240,23661,24341,24532,24838,25134,25353,25993,27082,27498,28158,28952,30208,30336", "endLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,329,334,339,349,359,360,370,387,391,398,405,410,422,441,449,466,485,506,509,517", "endColumns": "49,49,49,49,55,54,54,54,64,65,67,54,52,48,64,79,79,65,71,66,58,62,60,60,66,67,70,64,71,64,71,65,53,52,44,55,53,61,57,72,56,63,64,58,69,69,71,76,70,64,86,76,73,69,67,73,87,77,63,58,54,63,64,64,57,50,52,71,73,70,72,63,69,67,62,60,59,66,67,59,68,59,67,60,63,62,57,65,62,62,62,77,75,76,68,62,75,62,73,65,67,64,69,71,68,64,60,71,62,63,69,63,64,70,76,64,66,63,73,76,73,71,75,68,63,56,65,66,66,60,55,76,61,61,72,69,64,66,63,66,61,60,68,63,63,65,62,67,64,61,62,67,64,66,63,61,64,64,70,69,63,75,82,73,67,61,63,60,61,62,63,65,62,67,66,68,68,59,82,67,72,70,74,92,74,68,67,65,65,67,72,66,60,82,72,69,65,63,69,83,73,59,62,61,65,60,70,65,69,58,52,64,52,55,52,59,66,59,70,74,67,72,66,71,67,72,61,65,61,65,66,70,60,65,69,74,66,71,60,65,68,73,66,71,59,64,65,69,63,68,72,77,62,67,62,67,62,67,63,68,52,51,61,68,65,60,63,60,67,64,59,52,43,62,66,57,49,53,56,67,80,69,71,66,65,51,57,47,57,76,76,60,78,67,60,80,71,75,62,61,61,77,56,59,50,45,63,76,69,76,74,73,61,52,10,10,10,10,10,250,22,22,22,22,22,22,22,22,22,22,22,22,22,22", "endOffsets": "187,237,287,337,393,448,503,558,623,689,757,812,865,914,979,1059,1139,1205,1277,1344,1403,1466,1527,1588,1655,1723,1794,1859,1931,1996,2068,2134,2188,2241,2286,2342,2669,2731,2789,2862,2919,2983,3048,3107,3177,3247,3319,3396,3467,3532,3619,3696,3770,3840,3908,3982,4070,4148,4212,4271,4326,4390,4455,4520,4578,4629,4682,4754,4828,4899,4972,5036,5106,5174,5237,5298,5358,5425,5493,5553,5622,5682,5750,5811,5875,5938,5996,6062,6125,6188,6251,6329,6405,6482,6551,6614,6690,6753,6827,6893,6961,7026,7096,7168,7237,7302,7363,7435,7498,7562,7632,7696,7761,7832,7909,7974,8041,8105,8179,8256,8330,8402,8478,8547,8611,8668,8734,8801,8868,8929,8985,9062,9124,9186,9259,9329,9394,9461,9525,9592,9654,9715,9784,9848,9912,9978,10041,10109,10174,10236,10299,10367,10432,10499,10563,10625,10690,10755,10826,10896,10960,11036,11119,11193,11261,11323,11387,11448,11510,11573,11637,11703,11766,11834,11901,11970,12039,12099,12182,12250,12323,12394,12469,12562,12637,12706,12774,12840,12906,12974,13047,13114,13175,13258,13331,13401,13467,13531,13601,13685,13759,13819,13882,13944,14010,14071,14142,14208,14278,14337,14390,14455,14508,14564,14617,14677,14744,14804,14875,14950,15018,15091,15158,15230,15298,15371,15433,15499,15561,15627,15694,15765,15826,15892,15962,16037,16104,16176,16237,16303,16372,16446,16513,16585,16645,16710,16776,16846,16910,16979,17052,17130,17193,17261,17324,17392,17455,17523,17587,17656,17709,17761,17823,17892,17958,18019,18083,18144,18212,18277,18337,18390,18434,18497,18608,18666,18716,18770,18827,18895,18976,19046,19118,19185,19251,19303,19361,19409,19467,19544,19621,19682,19761,19829,19890,19971,20043,20119,20182,20244,20306,20384,20441,20501,20552,20598,20662,20739,20809,20886,20961,21035,21097,21150,21522,21743,21988,22485,22984,23235,23656,24336,24527,24833,25129,25348,25988,27077,27493,28153,28947,30203,30331,30750"}, "to": {"startLines": "230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,551,552,553,554,555,556,557,558,559,560,561,562,563,564,565,566,567,568,569,570,571,572,573,574,575,576,577,578,579,580,581,582,583,584,585,586,587,588,589,590,591,592,593,594,595,596,597,598,599,600,601,602,603,604,605,606,607,608,609,610,611,612,613,614,615,616,617,618,619,620,621,622,623,624,625,626,627,628,629,630,631,632,633,634,635,636,637,638,639,640,641,642,643,644,645,646,647,648,649,650,651,652,653,654,655,656,657,658,659,660,661,662,663,664,665,666,667,668,669,670,671,672,673,674,675,676,677,678,679,680,681,682,683,684,685,686,687,688,689,690,691,692,693,694,695,696,697,698,699,700,701,702,703,704,705,706,707,708,709,710,711,712,713,714,715,716,717,718,719,720,721,722,723,724,725,726,727,728,729,730,731,732,733,734,735,736,737,738,739,740,741,742,743,744,745,746,747,748,749,750,751,752,753,754,755,756,757,758,759,760,761,762,763,764,765,766,767,768,769,770,771,772,773,774,775,776,777,778,779,780,781,782,783,784,785,786,876,877,1001,1002,1003,1004,1005,1006,1007,1008,1009,1010,1011,1012,1013,1014,1015,1016,1017,1018,1019,1020,1021,1022,1023,1024,1025,1026,1027,1028,1029,1030,1031,1032,1033,1034,1035,1036,1037,1038,1039,1040,3407,3816,3821,3826,3836,5670,5671,5681,5698,5702,5709,5716,5721,5733,5752,5760,5777,5796,5817,5820", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "12303,12353,12403,12453,12503,12559,12614,12669,12724,12789,12855,12923,12978,13031,13080,13145,13225,13305,13371,13443,13510,13569,13632,13693,13754,13821,13889,13960,14025,14097,14162,14234,14300,14354,14407,14452,32592,32646,32708,32766,32839,32896,32960,33025,33084,33154,33224,33296,33373,33444,33509,33596,33673,33747,33817,33885,33959,34047,34125,34189,34248,34303,34367,34432,34497,34555,34606,34659,34731,34805,34876,34949,35013,35083,35151,35214,35275,35335,35402,35470,35530,35599,35659,35727,35788,35852,35915,35973,36039,36102,36165,36228,36306,36382,36459,36528,36591,36667,36730,36804,36870,36938,37003,37073,37145,37214,37279,37340,37412,37475,37539,37609,37673,37738,37809,37886,37951,38018,38082,38156,38233,38307,38379,38455,38524,38588,38645,38711,38778,38845,38906,38962,39039,39101,39163,39236,39306,39371,39438,39502,39569,39631,39692,39761,39825,39889,39955,40018,40086,40151,40213,40276,40344,40409,40476,40540,40602,40667,40732,40803,40873,40937,41013,41096,41170,41238,41300,41364,41425,41487,41550,41614,41680,41743,41811,41878,41947,42016,42076,42159,42227,42300,42371,42446,42539,42614,42683,42751,42817,42883,42951,43024,43091,43152,43235,43308,43378,43444,43508,43578,43662,43736,43796,43859,43921,43987,44048,44119,44185,44255,44314,44367,44432,44485,44541,44594,44654,44721,44781,44852,44927,44995,45068,45135,45207,45275,45348,45410,45476,45538,45604,45671,45742,45803,45869,45939,46014,46081,46153,46214,46280,46349,46423,46490,46562,46622,46687,46753,46823,46887,46956,47029,47107,47170,47238,47301,47369,47432,47500,47564,47633,47686,47738,47800,47869,47935,47996,48060,48121,48189,48254,48314,53667,53711,61851,61918,61976,62026,62080,62137,62205,62286,62356,62428,62495,62561,62613,62671,62719,62777,62854,62931,62992,63071,63139,63200,63281,63353,63429,63492,63554,63616,63694,63751,63811,63862,63908,63972,64049,64119,64196,64271,64345,64407,218217,244518,244739,244984,245481,329478,329729,330150,330686,330877,331183,331479,331698,332338,333427,333843,334406,335200,336456,336584", "endLines": "230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,551,552,553,554,555,556,557,558,559,560,561,562,563,564,565,566,567,568,569,570,571,572,573,574,575,576,577,578,579,580,581,582,583,584,585,586,587,588,589,590,591,592,593,594,595,596,597,598,599,600,601,602,603,604,605,606,607,608,609,610,611,612,613,614,615,616,617,618,619,620,621,622,623,624,625,626,627,628,629,630,631,632,633,634,635,636,637,638,639,640,641,642,643,644,645,646,647,648,649,650,651,652,653,654,655,656,657,658,659,660,661,662,663,664,665,666,667,668,669,670,671,672,673,674,675,676,677,678,679,680,681,682,683,684,685,686,687,688,689,690,691,692,693,694,695,696,697,698,699,700,701,702,703,704,705,706,707,708,709,710,711,712,713,714,715,716,717,718,719,720,721,722,723,724,725,726,727,728,729,730,731,732,733,734,735,736,737,738,739,740,741,742,743,744,745,746,747,748,749,750,751,752,753,754,755,756,757,758,759,760,761,762,763,764,765,766,767,768,769,770,771,772,773,774,775,776,777,778,779,780,781,782,783,784,785,786,876,877,1001,1002,1003,1004,1005,1006,1007,1008,1009,1010,1011,1012,1013,1014,1015,1016,1017,1018,1019,1020,1021,1022,1023,1024,1025,1026,1027,1028,1029,1030,1031,1032,1033,1034,1035,1036,1037,1038,1039,1040,3412,3820,3825,3835,3845,5670,5680,5697,5701,5708,5715,5720,5732,5751,5759,5776,5795,5816,5819,5827", "endColumns": "49,49,49,49,55,54,54,54,64,65,67,54,52,48,64,79,79,65,71,66,58,62,60,60,66,67,70,64,71,64,71,65,53,52,44,55,53,61,57,72,56,63,64,58,69,69,71,76,70,64,86,76,73,69,67,73,87,77,63,58,54,63,64,64,57,50,52,71,73,70,72,63,69,67,62,60,59,66,67,59,68,59,67,60,63,62,57,65,62,62,62,77,75,76,68,62,75,62,73,65,67,64,69,71,68,64,60,71,62,63,69,63,64,70,76,64,66,63,73,76,73,71,75,68,63,56,65,66,66,60,55,76,61,61,72,69,64,66,63,66,61,60,68,63,63,65,62,67,64,61,62,67,64,66,63,61,64,64,70,69,63,75,82,73,67,61,63,60,61,62,63,65,62,67,66,68,68,59,82,67,72,70,74,92,74,68,67,65,65,67,72,66,60,82,72,69,65,63,69,83,73,59,62,61,65,60,70,65,69,58,52,64,52,55,52,59,66,59,70,74,67,72,66,71,67,72,61,65,61,65,66,70,60,65,69,74,66,71,60,65,68,73,66,71,59,64,65,69,63,68,72,77,62,67,62,67,62,67,63,68,52,51,61,68,65,60,63,60,67,64,59,52,43,62,66,57,49,53,56,67,80,69,71,66,65,51,57,47,57,76,76,60,78,67,60,80,71,75,62,61,61,77,56,59,50,45,63,76,69,76,74,73,61,52,10,10,10,10,10,250,22,22,22,22,22,22,22,22,22,22,22,22,22,22", "endOffsets": "12348,12398,12448,12498,12554,12609,12664,12719,12784,12850,12918,12973,13026,13075,13140,13220,13300,13366,13438,13505,13564,13627,13688,13749,13816,13884,13955,14020,14092,14157,14229,14295,14349,14402,14447,14503,32641,32703,32761,32834,32891,32955,33020,33079,33149,33219,33291,33368,33439,33504,33591,33668,33742,33812,33880,33954,34042,34120,34184,34243,34298,34362,34427,34492,34550,34601,34654,34726,34800,34871,34944,35008,35078,35146,35209,35270,35330,35397,35465,35525,35594,35654,35722,35783,35847,35910,35968,36034,36097,36160,36223,36301,36377,36454,36523,36586,36662,36725,36799,36865,36933,36998,37068,37140,37209,37274,37335,37407,37470,37534,37604,37668,37733,37804,37881,37946,38013,38077,38151,38228,38302,38374,38450,38519,38583,38640,38706,38773,38840,38901,38957,39034,39096,39158,39231,39301,39366,39433,39497,39564,39626,39687,39756,39820,39884,39950,40013,40081,40146,40208,40271,40339,40404,40471,40535,40597,40662,40727,40798,40868,40932,41008,41091,41165,41233,41295,41359,41420,41482,41545,41609,41675,41738,41806,41873,41942,42011,42071,42154,42222,42295,42366,42441,42534,42609,42678,42746,42812,42878,42946,43019,43086,43147,43230,43303,43373,43439,43503,43573,43657,43731,43791,43854,43916,43982,44043,44114,44180,44250,44309,44362,44427,44480,44536,44589,44649,44716,44776,44847,44922,44990,45063,45130,45202,45270,45343,45405,45471,45533,45599,45666,45737,45798,45864,45934,46009,46076,46148,46209,46275,46344,46418,46485,46557,46617,46682,46748,46818,46882,46951,47024,47102,47165,47233,47296,47364,47427,47495,47559,47628,47681,47733,47795,47864,47930,47991,48055,48116,48184,48249,48309,48362,53706,53769,61913,61971,62021,62075,62132,62200,62281,62351,62423,62490,62556,62608,62666,62714,62772,62849,62926,62987,63066,63134,63195,63276,63348,63424,63487,63549,63611,63689,63746,63806,63857,63903,63967,64044,64114,64191,64266,64340,64402,64455,218584,244734,244979,245476,245975,329724,330145,330681,330872,331178,331474,331693,332333,333422,333838,334401,335195,336451,336579,336998"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b255ac174ecb581f0983b73e2410e4dd\\transformed\\cardview-v7-28.0.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,35,36,37,38,45,47,50,7", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,107,168,230,292,2179,2238,2295,2349,2763,2827,2953,356", "endLines": "2,3,4,5,6,35,36,37,44,46,49,52,34", "endColumns": "51,60,61,61,63,58,56,53,12,12,12,12,24", "endOffsets": "102,163,225,287,351,2233,2290,2344,2758,2822,2948,3076,2174"}, "to": {"startLines": "20,201,202,203,204,482,483,484,1295,2735,2737,2740,4764", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1006,10396,10457,10519,10581,27960,28019,28076,82782,174522,174586,174712,280814", "endLines": "20,201,202,203,204,482,483,484,1301,2736,2739,2742,4791", "endColumns": "51,60,61,61,63,58,56,53,12,12,12,12,24", "endOffsets": "1053,10452,10514,10576,10640,28014,28071,28125,83191,174581,174707,174835,281733"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b66139be6585a43ae23903a8750be796\\transformed\\wind-sdk-4.23.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,17,35,53,59,63,69,83", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,93,136,179,234,328,380,440,493,551,605,659,851,1630,2428,2748,2925,3238,3961", "endLines": "2,3,4,5,6,7,8,9,10,11,12,16,34,52,58,62,68,82,85", "endColumns": "37,42,42,54,93,51,59,52,57,53,53,12,12,12,12,12,12,12,24", "endOffsets": "88,131,174,229,323,375,435,488,546,600,654,846,1625,2423,2743,2920,3233,3956,4078"}, "to": {"startLines": "1069,1070,1071,1072,1073,1074,1075,1076,1077,1078,1079,3862,3866,3882,3900,3906,3910,3916,5412", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "67023,67061,67104,67147,67202,67296,67348,67408,67461,67519,67573,246706,246863,247384,247983,248263,248405,248695,316792", "endLines": "1069,1070,1071,1072,1073,1074,1075,1076,1077,1078,1079,3865,3881,3899,3905,3909,3915,3929,5414", "endColumns": "37,42,42,54,93,51,59,52,57,53,53,12,12,12,12,12,12,12,24", "endOffsets": "67056,67099,67142,67197,67291,67343,67403,67456,67514,67568,67622,246858,247379,247978,248258,248400,248690,249277,316909"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a7007720781ce56b641f8588ab1a8654\\transformed\\coordinatorlayout-28.0.0\\res\\values\\values.xml", "from": {"startLines": "2,102,3,13", "startColumns": "4,4,4,4", "startOffsets": "55,5935,116,724", "endLines": "2,104,12,101", "endColumns": "60,12,24,24", "endOffsets": "111,6075,719,5930"}, "to": {"startLines": "29,3782,4881,4887", "startColumns": "4,4,4,4", "startOffsets": "1473,242735,293320,293531", "endLines": "29,3784,4886,4970", "endColumns": "60,12,24,24", "endOffsets": "1529,242875,293526,298042"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a3ae461f31008f99a87e29f3c99a3c83\\transformed\\appcompat-v7-28.0.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,1303,1304,1305,1306,1307,1308,1309,1310,1311,1312,1313,1314,1315,1316,1317,1318,1319,1320,1321,1322,1323,1324,1325,1326,1327,1328,1329,1330,1331,1332,1333,1334,1335,1336,1337,1338,1339,1340,1341,1342,1343,1344,1345,1346,1347,1348,1349,1350,1351,1352,1353,1354,1355,1356,1357,1358,1359,1360,1361,1362,1363,1364,1365,1366,1367,1368,1369,1370,1371,1372,1373,1374,1375,1376,1377,1378,1379,1380,1381,1382,1383,1384,1385,1386,1387,1388,1389,1390,1391,1392,1393,1394,1395,1396,1397,1398,1399,1400,1401,1402,1403,1404,1405,1406,1407,1408,1409,1410,1411,1412,1413,1414,1415,1416,1417,1418,1419,1420,1421,1422,1423,1424,1425,1426,1427,1428,1429,1430,1431,1432,1433,1434,1435,1436,1437,1438,1439,1440,1441,1442,1443,1444,1445,1446,1447,1448,1449,1450,1451,1452,1453,1461,1462,1466,1470,1474,1479,1485,1492,1496,1500,1505,1509,1513,1517,1521,1525,1529,1535,1539,1545,1549,1555,1559,1564,1568,1571,1575,1581,1585,1591,1595,1601,1604,1608,1612,1616,1620,1624,1625,1626,1627,1630,1633,1636,1639,1643,1644,1645,1646,1647,1650,1652,1654,1656,1661,1662,1666,1672,1676,1677,1679,1690,1691,1695,1701,1705,1706,1707,1711,1738,1742,1743,1747,1775,1943,1969,2138,2164,2195,2203,2209,2223,2245,2250,2255,2265,2274,2283,2287,2294,2302,2309,2310,2319,2322,2325,2329,2333,2337,2340,2341,2345,2349,2359,2364,2371,2377,2378,2381,2385,2390,2392,2394,2397,2400,2402,2406,2409,2416,2419,2422,2426,2428,2432,2434,2436,2438,2442,2450,2458,2470,2476,2485,2488,2499,2502,2507,2508,2513,2571,2630,2631,2641,2650,2651,2653,2657,2660,2663,2666,2669,2672,2675,2678,2682,2685,2688,2691,2695,2698,2702,2706,2707,2708,2709,2710,2711,2712,2713,2714,2715,2716,2717,2718,2719,2720,2721,2722,2723,2724,2725,2726,2728,2730,2731,2732,2733,2734,2735,2736,2737,2739,2740,2742,2743,2745,2747,2748,2750,2751,2752,2753,2754,2755,2757,2758,2759,2760,2761,2762,2764,2766,2768,2769,2770,2771,2772,2773,2774,2775,2776,2777,2778,2779,2780,2782,2783,2784,2785,2786,2787,2789,2793,2797,2798,2799,2800,2801,2802,2803,2804,2806,2808,2810,2812,2814,2815,2816,2817,2819,2821,2823,2824,2825,2826,2827,2828,2829,2830,2831,2832,2833,2834,2837,2838,2839,2840,2842,2844,2845,2847,2848,2850,2852,2854,2855,2856,2857,2858,2859,2860,2861,2862,2863,2864,2865,2867,2868,2869,2870,2872,2873,2874,2875,2876,2878,2880,2882,2884,2885,2886,2887,2888,2889,2890,2891,2892,2893,2894,2895,2896,2897,69,152,155,158,161,175,186,196,223,230,241,271,298,307,344,725,730,756,774,810,816,822,845,986,1006,1012,1016,1022,1059,1071,1098,1103,1169,1184,1249,1268,1294", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,160,205,254,295,350,409,471,552,613,688,764,841,919,1004,1086,1162,1238,1315,1393,1499,1605,1684,1764,1821,1879,1953,2028,2093,2159,2219,2280,2352,2425,2492,2560,2619,2678,2737,2796,2855,2909,2963,3016,3070,3124,3178,3232,3306,3385,3458,3532,3603,3675,3747,3820,3877,3935,4008,4082,4156,4231,4303,4376,4446,4517,4577,70911,70980,71049,71119,71193,71269,71333,71410,71486,71563,71628,71697,71774,71849,71918,71986,72063,72129,72190,72287,72352,72421,72520,72591,72650,72708,72765,72824,72888,72959,73031,73103,73175,73247,73314,73382,73450,73509,73572,73636,73726,73817,73877,73943,74010,74076,74146,74210,74263,74376,74434,74497,74562,74627,74702,74775,74847,74896,74957,75018,75079,75141,75205,75269,75333,75398,75461,75521,75582,75648,75707,75767,75829,75900,75960,76028,76114,76201,76291,76378,76466,76548,76631,76721,76812,76864,76922,76967,77033,77097,77154,77211,77265,77322,77370,77419,77470,77504,77551,77600,77646,77678,77742,77804,77864,77921,77995,78065,78143,78197,78267,78352,78400,78446,78517,78595,78673,78745,78819,78893,78967,79047,79120,79189,79261,79338,79399,79462,79528,79592,79663,79726,79791,79855,79916,79977,80029,80102,80176,80245,80320,80394,80468,80609,80679,80732,80810,80900,80988,81084,81174,81756,81845,82092,82373,82625,82910,83303,83780,84002,84224,84500,84727,84957,85187,85417,85647,85874,86293,86519,86944,87174,87602,87821,88104,88312,88443,88670,89096,89321,89748,89969,90394,90514,90790,91091,91415,91706,92020,92157,92288,92393,92635,92802,93006,93214,93485,93597,93709,93814,93931,94145,94291,94431,94517,94865,94953,95199,95617,95866,95948,96046,96663,96763,97015,97439,97694,97788,97877,98114,100166,100408,100510,100763,102947,113668,115184,126004,127532,129289,129915,130335,131396,132661,132917,133153,133700,134194,134799,134997,135577,136141,136516,136634,137172,137329,137525,137798,138054,138224,138365,138429,138711,138997,139673,139937,140275,140628,140722,140908,141214,141476,141601,141728,141967,142178,142297,142490,142667,143122,143303,143425,143684,143797,143984,144086,144193,144322,144597,145105,145601,146478,146772,147342,147491,148223,148395,148731,148823,149101,153445,157932,157994,158624,159238,159329,159442,159671,159831,159983,160154,160320,160489,160656,160819,161062,161232,161405,161576,161850,162049,162254,162584,162668,162764,162860,162958,163058,163160,163262,163364,163466,163568,163668,163764,163876,164005,164128,164259,164390,164488,164602,164696,164836,164970,165066,165178,165278,165394,165490,165602,165702,165842,165978,166142,166272,166430,166580,166721,166865,167000,167112,167262,167390,167518,167654,167786,167916,168046,168158,168298,168444,168588,168726,168792,168882,168958,169062,169152,169254,169362,169470,169570,169650,169742,169840,169950,170028,170134,170226,170330,170440,170562,170725,170882,170962,171062,171152,171262,171356,171462,171554,171654,171766,171880,171996,172112,172206,172320,172432,172534,172654,172776,172858,172962,173082,173208,173306,173400,173488,173600,173716,173838,173950,174125,174241,174327,174419,174531,174655,174722,174848,174916,175044,175188,175316,175385,175480,175595,175708,175807,175916,176027,176138,176239,176344,176444,176574,176665,176788,176882,176994,177080,177184,177280,177368,177486,177590,177694,177820,177908,178016,178116,178206,178316,178400,178502,178586,178640,178704,178810,178920,179004,4638,9782,9900,10015,10147,10862,11554,12071,13718,14103,14700,16299,17832,18220,20527,40045,40305,41697,42730,44743,45005,45361,46191,52973,54107,54401,54624,54951,57001,57649,59282,59552,63403,64004,67813,69028,70437", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,1303,1304,1305,1306,1307,1308,1309,1310,1311,1312,1313,1314,1315,1316,1317,1318,1319,1320,1321,1322,1323,1324,1325,1326,1327,1328,1329,1330,1331,1332,1333,1334,1335,1336,1337,1338,1339,1340,1341,1342,1343,1344,1345,1346,1347,1348,1349,1350,1351,1352,1353,1354,1355,1356,1357,1358,1359,1360,1361,1362,1363,1364,1365,1366,1367,1368,1369,1370,1371,1372,1373,1374,1375,1376,1377,1378,1379,1380,1381,1382,1383,1384,1385,1386,1387,1388,1389,1390,1391,1392,1393,1394,1395,1396,1397,1398,1399,1400,1401,1402,1403,1404,1405,1406,1407,1408,1409,1410,1411,1412,1413,1414,1415,1416,1417,1418,1419,1420,1421,1422,1423,1424,1425,1426,1427,1428,1429,1430,1431,1432,1433,1434,1435,1436,1437,1438,1439,1440,1441,1442,1443,1444,1445,1446,1447,1448,1449,1450,1451,1452,1460,1461,1465,1469,1473,1478,1484,1491,1495,1499,1504,1508,1512,1516,1520,1524,1528,1534,1538,1544,1548,1554,1558,1563,1567,1570,1574,1580,1584,1590,1594,1600,1603,1607,1611,1615,1619,1623,1624,1625,1626,1629,1632,1635,1638,1642,1643,1644,1645,1646,1649,1651,1653,1655,1660,1661,1665,1671,1675,1676,1678,1689,1690,1694,1700,1704,1705,1706,1710,1737,1741,1742,1746,1774,1942,1968,2137,2163,2194,2202,2208,2222,2244,2249,2254,2264,2273,2282,2286,2293,2301,2308,2309,2318,2321,2324,2328,2332,2336,2339,2340,2344,2348,2358,2363,2370,2376,2377,2380,2384,2389,2391,2393,2396,2399,2401,2405,2408,2415,2418,2421,2425,2427,2431,2433,2435,2437,2441,2449,2457,2469,2475,2484,2487,2498,2501,2506,2507,2512,2570,2629,2630,2640,2649,2650,2652,2656,2659,2662,2665,2668,2671,2674,2677,2681,2684,2687,2690,2694,2697,2701,2705,2706,2707,2708,2709,2710,2711,2712,2713,2714,2715,2716,2717,2718,2719,2720,2721,2722,2723,2724,2725,2727,2729,2730,2731,2732,2733,2734,2735,2736,2738,2739,2741,2742,2744,2746,2747,2749,2750,2751,2752,2753,2754,2756,2757,2758,2759,2760,2761,2763,2765,2767,2768,2769,2770,2771,2772,2773,2774,2775,2776,2777,2778,2779,2781,2782,2783,2784,2785,2786,2788,2792,2796,2797,2798,2799,2800,2801,2802,2803,2805,2807,2809,2811,2813,2814,2815,2816,2818,2820,2822,2823,2824,2825,2826,2827,2828,2829,2830,2831,2832,2833,2836,2837,2838,2839,2841,2843,2844,2846,2847,2849,2851,2853,2854,2855,2856,2857,2858,2859,2860,2861,2862,2863,2864,2866,2867,2868,2869,2871,2872,2873,2874,2875,2877,2879,2881,2883,2884,2885,2886,2887,2888,2889,2890,2891,2892,2893,2894,2895,2896,2897,151,154,157,160,174,185,195,222,229,240,270,297,306,343,724,729,755,773,809,815,821,844,985,1005,1011,1015,1021,1058,1070,1097,1102,1168,1183,1248,1267,1293,1302", "endColumns": "54,44,48,40,54,58,61,80,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,112,57,62,64,64,74,72,71,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,70,77,77,71,73,73,73,79,72,68,71,76,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,77,105,91,103,109,12,12,12,79,99,89,109,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "155,200,249,290,345,404,466,547,608,683,759,836,914,999,1081,1157,1233,1310,1388,1494,1600,1679,1759,1816,1874,1948,2023,2088,2154,2214,2275,2347,2420,2487,2555,2614,2673,2732,2791,2850,2904,2958,3011,3065,3119,3173,3227,3301,3380,3453,3527,3598,3670,3742,3815,3872,3930,4003,4077,4151,4226,4298,4371,4441,4512,4572,4633,70975,71044,71114,71188,71264,71328,71405,71481,71558,71623,71692,71769,71844,71913,71981,72058,72124,72185,72282,72347,72416,72515,72586,72645,72703,72760,72819,72883,72954,73026,73098,73170,73242,73309,73377,73445,73504,73567,73631,73721,73812,73872,73938,74005,74071,74141,74205,74258,74371,74429,74492,74557,74622,74697,74770,74842,74891,74952,75013,75074,75136,75200,75264,75328,75393,75456,75516,75577,75643,75702,75762,75824,75895,75955,76023,76109,76196,76286,76373,76461,76543,76626,76716,76807,76859,76917,76962,77028,77092,77149,77206,77260,77317,77365,77414,77465,77499,77546,77595,77641,77673,77737,77799,77859,77916,77990,78060,78138,78192,78262,78347,78395,78441,78512,78590,78668,78740,78814,78888,78962,79042,79115,79184,79256,79333,79394,79457,79523,79587,79658,79721,79786,79850,79911,79972,80024,80097,80171,80240,80315,80389,80463,80604,80674,80727,80805,80895,80983,81079,81169,81751,81840,82087,82368,82620,82905,83298,83775,83997,84219,84495,84722,84952,85182,85412,85642,85869,86288,86514,86939,87169,87597,87816,88099,88307,88438,88665,89091,89316,89743,89964,90389,90509,90785,91086,91410,91701,92015,92152,92283,92388,92630,92797,93001,93209,93480,93592,93704,93809,93926,94140,94286,94426,94512,94860,94948,95194,95612,95861,95943,96041,96658,96758,97010,97434,97689,97783,97872,98109,100161,100403,100505,100758,102942,113663,115179,125999,127527,129284,129910,130330,131391,132656,132912,133148,133695,134189,134794,134992,135572,136136,136511,136629,137167,137324,137520,137793,138049,138219,138360,138424,138706,138992,139668,139932,140270,140623,140717,140903,141209,141471,141596,141723,141962,142173,142292,142485,142662,143117,143298,143420,143679,143792,143979,144081,144188,144317,144592,145100,145596,146473,146767,147337,147486,148218,148390,148726,148818,149096,153440,157927,157989,158619,159233,159324,159437,159666,159826,159978,160149,160315,160484,160651,160814,161057,161227,161400,161571,161845,162044,162249,162579,162663,162759,162855,162953,163053,163155,163257,163359,163461,163563,163663,163759,163871,164000,164123,164254,164385,164483,164597,164691,164831,164965,165061,165173,165273,165389,165485,165597,165697,165837,165973,166137,166267,166425,166575,166716,166860,166995,167107,167257,167385,167513,167649,167781,167911,168041,168153,168293,168439,168583,168721,168787,168877,168953,169057,169147,169249,169357,169465,169565,169645,169737,169835,169945,170023,170129,170221,170325,170435,170557,170720,170877,170957,171057,171147,171257,171351,171457,171549,171649,171761,171875,171991,172107,172201,172315,172427,172529,172649,172771,172853,172957,173077,173203,173301,173395,173483,173595,173711,173833,173945,174120,174236,174322,174414,174526,174650,174717,174843,174911,175039,175183,175311,175380,175475,175590,175703,175802,175911,176022,176133,176234,176339,176439,176569,176660,176783,176877,176989,177075,177179,177275,177363,177481,177585,177689,177815,177903,178011,178111,178201,178311,178395,178497,178581,178635,178699,178805,178915,178999,179119,9777,9895,10010,10142,10857,11549,12066,13713,14098,14695,16294,17827,18215,20522,40040,40300,41692,42725,44738,45000,45356,46186,52968,54102,54396,54619,54946,56996,57644,59277,59547,63398,63999,67808,69023,70432,70906"}, "to": {"startLines": "30,36,38,173,174,175,176,178,179,180,181,182,183,187,188,190,191,193,194,195,196,197,198,199,200,216,217,218,219,221,222,225,226,227,228,266,267,268,269,270,271,272,273,274,275,276,277,302,303,304,305,306,307,308,309,313,314,315,316,317,318,320,321,322,323,328,329,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,536,537,541,542,543,544,545,546,547,854,855,856,857,858,859,860,861,869,870,871,872,874,883,884,890,915,917,918,921,922,933,934,935,936,937,938,939,940,941,942,943,944,945,946,947,948,949,950,951,952,953,954,955,956,957,958,959,960,961,962,963,964,965,966,967,968,969,970,971,1068,1258,1259,1264,1265,1266,1274,1282,1283,1287,1291,1302,1307,1313,1320,1324,1328,1333,1337,1341,1345,1349,1353,1357,1363,1367,1373,1377,1383,1387,1392,1396,1399,1403,1409,1413,1419,1423,1429,1432,1436,1440,1444,1448,1452,1453,1454,1455,1458,1461,1464,1467,1471,1472,1473,1474,1475,1478,1480,1482,1484,1489,1490,1494,1500,1504,1505,1507,1518,1519,1523,1529,1533,1587,1588,1592,1619,1623,1624,1628,1878,2045,2071,2239,2265,2296,2304,2310,2324,2346,2351,2356,2366,2375,2384,2388,2395,2403,2410,2411,2420,2423,2426,2430,2434,2438,2441,2442,2446,2450,2460,2465,2472,2478,2479,2482,2486,2491,2493,2495,2498,2501,2503,2507,2510,2517,2520,2523,2527,2529,2533,2535,2537,2539,2543,2551,2559,2571,2577,2586,2589,2600,2603,2608,2609,2829,2887,2950,2951,2961,2970,2975,2977,2981,2984,2987,2990,2993,2996,2999,3002,3006,3009,3012,3015,3019,3022,3026,3042,3043,3044,3045,3046,3047,3048,3049,3050,3051,3052,3053,3054,3055,3056,3057,3058,3059,3060,3061,3062,3064,3066,3067,3068,3069,3070,3071,3072,3073,3075,3076,3078,3079,3081,3083,3084,3086,3087,3088,3089,3090,3091,3093,3094,3095,3096,3097,3252,3254,3256,3258,3259,3260,3261,3262,3263,3264,3265,3266,3267,3268,3269,3270,3272,3273,3274,3275,3276,3277,3279,3283,3378,3379,3380,3381,3382,3383,3384,3413,3415,3417,3419,3421,3423,3424,3425,3426,3428,3430,3432,3433,3434,3435,3436,3437,3438,3439,3440,3441,3442,3443,3446,3447,3448,3449,3451,3453,3454,3456,3457,3459,3461,3463,3464,3465,3466,3467,3468,3469,3470,3471,3472,3473,3474,3476,3477,3478,3479,3481,3482,3483,3484,3485,3487,3489,3491,3493,3494,3495,3496,3497,3498,3499,3500,3501,3502,3503,3504,3505,3506,4050,4125,4128,4131,4134,4148,4219,4229,4245,4251,4282,4311,4338,4347,4376,4760,4852,4972,5093,5117,5123,5137,5158,5282,5339,5345,5349,5377,5417,5429,5445,5449,5539,5574,5625,5637,5663", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1534,1761,1859,8444,8485,8540,8599,8723,8804,8865,8940,9016,9093,9343,9428,9561,9637,9755,9832,9910,10016,10122,10201,10281,10338,11408,11482,11557,11622,11751,11811,11966,12038,12111,12178,14508,14567,14626,14685,14744,14803,14857,14911,14964,15018,15072,15126,16635,16709,16788,16861,16935,17006,17078,17150,17364,17421,17479,17552,17626,17700,17823,17895,17968,18038,18295,18355,22843,22912,22981,23051,23125,23201,23265,23342,23418,23495,23560,23629,23706,23781,23850,23918,23995,24061,24122,24219,24284,24353,24452,24523,24582,24640,24697,24756,24820,24891,24963,25035,25107,25179,25246,25314,25382,25441,25504,25568,25658,25749,25809,25875,25942,26008,26078,26142,26195,26308,26366,26429,26494,26559,26634,26707,26779,26828,26889,26950,27011,27073,27137,27201,27265,27330,27393,27453,27514,27580,27639,27699,27761,27832,27892,31422,31508,31758,31848,31935,32023,32105,32188,32278,52407,52459,52517,52562,52628,52692,52749,52806,53322,53379,53427,53476,53567,54007,54054,54338,55663,55743,55807,55997,56057,56734,56808,56878,56956,57010,57080,57165,57213,57259,57330,57408,57486,57558,57632,57706,57780,57860,57933,58002,58074,58151,58212,58275,58341,58405,58476,58539,58604,58668,58729,58790,58842,58915,58989,59058,59133,59207,59281,59422,66970,80292,80370,80684,80772,80868,81331,81913,82002,82249,82530,83196,83481,83874,84351,84573,84795,85071,85298,85528,85758,85988,86218,86445,86864,87090,87515,87745,88173,88392,88675,88883,89014,89241,89667,89892,90319,90540,90965,91085,91361,91662,91986,92277,92591,92728,92859,92964,93206,93373,93577,93785,94056,94168,94280,94385,94502,94716,94862,95002,95088,95436,95524,95770,96188,96437,96519,96617,97209,97309,97561,97985,98240,102042,102131,102368,104392,104634,104736,104989,123006,133135,134651,144879,146407,148164,148790,149210,150271,151536,151792,152028,152575,153069,153674,153872,154452,155016,155391,155509,156047,156204,156400,156673,156929,157099,157240,157304,157586,157872,158548,158812,159150,159503,159597,159783,160089,160351,160476,160603,160842,161053,161172,161365,161542,161997,162178,162300,162559,162672,162859,162961,163068,163197,163472,163980,164476,165353,165647,166217,166366,167098,167270,167606,167698,178952,183234,188003,188065,188643,189227,189552,189665,189894,190054,190206,190377,190543,190712,190879,191042,191285,191455,191628,191799,192073,192272,192477,193502,193586,193682,193778,193876,193976,194078,194180,194282,194384,194486,194586,194682,194794,194923,195046,195177,195308,195406,195520,195614,195754,195888,195984,196096,196196,196312,196408,196520,196620,196760,196896,197060,197190,197348,197498,197639,197783,197918,198030,198180,198308,198436,198572,198704,198834,198964,199076,207690,207836,207980,208118,208184,208274,208350,208454,208544,208646,208754,208862,208962,209042,209134,209232,209342,209420,209526,209618,209722,209832,209954,210117,215939,216019,216119,216209,216319,216413,216519,218589,218689,218801,218915,219031,219147,219241,219355,219467,219569,219689,219811,219893,219997,220117,220243,220341,220435,220523,220635,220751,220873,220985,221160,221276,221362,221454,221566,221690,221757,221883,221951,222079,222223,222351,222420,222515,222630,222743,222842,222951,223062,223173,223274,223379,223479,223609,223700,223823,223917,224029,224115,224219,224315,224403,224521,224625,224729,224855,224943,225051,225151,225241,225351,225435,225537,225621,225675,225739,225845,225955,226039,255987,258603,258721,258836,258916,259277,262223,262740,263165,263343,264678,266022,267383,267771,268849,280679,286055,298178,303239,303990,304252,306426,306805,311083,314057,314286,314437,315709,317249,317561,317972,318099,323054,325703,327624,327964,329275", "endLines": "30,36,38,173,174,175,176,178,179,180,181,182,183,187,188,190,191,193,194,195,196,197,198,199,200,216,217,218,219,221,222,225,226,227,228,266,267,268,269,270,271,272,273,274,275,276,277,302,303,304,305,306,307,308,309,313,314,315,316,317,318,320,321,322,323,328,329,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,536,537,541,542,543,544,545,546,547,854,855,856,857,858,859,860,861,869,870,871,872,874,883,884,890,915,917,918,921,922,933,934,935,936,937,938,939,940,941,942,943,944,945,946,947,948,949,950,951,952,953,954,955,956,957,958,959,960,961,962,963,964,965,966,967,968,969,970,971,1068,1258,1259,1264,1265,1266,1281,1282,1286,1290,1294,1306,1312,1319,1323,1327,1332,1336,1340,1344,1348,1352,1356,1362,1366,1372,1376,1382,1386,1391,1395,1398,1402,1408,1412,1418,1422,1428,1431,1435,1439,1443,1447,1451,1452,1453,1454,1457,1460,1463,1466,1470,1471,1472,1473,1474,1477,1479,1481,1483,1488,1489,1493,1499,1503,1504,1506,1517,1518,1522,1528,1532,1533,1587,1591,1618,1622,1623,1627,1655,2044,2070,2238,2264,2295,2303,2309,2323,2345,2350,2355,2365,2374,2383,2387,2394,2402,2409,2410,2419,2422,2425,2429,2433,2437,2440,2441,2445,2449,2459,2464,2471,2477,2478,2481,2485,2490,2492,2494,2497,2500,2502,2506,2509,2516,2519,2522,2526,2528,2532,2534,2536,2538,2542,2550,2558,2570,2576,2585,2588,2599,2602,2607,2608,2613,2886,2945,2950,2960,2969,2970,2976,2980,2983,2986,2989,2992,2995,2998,3001,3005,3008,3011,3014,3018,3021,3025,3029,3042,3043,3044,3045,3046,3047,3048,3049,3050,3051,3052,3053,3054,3055,3056,3057,3058,3059,3060,3061,3063,3065,3066,3067,3068,3069,3070,3071,3072,3074,3075,3077,3078,3080,3082,3083,3085,3086,3087,3088,3089,3090,3092,3093,3094,3095,3096,3097,3253,3255,3257,3258,3259,3260,3261,3262,3263,3264,3265,3266,3267,3268,3269,3271,3272,3273,3274,3275,3276,3278,3282,3286,3378,3379,3380,3381,3382,3383,3384,3414,3416,3418,3420,3422,3423,3424,3425,3427,3429,3431,3432,3433,3434,3435,3436,3437,3438,3439,3440,3441,3442,3445,3446,3447,3448,3450,3452,3453,3455,3456,3458,3460,3462,3463,3464,3465,3466,3467,3468,3469,3470,3471,3472,3473,3475,3476,3477,3478,3480,3481,3482,3483,3484,3486,3488,3490,3492,3493,3494,3495,3496,3497,3498,3499,3500,3501,3502,3503,3504,3505,3506,4124,4127,4130,4133,4147,4153,4228,4244,4250,4260,4310,4337,4346,4375,4718,4763,4877,4989,5116,5122,5128,5157,5281,5301,5344,5348,5354,5411,5428,5444,5448,5514,5553,5624,5636,5662,5669", "endColumns": "54,44,48,40,54,58,61,80,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,112,57,62,64,64,74,72,71,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,70,77,77,71,73,73,73,79,72,68,71,76,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,77,105,91,103,109,12,12,12,79,99,89,109,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "1584,1801,1903,8480,8535,8594,8656,8799,8860,8935,9011,9088,9166,9423,9505,9632,9708,9827,9905,10011,10117,10196,10276,10333,10391,11477,11552,11617,11683,11806,11867,12033,12106,12173,12241,14562,14621,14680,14739,14798,14852,14906,14959,15013,15067,15121,15175,16704,16783,16856,16930,17001,17073,17145,17218,17416,17474,17547,17621,17695,17770,17890,17963,18033,18104,18350,18411,22907,22976,23046,23120,23196,23260,23337,23413,23490,23555,23624,23701,23776,23845,23913,23990,24056,24117,24214,24279,24348,24447,24518,24577,24635,24692,24751,24815,24886,24958,25030,25102,25174,25241,25309,25377,25436,25499,25563,25653,25744,25804,25870,25937,26003,26073,26137,26190,26303,26361,26424,26489,26554,26629,26702,26774,26823,26884,26945,27006,27068,27132,27196,27260,27325,27388,27448,27509,27575,27634,27694,27756,27827,27887,27955,31503,31590,31843,31930,32018,32100,32183,32273,32364,52454,52512,52557,52623,52687,52744,52801,52855,53374,53422,53471,53522,53596,54049,54098,54379,55690,55802,55864,56052,56109,56803,56873,56951,57005,57075,57160,57208,57254,57325,57403,57481,57553,57627,57701,57775,57855,57928,57997,58069,58146,58207,58270,58336,58400,58471,58534,58599,58663,58724,58785,58837,58910,58984,59053,59128,59202,59276,59417,59487,67018,80365,80455,80767,80863,80953,81908,81997,82244,82525,82777,83476,83869,84346,84568,84790,85066,85293,85523,85753,85983,86213,86440,86859,87085,87510,87740,88168,88387,88670,88878,89009,89236,89662,89887,90314,90535,90960,91080,91356,91657,91981,92272,92586,92723,92854,92959,93201,93368,93572,93780,94051,94163,94275,94380,94497,94711,94857,94997,95083,95431,95519,95765,96183,96432,96514,96612,97204,97304,97556,97980,98235,98329,102126,102363,104387,104629,104731,104984,107140,133130,134646,144874,146402,148159,148785,149205,150266,151531,151787,152023,152570,153064,153669,153867,154447,155011,155386,155504,156042,156199,156395,156668,156924,157094,157235,157299,157581,157867,158543,158807,159145,159498,159592,159778,160084,160346,160471,160598,160837,161048,161167,161360,161537,161992,162173,162295,162554,162667,162854,162956,163063,163192,163467,163975,164471,165348,165642,166212,166361,167093,167265,167601,167693,167971,183229,187654,188060,188638,189222,189313,189660,189889,190049,190201,190372,190538,190707,190874,191037,191280,191450,191623,191794,192068,192267,192472,192802,193581,193677,193773,193871,193971,194073,194175,194277,194379,194481,194581,194677,194789,194918,195041,195172,195303,195401,195515,195609,195749,195883,195979,196091,196191,196307,196403,196515,196615,196755,196891,197055,197185,197343,197493,197634,197778,197913,198025,198175,198303,198431,198567,198699,198829,198959,199071,199211,207831,207975,208113,208179,208269,208345,208449,208539,208641,208749,208857,208957,209037,209129,209227,209337,209415,209521,209613,209717,209827,209949,210112,210269,216014,216114,216204,216314,216408,216514,216606,218684,218796,218910,219026,219142,219236,219350,219462,219564,219684,219806,219888,219992,220112,220238,220336,220430,220518,220630,220746,220868,220980,221155,221271,221357,221449,221561,221685,221752,221878,221946,222074,222218,222346,222415,222510,222625,222738,222837,222946,223057,223168,223269,223374,223474,223604,223695,223818,223912,224024,224110,224214,224310,224398,224516,224620,224724,224850,224938,225046,225146,225236,225346,225430,225532,225616,225670,225734,225840,225950,226034,226154,258598,258716,258831,258911,259272,259505,262735,263160,263338,263622,266017,267378,267766,268844,278362,280809,287343,298745,303985,304247,304447,306800,311078,311684,314281,314432,314647,316787,317556,317967,318094,321120,323650,327619,327959,329270,329473"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\e779f411b6970bbfe6a7d381c7d1cfc3\\transformed\\transition-28.0.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,95,138,185,240,285,339,391,440,501", "endColumns": "39,42,46,54,44,53,51,48,60,49", "endOffsets": "90,133,180,235,280,334,386,435,496,546"}, "to": {"startLines": "873,882,885,886,887,901,902,903,904,905", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "53527,53964,54103,54150,54205,54836,54890,54942,54991,55052", "endColumns": "39,42,46,54,44,53,51,48,60,49", "endOffsets": "53562,54002,54145,54200,54245,54885,54937,54986,55047,55097"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\6e168fd8550fecc7fe244221549ae7ea\\transformed\\constraint-layout-1.1.3\\res\\values\\values.xml", "from": {"startLines": "2,3,11,12,13,14,15,19,20,21,22,25,26,29,32,33,34,35,36,39,42,43,44,45,50,53,56,57,58,63,64,65,68,71,72,75,78,81,84,85,88,91,92,97,98,103,106,109,110,111,112,113,114,115,116,117,118,127,128,129,130", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,116,407,455,507,568,614,741,802,862,932,1065,1133,1262,1388,1450,1515,1583,1650,1773,1898,1965,2030,2095,2276,2397,2518,2584,2651,2861,2930,2996,3121,3247,3314,3440,3567,3692,3819,3884,4010,4133,4198,4406,4473,4653,4773,4893,4958,5020,5082,5144,5203,5263,5324,5385,5444,5819,8395,8527,11791", "endLines": "2,10,11,12,13,14,18,19,20,21,24,25,28,31,32,33,34,35,38,41,42,43,44,49,52,55,56,57,62,63,64,67,70,71,74,77,80,83,84,87,90,91,96,97,102,105,108,109,110,111,112,113,114,115,116,117,126,127,128,129,130", "endColumns": "60,11,47,51,60,45,11,60,59,69,11,67,11,11,61,64,67,66,11,11,66,64,64,11,11,11,65,66,11,68,65,11,11,66,11,11,11,11,64,11,11,64,11,66,11,11,11,64,61,61,61,58,59,60,60,58,11,2575,131,3263,107", "endOffsets": "111,402,450,502,563,609,736,797,857,927,1060,1128,1257,1383,1445,1510,1578,1645,1768,1893,1960,2025,2090,2271,2392,2513,2579,2646,2856,2925,2991,3116,3242,3309,3435,3562,3687,3814,3879,4005,4128,4193,4401,4468,4648,4768,4888,4953,5015,5077,5139,5198,5258,5319,5380,5439,5814,8390,8522,11786,11894"}, "to": {"startLines": "4,5,21,26,27,28,31,39,40,41,42,45,46,49,52,53,54,55,56,59,62,63,64,65,70,73,76,77,78,83,84,85,88,91,92,95,98,101,104,105,108,111,112,117,118,123,126,129,130,131,132,133,134,135,136,137,138,4878,4879,4880,5092", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "275,336,1058,1314,1366,1427,1589,1908,1969,2029,2099,2232,2300,2429,2555,2617,2682,2750,2817,2940,3065,3132,3197,3262,3443,3564,3685,3751,3818,4028,4097,4163,4288,4414,4481,4607,4734,4859,4986,5051,5177,5300,5365,5573,5640,5820,5940,6060,6125,6187,6249,6311,6370,6430,6491,6552,6611,287348,289924,290056,303131", "endLines": "4,12,21,26,27,28,34,39,40,41,44,45,48,51,52,53,54,55,58,61,62,63,64,69,72,75,76,77,82,83,84,87,90,91,94,97,100,103,104,107,110,111,116,117,122,125,128,129,130,131,132,133,134,135,136,137,146,4878,4879,4880,5092", "endColumns": "60,11,47,51,60,45,11,60,59,69,11,67,11,11,61,64,67,66,11,11,66,64,64,11,11,11,65,66,11,68,65,11,11,66,11,11,11,11,64,11,11,64,11,66,11,11,11,64,61,61,61,58,59,60,60,58,11,2575,131,3263,107", "endOffsets": "331,622,1101,1361,1422,1468,1711,1964,2024,2094,2227,2295,2424,2550,2612,2677,2745,2812,2935,3060,3127,3192,3257,3438,3559,3680,3746,3813,4023,4092,4158,4283,4409,4476,4602,4729,4854,4981,5046,5172,5295,5360,5568,5635,5815,5935,6055,6120,6182,6244,6306,6365,6425,6486,6547,6606,6948,289919,290051,293315,303234"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\194766323b8e4d092ba620fa2362821d\\transformed\\support-media-compat-28.0.0\\res\\values\\values.xml", "from": {"startLines": "2,5,6,7,8,10,11,12,13,14", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,288,341,394,447,560,626,748,809,875", "endColumns": "88,52,52,52,52,65,121,60,65,66", "endOffsets": "139,336,389,442,495,621,743,804,870,937"}, "to": {"startLines": "288,850,851,852,853,3103,3105,3106,3111,3113", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "15864,52195,52248,52301,52354,199534,199710,199832,200094,200289", "endColumns": "88,52,52,52,52,65,121,60,65,66", "endOffsets": "15948,52243,52296,52349,52402,199595,199827,199888,200155,200351"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\275e6b20e6ac21f664e4a13571bf830c\\transformed\\recyclerview-v7-28.0.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endLines": "29", "endColumns": "24", "endOffsets": "1530"}, "to": {"startLines": "5355", "startColumns": "4", "startOffsets": "314652", "endLines": "5374", "endColumns": "24", "endOffsets": "315442"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\71b2f468c1ec12de737a4945bfcdd2ee\\transformed\\design-28.0.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,345,346,347,352,353,357,363,367,368,369,370,381,382,383,387,393,397,398,399,400,430,450,496,526,546,566,612,616,620,634,675,683,693,694,695,696,697,700,701,704,707,708,711,715,720,728,736,745,753,757,765,773,781,789,797,806,815,823,832,835,837,842,844,849,853,857,858,863,864,865,866,867,868,870,871,876,877,878,879,880,881,882,884,888,892,896,900,901,902,903,904,905,906,907,908,911,915,918,922,930,937,946,950,965,973,976,985,990,1001,1009,1012,1021,1028,1029,1048,1051,1057,1060,1069,1072,1075,1078,1081,1084,1088,1091,1100,1103,1111,1116,1124,1129,1133,1134,1145,1152,1156,1160,1161,1165,1173,1177,1182,1187,55,56,57,76,82,96,97,98,141,149,150,158,159,160,161,167,168,169,170,171,172,173,174,175,198,217", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,156,216,277,332,385,443,491,540,585,638,696,756,814,860,920,973,1019,1069,1116,1174,1232,1291,1351,1413,1475,1537,1599,1661,1723,1784,1846,1908,1961,2023,2097,2160,2228,2309,2373,2439,2509,2579,2649,2719,2786,2849,2914,2980,3033,3109,3175,3262,18372,18426,18505,18583,18656,18721,18784,18850,18921,18992,19054,19123,19189,19256,19323,19379,19430,19483,19535,19589,19660,19723,19782,19844,19903,19976,20043,20103,20166,20241,20313,20384,20440,20511,20568,20625,20691,20755,20826,20883,20936,20999,21051,21109,21176,21242,21308,21389,21464,21520,21573,21634,21692,21742,21791,21840,21889,21951,22003,22048,22129,22183,22236,22290,22341,22390,22441,22502,22563,22625,22675,22716,22766,22814,22876,22927,22976,23045,23106,23162,23233,23298,23367,23418,23481,23551,23620,23690,23752,23822,23892,23967,24026,24084,24146,24191,24234,24281,24326,24377,24425,24491,24553,24616,24688,24745,24802,24862,24920,24990,25047,25192,25313,25417,25504,25656,25808,25956,26037,26115,26416,26582,26737,26839,27116,27209,27316,27659,27766,27995,28404,28636,28736,28841,28960,29583,29730,29849,30084,30499,30737,30849,30970,31103,33241,34757,38048,40182,41710,43254,46541,46787,47046,47850,49610,50060,50775,50848,50935,51020,51119,51314,51406,51579,51741,51836,52005,52248,52541,52950,53364,53824,54242,54483,54913,55348,55758,56180,56590,57047,57501,57917,58383,58565,58633,58977,59057,59413,59563,59707,59791,60156,60254,60362,60460,60570,60686,60812,60908,61285,61395,61519,61657,61767,61889,62017,62155,62317,62533,62689,62893,62977,63081,63175,63289,63401,63525,63621,63701,63890,64096,64289,64499,64931,65352,65777,65974,66922,67443,67566,68203,68424,69239,69708,69891,70487,70947,71052,72313,72463,72880,73045,73725,73884,74046,74201,74397,74564,74786,74946,75323,75482,75810,76027,76602,76952,77201,77298,78004,78442,78683,78872,79006,79197,79834,80084,80387,80602,3338,3636,3892,4389,4823,5823,7362,7710,9607,9929,10060,10766,10903,11055,11257,11962,12082,13231,13798,13927,14060,14220,14395,14537,16324,18162", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,344,345,346,351,352,356,362,366,367,368,369,380,381,382,386,392,396,397,398,399,429,449,495,525,545,565,611,615,619,633,674,682,692,693,694,695,696,699,700,703,706,707,710,714,719,727,735,744,752,756,764,772,780,788,796,805,814,822,831,834,836,841,843,848,852,856,857,862,863,864,865,866,867,869,870,875,876,877,878,879,880,881,883,887,891,895,899,900,901,902,903,904,905,906,907,910,914,917,921,929,936,945,949,964,972,975,984,989,1000,1008,1011,1020,1027,1028,1047,1050,1056,1059,1068,1071,1074,1077,1080,1083,1087,1090,1099,1102,1110,1115,1123,1128,1132,1133,1144,1151,1155,1159,1160,1164,1172,1176,1181,1186,1195,55,56,75,81,91,96,97,140,148,149,157,158,159,160,166,167,168,169,170,171,172,173,174,197,216,217", "endColumns": "55,59,60,54,52,57,47,48,44,52,57,59,57,45,59,52,45,49,46,57,57,58,59,61,61,61,61,61,61,60,61,61,52,61,73,62,67,80,63,65,69,69,69,69,66,62,64,65,52,75,65,86,75,53,78,77,72,64,62,65,70,70,61,68,65,66,66,55,50,52,51,53,70,62,58,61,58,72,66,59,62,74,71,70,55,70,56,56,65,63,70,56,52,62,51,57,66,65,65,80,74,55,52,60,57,49,48,48,48,61,51,44,80,53,52,53,50,48,50,60,60,61,49,40,49,47,61,50,48,68,60,55,70,64,68,50,62,69,68,69,61,69,69,74,58,57,61,44,42,46,44,50,47,65,61,62,71,56,56,59,57,69,56,144,120,103,86,151,151,147,80,77,300,165,154,101,10,92,106,10,106,10,10,10,99,104,118,10,146,118,10,10,10,111,120,132,10,10,10,10,10,10,10,10,10,10,10,10,10,72,86,84,98,10,91,10,10,94,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,83,10,97,107,97,109,115,10,95,10,109,123,137,109,121,127,10,10,10,10,10,83,103,93,113,111,123,95,79,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,104,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,96,10,10,10,10,133,10,10,10,10,10,10,297,255,90,243,367,1538,347,95,94,130,412,136,151,201,220,119,1148,566,128,132,159,174,141,125,457,209", "endOffsets": "151,211,272,327,380,438,486,535,580,633,691,751,809,855,915,968,1014,1064,1111,1169,1227,1286,1346,1408,1470,1532,1594,1656,1718,1779,1841,1903,1956,2018,2092,2155,2223,2304,2368,2434,2504,2574,2644,2714,2781,2844,2909,2975,3028,3104,3170,3257,3333,18421,18500,18578,18651,18716,18779,18845,18916,18987,19049,19118,19184,19251,19318,19374,19425,19478,19530,19584,19655,19718,19777,19839,19898,19971,20038,20098,20161,20236,20308,20379,20435,20506,20563,20620,20686,20750,20821,20878,20931,20994,21046,21104,21171,21237,21303,21384,21459,21515,21568,21629,21687,21737,21786,21835,21884,21946,21998,22043,22124,22178,22231,22285,22336,22385,22436,22497,22558,22620,22670,22711,22761,22809,22871,22922,22971,23040,23101,23157,23228,23293,23362,23413,23476,23546,23615,23685,23747,23817,23887,23962,24021,24079,24141,24186,24229,24276,24321,24372,24420,24486,24548,24611,24683,24740,24797,24857,24915,24985,25042,25187,25308,25412,25499,25651,25803,25951,26032,26110,26411,26577,26732,26834,27111,27204,27311,27654,27761,27990,28399,28631,28731,28836,28955,29578,29725,29844,30079,30494,30732,30844,30965,31098,33236,34752,38043,40177,41705,43249,46536,46782,47041,47845,49605,50055,50770,50843,50930,51015,51114,51309,51401,51574,51736,51831,52000,52243,52536,52945,53359,53819,54237,54478,54908,55343,55753,56175,56585,57042,57496,57912,58378,58560,58628,58972,59052,59408,59558,59702,59786,60151,60249,60357,60455,60565,60681,60807,60903,61280,61390,61514,61652,61762,61884,62012,62150,62312,62528,62684,62888,62972,63076,63170,63284,63396,63520,63616,63696,63885,64091,64284,64494,64926,65347,65772,65969,66917,67438,67561,68198,68419,69234,69703,69886,70482,70942,71047,72308,72458,72875,73040,73720,73879,74041,74196,74392,74559,74781,74941,75318,75477,75805,76022,76597,76947,77196,77293,77999,78437,78678,78867,79001,79192,79829,80079,80382,80597,81178,3631,3887,4384,4818,5475,7357,7705,9602,9924,10055,10761,10898,11050,11252,11957,12077,13226,13793,13922,14055,14215,14390,14532,16319,18157,18367"}, "to": {"startLines": "16,17,18,19,22,23,24,25,35,37,148,149,150,151,152,153,155,156,157,159,160,161,162,163,164,165,166,167,168,169,170,171,172,177,205,206,207,208,209,210,211,212,213,214,215,278,279,280,281,282,283,284,285,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522,523,524,525,526,527,528,529,530,531,532,533,534,535,787,788,789,790,791,792,793,794,795,796,797,798,799,800,801,802,803,804,805,806,807,808,809,810,811,812,813,814,815,816,817,818,819,820,821,822,823,824,825,826,827,828,829,830,831,832,833,834,880,881,888,889,897,898,899,916,919,920,923,924,925,927,928,929,930,931,974,990,991,992,994,995,997,1049,1055,1056,1057,1058,1059,1267,1534,1535,1536,1541,1542,1546,1552,1556,1557,1558,1559,1570,1571,1572,1576,1582,1586,1656,1657,1658,1688,1708,1754,1784,1804,1824,1870,1874,2614,2628,2669,2677,2946,2947,2948,2949,3114,3117,3118,3121,3124,3125,3128,3132,3137,3145,3153,3162,3170,3174,3182,3190,3198,3206,3214,3223,3232,3240,3249,3287,3289,3294,3296,3301,3305,3322,3323,3328,3329,3330,3331,3332,3333,3335,3336,3341,3342,3343,3344,3345,3346,3347,3349,3353,3357,3361,3385,3386,3387,3388,3389,3390,3391,3392,3393,3396,3400,3403,3509,3517,3524,3533,3537,3552,3560,3563,3572,3577,3588,3596,3599,3608,3615,3616,3635,3638,3644,3647,3656,3659,3662,3665,3668,3671,3675,3678,3687,3690,3698,3703,3711,3716,3720,3721,3732,3739,3743,3747,3748,3752,3760,3764,3769,3774,4261,4262,4263,4740,4746,4792,4793,4794,4837,4971,4990,4998,4999,5052,5129,5135,5136,5302,5375,5376,5415,5416,5515,5516,5554,5573", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "774,830,890,951,1106,1159,1217,1265,1716,1806,7021,7079,7139,7197,7243,7303,7415,7461,7511,7599,7657,7715,7774,7834,7896,7958,8020,8082,8144,8206,8267,8329,8391,8661,10645,10719,10782,10850,10931,10995,11061,11131,11201,11271,11341,15180,15243,15308,15374,15427,15503,15569,15656,28618,28672,28751,28829,28902,28967,29030,29096,29167,29238,29300,29369,29435,29502,29569,29625,29676,29729,29781,29835,29906,29969,30028,30090,30149,30222,30289,30349,30412,30487,30559,30630,30686,30757,30814,30871,30937,31001,31072,31129,31182,31245,31297,31355,48367,48433,48499,48580,48655,48711,48764,48825,48883,48933,48982,49031,49080,49142,49194,49239,49320,49374,49427,49481,49532,49581,49632,49693,49754,49816,49866,49907,49957,50005,50067,50118,50167,50236,50297,50353,50424,50489,50558,50609,50672,50742,50811,50881,50943,51013,51083,51158,53844,53902,54250,54295,54658,54705,54750,55695,55869,55935,56114,56177,56249,56362,56419,56479,56537,56607,59591,60792,60913,61017,61147,61299,61565,64893,65216,65294,65595,65761,65916,80958,98334,98427,98534,98877,98984,99213,99622,99854,99954,100059,100178,100776,100923,101042,101277,101692,101930,107145,107266,107399,109478,110974,114208,116283,117791,119315,122545,122769,167976,168780,170540,170990,187659,187732,187819,187904,200356,200551,200643,200816,200978,201073,201242,201485,201778,202187,202601,203033,203451,203692,204122,204557,204967,205389,205799,206228,206654,207070,207508,210274,210342,210686,210766,211122,211272,212207,212291,212656,212754,212862,212960,213070,213186,213312,213408,213785,213895,214019,214157,214267,214389,214517,214655,214817,215033,215189,216611,216695,216799,216893,217007,217119,217243,217339,217419,217608,217814,218007,226298,226730,227151,227576,227773,228721,229242,229365,230002,230223,231038,231507,231690,232286,232746,232851,234112,234262,234679,234844,235524,235683,235774,235858,236054,236221,236443,236603,236980,237139,237467,237684,238259,238609,238858,238955,239661,240099,240340,240529,240663,240854,241491,241741,242044,242259,263627,263925,264181,279245,279679,281738,283277,283625,285522,298047,298750,299456,299593,302080,304452,305157,305277,311689,315447,315576,316914,317074,321125,321267,323655,325493", "endLines": "16,17,18,19,22,23,24,25,35,37,148,149,150,151,152,153,155,156,157,159,160,161,162,163,164,165,166,167,168,169,170,171,172,177,205,206,207,208,209,210,211,212,213,214,215,278,279,280,281,282,283,284,285,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522,523,524,525,526,527,528,529,530,531,532,533,534,535,787,788,789,790,791,792,793,794,795,796,797,798,799,800,801,802,803,804,805,806,807,808,809,810,811,812,813,814,815,816,817,818,819,820,821,822,823,824,825,826,827,828,829,830,831,832,833,834,880,881,888,889,897,898,899,916,919,920,923,924,925,927,928,929,930,931,974,990,991,992,994,995,997,1049,1055,1056,1057,1058,1059,1270,1534,1535,1540,1541,1545,1551,1555,1556,1557,1558,1569,1570,1571,1575,1581,1585,1586,1656,1657,1687,1707,1753,1783,1803,1823,1869,1873,1877,2627,2668,2676,2686,2946,2947,2948,2949,3116,3117,3120,3123,3124,3127,3131,3136,3144,3152,3161,3169,3173,3181,3189,3197,3205,3213,3222,3231,3239,3248,3251,3288,3293,3295,3300,3304,3308,3322,3327,3328,3329,3330,3331,3332,3334,3335,3340,3341,3342,3343,3344,3345,3346,3348,3352,3356,3360,3364,3385,3386,3387,3388,3389,3390,3391,3392,3395,3399,3402,3406,3516,3523,3532,3536,3551,3559,3562,3571,3576,3587,3595,3598,3607,3614,3615,3634,3637,3643,3646,3655,3658,3661,3664,3667,3670,3674,3677,3686,3689,3697,3702,3710,3715,3719,3720,3731,3738,3742,3746,3747,3751,3759,3763,3768,3773,3781,4261,4262,4281,4745,4755,4792,4793,4836,4844,4971,4997,4998,4999,5052,5134,5135,5136,5302,5375,5376,5415,5416,5515,5538,5572,5573", "endColumns": "55,59,60,54,52,57,47,48,44,52,57,59,57,45,59,52,45,49,46,57,57,58,59,61,61,61,61,61,61,60,61,61,52,61,73,62,67,80,63,65,69,69,69,69,66,62,64,65,52,75,65,86,75,53,78,77,72,64,62,65,70,70,61,68,65,66,66,55,50,52,51,53,70,62,58,61,58,72,66,59,62,74,71,70,55,70,56,56,65,63,70,56,52,62,51,57,66,65,65,80,74,55,52,60,57,49,48,48,48,61,51,44,80,53,52,53,50,48,50,60,60,61,49,40,49,47,61,50,48,68,60,55,70,64,68,50,62,69,68,69,61,69,69,74,58,57,61,44,42,46,44,50,47,65,61,62,71,56,56,59,57,69,56,144,120,103,86,151,151,147,80,77,300,165,154,101,10,92,106,10,106,10,10,10,99,104,118,10,146,118,10,10,10,111,120,132,10,10,10,10,10,10,10,10,10,10,10,10,10,72,86,84,98,10,91,10,10,94,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,83,10,97,107,97,109,115,10,95,10,109,123,137,109,121,127,10,10,10,10,10,83,103,93,113,111,123,95,79,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,104,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,96,10,10,10,10,133,10,10,10,10,10,10,297,255,90,243,367,1538,347,95,94,130,412,136,151,201,220,119,1148,566,128,132,159,174,141,125,457,209", "endOffsets": "825,885,946,1001,1154,1212,1260,1309,1756,1854,7074,7134,7192,7238,7298,7351,7456,7506,7553,7652,7710,7769,7829,7891,7953,8015,8077,8139,8201,8262,8324,8386,8439,8718,10714,10777,10845,10926,10990,11056,11126,11196,11266,11336,11403,15238,15303,15369,15422,15498,15564,15651,15727,28667,28746,28824,28897,28962,29025,29091,29162,29233,29295,29364,29430,29497,29564,29620,29671,29724,29776,29830,29901,29964,30023,30085,30144,30217,30284,30344,30407,30482,30554,30625,30681,30752,30809,30866,30932,30996,31067,31124,31177,31240,31292,31350,31417,48428,48494,48575,48650,48706,48759,48820,48878,48928,48977,49026,49075,49137,49189,49234,49315,49369,49422,49476,49527,49576,49627,49688,49749,49811,49861,49902,49952,50000,50062,50113,50162,50231,50292,50348,50419,50484,50553,50604,50667,50737,50806,50876,50938,51008,51078,51153,51212,53897,53959,54290,54333,54700,54745,54796,55738,55930,55992,56172,56244,56301,56414,56474,56532,56602,56659,59731,60908,61012,61099,61294,61446,61708,64969,65289,65590,65756,65911,66013,81230,98422,98529,98872,98979,99208,99617,99849,99949,100054,100173,100771,100918,101037,101272,101687,101925,102037,107261,107394,109473,110969,114203,116278,117786,119310,122540,122764,123001,168775,170535,170985,171700,187727,187814,187899,187998,200546,200638,200811,200973,201068,201237,201480,201773,202182,202596,203028,203446,203687,204117,204552,204962,205384,205794,206223,206649,207065,207503,207685,210337,210681,210761,211117,211267,211411,212286,212651,212749,212857,212955,213065,213181,213307,213403,213780,213890,214014,214152,214262,214384,214512,214650,214812,215028,215184,215388,216690,216794,216888,217002,217114,217238,217334,217414,217603,217809,218002,218212,226725,227146,227571,227768,228716,229237,229360,229997,230218,231033,231502,231685,232281,232741,232846,234107,234257,234674,234839,235519,235678,235769,235853,236049,236216,236438,236598,236975,237134,237462,237679,238254,238604,238853,238950,239656,240094,240335,240524,240658,240849,241486,241736,242039,242254,242730,263920,264176,264673,279674,280331,283272,283620,285517,285839,298173,299451,299588,299740,302277,305152,305272,306421,312251,315571,315704,317069,317244,321262,323049,325488,325698"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\077c69690200a4c0c0ddf0630ba2f6e7\\transformed\\open_ad_sdk_6.8.4.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,276,280,284,287,294,298,302,306,309,313,317,324,328,332,336,340,344,348,351,361,364,370,374,384,390,404,410,411,412,418,424,433,445,451,459,466,470,478,482,494,495", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,213,268,321,379,445,503,561,617,709,787,841,896,951,1005,1059,1108,1171,1230,1275,1330,1393,1460,1522,1573,1626,1697,1771,1830,1889,1940,1984,2032,2086,2147,2199,2260,2309,2370,2428,2476,2544,2601,2658,2708,2758,2806,2857,2908,2971,3034,3082,3145,3223,3271,3327,3382,3443,3504,3550,3599,3663,3727,3790,3841,3897,3948,4011,4087,4145,4214,4268,4340,4397,4440,4501,4560,4608,4664,4739,4815,4887,4954,5015,5076,5138,5201,5267,5313,5362,5438,5509,5583,5657,5726,5782,5882,5931,5994,6037,6074,6120,6169,6224,6285,6358,6430,6499,6625,6730,6809,6903,6999,7094,7177,7238,7310,7367,7440,7512,7606,7708,7807,7897,7990,8072,8152,8229,8307,8389,8468,8568,8658,8751,8828,8921,9033,9131,9220,9310,9399,9471,9543,9623,9703,9819,9896,9994,10085,10137,10375,10457,10646,10731,10788,10844,10885,10933,10982,11033,11093,11141,11204,11268,11327,11399,11472,11540,11613,11694,11751,11809,11867,11930,11979,12024,12089,12161,12238,12314,12366,12409,12467,12520,12564,12611,12654,12702,12756,12808,12860,12909,12969,13023,13081,13136,13178,13220,13297,13433,13543,13667,13775,13878,13935,13988,14060,14149,14232,14323,14377,14443,14504,14569,14624,14681,14759,14821,14875,14925,14978,15039,15078,15157,15230,15311,15377,15443,15493,15543,15612,15681,15765,15832,15889,15945,16007,16069,16129,16180,16237,16296,16398,16478,16536,16623,16683,16777,16828,16904,16963,17060,17131,17227,17293,17360,17398,17441,17490,17540,17592,17648,17701,17755,17810,17869,17930,17991,18043,18089,18313,18540,18808,18972,19372,19586,19804,20006,20158,20373,20573,20918,21141,21328,21562,21770,21994,22257,22433,23048,23187,23553,23766,24258,24594,25443,25844,25960,26083,26471,26867,27328,27844,28130,28604,29117,29376,29841,30081,30600,30669", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,275,279,283,286,293,297,301,305,308,312,316,323,327,331,335,339,343,347,350,360,363,369,373,383,389,403,409,410,411,417,423,432,444,450,458,465,469,477,481,493,494,503", "endColumns": "62,54,52,57,65,57,57,55,91,77,53,54,54,53,53,48,62,58,44,54,62,66,61,50,52,70,73,58,58,50,43,47,53,60,51,60,48,60,57,47,67,56,56,49,49,47,50,50,62,62,47,62,77,47,55,54,60,60,45,48,63,63,62,50,55,50,62,75,57,68,53,71,56,42,60,58,47,55,74,75,71,66,60,60,61,62,65,45,48,75,70,73,73,68,55,45,48,62,42,36,45,48,54,60,72,71,68,125,104,78,93,95,94,82,60,71,56,72,71,93,101,98,89,92,81,79,76,77,81,78,99,89,92,76,92,111,97,88,89,88,71,71,79,79,115,76,97,90,51,237,81,188,84,56,55,40,47,48,50,59,47,62,63,58,71,72,67,72,80,56,57,57,62,48,44,64,71,76,75,51,42,57,52,43,46,42,47,53,51,51,48,59,53,57,54,41,41,76,135,109,123,107,102,56,52,71,88,82,90,53,65,60,64,54,56,77,61,53,49,52,60,38,78,72,80,65,65,49,49,68,68,83,66,56,55,61,61,59,50,56,58,101,79,57,86,59,93,50,75,58,96,70,95,65,66,37,42,48,49,51,55,52,53,54,58,60,60,51,45,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,115,122,12,12,12,12,12,12,12,12,12,12,12,68,12", "endOffsets": "208,263,316,374,440,498,556,612,704,782,836,891,946,1000,1054,1103,1166,1225,1270,1325,1388,1455,1517,1568,1621,1692,1766,1825,1884,1935,1979,2027,2081,2142,2194,2255,2304,2365,2423,2471,2539,2596,2653,2703,2753,2801,2852,2903,2966,3029,3077,3140,3218,3266,3322,3377,3438,3499,3545,3594,3658,3722,3785,3836,3892,3943,4006,4082,4140,4209,4263,4335,4392,4435,4496,4555,4603,4659,4734,4810,4882,4949,5010,5071,5133,5196,5262,5308,5357,5433,5504,5578,5652,5721,5777,5823,5926,5989,6032,6069,6115,6164,6219,6280,6353,6425,6494,6620,6725,6804,6898,6994,7089,7172,7233,7305,7362,7435,7507,7601,7703,7802,7892,7985,8067,8147,8224,8302,8384,8463,8563,8653,8746,8823,8916,9028,9126,9215,9305,9394,9466,9538,9618,9698,9814,9891,9989,10080,10132,10370,10452,10641,10726,10783,10839,10880,10928,10977,11028,11088,11136,11199,11263,11322,11394,11467,11535,11608,11689,11746,11804,11862,11925,11974,12019,12084,12156,12233,12309,12361,12404,12462,12515,12559,12606,12649,12697,12751,12803,12855,12904,12964,13018,13076,13131,13173,13215,13292,13428,13538,13662,13770,13873,13930,13983,14055,14144,14227,14318,14372,14438,14499,14564,14619,14676,14754,14816,14870,14920,14973,15034,15073,15152,15225,15306,15372,15438,15488,15538,15607,15676,15760,15827,15884,15940,16002,16064,16124,16175,16232,16291,16393,16473,16531,16618,16678,16772,16823,16899,16958,17055,17126,17222,17288,17355,17393,17436,17485,17535,17587,17643,17696,17750,17805,17864,17925,17986,18038,18084,18308,18535,18803,18967,19367,19581,19799,20001,20153,20368,20568,20913,21136,21323,21557,21765,21989,22252,22428,23043,23182,23548,23761,24253,24589,25438,25839,25955,26078,26466,26862,27323,27839,28125,28599,29112,29371,29836,30076,30595,30664,31219"}, "to": {"startLines": "220,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,538,539,540,548,549,550,862,863,864,865,866,875,906,907,908,909,910,911,912,913,914,1047,1048,1086,1087,1088,1089,1090,1091,1092,1093,1094,1095,1096,1097,1098,1099,1100,1101,1102,1103,1104,1105,1106,1107,1108,1109,1110,1111,1112,1113,1114,1115,1116,1117,1118,1119,1120,1121,1122,1123,1124,1125,1126,1127,1128,1129,1130,1131,1132,1133,1134,1135,1136,1137,1138,1139,1140,1141,1142,1143,1144,1145,1146,1147,1148,1149,1150,1151,1152,1153,1154,1155,1156,1157,1158,1159,1160,1161,1162,1163,1164,1165,1166,1167,1168,1169,1170,1171,1172,1173,1174,1175,1176,1177,1178,1179,1180,1181,1182,1183,1184,1185,1186,1187,1188,1189,1190,1191,1192,1193,1194,1195,1196,1197,1198,1199,1200,1201,1202,1203,1204,1205,1206,1207,1208,1209,1210,1211,1212,1213,1214,1215,1216,1217,1218,1219,1220,1221,1222,1223,1224,1225,1226,1227,1228,1229,1230,1231,1232,1233,1234,1235,1236,1237,1238,1239,1240,1241,1242,1243,1244,1245,1246,1247,1248,1249,1250,1251,1252,1253,1254,1255,1256,1260,2727,2731,2743,2755,2762,2766,2770,2774,2777,2781,2785,2792,2796,2971,3030,3034,3038,3309,3312,3803,3806,3812,3852,3930,3936,3950,3956,3957,3958,3964,3970,3979,3991,3997,4005,4012,4016,4024,4028,4040,4041", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "11688,18416,18471,18524,18582,18648,18706,18764,18820,18912,18990,19044,19099,19154,19208,19262,19311,19374,19433,19478,19533,19596,19663,19725,19776,19829,19900,19974,20033,20092,20143,20187,20235,20289,20350,20402,20463,20512,20573,20631,20679,20747,20804,20861,20911,20961,21009,21060,21111,21174,21237,21285,21348,21426,21474,21530,21585,21646,21707,21753,21802,21866,21930,21993,22044,22100,22151,22214,22290,22348,22417,22471,22543,22600,22643,31595,31654,31702,32369,32444,32520,52860,52927,52988,53049,53111,53601,55102,55148,55197,55273,55344,55418,55492,55561,55617,64781,64830,68152,68195,68232,68278,68327,68382,68443,68516,68588,68657,68783,68888,68967,69061,69157,69252,69335,69396,69468,69525,69598,69670,69764,69866,69965,70055,70148,70230,70310,70387,70465,70547,70626,70726,70816,70909,70986,71079,71191,71289,71378,71468,71557,71629,71701,71781,71861,71977,72054,72152,72243,72295,72533,72615,72804,72889,72946,73002,73043,73091,73140,73191,73251,73299,73362,73426,73485,73557,73630,73698,73771,73852,73909,73967,74025,74088,74137,74182,74247,74319,74396,74472,74524,74567,74625,74678,74722,74769,74812,74860,74914,74966,75018,75067,75127,75181,75239,75294,75336,75378,75455,75591,75701,75825,75933,76036,76093,76146,76218,76307,76390,76481,76535,76601,76662,76727,76782,76839,76917,76979,77033,77083,77136,77197,77236,77315,77388,77469,77535,77601,77651,77701,77770,77839,77923,77990,78047,78103,78165,78227,78287,78338,78395,78454,78556,78636,78694,78781,78841,78935,78986,79062,79121,79218,79289,79385,79451,79518,79556,79599,79648,79698,79750,79806,79859,79913,79968,80027,80088,80149,80201,80460,174027,174254,174840,175394,175794,176008,176226,176428,176580,176795,176995,177340,177563,189318,192807,193015,193239,211416,211592,243915,244054,244305,246259,249282,249618,250467,250868,250984,251107,251495,251891,252283,252722,253008,253482,253957,254216,254681,254921,255363,255432", "endLines": "220,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,538,539,540,548,549,550,862,863,864,865,866,875,906,907,908,909,910,911,912,913,914,1047,1048,1086,1087,1088,1089,1090,1091,1092,1093,1094,1095,1096,1097,1098,1099,1100,1101,1102,1103,1104,1105,1106,1107,1108,1109,1110,1111,1112,1113,1114,1115,1116,1117,1118,1119,1120,1121,1122,1123,1124,1125,1126,1127,1128,1129,1130,1131,1132,1133,1134,1135,1136,1137,1138,1139,1140,1141,1142,1143,1144,1145,1146,1147,1148,1149,1150,1151,1152,1153,1154,1155,1156,1157,1158,1159,1160,1161,1162,1163,1164,1165,1166,1167,1168,1169,1170,1171,1172,1173,1174,1175,1176,1177,1178,1179,1180,1181,1182,1183,1184,1185,1186,1187,1188,1189,1190,1191,1192,1193,1194,1195,1196,1197,1198,1199,1200,1201,1202,1203,1204,1205,1206,1207,1208,1209,1210,1211,1212,1213,1214,1215,1216,1217,1218,1219,1220,1221,1222,1223,1224,1225,1226,1227,1228,1229,1230,1231,1232,1233,1234,1235,1236,1237,1238,1239,1240,1241,1242,1243,1244,1245,1246,1247,1248,1249,1250,1251,1252,1253,1254,1255,1256,1263,2730,2734,2745,2761,2765,2769,2773,2776,2780,2784,2791,2795,2799,2974,3033,3037,3041,3311,3321,3805,3811,3815,3861,3935,3949,3955,3956,3957,3963,3969,3978,3990,3996,4004,4011,4015,4023,4027,4039,4040,4049", "endColumns": "62,54,52,57,65,57,57,55,91,77,53,54,54,53,53,48,62,58,44,54,62,66,61,50,52,70,73,58,58,50,43,47,53,60,51,60,48,60,57,47,67,56,56,49,49,47,50,50,62,62,47,62,77,47,55,54,60,60,45,48,63,63,62,50,55,50,62,75,57,68,53,71,56,42,60,58,47,55,74,75,71,66,60,60,61,62,65,45,48,75,70,73,73,68,55,45,48,62,42,36,45,48,54,60,72,71,68,125,104,78,93,95,94,82,60,71,56,72,71,93,101,98,89,92,81,79,76,77,81,78,99,89,92,76,92,111,97,88,89,88,71,71,79,79,115,76,97,90,51,237,81,188,84,56,55,40,47,48,50,59,47,62,63,58,71,72,67,72,80,56,57,57,62,48,44,64,71,76,75,51,42,57,52,43,46,42,47,53,51,51,48,59,53,57,54,41,41,76,135,109,123,107,102,56,52,71,88,82,90,53,65,60,64,54,56,77,61,53,49,52,60,38,78,72,80,65,65,49,49,68,68,83,66,56,55,61,61,59,50,56,58,101,79,57,86,59,93,50,75,58,96,70,95,65,66,37,42,48,49,51,55,52,53,54,58,60,60,51,45,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,115,122,12,12,12,12,12,12,12,12,12,12,12,68,12", "endOffsets": "11746,18466,18519,18577,18643,18701,18759,18815,18907,18985,19039,19094,19149,19203,19257,19306,19369,19428,19473,19528,19591,19658,19720,19771,19824,19895,19969,20028,20087,20138,20182,20230,20284,20345,20397,20458,20507,20568,20626,20674,20742,20799,20856,20906,20956,21004,21055,21106,21169,21232,21280,21343,21421,21469,21525,21580,21641,21702,21748,21797,21861,21925,21988,22039,22095,22146,22209,22285,22343,22412,22466,22538,22595,22638,22699,31649,31697,31753,32439,32515,32587,52922,52983,53044,53106,53169,53662,55143,55192,55268,55339,55413,55487,55556,55612,55658,64825,64888,68190,68227,68273,68322,68377,68438,68511,68583,68652,68778,68883,68962,69056,69152,69247,69330,69391,69463,69520,69593,69665,69759,69861,69960,70050,70143,70225,70305,70382,70460,70542,70621,70721,70811,70904,70981,71074,71186,71284,71373,71463,71552,71624,71696,71776,71856,71972,72049,72147,72238,72290,72528,72610,72799,72884,72941,72997,73038,73086,73135,73186,73246,73294,73357,73421,73480,73552,73625,73693,73766,73847,73904,73962,74020,74083,74132,74177,74242,74314,74391,74467,74519,74562,74620,74673,74717,74764,74807,74855,74909,74961,75013,75062,75122,75176,75234,75289,75331,75373,75450,75586,75696,75820,75928,76031,76088,76141,76213,76302,76385,76476,76530,76596,76657,76722,76777,76834,76912,76974,77028,77078,77131,77192,77231,77310,77383,77464,77530,77596,77646,77696,77765,77834,77918,77985,78042,78098,78160,78222,78282,78333,78390,78449,78551,78631,78689,78776,78836,78930,78981,79057,79116,79213,79284,79380,79446,79513,79551,79594,79643,79693,79745,79801,79854,79908,79963,80022,80083,80144,80196,80242,80679,174249,174517,174999,175789,176003,176221,176423,176575,176790,176990,177335,177558,177745,189547,193010,193234,193497,211587,212202,244049,244300,244513,246701,249613,250462,250863,250979,251102,251490,251886,252278,252717,253003,253477,253952,254211,254676,254916,255358,255427,255982"}}]}, {"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\values-zh_values-zh.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\077c69690200a4c0c0ddf0630ba2f6e7\\transformed\\open_ad_sdk_6.8.4.0\\res\\values-zh\\values-zh.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,113,155,197,247,295,361,425,494,554,617,680,740,816,878", "endColumns": "57,41,41,49,47,65,63,68,59,62,62,59,75,61,60", "endOffsets": "108,150,192,242,290,356,420,489,549,612,675,735,811,873,934"}, "to": {"startLines": "13,14,15,16,17,18,19,20,21,22,23,24,25,26,27", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "615,673,715,757,807,855,921,985,1054,1114,1177,1240,1300,1376,1438", "endColumns": "57,41,41,49,47,65,63,68,59,62,62,59,75,61,60", "endOffsets": "668,710,752,802,850,916,980,1049,1109,1172,1235,1295,1371,1433,1494"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b66139be6585a43ae23903a8750be796\\transformed\\wind-sdk-4.23.0\\res\\values-zh\\values-zh.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12", "startColumns": "4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,93,133,174,227,299,348,405,456,511,563", "endColumns": "37,39,40,52,71,48,56,50,54,51,51", "endOffsets": "88,128,169,222,294,343,400,451,506,558,610"}}]}, {"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\values-ro_values-ro.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a3ae461f31008f99a87e29f3c99a3c83\\transformed\\appcompat-v7-28.0.0\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,226,330,443,531,643,764,849,930,1021,1114,1210,1304,1404,1497,1592,1687,1778,1870,1953,2065,2178,2278,2392,2497,2603,2767,2870", "endColumns": "120,103,112,87,111,120,84,80,90,92,95,93,99,92,94,94,90,91,82,111,112,99,113,104,105,163,102,82", "endOffsets": "221,325,438,526,638,759,844,925,1016,1109,1205,1299,1399,1492,1587,1682,1773,1865,1948,2060,2173,2273,2387,2492,2598,2762,2865,2948"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\daefd50eed0506e326ae247974906cd2\\transformed\\support-compat-28.0.0\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2953", "endColumns": "100", "endOffsets": "3049"}}]}, {"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\values-pa_values-pa.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\daefd50eed0506e326ae247974906cd2\\transformed\\support-compat-28.0.0\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2839", "endColumns": "100", "endOffsets": "2935"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a3ae461f31008f99a87e29f3c99a3c83\\transformed\\appcompat-v7-28.0.0\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,305,410,496,596,709,786,862,953,1046,1142,1236,1337,1430,1525,1619,1710,1801,1880,1981,2085,2182,2291,2390,2500,2659,2759", "endColumns": "102,96,104,85,99,112,76,75,90,92,95,93,100,92,94,93,90,90,78,100,103,96,108,98,109,158,99,79", "endOffsets": "203,300,405,491,591,704,781,857,948,1041,1137,1231,1332,1425,1520,1614,1705,1796,1875,1976,2080,2177,2286,2385,2495,2654,2754,2834"}}]}, {"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\values-ne_values-ne.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\daefd50eed0506e326ae247974906cd2\\transformed\\support-compat-28.0.0\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2988", "endColumns": "100", "endOffsets": "3084"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a3ae461f31008f99a87e29f3c99a3c83\\transformed\\appcompat-v7-28.0.0\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,314,422,513,620,747,841,931,1022,1115,1211,1305,1406,1499,1594,1688,1779,1870,1958,2068,2184,2287,2402,2504,2619,2790,2902", "endColumns": "104,103,107,90,106,126,93,89,90,92,95,93,100,92,94,93,90,90,87,109,115,102,114,101,114,170,111,85", "endOffsets": "205,309,417,508,615,742,836,926,1017,1110,1206,1300,1401,1494,1589,1683,1774,1865,1953,2063,2179,2282,2397,2499,2614,2785,2897,2983"}}]}, {"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\values-night-v8_values-night-v8.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a3ae461f31008f99a87e29f3c99a3c83\\transformed\\appcompat-v7-28.0.0\\res\\values-night-v8\\values-night-v8.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,125,209,293,389,491,593", "endColumns": "69,83,83,95,101,101,93", "endOffsets": "120,204,288,384,486,588,682"}}]}, {"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\values-pt_values-pt.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\daefd50eed0506e326ae247974906cd2\\transformed\\support-compat-28.0.0\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2929", "endColumns": "100", "endOffsets": "3025"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a3ae461f31008f99a87e29f3c99a3c83\\transformed\\appcompat-v7-28.0.0\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,331,438,527,628,746,829,909,1000,1093,1189,1283,1384,1477,1572,1667,1758,1849,1936,2043,2155,2257,2365,2472,2582,2744,2844", "endColumns": "119,105,106,88,100,117,82,79,90,92,95,93,100,92,94,94,90,90,86,106,111,101,107,106,109,161,99,84", "endOffsets": "220,326,433,522,623,741,824,904,995,1088,1184,1278,1379,1472,1567,1662,1753,1844,1931,2038,2150,2252,2360,2467,2577,2739,2839,2924"}}]}, {"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\values-sw_values-sw.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a3ae461f31008f99a87e29f3c99a3c83\\transformed\\appcompat-v7-28.0.0\\res\\values-sw\\values-sw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,306,414,504,609,726,808,891,982,1075,1169,1263,1364,1457,1552,1647,1738,1830,1912,2013,2122,2221,2328,2437,2542,2704,2801", "endColumns": "102,97,107,89,104,116,81,82,90,92,93,93,100,92,94,94,90,91,81,100,108,98,106,108,104,161,96,81", "endOffsets": "203,301,409,499,604,721,803,886,977,1070,1164,1258,1359,1452,1547,1642,1733,1825,1907,2008,2117,2216,2323,2432,2537,2699,2796,2878"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\daefd50eed0506e326ae247974906cd2\\transformed\\support-compat-28.0.0\\res\\values-sw\\values-sw.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2883", "endColumns": "100", "endOffsets": "2979"}}]}, {"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\values-zh-rCN_values-zh-rCN.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\daefd50eed0506e326ae247974906cd2\\transformed\\support-compat-28.0.0\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2744", "endColumns": "100", "endOffsets": "2840"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a3ae461f31008f99a87e29f3c99a3c83\\transformed\\appcompat-v7-28.0.0\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,201,296,396,478,575,681,757,833,924,1017,1115,1211,1306,1399,1494,1586,1677,1768,1846,1942,2038,2133,2230,2325,2423,2572,2666", "endColumns": "95,94,99,81,96,105,75,75,90,92,97,95,94,92,94,91,90,90,77,95,95,94,96,94,97,148,93,77", "endOffsets": "196,291,391,473,570,676,752,828,919,1012,1110,1206,1301,1394,1489,1581,1672,1763,1841,1937,2033,2128,2225,2320,2418,2567,2661,2739"}}]}, {"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\values-hi_values-hi.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a3ae461f31008f99a87e29f3c99a3c83\\transformed\\appcompat-v7-28.0.0\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,211,308,418,504,606,728,805,883,974,1067,1163,1257,1358,1451,1546,1640,1731,1822,1912,2021,2124,2226,2336,2437,2549,2711,2812", "endColumns": "105,96,109,85,101,121,76,77,90,92,95,93,100,92,94,93,90,90,89,108,102,101,109,100,111,161,100,79", "endOffsets": "206,303,413,499,601,723,800,878,969,1062,1158,1252,1353,1446,1541,1635,1726,1817,1907,2016,2119,2221,2331,2432,2544,2706,2807,2887"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\daefd50eed0506e326ae247974906cd2\\transformed\\support-compat-28.0.0\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2892", "endColumns": "100", "endOffsets": "2988"}}]}, {"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\values-fi_values-fi.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a3ae461f31008f99a87e29f3c99a3c83\\transformed\\appcompat-v7-28.0.0\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,313,414,500,605,723,810,892,983,1076,1172,1266,1361,1454,1550,1649,1740,1834,1914,2021,2124,2221,2327,2426,2530,2693,2792", "endColumns": "107,99,100,85,104,117,86,81,90,92,95,93,94,92,95,98,90,93,79,106,102,96,105,98,103,162,98,79", "endOffsets": "208,308,409,495,600,718,805,887,978,1071,1167,1261,1356,1449,1545,1644,1735,1829,1909,2016,2119,2216,2322,2421,2525,2688,2787,2867"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\daefd50eed0506e326ae247974906cd2\\transformed\\support-compat-28.0.0\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2872", "endColumns": "100", "endOffsets": "2968"}}]}, {"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\values-ca_values-ca.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a3ae461f31008f99a87e29f3c99a3c83\\transformed\\appcompat-v7-28.0.0\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,223,329,436,519,627,753,837,918,1009,1102,1196,1291,1390,1483,1576,1670,1761,1852,1935,2046,2155,2253,2363,2467,2575,2735,2834", "endColumns": "117,105,106,82,107,125,83,80,90,92,93,94,98,92,92,93,90,90,82,110,108,97,109,103,107,159,98,80", "endOffsets": "218,324,431,514,622,748,832,913,1004,1097,1191,1286,1385,1478,1571,1665,1756,1847,1930,2041,2150,2248,2358,2462,2570,2730,2829,2910"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\daefd50eed0506e326ae247974906cd2\\transformed\\support-compat-28.0.0\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2915", "endColumns": "100", "endOffsets": "3011"}}]}, {"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\values-h720dp-v13_values-h720dp-v13.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a3ae461f31008f99a87e29f3c99a3c83\\transformed\\appcompat-v7-28.0.0\\res\\values-h720dp-v13\\values-h720dp-v13.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "66", "endOffsets": "117"}}]}, {"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\values-gu_values-gu.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\daefd50eed0506e326ae247974906cd2\\transformed\\support-compat-28.0.0\\res\\values-gu\\values-gu.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2882", "endColumns": "100", "endOffsets": "2978"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a3ae461f31008f99a87e29f3c99a3c83\\transformed\\appcompat-v7-28.0.0\\res\\values-gu\\values-gu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,318,425,512,613,736,813,891,982,1075,1174,1268,1369,1462,1557,1654,1745,1836,1916,2022,2124,2221,2330,2429,2539,2699,2802", "endColumns": "108,103,106,86,100,122,76,77,90,92,98,93,100,92,94,96,90,90,79,105,101,96,108,98,109,159,102,79", "endOffsets": "209,313,420,507,608,731,808,886,977,1070,1169,1263,1364,1457,1552,1649,1740,1831,1911,2017,2119,2216,2325,2424,2534,2694,2797,2877"}}]}]}