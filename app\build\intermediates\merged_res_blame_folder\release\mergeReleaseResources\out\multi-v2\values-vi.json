{"logs": [{"outputFile": "com.ainative.mountainsurvival.app-mergeReleaseResources-22:/values-vi/values-vi.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a3ae461f31008f99a87e29f3c99a3c83\\transformed\\appcompat-v7-28.0.0\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,327,436,520,623,742,819,896,987,1080,1176,1270,1371,1464,1559,1657,1748,1839,1923,2027,2136,2237,2342,2456,2561,2718,2817", "endColumns": "113,107,108,83,102,118,76,76,90,92,95,93,100,92,94,97,90,90,83,103,108,100,104,113,104,156,98,83", "endOffsets": "214,322,431,515,618,737,814,891,982,1075,1171,1265,1366,1459,1554,1652,1743,1834,1918,2022,2131,2232,2337,2451,2556,2713,2812,2896"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\daefd50eed0506e326ae247974906cd2\\transformed\\support-compat-28.0.0\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2901", "endColumns": "100", "endOffsets": "2997"}}]}]}