package com.ainative.mountainsurvival

import android.content.Intent
import android.media.MediaPlayer
import android.os.Bundle
import android.util.Log
import android.support.v7.app.AlertDialog
import android.support.v7.app.AppCompatActivity
import com.ainative.mountainsurvival.databinding.ActivityStartBinding

/**
 * 游戏开始界面
 * 显示游戏标题、描述和开始/退出按钮
 */
class StartActivity : AppCompatActivity() {

    companion object {
        private const val TAG = "StartActivity"
        private const val PRIVACY_POLICY_REQUEST_CODE = 1001
    }

    private lateinit var binding: ActivityStartBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        Log.d(TAG, "创建开始界面")

        // 初始化语言设置
        LanguageManager.initializeLanguage(this)

        // 检查隐私政策同意状态
        if (!checkPrivacyPolicyAgreed()) {
            Log.d(TAG, "用户未同意隐私政策，显示隐私政策弹窗")
            // 设置一个临时的空视图，避免Activity处于不稳定状态
            val emptyView = android.view.View(this)
            setContentView(emptyView)
            showPrivacyPolicyDialog()
            return
        }

        // 用户已经同意隐私政策，初始化广告SDK
        Log.d(TAG, "用户已同意隐私政策，初始化广告SDK")
        initializeAdSDK()

        // 生成隐私合规报告
        val complianceReport = PrivacyAuditLogger.generateComplianceReport(this)
        Log.i(TAG, complianceReport)

        // 初始化正常的开始界面
        initializeStartScreen()
    }

    /**
     * 初始化广告SDK
     * 只有在用户同意隐私政策后才能调用
     */
    private fun initializeAdSDK() {
        Log.d(TAG, "开始初始化广告SDK")

        try {
            val application = application as MountainSurvivalApplication
            application.initAdSDK()
            Log.d(TAG, "广告SDK初始化调用完成")
        } catch (e: Exception) {
            Log.e(TAG, "广告SDK初始化异常", e)
        }
    }

    /**
     * 初始化正常的开始界面
     */
    private fun initializeStartScreen() {
        Log.d(TAG, "初始化开始界面")

        // 初始化视图绑定
        binding = ActivityStartBinding.inflate(layoutInflater)
        setContentView(binding.root)

        // 隐藏ActionBar
        supportActionBar?.hide()

        // 设置按钮点击监听器
        setupClickListeners()

        // 初始化音乐管理器并播放背景音乐
        MusicManager.initialize(this)
        MusicManager.startBackgroundMusic(this)

        // 更新音乐按钮文本
        updateMusicButtonText()

        Log.d(TAG, "开始界面初始化完成")
    }

    /**
     * 显示隐私政策弹窗
     */
    private fun showPrivacyPolicyDialog() {
        Log.d(TAG, "显示隐私政策弹窗")

        // 启动隐私政策Activity
        val intent = Intent(this, PrivacyPolicyActivity::class.java)
        startActivityForResult(intent, PRIVACY_POLICY_REQUEST_CODE)
    }

    /**
     * 检查用户是否已同意隐私政策
     */
    private fun checkPrivacyPolicyAgreed(): Boolean {
        val sharedPreferences = getSharedPreferences("privacy_policy", MODE_PRIVATE)
        val agreed = sharedPreferences.getBoolean("agreed", false)
        val agreeTime = sharedPreferences.getLong("agree_time", 0)

        Log.d(TAG, "隐私政策同意状态: agreed=$agreed, agreeTime=$agreeTime")

        return agreed
    }

    /**
     * 设置按钮点击监听器
     */
    private fun setupClickListeners() {
        // 开始游戏按钮
        binding.startGameButton.setOnClickListener {
            Log.d(TAG, "用户点击开始游戏")
            startGame()
        }

        // 退出游戏按钮
        binding.exitGameButton.setOnClickListener {
            Log.d(TAG, "用户点击退出游戏")
            exitGame()
        }

        // 音乐控制按钮
        binding.musicToggleButton.setOnClickListener {
            Log.d(TAG, "用户点击音乐控制按钮")
            MusicManager.toggleMusic(this)
            updateMusicButtonText()
        }

        // 语言选择按钮
        binding.languageButton.setOnClickListener {
            Log.d(TAG, "用户点击语言选择按钮")
            showLanguageSelectionDialog()
        }

        // 返回键处理在onBackPressed方法中实现
    }

    /**
     * 开始游戏
     * 跳转到主游戏界面
     */
    private fun startGame() {
        Log.d(TAG, "启动游戏，跳转到MainActivity")

        val intent = Intent(this, MainActivity::class.java)
        startActivity(intent)

        // 关闭开始界面
        finish()
    }

    /**
     * 退出游戏
     * 关闭应用程序
     */
    private fun exitGame() {
        Log.d(TAG, "退出游戏")

        // 停止背景音乐
        MusicManager.stopBackgroundMusic()

        // 关闭应用程序
        finish()
    }

    /**
     * 更新音乐按钮文本
     */
    private fun updateMusicButtonText() {
        // 检查binding是否已初始化，避免在隐私政策弹窗显示时崩溃
        if (::binding.isInitialized) {
            val musicText = if (MusicManager.isMusicEnabled()) {
                getString(R.string.music_on)
            } else {
                getString(R.string.music_off)
            }
            binding.musicToggleButton.text = musicText
        }
    }

    /**
     * 处理Activity返回结果
     */
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)

        if (requestCode == PRIVACY_POLICY_REQUEST_CODE) {
            if (resultCode == RESULT_OK) {
                // 用户同意了隐私政策，现在可以初始化广告SDK
                Log.d(TAG, "用户同意隐私政策，初始化广告SDK")
                initializeAdSDK()

                // 然后初始化开始界面
                Log.d(TAG, "初始化开始界面")
                initializeStartScreen()
            } else {
                // 用户不同意隐私政策，退出应用
                Log.d(TAG, "用户不同意隐私政策，退出应用")
                finish()
            }
        }
    }

    /**
     * 处理返回键
     */
    override fun onBackPressed() {
        // 在开始界面按返回键直接退出应用
        Log.d(TAG, "用户按返回键，退出应用")
        MusicManager.stopBackgroundMusic()
        finish()
    }

    /**
     * Activity暂停时暂停音乐
     */
    override fun onPause() {
        super.onPause()
        MusicManager.pauseBackgroundMusic()
    }

    /**
     * Activity恢复时恢复音乐
     */
    override fun onResume() {
        super.onResume()
        MusicManager.resumeBackgroundMusic()
        // 只有在binding已初始化时才更新音乐按钮文本
        updateMusicButtonText()
    }

    /**
     * Activity销毁时释放音乐资源
     */
    override fun onDestroy() {
        super.onDestroy()
        // 注意：不在这里停止音乐，因为可能要跳转到游戏界面
    }

    /**
     * 显示语言选择对话框
     */
    private fun showLanguageSelectionDialog() {
        Log.d(TAG, "显示语言选择对话框")

        val languages = LanguageManager.getSupportedLanguages()
        val languageNames = languages.map { languageCode ->
            LanguageManager.getLanguageDisplayName(this, languageCode)
        }.toTypedArray()

        val currentLanguage = LanguageManager.getCurrentLanguage(this)
        val currentIndex = languages.indexOf(currentLanguage)

        AlertDialog.Builder(this)
            .setTitle(getString(R.string.language_selection_title))
            .setSingleChoiceItems(languageNames, currentIndex) { dialog, which ->
                val selectedLanguage = languages[which]
                Log.d(TAG, "用户选择语言: $selectedLanguage")

                if (selectedLanguage != currentLanguage) {
                    // 设置新语言
                    LanguageManager.setLanguage(this, selectedLanguage)

                    // 重新创建Activity以应用新语言
                    recreate()
                }

                dialog.dismiss()
            }
            .setNegativeButton(getString(R.string.language_cancel)) { dialog, _ ->
                dialog.dismiss()
            }
            .show()
    }
}
