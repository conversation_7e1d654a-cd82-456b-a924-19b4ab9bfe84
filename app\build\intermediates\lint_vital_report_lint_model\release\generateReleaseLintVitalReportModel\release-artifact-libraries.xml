<libraries>
  <library
      name="__local_aars__:E:\Ai\AiCode\game\MountainSurvival\code\app\libs\adset-bz-adatper-*******.1.aar:unspecified@jar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b1557b03863096af3ecde958c37f66ad\transformed\adset-bz-adatper-*******.1\jars\classes.jar"
      resolved="__local_aars__:E:\Ai\AiCode\game\MountainSurvival\code\app\libs\adset-bz-adatper-*******.1.aar:unspecified"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b1557b03863096af3ecde958c37f66ad\transformed\adset-bz-adatper-*******.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="__local_aars__:E:\Ai\AiCode\game\MountainSurvival\code\app\libs\adset-gdt-adatper-4.640.1510.1.aar:unspecified@jar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\931f9ce2dbe0aec634ea6adf21c22af3\transformed\adset-gdt-adatper-4.640.1510.1\jars\classes.jar"
      resolved="__local_aars__:E:\Ai\AiCode\game\MountainSurvival\code\app\libs\adset-gdt-adatper-4.640.1510.1.aar:unspecified"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\931f9ce2dbe0aec634ea6adf21c22af3\transformed\adset-gdt-adatper-4.640.1510.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="__local_aars__:E:\Ai\AiCode\game\MountainSurvival\code\app\libs\adset-gm-ad-adatper-*******.1.aar:unspecified@jar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f1516e90990b050480c479c0e18566d7\transformed\adset-gm-ad-adatper-*******.1\jars\classes.jar"
      resolved="__local_aars__:E:\Ai\AiCode\game\MountainSurvival\code\app\libs\adset-gm-ad-adatper-*******.1.aar:unspecified"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f1516e90990b050480c479c0e18566d7\transformed\adset-gm-ad-adatper-*******.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="__local_aars__:E:\Ai\AiCode\game\MountainSurvival\code\app\libs\adset-ks-ad-adatper-********.2.aar:unspecified@jar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2fc0105059ba8c1b02b7a206e8b498ae\transformed\adset-ks-ad-adatper-********.2\jars\classes.jar"
      resolved="__local_aars__:E:\Ai\AiCode\game\MountainSurvival\code\app\libs\adset-ks-ad-adatper-********.2.aar:unspecified"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2fc0105059ba8c1b02b7a206e8b498ae\transformed\adset-ks-ad-adatper-********.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="__local_aars__:E:\Ai\AiCode\game\MountainSurvival\code\app\libs\adset-sg-adatper-********.aar:unspecified@jar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3c5011997551c7077921047c8a391cd0\transformed\adset-sg-adatper-********\jars\classes.jar"
      resolved="__local_aars__:E:\Ai\AiCode\game\MountainSurvival\code\app\libs\adset-sg-adatper-********.aar:unspecified"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3c5011997551c7077921047c8a391cd0\transformed\adset-sg-adatper-********"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="__local_aars__:E:\Ai\AiCode\game\MountainSurvival\code\app\libs\beizi_fusion_sdk_*******.aar:unspecified@jar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62922e5f87afeea95a66affda0037661\transformed\beizi_fusion_sdk_*******\jars\classes.jar"
      resolved="__local_aars__:E:\Ai\AiCode\game\MountainSurvival\code\app\libs\beizi_fusion_sdk_*******.aar:unspecified"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62922e5f87afeea95a66affda0037661\transformed\beizi_fusion_sdk_*******"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="__local_aars__:E:\Ai\AiCode\game\MountainSurvival\code\app\libs\GDTSDK.unionNormal.4.640.1510.aar:unspecified@jar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2144dbf5c98623e2472a5ef01095ffd5\transformed\GDTSDK.unionNormal.4.640.1510\jars\classes.jar"
      resolved="__local_aars__:E:\Ai\AiCode\game\MountainSurvival\code\app\libs\GDTSDK.unionNormal.4.640.1510.aar:unspecified"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2144dbf5c98623e2472a5ef01095ffd5\transformed\GDTSDK.unionNormal.4.640.1510"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="__local_aars__:E:\Ai\AiCode\game\MountainSurvival\code\app\libs\kssdk-ad-********-publishRelease-aa0a55f514.aar:unspecified@jar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cb0f3cc3125274cec1b491c0584a407c\transformed\kssdk-ad-********-publishRelease-aa0a55f514\jars\classes.jar"
      resolved="__local_aars__:E:\Ai\AiCode\game\MountainSurvival\code\app\libs\kssdk-ad-********-publishRelease-aa0a55f514.aar:unspecified"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cb0f3cc3125274cec1b491c0584a407c\transformed\kssdk-ad-********-publishRelease-aa0a55f514"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="__local_aars__:E:\Ai\AiCode\game\MountainSurvival\code\app\libs\oaid_sdk_1.0.25.aar:unspecified@jar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc935b565fb69051e331687c8be34990\transformed\oaid_sdk_1.0.25\jars\classes.jar"
      resolved="__local_aars__:E:\Ai\AiCode\game\MountainSurvival\code\app\libs\oaid_sdk_1.0.25.aar:unspecified"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc935b565fb69051e331687c8be34990\transformed\oaid_sdk_1.0.25"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="__local_aars__:E:\Ai\AiCode\game\MountainSurvival\code\app\libs\openset_sdk_6.5.2.6.aar:unspecified@jar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0457feaf864ab549dda35ffc4a13a2e6\transformed\openset_sdk_6.5.2.6\jars\classes.jar"
      resolved="__local_aars__:E:\Ai\AiCode\game\MountainSurvival\code\app\libs\openset_sdk_6.5.2.6.aar:unspecified"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0457feaf864ab549dda35ffc4a13a2e6\transformed\openset_sdk_6.5.2.6"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="__local_aars__:E:\Ai\AiCode\game\MountainSurvival\code\app\libs\open_ad_sdk_*******.aar:unspecified@jar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\077c69690200a4c0c0ddf0630ba2f6e7\transformed\open_ad_sdk_*******\jars\classes.jar"
      resolved="__local_aars__:E:\Ai\AiCode\game\MountainSurvival\code\app\libs\open_ad_sdk_*******.aar:unspecified"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\077c69690200a4c0c0ddf0630ba2f6e7\transformed\open_ad_sdk_*******"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="__local_aars__:E:\Ai\AiCode\game\MountainSurvival\code\app\libs\wind-common-1.8.1.aar:unspecified@jar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dcbaddd2045e5dd8b6d4dc7e2d7d119c\transformed\wind-common-1.8.1\jars\classes.jar"
      resolved="__local_aars__:E:\Ai\AiCode\game\MountainSurvival\code\app\libs\wind-common-1.8.1.aar:unspecified"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dcbaddd2045e5dd8b6d4dc7e2d7d119c\transformed\wind-common-1.8.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="__local_aars__:E:\Ai\AiCode\game\MountainSurvival\code\app\libs\wind-sdk-4.23.0.aar:unspecified@jar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b66139be6585a43ae23903a8750be796\transformed\wind-sdk-4.23.0\jars\classes.jar"
      resolved="__local_aars__:E:\Ai\AiCode\game\MountainSurvival\code\app\libs\wind-sdk-4.23.0.aar:unspecified"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b66139be6585a43ae23903a8750be796\transformed\wind-sdk-4.23.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.android.databinding:viewbinding:8.7.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6cf7067a60607eab81a04cba360bb413\transformed\viewbinding-8.7.2\jars\classes.jar"
      resolved="com.android.databinding:viewbinding:8.7.2"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6cf7067a60607eab81a04cba360bb413\transformed\viewbinding-8.7.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.squareup.okhttp3:okhttp:4.12.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.squareup.okhttp3\okhttp\4.12.0\2f4525d4a200e97e1b87449c2cd9bd2e25b7e8cd\okhttp-4.12.0.jar"
      resolved="com.squareup.okhttp3:okhttp:4.12.0"/>
  <library
      name="com.squareup.okio:okio-jvm:3.6.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.squareup.okio\okio-jvm\3.6.0\5600569133b7bdefe1daf9ec7f4abeb6d13e1786\okio-jvm-3.6.0.jar"
      resolved="com.squareup.okio:okio-jvm:3.6.0"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.10@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib-jdk8\1.9.10\c7510d64a83411a649c76f2778304ddf71d7437b\kotlin-stdlib-jdk8-1.9.10.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.10"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.10@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib-jdk7\1.9.10\bc5bfc2690338defd5195b05c57562f2194eeb10\kotlin-stdlib-jdk7-1.9.10.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.10"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib:2.0.21@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib\2.0.21\618b539767b4899b4660a83006e052b63f1db551\kotlin-stdlib-2.0.21.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib:2.0.21"/>
  <library
      name="com.google.code.gson:gson:2.9.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.code.gson\gson\2.9.0\8a1167e089096758b49f9b34066ef98b2f4b37aa\gson-2.9.0.jar"
      resolved="com.google.code.gson:gson:2.9.0"/>
  <library
      name="com.android.support:design:28.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\71b2f468c1ec12de737a4945bfcdd2ee\transformed\design-28.0.0\jars\classes.jar"
      resolved="com.android.support:design:28.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\71b2f468c1ec12de737a4945bfcdd2ee\transformed\design-28.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.android.support:appcompat-v7:28.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a3ae461f31008f99a87e29f3c99a3c83\transformed\appcompat-v7-28.0.0\jars\classes.jar"
      resolved="com.android.support:appcompat-v7:28.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a3ae461f31008f99a87e29f3c99a3c83\transformed\appcompat-v7-28.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.android.support.constraint:constraint-layout:1.1.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6e168fd8550fecc7fe244221549ae7ea\transformed\constraint-layout-1.1.3\jars\classes.jar"
      resolved="com.android.support.constraint:constraint-layout:1.1.3"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6e168fd8550fecc7fe244221549ae7ea\transformed\constraint-layout-1.1.3"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.android.support:support-v4:28.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\58aa785da54a82955b8944f378983ec5\transformed\support-v4-28.0.0\jars\classes.jar"
      resolved="com.android.support:support-v4:28.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\58aa785da54a82955b8944f378983ec5\transformed\support-v4-28.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.android.support:recyclerview-v7:28.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\275e6b20e6ac21f664e4a13571bf830c\transformed\recyclerview-v7-28.0.0\jars\classes.jar"
      resolved="com.android.support:recyclerview-v7:28.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\275e6b20e6ac21f664e4a13571bf830c\transformed\recyclerview-v7-28.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="io.github.aliyun-sls:aliyun-log-android-sdk:2.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\655b8f2e72a823354e277c75aa81de5d\transformed\aliyun-log-android-sdk-2.7.0\jars\classes.jar"
      resolved="io.github.aliyun-sls:aliyun-log-android-sdk:2.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\655b8f2e72a823354e277c75aa81de5d\transformed\aliyun-log-android-sdk-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.hihonor.mcs:ads-identifier:1.0.2.301@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a89994faa2a5f59958fe4afbfa06d206\transformed\ads-identifier-1.0.2.301\jars\classes.jar"
      resolved="com.hihonor.mcs:ads-identifier:1.0.2.301"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a89994faa2a5f59958fe4afbfa06d206\transformed\ads-identifier-1.0.2.301"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.android.support:multidex:1.0.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c524408c9e34ec0a247b282de453e6e6\transformed\multidex-1.0.3\jars\classes.jar"
      resolved="com.android.support:multidex:1.0.3"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c524408c9e34ec0a247b282de453e6e6\transformed\multidex-1.0.3"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.tencent.mm.opensdk:wechat-sdk-android:6.8.28@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\37cbe4b86f6b6a04ea5d14efdca22993\transformed\wechat-sdk-android-6.8.28\jars\classes.jar"
      resolved="com.tencent.mm.opensdk:wechat-sdk-android:6.8.28"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\37cbe4b86f6b6a04ea5d14efdca22993\transformed\wechat-sdk-android-6.8.28"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.android.support:support-fragment:28.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\56fe7821f676d722ee64ba76456c485b\transformed\support-fragment-28.0.0\jars\classes.jar"
      resolved="com.android.support:support-fragment:28.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\56fe7821f676d722ee64ba76456c485b\transformed\support-fragment-28.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.android.support:animated-vector-drawable:28.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\76fcd764e8f549c61c80a2950bf73a66\transformed\animated-vector-drawable-28.0.0\jars\classes.jar"
      resolved="com.android.support:animated-vector-drawable:28.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\76fcd764e8f549c61c80a2950bf73a66\transformed\animated-vector-drawable-28.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.android.support:support-core-ui:28.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0207922e4efddcbaab7193a0d28923d8\transformed\support-core-ui-28.0.0\jars\classes.jar"
      resolved="com.android.support:support-core-ui:28.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0207922e4efddcbaab7193a0d28923d8\transformed\support-core-ui-28.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.android.support:support-core-utils:28.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\37a9bb1e21c74c02e396cd0078f7d33a\transformed\support-core-utils-28.0.0\jars\classes.jar"
      resolved="com.android.support:support-core-utils:28.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\37a9bb1e21c74c02e396cd0078f7d33a\transformed\support-core-utils-28.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.android.support:support-vector-drawable:28.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0928246d15decb48b3304dcc1bce448c\transformed\support-vector-drawable-28.0.0\jars\classes.jar"
      resolved="com.android.support:support-vector-drawable:28.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0928246d15decb48b3304dcc1bce448c\transformed\support-vector-drawable-28.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.android.support:support-media-compat:28.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\194766323b8e4d092ba620fa2362821d\transformed\support-media-compat-28.0.0\jars\classes.jar"
      resolved="com.android.support:support-media-compat:28.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\194766323b8e4d092ba620fa2362821d\transformed\support-media-compat-28.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.android.support:transition:28.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e779f411b6970bbfe6a7d381c7d1cfc3\transformed\transition-28.0.0\jars\classes.jar"
      resolved="com.android.support:transition:28.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e779f411b6970bbfe6a7d381c7d1cfc3\transformed\transition-28.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.android.support:loader:28.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\268684ab7864a6a8b5e6e60bcea56bf4\transformed\loader-28.0.0\jars\classes.jar"
      resolved="com.android.support:loader:28.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\268684ab7864a6a8b5e6e60bcea56bf4\transformed\loader-28.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.android.support:viewpager:28.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aa1057031f9c93aa65269124a32c0ac0\transformed\viewpager-28.0.0\jars\classes.jar"
      resolved="com.android.support:viewpager:28.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aa1057031f9c93aa65269124a32c0ac0\transformed\viewpager-28.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.android.support:coordinatorlayout:28.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7007720781ce56b641f8588ab1a8654\transformed\coordinatorlayout-28.0.0\jars\classes.jar"
      resolved="com.android.support:coordinatorlayout:28.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7007720781ce56b641f8588ab1a8654\transformed\coordinatorlayout-28.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.android.support:drawerlayout:28.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\27d50ac90eb86166bfd58c614b56f070\transformed\drawerlayout-28.0.0\jars\classes.jar"
      resolved="com.android.support:drawerlayout:28.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\27d50ac90eb86166bfd58c614b56f070\transformed\drawerlayout-28.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.android.support:slidingpanelayout:28.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\76b3f23ecbd82b566d651810289e55e7\transformed\slidingpanelayout-28.0.0\jars\classes.jar"
      resolved="com.android.support:slidingpanelayout:28.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\76b3f23ecbd82b566d651810289e55e7\transformed\slidingpanelayout-28.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.android.support:customview:28.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e64d97b51ff2c731d8ea6f143fce5dc3\transformed\customview-28.0.0\jars\classes.jar"
      resolved="com.android.support:customview:28.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e64d97b51ff2c731d8ea6f143fce5dc3\transformed\customview-28.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.android.support:swiperefreshlayout:28.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eae4da99ab74b28d9e7bd7a63be15752\transformed\swiperefreshlayout-28.0.0\jars\classes.jar"
      resolved="com.android.support:swiperefreshlayout:28.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eae4da99ab74b28d9e7bd7a63be15752\transformed\swiperefreshlayout-28.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.android.support:asynclayoutinflater:28.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96fd16bfb71156b945a0987f0fa110dc\transformed\asynclayoutinflater-28.0.0\jars\classes.jar"
      resolved="com.android.support:asynclayoutinflater:28.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96fd16bfb71156b945a0987f0fa110dc\transformed\asynclayoutinflater-28.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.android.support:support-compat:28.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\daefd50eed0506e326ae247974906cd2\transformed\support-compat-28.0.0\jars\classes.jar"
      resolved="com.android.support:support-compat:28.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\daefd50eed0506e326ae247974906cd2\transformed\support-compat-28.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.android.support:versionedparcelable:28.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\883f4fff2cbbd0247cd7782de53777c1\transformed\versionedparcelable-28.0.0\jars\classes.jar"
      resolved="com.android.support:versionedparcelable:28.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\883f4fff2cbbd0247cd7782de53777c1\transformed\versionedparcelable-28.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.android.support:collections:28.0.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.android.support\collections\28.0.0\c1bcdade4d3cc2836130424a3f3e4182c666a745\collections-28.0.0.jar"
      resolved="com.android.support:collections:28.0.0"/>
  <library
      name="com.android.support:cursoradapter:28.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\14aa7ca6daac1f90f3aa0f0be6e5280e\transformed\cursoradapter-28.0.0\jars\classes.jar"
      resolved="com.android.support:cursoradapter:28.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\14aa7ca6daac1f90f3aa0f0be6e5280e\transformed\cursoradapter-28.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.android.support:cardview-v7:28.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b255ac174ecb581f0983b73e2410e4dd\transformed\cardview-v7-28.0.0\jars\classes.jar"
      resolved="com.android.support:cardview-v7:28.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b255ac174ecb581f0983b73e2410e4dd\transformed\cardview-v7-28.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="android.arch.lifecycle:runtime:1.1.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1475b58fc1f3cecd0067a810105062e0\transformed\runtime-1.1.1\jars\classes.jar"
      resolved="android.arch.lifecycle:runtime:1.1.1"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1475b58fc1f3cecd0067a810105062e0\transformed\runtime-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.android.support:documentfile:28.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\35ab1d4732f51d98aa1cd4769dbb325e\transformed\documentfile-28.0.0\jars\classes.jar"
      resolved="com.android.support:documentfile:28.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\35ab1d4732f51d98aa1cd4769dbb325e\transformed\documentfile-28.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.android.support:localbroadcastmanager:28.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\758958c4b66e4edd8a16c89111471eca\transformed\localbroadcastmanager-28.0.0\jars\classes.jar"
      resolved="com.android.support:localbroadcastmanager:28.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\758958c4b66e4edd8a16c89111471eca\transformed\localbroadcastmanager-28.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.android.support:print:28.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e575a2f1a77d639742481ac058422fdb\transformed\print-28.0.0\jars\classes.jar"
      resolved="com.android.support:print:28.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e575a2f1a77d639742481ac058422fdb\transformed\print-28.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="android.arch.lifecycle:viewmodel:1.1.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7c92e4b6a3e2e7acd3cc4883f3825012\transformed\viewmodel-1.1.1\jars\classes.jar"
      resolved="android.arch.lifecycle:viewmodel:1.1.1"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7c92e4b6a3e2e7acd3cc4883f3825012\transformed\viewmodel-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.android.support:interpolator:28.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9e5c22dc8f55011dba2a2ca0786da06d\transformed\interpolator-28.0.0\jars\classes.jar"
      resolved="com.android.support:interpolator:28.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9e5c22dc8f55011dba2a2ca0786da06d\transformed\interpolator-28.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="android.arch.lifecycle:livedata:1.1.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9274e6b131cdd56a80957596a03e901b\transformed\livedata-1.1.1\jars\classes.jar"
      resolved="android.arch.lifecycle:livedata:1.1.1"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9274e6b131cdd56a80957596a03e901b\transformed\livedata-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="android.arch.lifecycle:livedata-core:1.1.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b0c31b939ec82c7b9abf105ecf8aff88\transformed\livedata-core-1.1.1\jars\classes.jar"
      resolved="android.arch.lifecycle:livedata-core:1.1.1"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b0c31b939ec82c7b9abf105ecf8aff88\transformed\livedata-core-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="android.arch.lifecycle:common:1.1.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\android.arch.lifecycle\common\1.1.1\207a6efae6a3555e326de41f76bdadd9a239cbce\common-1.1.1.jar"
      resolved="android.arch.lifecycle:common:1.1.1"/>
  <library
      name="android.arch.core:runtime:1.1.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\15a2d8bfd490b0ae823b7fd611421d5a\transformed\runtime-1.1.1\jars\classes.jar"
      resolved="android.arch.core:runtime:1.1.1"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\15a2d8bfd490b0ae823b7fd611421d5a\transformed\runtime-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="android.arch.core:common:1.1.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\android.arch.core\common\1.1.1\e55b70d1f5620db124b3e85a7f4bdc7bd48d9f95\common-1.1.1.jar"
      resolved="android.arch.core:common:1.1.1"/>
  <library
      name="com.android.support:support-annotations:28.0.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.android.support\support-annotations\28.0.0\ed73f5337a002d1fd24339d5fb08c2c9d9ca60d8\support-annotations-28.0.0.jar"
      resolved="com.android.support:support-annotations:28.0.0"/>
  <library
      name="org.jetbrains:annotations:13.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains\annotations\13.0\919f0dfe192fb4e063e7dacadee7f8bb9a2672a9\annotations-13.0.jar"
      resolved="org.jetbrains:annotations:13.0"/>
  <library
      name="com.android.support.constraint:constraint-layout-solver:1.1.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.android.support.constraint\constraint-layout-solver\1.1.3\bde0667d7414c16ed62d3cfe993cff7f9d732373\constraint-layout-solver-1.1.3.jar"
      resolved="com.android.support.constraint:constraint-layout-solver:1.1.3"/>
</libraries>
