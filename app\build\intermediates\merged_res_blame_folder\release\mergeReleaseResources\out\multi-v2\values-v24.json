{"logs": [{"outputFile": "com.ainative.mountainsurvival.app-mergeReleaseResources-22:/values-v24/values-v24.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a3ae461f31008f99a87e29f3c99a3c83\\transformed\\appcompat-v7-28.0.0\\res\\values-v24\\values-v24.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,212", "endColumns": "156,134", "endOffsets": "207,342"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\194766323b8e4d092ba620fa2362821d\\transformed\\support-media-compat-28.0.0\\res\\values-v24\\values-v24.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,121,182,248", "endColumns": "65,60,65,66", "endOffsets": "116,177,243,310"}, "to": {"startLines": "4,5,6,7", "startColumns": "4,4,4,4", "startOffsets": "347,413,474,540", "endColumns": "65,60,65,66", "endOffsets": "408,469,535,602"}}]}]}