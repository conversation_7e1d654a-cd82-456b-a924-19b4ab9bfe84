<dependencies>
  <compile
      roots="__local_aars__:E:\Ai\AiCode\game\MountainSurvival\code\app\libs\adset-bz-adatper-5.2.1.6.1.aar:unspecified@jar,__local_aars__:E:\Ai\AiCode\game\MountainSurvival\code\app\libs\adset-gdt-adatper-4.640.1510.1.aar:unspecified@jar,__local_aars__:E:\Ai\AiCode\game\MountainSurvival\code\app\libs\adset-gm-ad-adatper-6.8.4.0.1.aar:unspecified@jar,__local_aars__:E:\Ai\AiCode\game\MountainSurvival\code\app\libs\adset-ks-ad-adatper-4.4.20.1.2.aar:unspecified@jar,__local_aars__:E:\Ai\AiCode\game\MountainSurvival\code\app\libs\adset-sg-adatper-4.23.0.1.aar:unspecified@jar,__local_aars__:E:\Ai\AiCode\game\MountainSurvival\code\app\libs\beizi_fusion_sdk_5.2.1.6.aar:unspecified@jar,__local_aars__:E:\Ai\AiCode\game\MountainSurvival\code\app\libs\GDTSDK.unionNormal.4.640.1510.aar:unspecified@jar,__local_aars__:E:\Ai\AiCode\game\MountainSurvival\code\app\libs\kssdk-ad-4.4.20.1-publishRelease-aa0a55f514.aar:unspecified@jar,__local_aars__:E:\Ai\AiCode\game\MountainSurvival\code\app\libs\oaid_sdk_1.0.25.aar:unspecified@jar,__local_aars__:E:\Ai\AiCode\game\MountainSurvival\code\app\libs\openset_sdk_6.5.2.6.aar:unspecified@jar,__local_aars__:E:\Ai\AiCode\game\MountainSurvival\code\app\libs\open_ad_sdk_6.8.4.0.aar:unspecified@jar,__local_aars__:E:\Ai\AiCode\game\MountainSurvival\code\app\libs\wind-common-1.8.1.aar:unspecified@jar,__local_aars__:E:\Ai\AiCode\game\MountainSurvival\code\app\libs\wind-sdk-4.23.0.aar:unspecified@jar,com.android.databinding:viewbinding:8.7.2@aar,com.squareup.okhttp3:okhttp:4.12.0@jar,com.squareup.okio:okio-jvm:3.6.0@jar,org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.10@jar,org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.10@jar,org.jetbrains.kotlin:kotlin-stdlib:2.0.21@jar,com.google.code.gson:gson:2.9.0@jar,com.android.support:design:28.0.0@aar,com.android.support:appcompat-v7:28.0.0@aar,com.android.support.constraint:constraint-layout:1.1.3@aar,com.android.support:support-v4:28.0.0@aar,com.android.support:recyclerview-v7:28.0.0@aar,io.github.aliyun-sls:aliyun-log-android-sdk:2.7.0@aar,com.hihonor.mcs:ads-identifier:1.0.2.301@aar,com.android.support:multidex:1.0.3@aar,com.tencent.mm.opensdk:wechat-sdk-android:6.8.28@aar,com.android.support:support-fragment:28.0.0@aar,com.android.support:animated-vector-drawable:28.0.0@aar,com.android.support:support-core-ui:28.0.0@aar,com.android.support:support-core-utils:28.0.0@aar,com.android.support:support-vector-drawable:28.0.0@aar,com.android.support:support-media-compat:28.0.0@aar,com.android.support:transition:28.0.0@aar,com.android.support:loader:28.0.0@aar,com.android.support:viewpager:28.0.0@aar,com.android.support:coordinatorlayout:28.0.0@aar,com.android.support:drawerlayout:28.0.0@aar,com.android.support:slidingpanelayout:28.0.0@aar,com.android.support:customview:28.0.0@aar,com.android.support:swiperefreshlayout:28.0.0@aar,com.android.support:asynclayoutinflater:28.0.0@aar,com.android.support:support-compat:28.0.0@aar,com.android.support:versionedparcelable:28.0.0@aar,com.android.support:collections:28.0.0@jar,com.android.support:cursoradapter:28.0.0@aar,com.android.support:cardview-v7:28.0.0@aar,android.arch.lifecycle:runtime:1.1.1@aar,com.android.support:documentfile:28.0.0@aar,com.android.support:localbroadcastmanager:28.0.0@aar,com.android.support:print:28.0.0@aar,android.arch.lifecycle:viewmodel:1.1.1@aar,com.android.support:interpolator:28.0.0@aar,android.arch.lifecycle:livedata:1.1.1@aar,android.arch.lifecycle:livedata-core:1.1.1@aar,android.arch.lifecycle:common:1.1.1@jar,android.arch.core:runtime:1.1.1@aar,android.arch.core:common:1.1.1@jar,com.android.support:support-annotations:28.0.0@jar,org.jetbrains:annotations:13.0@jar,com.android.support.constraint:constraint-layout-solver:1.1.3@jar">
    <dependency
        name="__local_aars__:E:\Ai\AiCode\game\MountainSurvival\code\app\libs\adset-bz-adatper-5.2.1.6.1.aar:unspecified@jar"
        simpleName="__local_aars__:E:\Ai\AiCode\game\MountainSurvival\code\app\libs\adset-bz-adatper-5.2.1.6.1.aar"/>
    <dependency
        name="__local_aars__:E:\Ai\AiCode\game\MountainSurvival\code\app\libs\adset-gdt-adatper-4.640.1510.1.aar:unspecified@jar"
        simpleName="__local_aars__:E:\Ai\AiCode\game\MountainSurvival\code\app\libs\adset-gdt-adatper-4.640.1510.1.aar"/>
    <dependency
        name="__local_aars__:E:\Ai\AiCode\game\MountainSurvival\code\app\libs\adset-gm-ad-adatper-6.8.4.0.1.aar:unspecified@jar"
        simpleName="__local_aars__:E:\Ai\AiCode\game\MountainSurvival\code\app\libs\adset-gm-ad-adatper-6.8.4.0.1.aar"/>
    <dependency
        name="__local_aars__:E:\Ai\AiCode\game\MountainSurvival\code\app\libs\adset-ks-ad-adatper-4.4.20.1.2.aar:unspecified@jar"
        simpleName="__local_aars__:E:\Ai\AiCode\game\MountainSurvival\code\app\libs\adset-ks-ad-adatper-4.4.20.1.2.aar"/>
    <dependency
        name="__local_aars__:E:\Ai\AiCode\game\MountainSurvival\code\app\libs\adset-sg-adatper-4.23.0.1.aar:unspecified@jar"
        simpleName="__local_aars__:E:\Ai\AiCode\game\MountainSurvival\code\app\libs\adset-sg-adatper-4.23.0.1.aar"/>
    <dependency
        name="__local_aars__:E:\Ai\AiCode\game\MountainSurvival\code\app\libs\beizi_fusion_sdk_5.2.1.6.aar:unspecified@jar"
        simpleName="__local_aars__:E:\Ai\AiCode\game\MountainSurvival\code\app\libs\beizi_fusion_sdk_5.2.1.6.aar"/>
    <dependency
        name="__local_aars__:E:\Ai\AiCode\game\MountainSurvival\code\app\libs\GDTSDK.unionNormal.4.640.1510.aar:unspecified@jar"
        simpleName="__local_aars__:E:\Ai\AiCode\game\MountainSurvival\code\app\libs\GDTSDK.unionNormal.4.640.1510.aar"/>
    <dependency
        name="__local_aars__:E:\Ai\AiCode\game\MountainSurvival\code\app\libs\kssdk-ad-4.4.20.1-publishRelease-aa0a55f514.aar:unspecified@jar"
        simpleName="__local_aars__:E:\Ai\AiCode\game\MountainSurvival\code\app\libs\kssdk-ad-4.4.20.1-publishRelease-aa0a55f514.aar"/>
    <dependency
        name="__local_aars__:E:\Ai\AiCode\game\MountainSurvival\code\app\libs\oaid_sdk_1.0.25.aar:unspecified@jar"
        simpleName="__local_aars__:E:\Ai\AiCode\game\MountainSurvival\code\app\libs\oaid_sdk_1.0.25.aar"/>
    <dependency
        name="__local_aars__:E:\Ai\AiCode\game\MountainSurvival\code\app\libs\openset_sdk_6.5.2.6.aar:unspecified@jar"
        simpleName="__local_aars__:E:\Ai\AiCode\game\MountainSurvival\code\app\libs\openset_sdk_6.5.2.6.aar"/>
    <dependency
        name="__local_aars__:E:\Ai\AiCode\game\MountainSurvival\code\app\libs\open_ad_sdk_6.8.4.0.aar:unspecified@jar"
        simpleName="__local_aars__:E:\Ai\AiCode\game\MountainSurvival\code\app\libs\open_ad_sdk_6.8.4.0.aar"/>
    <dependency
        name="__local_aars__:E:\Ai\AiCode\game\MountainSurvival\code\app\libs\wind-common-1.8.1.aar:unspecified@jar"
        simpleName="__local_aars__:E:\Ai\AiCode\game\MountainSurvival\code\app\libs\wind-common-1.8.1.aar"/>
    <dependency
        name="__local_aars__:E:\Ai\AiCode\game\MountainSurvival\code\app\libs\wind-sdk-4.23.0.aar:unspecified@jar"
        simpleName="__local_aars__:E:\Ai\AiCode\game\MountainSurvival\code\app\libs\wind-sdk-4.23.0.aar"/>
    <dependency
        name="com.android.databinding:viewbinding:8.7.2@aar"
        simpleName="com.android.databinding:viewbinding"/>
    <dependency
        name="com.squareup.okhttp3:okhttp:4.12.0@jar"
        simpleName="com.squareup.okhttp3:okhttp"/>
    <dependency
        name="com.squareup.okio:okio-jvm:3.6.0@jar"
        simpleName="com.squareup.okio:okio-jvm"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.10@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk8"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.10@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk7"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib:2.0.21@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib"/>
    <dependency
        name="com.google.code.gson:gson:2.9.0@jar"
        simpleName="com.google.code.gson:gson"/>
    <dependency
        name="com.android.support:design:28.0.0@aar"
        simpleName="com.android.support:design"/>
    <dependency
        name="com.android.support:appcompat-v7:28.0.0@aar"
        simpleName="com.android.support:appcompat-v7"/>
    <dependency
        name="com.android.support.constraint:constraint-layout:1.1.3@aar"
        simpleName="com.android.support.constraint:constraint-layout"/>
    <dependency
        name="com.android.support:support-v4:28.0.0@aar"
        simpleName="com.android.support:support-v4"/>
    <dependency
        name="com.android.support:recyclerview-v7:28.0.0@aar"
        simpleName="com.android.support:recyclerview-v7"/>
    <dependency
        name="io.github.aliyun-sls:aliyun-log-android-sdk:2.7.0@aar"
        simpleName="io.github.aliyun-sls:aliyun-log-android-sdk"/>
    <dependency
        name="com.hihonor.mcs:ads-identifier:1.0.2.301@aar"
        simpleName="com.hihonor.mcs:ads-identifier"/>
    <dependency
        name="com.android.support:multidex:1.0.3@aar"
        simpleName="com.android.support:multidex"/>
    <dependency
        name="com.tencent.mm.opensdk:wechat-sdk-android:6.8.28@aar"
        simpleName="com.tencent.mm.opensdk:wechat-sdk-android"/>
    <dependency
        name="com.android.support:support-fragment:28.0.0@aar"
        simpleName="com.android.support:support-fragment"/>
    <dependency
        name="com.android.support:animated-vector-drawable:28.0.0@aar"
        simpleName="com.android.support:animated-vector-drawable"/>
    <dependency
        name="com.android.support:support-core-ui:28.0.0@aar"
        simpleName="com.android.support:support-core-ui"/>
    <dependency
        name="com.android.support:support-core-utils:28.0.0@aar"
        simpleName="com.android.support:support-core-utils"/>
    <dependency
        name="com.android.support:support-vector-drawable:28.0.0@aar"
        simpleName="com.android.support:support-vector-drawable"/>
    <dependency
        name="com.android.support:support-media-compat:28.0.0@aar"
        simpleName="com.android.support:support-media-compat"/>
    <dependency
        name="com.android.support:transition:28.0.0@aar"
        simpleName="com.android.support:transition"/>
    <dependency
        name="com.android.support:loader:28.0.0@aar"
        simpleName="com.android.support:loader"/>
    <dependency
        name="com.android.support:viewpager:28.0.0@aar"
        simpleName="com.android.support:viewpager"/>
    <dependency
        name="com.android.support:coordinatorlayout:28.0.0@aar"
        simpleName="com.android.support:coordinatorlayout"/>
    <dependency
        name="com.android.support:drawerlayout:28.0.0@aar"
        simpleName="com.android.support:drawerlayout"/>
    <dependency
        name="com.android.support:slidingpanelayout:28.0.0@aar"
        simpleName="com.android.support:slidingpanelayout"/>
    <dependency
        name="com.android.support:customview:28.0.0@aar"
        simpleName="com.android.support:customview"/>
    <dependency
        name="com.android.support:swiperefreshlayout:28.0.0@aar"
        simpleName="com.android.support:swiperefreshlayout"/>
    <dependency
        name="com.android.support:asynclayoutinflater:28.0.0@aar"
        simpleName="com.android.support:asynclayoutinflater"/>
    <dependency
        name="com.android.support:support-compat:28.0.0@aar"
        simpleName="com.android.support:support-compat"/>
    <dependency
        name="com.android.support:versionedparcelable:28.0.0@aar"
        simpleName="com.android.support:versionedparcelable"/>
    <dependency
        name="com.android.support:collections:28.0.0@jar"
        simpleName="com.android.support:collections"/>
    <dependency
        name="com.android.support:cursoradapter:28.0.0@aar"
        simpleName="com.android.support:cursoradapter"/>
    <dependency
        name="com.android.support:cardview-v7:28.0.0@aar"
        simpleName="com.android.support:cardview-v7"/>
    <dependency
        name="android.arch.lifecycle:runtime:1.1.1@aar"
        simpleName="android.arch.lifecycle:runtime"/>
    <dependency
        name="com.android.support:documentfile:28.0.0@aar"
        simpleName="com.android.support:documentfile"/>
    <dependency
        name="com.android.support:localbroadcastmanager:28.0.0@aar"
        simpleName="com.android.support:localbroadcastmanager"/>
    <dependency
        name="com.android.support:print:28.0.0@aar"
        simpleName="com.android.support:print"/>
    <dependency
        name="android.arch.lifecycle:viewmodel:1.1.1@aar"
        simpleName="android.arch.lifecycle:viewmodel"/>
    <dependency
        name="com.android.support:interpolator:28.0.0@aar"
        simpleName="com.android.support:interpolator"/>
    <dependency
        name="android.arch.lifecycle:livedata:1.1.1@aar"
        simpleName="android.arch.lifecycle:livedata"/>
    <dependency
        name="android.arch.lifecycle:livedata-core:1.1.1@aar"
        simpleName="android.arch.lifecycle:livedata-core"/>
    <dependency
        name="android.arch.lifecycle:common:1.1.1@jar"
        simpleName="android.arch.lifecycle:common"/>
    <dependency
        name="android.arch.core:runtime:1.1.1@aar"
        simpleName="android.arch.core:runtime"/>
    <dependency
        name="android.arch.core:common:1.1.1@jar"
        simpleName="android.arch.core:common"/>
    <dependency
        name="com.android.support:support-annotations:28.0.0@jar"
        simpleName="com.android.support:support-annotations"/>
    <dependency
        name="org.jetbrains:annotations:13.0@jar"
        simpleName="org.jetbrains:annotations"/>
    <dependency
        name="com.android.support.constraint:constraint-layout-solver:1.1.3@jar"
        simpleName="com.android.support.constraint:constraint-layout-solver"/>
  </compile>
  <package
      roots="__local_aars__:E:\Ai\AiCode\game\MountainSurvival\code\app\libs\adset-bz-adatper-5.2.1.6.1.aar:unspecified@jar,__local_aars__:E:\Ai\AiCode\game\MountainSurvival\code\app\libs\adset-gdt-adatper-4.640.1510.1.aar:unspecified@jar,__local_aars__:E:\Ai\AiCode\game\MountainSurvival\code\app\libs\adset-gm-ad-adatper-6.8.4.0.1.aar:unspecified@jar,__local_aars__:E:\Ai\AiCode\game\MountainSurvival\code\app\libs\adset-ks-ad-adatper-4.4.20.1.2.aar:unspecified@jar,__local_aars__:E:\Ai\AiCode\game\MountainSurvival\code\app\libs\adset-sg-adatper-4.23.0.1.aar:unspecified@jar,__local_aars__:E:\Ai\AiCode\game\MountainSurvival\code\app\libs\beizi_fusion_sdk_5.2.1.6.aar:unspecified@jar,__local_aars__:E:\Ai\AiCode\game\MountainSurvival\code\app\libs\GDTSDK.unionNormal.4.640.1510.aar:unspecified@jar,__local_aars__:E:\Ai\AiCode\game\MountainSurvival\code\app\libs\kssdk-ad-4.4.20.1-publishRelease-aa0a55f514.aar:unspecified@jar,__local_aars__:E:\Ai\AiCode\game\MountainSurvival\code\app\libs\oaid_sdk_1.0.25.aar:unspecified@jar,__local_aars__:E:\Ai\AiCode\game\MountainSurvival\code\app\libs\openset_sdk_6.5.2.6.aar:unspecified@jar,__local_aars__:E:\Ai\AiCode\game\MountainSurvival\code\app\libs\open_ad_sdk_6.8.4.0.aar:unspecified@jar,__local_aars__:E:\Ai\AiCode\game\MountainSurvival\code\app\libs\wind-common-1.8.1.aar:unspecified@jar,__local_aars__:E:\Ai\AiCode\game\MountainSurvival\code\app\libs\wind-sdk-4.23.0.aar:unspecified@jar,com.android.databinding:viewbinding:8.7.2@aar,com.squareup.okhttp3:okhttp:4.12.0@jar,com.squareup.okio:okio-jvm:3.6.0@jar,org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.10@jar,org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.10@jar,org.jetbrains.kotlin:kotlin-stdlib:2.0.21@jar,com.google.code.gson:gson:2.9.0@jar,com.android.support:design:28.0.0@aar,com.android.support:appcompat-v7:28.0.0@aar,com.android.support.constraint:constraint-layout:1.1.3@aar,com.android.support:support-v4:28.0.0@aar,com.android.support:recyclerview-v7:28.0.0@aar,io.github.aliyun-sls:aliyun-log-android-sdk:2.7.0@aar,com.hihonor.mcs:ads-identifier:1.0.2.301@aar,com.android.support:multidex:1.0.3@aar,com.tencent.mm.opensdk:wechat-sdk-android:6.8.28@aar,com.android.support:support-fragment:28.0.0@aar,com.android.support:animated-vector-drawable:28.0.0@aar,com.android.support:support-core-ui:28.0.0@aar,com.android.support:support-core-utils:28.0.0@aar,com.android.support:support-vector-drawable:28.0.0@aar,com.android.support:support-media-compat:28.0.0@aar,com.android.support:transition:28.0.0@aar,com.android.support:loader:28.0.0@aar,com.android.support:viewpager:28.0.0@aar,com.android.support:coordinatorlayout:28.0.0@aar,com.android.support:drawerlayout:28.0.0@aar,com.android.support:slidingpanelayout:28.0.0@aar,com.android.support:customview:28.0.0@aar,com.android.support:swiperefreshlayout:28.0.0@aar,com.android.support:asynclayoutinflater:28.0.0@aar,com.android.support:support-compat:28.0.0@aar,com.android.support:versionedparcelable:28.0.0@aar,com.android.support:collections:28.0.0@jar,com.android.support:cursoradapter:28.0.0@aar,com.android.support:cardview-v7:28.0.0@aar,android.arch.lifecycle:runtime:1.1.1@aar,com.android.support:documentfile:28.0.0@aar,com.android.support:localbroadcastmanager:28.0.0@aar,com.android.support:print:28.0.0@aar,android.arch.lifecycle:viewmodel:1.1.1@aar,com.android.support:interpolator:28.0.0@aar,android.arch.lifecycle:livedata:1.1.1@aar,android.arch.lifecycle:livedata-core:1.1.1@aar,android.arch.lifecycle:common:1.1.1@jar,android.arch.core:runtime:1.1.1@aar,android.arch.core:common:1.1.1@jar,com.android.support:support-annotations:28.0.0@jar,org.jetbrains:annotations:13.0@jar,com.android.support.constraint:constraint-layout-solver:1.1.3@jar">
    <dependency
        name="__local_aars__:E:\Ai\AiCode\game\MountainSurvival\code\app\libs\adset-bz-adatper-5.2.1.6.1.aar:unspecified@jar"
        simpleName="__local_aars__:E:\Ai\AiCode\game\MountainSurvival\code\app\libs\adset-bz-adatper-5.2.1.6.1.aar"/>
    <dependency
        name="__local_aars__:E:\Ai\AiCode\game\MountainSurvival\code\app\libs\adset-gdt-adatper-4.640.1510.1.aar:unspecified@jar"
        simpleName="__local_aars__:E:\Ai\AiCode\game\MountainSurvival\code\app\libs\adset-gdt-adatper-4.640.1510.1.aar"/>
    <dependency
        name="__local_aars__:E:\Ai\AiCode\game\MountainSurvival\code\app\libs\adset-gm-ad-adatper-6.8.4.0.1.aar:unspecified@jar"
        simpleName="__local_aars__:E:\Ai\AiCode\game\MountainSurvival\code\app\libs\adset-gm-ad-adatper-6.8.4.0.1.aar"/>
    <dependency
        name="__local_aars__:E:\Ai\AiCode\game\MountainSurvival\code\app\libs\adset-ks-ad-adatper-4.4.20.1.2.aar:unspecified@jar"
        simpleName="__local_aars__:E:\Ai\AiCode\game\MountainSurvival\code\app\libs\adset-ks-ad-adatper-4.4.20.1.2.aar"/>
    <dependency
        name="__local_aars__:E:\Ai\AiCode\game\MountainSurvival\code\app\libs\adset-sg-adatper-4.23.0.1.aar:unspecified@jar"
        simpleName="__local_aars__:E:\Ai\AiCode\game\MountainSurvival\code\app\libs\adset-sg-adatper-4.23.0.1.aar"/>
    <dependency
        name="__local_aars__:E:\Ai\AiCode\game\MountainSurvival\code\app\libs\beizi_fusion_sdk_5.2.1.6.aar:unspecified@jar"
        simpleName="__local_aars__:E:\Ai\AiCode\game\MountainSurvival\code\app\libs\beizi_fusion_sdk_5.2.1.6.aar"/>
    <dependency
        name="__local_aars__:E:\Ai\AiCode\game\MountainSurvival\code\app\libs\GDTSDK.unionNormal.4.640.1510.aar:unspecified@jar"
        simpleName="__local_aars__:E:\Ai\AiCode\game\MountainSurvival\code\app\libs\GDTSDK.unionNormal.4.640.1510.aar"/>
    <dependency
        name="__local_aars__:E:\Ai\AiCode\game\MountainSurvival\code\app\libs\kssdk-ad-4.4.20.1-publishRelease-aa0a55f514.aar:unspecified@jar"
        simpleName="__local_aars__:E:\Ai\AiCode\game\MountainSurvival\code\app\libs\kssdk-ad-4.4.20.1-publishRelease-aa0a55f514.aar"/>
    <dependency
        name="__local_aars__:E:\Ai\AiCode\game\MountainSurvival\code\app\libs\oaid_sdk_1.0.25.aar:unspecified@jar"
        simpleName="__local_aars__:E:\Ai\AiCode\game\MountainSurvival\code\app\libs\oaid_sdk_1.0.25.aar"/>
    <dependency
        name="__local_aars__:E:\Ai\AiCode\game\MountainSurvival\code\app\libs\openset_sdk_6.5.2.6.aar:unspecified@jar"
        simpleName="__local_aars__:E:\Ai\AiCode\game\MountainSurvival\code\app\libs\openset_sdk_6.5.2.6.aar"/>
    <dependency
        name="__local_aars__:E:\Ai\AiCode\game\MountainSurvival\code\app\libs\open_ad_sdk_6.8.4.0.aar:unspecified@jar"
        simpleName="__local_aars__:E:\Ai\AiCode\game\MountainSurvival\code\app\libs\open_ad_sdk_6.8.4.0.aar"/>
    <dependency
        name="__local_aars__:E:\Ai\AiCode\game\MountainSurvival\code\app\libs\wind-common-1.8.1.aar:unspecified@jar"
        simpleName="__local_aars__:E:\Ai\AiCode\game\MountainSurvival\code\app\libs\wind-common-1.8.1.aar"/>
    <dependency
        name="__local_aars__:E:\Ai\AiCode\game\MountainSurvival\code\app\libs\wind-sdk-4.23.0.aar:unspecified@jar"
        simpleName="__local_aars__:E:\Ai\AiCode\game\MountainSurvival\code\app\libs\wind-sdk-4.23.0.aar"/>
    <dependency
        name="com.android.databinding:viewbinding:8.7.2@aar"
        simpleName="com.android.databinding:viewbinding"/>
    <dependency
        name="com.squareup.okhttp3:okhttp:4.12.0@jar"
        simpleName="com.squareup.okhttp3:okhttp"/>
    <dependency
        name="com.squareup.okio:okio-jvm:3.6.0@jar"
        simpleName="com.squareup.okio:okio-jvm"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.10@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk8"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.10@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk7"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib:2.0.21@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib"/>
    <dependency
        name="com.google.code.gson:gson:2.9.0@jar"
        simpleName="com.google.code.gson:gson"/>
    <dependency
        name="com.android.support:design:28.0.0@aar"
        simpleName="com.android.support:design"/>
    <dependency
        name="com.android.support:appcompat-v7:28.0.0@aar"
        simpleName="com.android.support:appcompat-v7"/>
    <dependency
        name="com.android.support.constraint:constraint-layout:1.1.3@aar"
        simpleName="com.android.support.constraint:constraint-layout"/>
    <dependency
        name="com.android.support:support-v4:28.0.0@aar"
        simpleName="com.android.support:support-v4"/>
    <dependency
        name="com.android.support:recyclerview-v7:28.0.0@aar"
        simpleName="com.android.support:recyclerview-v7"/>
    <dependency
        name="io.github.aliyun-sls:aliyun-log-android-sdk:2.7.0@aar"
        simpleName="io.github.aliyun-sls:aliyun-log-android-sdk"/>
    <dependency
        name="com.hihonor.mcs:ads-identifier:1.0.2.301@aar"
        simpleName="com.hihonor.mcs:ads-identifier"/>
    <dependency
        name="com.android.support:multidex:1.0.3@aar"
        simpleName="com.android.support:multidex"/>
    <dependency
        name="com.tencent.mm.opensdk:wechat-sdk-android:6.8.28@aar"
        simpleName="com.tencent.mm.opensdk:wechat-sdk-android"/>
    <dependency
        name="com.android.support:support-fragment:28.0.0@aar"
        simpleName="com.android.support:support-fragment"/>
    <dependency
        name="com.android.support:animated-vector-drawable:28.0.0@aar"
        simpleName="com.android.support:animated-vector-drawable"/>
    <dependency
        name="com.android.support:support-core-ui:28.0.0@aar"
        simpleName="com.android.support:support-core-ui"/>
    <dependency
        name="com.android.support:support-core-utils:28.0.0@aar"
        simpleName="com.android.support:support-core-utils"/>
    <dependency
        name="com.android.support:support-vector-drawable:28.0.0@aar"
        simpleName="com.android.support:support-vector-drawable"/>
    <dependency
        name="com.android.support:support-media-compat:28.0.0@aar"
        simpleName="com.android.support:support-media-compat"/>
    <dependency
        name="com.android.support:transition:28.0.0@aar"
        simpleName="com.android.support:transition"/>
    <dependency
        name="com.android.support:loader:28.0.0@aar"
        simpleName="com.android.support:loader"/>
    <dependency
        name="com.android.support:viewpager:28.0.0@aar"
        simpleName="com.android.support:viewpager"/>
    <dependency
        name="com.android.support:coordinatorlayout:28.0.0@aar"
        simpleName="com.android.support:coordinatorlayout"/>
    <dependency
        name="com.android.support:drawerlayout:28.0.0@aar"
        simpleName="com.android.support:drawerlayout"/>
    <dependency
        name="com.android.support:slidingpanelayout:28.0.0@aar"
        simpleName="com.android.support:slidingpanelayout"/>
    <dependency
        name="com.android.support:customview:28.0.0@aar"
        simpleName="com.android.support:customview"/>
    <dependency
        name="com.android.support:swiperefreshlayout:28.0.0@aar"
        simpleName="com.android.support:swiperefreshlayout"/>
    <dependency
        name="com.android.support:asynclayoutinflater:28.0.0@aar"
        simpleName="com.android.support:asynclayoutinflater"/>
    <dependency
        name="com.android.support:support-compat:28.0.0@aar"
        simpleName="com.android.support:support-compat"/>
    <dependency
        name="com.android.support:versionedparcelable:28.0.0@aar"
        simpleName="com.android.support:versionedparcelable"/>
    <dependency
        name="com.android.support:collections:28.0.0@jar"
        simpleName="com.android.support:collections"/>
    <dependency
        name="com.android.support:cursoradapter:28.0.0@aar"
        simpleName="com.android.support:cursoradapter"/>
    <dependency
        name="com.android.support:cardview-v7:28.0.0@aar"
        simpleName="com.android.support:cardview-v7"/>
    <dependency
        name="android.arch.lifecycle:runtime:1.1.1@aar"
        simpleName="android.arch.lifecycle:runtime"/>
    <dependency
        name="com.android.support:documentfile:28.0.0@aar"
        simpleName="com.android.support:documentfile"/>
    <dependency
        name="com.android.support:localbroadcastmanager:28.0.0@aar"
        simpleName="com.android.support:localbroadcastmanager"/>
    <dependency
        name="com.android.support:print:28.0.0@aar"
        simpleName="com.android.support:print"/>
    <dependency
        name="android.arch.lifecycle:viewmodel:1.1.1@aar"
        simpleName="android.arch.lifecycle:viewmodel"/>
    <dependency
        name="com.android.support:interpolator:28.0.0@aar"
        simpleName="com.android.support:interpolator"/>
    <dependency
        name="android.arch.lifecycle:livedata:1.1.1@aar"
        simpleName="android.arch.lifecycle:livedata"/>
    <dependency
        name="android.arch.lifecycle:livedata-core:1.1.1@aar"
        simpleName="android.arch.lifecycle:livedata-core"/>
    <dependency
        name="android.arch.lifecycle:common:1.1.1@jar"
        simpleName="android.arch.lifecycle:common"/>
    <dependency
        name="android.arch.core:runtime:1.1.1@aar"
        simpleName="android.arch.core:runtime"/>
    <dependency
        name="android.arch.core:common:1.1.1@jar"
        simpleName="android.arch.core:common"/>
    <dependency
        name="com.android.support:support-annotations:28.0.0@jar"
        simpleName="com.android.support:support-annotations"/>
    <dependency
        name="org.jetbrains:annotations:13.0@jar"
        simpleName="org.jetbrains:annotations"/>
    <dependency
        name="com.android.support.constraint:constraint-layout-solver:1.1.3@jar"
        simpleName="com.android.support.constraint:constraint-layout-solver"/>
  </package>
</dependencies>
