{"logs": [{"outputFile": "com.ainative.mountainsurvival.app-mergeReleaseResources-22:/values-it/values-it.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\daefd50eed0506e326ae247974906cd2\\transformed\\support-compat-28.0.0\\res\\values-it\\values-it.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2906", "endColumns": "100", "endOffsets": "3002"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a3ae461f31008f99a87e29f3c99a3c83\\transformed\\appcompat-v7-28.0.0\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,314,423,507,616,741,818,894,986,1080,1174,1268,1370,1464,1561,1667,1759,1851,1932,2038,2146,2244,2348,2453,2560,2723,2823", "endColumns": "108,99,108,83,108,124,76,75,91,93,93,93,101,93,96,105,91,91,80,105,107,97,103,104,106,162,99,82", "endOffsets": "209,309,418,502,611,736,813,889,981,1075,1169,1263,1365,1459,1556,1662,1754,1846,1927,2033,2141,2239,2343,2448,2555,2718,2818,2901"}}]}]}