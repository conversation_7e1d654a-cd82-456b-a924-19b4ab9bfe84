// Generated by view binder compiler. Do not edit!
package com.ainative.mountainsurvival.databinding;

import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.viewbinding.ViewBinding;
import android.viewbinding.ViewBindings;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.TextView;
import com.ainative.mountainsurvival.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityPrivacyPolicyBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final Button agreeButton;

  @NonNull
  public final Button disagreeButton;

  @NonNull
  public final TextView privacyContentTextView;

  private ActivityPrivacyPolicyBinding(@NonNull LinearLayout rootView, @NonNull Button agreeButton,
      @NonNull Button disagreeButton, @NonNull TextView privacyContentTextView) {
    this.rootView = rootView;
    this.agreeButton = agreeButton;
    this.disagreeButton = disagreeButton;
    this.privacyContentTextView = privacyContentTextView;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityPrivacyPolicyBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityPrivacyPolicyBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_privacy_policy, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityPrivacyPolicyBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.agreeButton;
      Button agreeButton = ViewBindings.findChildViewById(rootView, id);
      if (agreeButton == null) {
        break missingId;
      }

      id = R.id.disagreeButton;
      Button disagreeButton = ViewBindings.findChildViewById(rootView, id);
      if (disagreeButton == null) {
        break missingId;
      }

      id = R.id.privacyContentTextView;
      TextView privacyContentTextView = ViewBindings.findChildViewById(rootView, id);
      if (privacyContentTextView == null) {
        break missingId;
      }

      return new ActivityPrivacyPolicyBinding((LinearLayout) rootView, agreeButton, disagreeButton,
          privacyContentTextView);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
