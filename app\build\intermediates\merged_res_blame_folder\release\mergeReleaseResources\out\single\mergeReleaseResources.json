[{"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\mipmap-xhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-main-25:\\mipmap-xhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\raw_winter.mp3.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-main-25:\\raw\\winter.mp3"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\drawable_background1.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-main-25:\\drawable\\background1.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\mipmap-hdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-main-25:\\mipmap-hdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\drawable_game_overlay.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-main-25:\\drawable\\game_overlay.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\mipmap-xxhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-main-25:\\mipmap-xxhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\mipmap-hdpi_ic_launcher_foreground.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-main-25:\\mipmap-hdpi\\ic_launcher_foreground.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\drawable_start_overlay.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-main-25:\\drawable\\start_overlay.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\mipmap-anydpi-v26_ic_launcher.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-main-25:\\mipmap-anydpi-v26\\ic_launcher.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\xml_file_paths.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-main-25:\\xml\\file_paths.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\drawable_button_black_frame.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-main-25:\\drawable\\button_black_frame.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\mipmap-xxxhdpi_ic_launcher_foreground.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-main-25:\\mipmap-xxxhdpi\\ic_launcher_foreground.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\drawable_button_background_black.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-main-25:\\drawable\\button_background_black.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\drawable_background2.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-main-25:\\drawable\\background2.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\mipmap-mdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-main-25:\\mipmap-mdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\xml_backup_rules.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-main-25:\\xml\\backup_rules.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\xml_network_security_config.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-main-25:\\xml\\network_security_config.xml"}, {"merged": "com.ainative.mountainsurvival.app-release-24:/layout_activity_start.xml.flat", "source": "com.ainative.mountainsurvival.app-main-25:/layout/activity_start.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\drawable_title.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-main-25:\\drawable\\title.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\drawable_button_background_gray.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-main-25:\\drawable\\button_background_gray.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\xml_oset_file_paths.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-main-25:\\xml\\oset_file_paths.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\mipmap-xxhdpi_ic_launcher_foreground.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-main-25:\\mipmap-xxhdpi\\ic_launcher_foreground.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\mipmap-hdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-main-25:\\mipmap-hdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\drawable_button_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-main-25:\\drawable\\button_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\layout_activity_main.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-main-25:\\layout\\activity_main.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\mipmap-xxxhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-main-25:\\mipmap-xxxhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\mipmap-xhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-main-25:\\mipmap-xhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\layout_activity_start.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-main-25:\\layout\\activity_start.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\drawable_ic_launcher_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-main-25:\\drawable\\ic_launcher_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\drawable_dialog_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-main-25:\\drawable\\dialog_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\mipmap-xxxhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-main-25:\\mipmap-xxxhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\layout_activity_privacy_policy.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-main-25:\\layout\\activity_privacy_policy.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\drawable_button_background_secondary.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-main-25:\\drawable\\button_background_secondary.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\drawable_ic_launcher_foreground.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-main-25:\\drawable\\ic_launcher_foreground.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\mipmap-mdpi_ic_launcher_foreground.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-main-25:\\mipmap-mdpi\\ic_launcher_foreground.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\mipmap-xxhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-main-25:\\mipmap-xxhdpi\\ic_launcher_round.webp"}, {"merged": "com.ainative.mountainsurvival.app-release-24:/drawable_title_en.png.flat", "source": "com.ainative.mountainsurvival.app-main-25:/drawable/title_en.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\xml_data_extraction_rules.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-main-25:\\xml\\data_extraction_rules.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\mipmap-anydpi-v26_ic_launcher_round.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-main-25:\\mipmap-anydpi-v26\\ic_launcher_round.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\mipmap-xhdpi_ic_launcher_foreground.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-main-25:\\mipmap-xhdpi\\ic_launcher_foreground.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-release-24:\\mipmap-mdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-main-25:\\mipmap-mdpi\\ic_launcher.webp"}]