{"logs": [{"outputFile": "com.ainative.mountainsurvival.app-mergeReleaseResources-22:/values-v23/values-v23.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b255ac174ecb581f0983b73e2410e4dd\\transformed\\cardview-v7-28.0.0\\res\\values-v23\\values-v23.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endLines": "4", "endColumns": "12", "endOffsets": "200"}, "to": {"startLines": "37", "startColumns": "4", "startOffsets": "2354", "endLines": "39", "endColumns": "12", "endOffsets": "2499"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a3ae461f31008f99a87e29f3c99a3c83\\transformed\\appcompat-v7-28.0.0\\res\\values-v23\\values-v23.xml", "from": {"startLines": "2,3,4,5,6,19,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,190,325,400,487,1277,2079,2206,2311,2426,2533", "endLines": "2,3,4,5,18,31,32,33,34,35,36", "endColumns": "134,134,74,86,12,12,126,104,114,106,112", "endOffsets": "185,320,395,482,1272,2074,2201,2306,2421,2528,2641"}, "to": {"startLines": "2,3,4,5,6,19,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,190,325,400,487,1131,1787,1914,2019,2134,2241", "endLines": "2,3,4,5,18,31,32,33,34,35,36", "endColumns": "134,134,74,86,12,12,126,104,114,106,112", "endOffsets": "185,320,395,482,1126,1782,1909,2014,2129,2236,2349"}}]}]}