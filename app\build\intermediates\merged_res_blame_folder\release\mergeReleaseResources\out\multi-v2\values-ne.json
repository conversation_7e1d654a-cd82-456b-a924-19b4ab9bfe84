{"logs": [{"outputFile": "com.ainative.mountainsurvival.app-mergeReleaseResources-22:/values-ne/values-ne.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\daefd50eed0506e326ae247974906cd2\\transformed\\support-compat-28.0.0\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2988", "endColumns": "100", "endOffsets": "3084"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a3ae461f31008f99a87e29f3c99a3c83\\transformed\\appcompat-v7-28.0.0\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,314,422,513,620,747,841,931,1022,1115,1211,1305,1406,1499,1594,1688,1779,1870,1958,2068,2184,2287,2402,2504,2619,2790,2902", "endColumns": "104,103,107,90,106,126,93,89,90,92,95,93,100,92,94,93,90,90,87,109,115,102,114,101,114,170,111,85", "endOffsets": "205,309,417,508,615,742,836,926,1017,1110,1206,1300,1401,1494,1589,1683,1774,1865,1953,2063,2179,2282,2397,2499,2614,2785,2897,2983"}}]}]}