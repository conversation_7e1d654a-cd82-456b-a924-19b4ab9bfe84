  holo_blue_dark android.R.color  Activity android.app  Application android.app  ActivityMainBinding android.app.Activity  ActivityPrivacyPolicyBinding android.app.Activity  ActivityStartBinding android.app.Activity  	AdManager android.app.Activity  AlertDialog android.app.Activity  Choice android.app.Activity  
ClickableSpan android.app.Activity  Double android.app.Activity  	Exception android.app.Activity  Float android.app.Activity  ForegroundColorSpan android.app.Activity  
GameEngine android.app.Activity  GameEventUtils android.app.Activity  GameLoopTest android.app.Activity  GameManager android.app.Activity  	GameState android.app.Activity  GameUIState android.app.Activity  Int android.app.Activity  Intent android.app.Activity  LinkMovementMethod android.app.Activity  Log android.app.Activity  Long android.app.Activity  MODE_PRIVATE android.app.Activity  MainActivity android.app.Activity  MountainSurvivalApplication android.app.Activity  PRIVACY_POLICY_REQUEST_CODE android.app.Activity  PRIVACY_POLICY_URL android.app.Activity  Pair android.app.Activity  PrivacyAuditLogger android.app.Activity  PrivacyPolicyActivity android.app.Activity  R android.app.Activity  RESULT_CANCELED android.app.Activity  	RESULT_OK android.app.Activity  SpannableString android.app.Activity  Spanned android.app.Activity  String android.app.Activity  System android.app.Activity  TAG android.app.Activity  Triple android.app.Activity  Uri android.app.Activity  View android.app.Activity  android android.app.Activity  any android.app.Activity  application android.app.Activity  applyChoice android.app.Activity  
checkGameOver android.app.Activity  coerceAtMost android.app.Activity  
component1 android.app.Activity  
component2 android.app.Activity  contains android.app.Activity  	emptyList android.app.Activity  emptyMap android.app.Activity  filter android.app.Activity  finish android.app.Activity  forEachIndexed android.app.Activity  generateComplianceReport android.app.Activity  getEvent android.app.Activity  getEventMapStatistics android.app.Activity  getGameOverReason android.app.Activity  giveResourceReward android.app.Activity  indexOf android.app.Activity  indices android.app.Activity  initializeGame android.app.Activity  isChoiceAvailable android.app.Activity  
isNotEmpty android.app.Activity  
isNullOrEmpty android.app.Activity  	isVictory android.app.Activity  java android.app.Activity  joinToString android.app.Activity  layoutInflater android.app.Activity  let android.app.Activity  listOf android.app.Activity  
loadEvents android.app.Activity  mapOf android.app.Activity  
mutableListOf android.app.Activity  mutableMapOf android.app.Activity  onActivityResult android.app.Activity  onCreate android.app.Activity  openPrivacyPolicyUrl android.app.Activity  packageName android.app.Activity  performNightPhase android.app.Activity  preloadRewardVideo android.app.Activity  	resetGame android.app.Activity  reviveCharacter android.app.Activity  run android.app.Activity  set android.app.Activity  setGameState android.app.Activity  	setResult android.app.Activity  showAdErrorDialog android.app.Activity  showReviveAd android.app.Activity  showReviveSuccessDialog android.app.Activity  showRewardDialog android.app.Activity  showRewardVideoForResource android.app.Activity  split android.app.Activity  
startActivity android.app.Activity  take android.app.Activity  testGameLoop android.app.Activity  to android.app.Activity  toIntOrNull android.app.Activity  toList android.app.Activity  toMutableMap android.app.Activity  toMutableSet android.app.Activity  trim android.app.Activity  
trimIndent android.app.Activity  validateEventMap android.app.Activity  
AdCallback android.app.Activity.AdManager  ResourceType android.app.Activity.AdManager  ReviveAdCallback android.app.Activity.AdManager  ChoiceResult  android.app.Activity.GameManager  APP_KEY android.app.Application  Build android.app.Application  	Exception android.app.Application  Log android.app.Application  OSETInitListener android.app.Application  OSETSDK android.app.Application  PrivacyComplianceManager android.app.Application  REWARD_VIDEO_AD_ID android.app.Application  String android.app.Application  TAG android.app.Application  WebView android.app.Application  createCustomController android.app.Application  getProcessName android.app.Application  isAdSDKInitialized android.app.Application  onCreate android.app.Application  show android.app.Dialog  Context android.content  Intent android.content  APP_KEY android.content.Context  ActivityMainBinding android.content.Context  ActivityPrivacyPolicyBinding android.content.Context  ActivityStartBinding android.content.Context  	AdManager android.content.Context  AlertDialog android.content.Context  Build android.content.Context  Choice android.content.Context  
ClickableSpan android.content.Context  Double android.content.Context  	Exception android.content.Context  Float android.content.Context  ForegroundColorSpan android.content.Context  
GameEngine android.content.Context  GameEventUtils android.content.Context  GameLoopTest android.content.Context  GameManager android.content.Context  	GameState android.content.Context  GameUIState android.content.Context  Int android.content.Context  Intent android.content.Context  LinkMovementMethod android.content.Context  Log android.content.Context  Long android.content.Context  MODE_PRIVATE android.content.Context  MainActivity android.content.Context  MountainSurvivalApplication android.content.Context  OSETInitListener android.content.Context  OSETSDK android.content.Context  PRIVACY_POLICY_REQUEST_CODE android.content.Context  PRIVACY_POLICY_URL android.content.Context  Pair android.content.Context  PrivacyAuditLogger android.content.Context  PrivacyComplianceManager android.content.Context  PrivacyPolicyActivity android.content.Context  R android.content.Context  RESULT_CANCELED android.content.Context  	RESULT_OK android.content.Context  REWARD_VIDEO_AD_ID android.content.Context  SpannableString android.content.Context  Spanned android.content.Context  String android.content.Context  System android.content.Context  TAG android.content.Context  Triple android.content.Context  Uri android.content.Context  View android.content.Context  WebView android.content.Context  android android.content.Context  any android.content.Context  applicationContext android.content.Context  applyChoice android.content.Context  assets android.content.Context  
checkGameOver android.content.Context  coerceAtMost android.content.Context  
component1 android.content.Context  
component2 android.content.Context  contains android.content.Context  createCustomController android.content.Context  	emptyList android.content.Context  emptyMap android.content.Context  filter android.content.Context  forEachIndexed android.content.Context  generateComplianceReport android.content.Context  getEvent android.content.Context  getEventMapStatistics android.content.Context  getGameOverReason android.content.Context  getProcessName android.content.Context  getSharedPreferences android.content.Context  	getString android.content.Context  giveResourceReward android.content.Context  indexOf android.content.Context  indices android.content.Context  initializeGame android.content.Context  isAdSDKInitialized android.content.Context  isChoiceAvailable android.content.Context  
isNotEmpty android.content.Context  
isNullOrEmpty android.content.Context  	isVictory android.content.Context  java android.content.Context  joinToString android.content.Context  let android.content.Context  listOf android.content.Context  
loadEvents android.content.Context  mapOf android.content.Context  
mutableListOf android.content.Context  mutableMapOf android.content.Context  openPrivacyPolicyUrl android.content.Context  packageName android.content.Context  performNightPhase android.content.Context  preloadRewardVideo android.content.Context  	resetGame android.content.Context  reviveCharacter android.content.Context  run android.content.Context  set android.content.Context  setGameState android.content.Context  showAdErrorDialog android.content.Context  showReviveAd android.content.Context  showReviveSuccessDialog android.content.Context  showRewardDialog android.content.Context  showRewardVideoForResource android.content.Context  split android.content.Context  take android.content.Context  testGameLoop android.content.Context  to android.content.Context  toIntOrNull android.content.Context  toList android.content.Context  toMutableMap android.content.Context  toMutableSet android.content.Context  trim android.content.Context  
trimIndent android.content.Context  validateEventMap android.content.Context  
AdCallback !android.content.Context.AdManager  ResourceType !android.content.Context.AdManager  ReviveAdCallback !android.content.Context.AdManager  ChoiceResult #android.content.Context.GameManager  APP_KEY android.content.ContextWrapper  ActivityMainBinding android.content.ContextWrapper  ActivityPrivacyPolicyBinding android.content.ContextWrapper  ActivityStartBinding android.content.ContextWrapper  	AdManager android.content.ContextWrapper  AlertDialog android.content.ContextWrapper  Build android.content.ContextWrapper  Choice android.content.ContextWrapper  
ClickableSpan android.content.ContextWrapper  Double android.content.ContextWrapper  	Exception android.content.ContextWrapper  Float android.content.ContextWrapper  ForegroundColorSpan android.content.ContextWrapper  
GameEngine android.content.ContextWrapper  GameEventUtils android.content.ContextWrapper  GameLoopTest android.content.ContextWrapper  GameManager android.content.ContextWrapper  	GameState android.content.ContextWrapper  GameUIState android.content.ContextWrapper  Int android.content.ContextWrapper  Intent android.content.ContextWrapper  LinkMovementMethod android.content.ContextWrapper  Log android.content.ContextWrapper  Long android.content.ContextWrapper  MODE_PRIVATE android.content.ContextWrapper  MainActivity android.content.ContextWrapper  MountainSurvivalApplication android.content.ContextWrapper  OSETInitListener android.content.ContextWrapper  OSETSDK android.content.ContextWrapper  PRIVACY_POLICY_REQUEST_CODE android.content.ContextWrapper  PRIVACY_POLICY_URL android.content.ContextWrapper  Pair android.content.ContextWrapper  PrivacyAuditLogger android.content.ContextWrapper  PrivacyComplianceManager android.content.ContextWrapper  PrivacyPolicyActivity android.content.ContextWrapper  R android.content.ContextWrapper  RESULT_CANCELED android.content.ContextWrapper  	RESULT_OK android.content.ContextWrapper  REWARD_VIDEO_AD_ID android.content.ContextWrapper  SpannableString android.content.ContextWrapper  Spanned android.content.ContextWrapper  String android.content.ContextWrapper  System android.content.ContextWrapper  TAG android.content.ContextWrapper  Triple android.content.ContextWrapper  Uri android.content.ContextWrapper  View android.content.ContextWrapper  WebView android.content.ContextWrapper  android android.content.ContextWrapper  any android.content.ContextWrapper  applyChoice android.content.ContextWrapper  
checkGameOver android.content.ContextWrapper  coerceAtMost android.content.ContextWrapper  
component1 android.content.ContextWrapper  
component2 android.content.ContextWrapper  contains android.content.ContextWrapper  createCustomController android.content.ContextWrapper  	emptyList android.content.ContextWrapper  emptyMap android.content.ContextWrapper  filter android.content.ContextWrapper  forEachIndexed android.content.ContextWrapper  generateComplianceReport android.content.ContextWrapper  getEvent android.content.ContextWrapper  getEventMapStatistics android.content.ContextWrapper  getGameOverReason android.content.ContextWrapper  getProcessName android.content.ContextWrapper  getSharedPreferences android.content.ContextWrapper  giveResourceReward android.content.ContextWrapper  indexOf android.content.ContextWrapper  indices android.content.ContextWrapper  initializeGame android.content.ContextWrapper  isAdSDKInitialized android.content.ContextWrapper  isChoiceAvailable android.content.ContextWrapper  
isNotEmpty android.content.ContextWrapper  
isNullOrEmpty android.content.ContextWrapper  	isVictory android.content.ContextWrapper  java android.content.ContextWrapper  joinToString android.content.ContextWrapper  let android.content.ContextWrapper  listOf android.content.ContextWrapper  
loadEvents android.content.ContextWrapper  mapOf android.content.ContextWrapper  
mutableListOf android.content.ContextWrapper  mutableMapOf android.content.ContextWrapper  openPrivacyPolicyUrl android.content.ContextWrapper  packageName android.content.ContextWrapper  performNightPhase android.content.ContextWrapper  preloadRewardVideo android.content.ContextWrapper  	resetGame android.content.ContextWrapper  reviveCharacter android.content.ContextWrapper  run android.content.ContextWrapper  set android.content.ContextWrapper  setGameState android.content.ContextWrapper  showAdErrorDialog android.content.ContextWrapper  showReviveAd android.content.ContextWrapper  showReviveSuccessDialog android.content.ContextWrapper  showRewardDialog android.content.ContextWrapper  showRewardVideoForResource android.content.ContextWrapper  split android.content.ContextWrapper  take android.content.ContextWrapper  testGameLoop android.content.ContextWrapper  to android.content.ContextWrapper  toIntOrNull android.content.ContextWrapper  toList android.content.ContextWrapper  toMutableMap android.content.ContextWrapper  toMutableSet android.content.ContextWrapper  trim android.content.ContextWrapper  
trimIndent android.content.ContextWrapper  validateEventMap android.content.ContextWrapper  
AdCallback (android.content.ContextWrapper.AdManager  ResourceType (android.content.ContextWrapper.AdManager  ReviveAdCallback (android.content.ContextWrapper.AdManager  ChoiceResult *android.content.ContextWrapper.GameManager  OnClickListener android.content.DialogInterface  dismiss android.content.DialogInterface  <SAM-CONSTRUCTOR> /android.content.DialogInterface.OnClickListener  ACTION_VIEW android.content.Intent  edit !android.content.SharedPreferences  
getBoolean !android.content.SharedPreferences  getLong !android.content.SharedPreferences  apply (android.content.SharedPreferences.Editor  
putBoolean (android.content.SharedPreferences.Editor  putLong (android.content.SharedPreferences.Editor  open  android.content.res.AssetManager  getColor android.content.res.Resources  Location android.location  Uri android.net  parse android.net.Uri  Build 
android.os  Bundle 
android.os  SDK_INT android.os.Build.VERSION  P android.os.Build.VERSION_CODES  ConstraintLayout android.support.constraint  MultiDexApplication android.support.multidex  APP_KEY ,android.support.multidex.MultiDexApplication  Build ,android.support.multidex.MultiDexApplication  	Exception ,android.support.multidex.MultiDexApplication  Log ,android.support.multidex.MultiDexApplication  OSETInitListener ,android.support.multidex.MultiDexApplication  OSETSDK ,android.support.multidex.MultiDexApplication  PrivacyComplianceManager ,android.support.multidex.MultiDexApplication  REWARD_VIDEO_AD_ID ,android.support.multidex.MultiDexApplication  String ,android.support.multidex.MultiDexApplication  TAG ,android.support.multidex.MultiDexApplication  WebView ,android.support.multidex.MultiDexApplication  attachBaseContext ,android.support.multidex.MultiDexApplication  createCustomController ,android.support.multidex.MultiDexApplication  getProcessName ,android.support.multidex.MultiDexApplication  isAdSDKInitialized ,android.support.multidex.MultiDexApplication  onCreate ,android.support.multidex.MultiDexApplication  ActivityMainBinding 'android.support.v4.app.FragmentActivity  ActivityPrivacyPolicyBinding 'android.support.v4.app.FragmentActivity  ActivityStartBinding 'android.support.v4.app.FragmentActivity  	AdManager 'android.support.v4.app.FragmentActivity  AlertDialog 'android.support.v4.app.FragmentActivity  Choice 'android.support.v4.app.FragmentActivity  
ClickableSpan 'android.support.v4.app.FragmentActivity  Double 'android.support.v4.app.FragmentActivity  	Exception 'android.support.v4.app.FragmentActivity  Float 'android.support.v4.app.FragmentActivity  ForegroundColorSpan 'android.support.v4.app.FragmentActivity  
GameEngine 'android.support.v4.app.FragmentActivity  GameEventUtils 'android.support.v4.app.FragmentActivity  GameLoopTest 'android.support.v4.app.FragmentActivity  GameManager 'android.support.v4.app.FragmentActivity  	GameState 'android.support.v4.app.FragmentActivity  GameUIState 'android.support.v4.app.FragmentActivity  Int 'android.support.v4.app.FragmentActivity  Intent 'android.support.v4.app.FragmentActivity  LinkMovementMethod 'android.support.v4.app.FragmentActivity  Log 'android.support.v4.app.FragmentActivity  Long 'android.support.v4.app.FragmentActivity  MODE_PRIVATE 'android.support.v4.app.FragmentActivity  MainActivity 'android.support.v4.app.FragmentActivity  MountainSurvivalApplication 'android.support.v4.app.FragmentActivity  PRIVACY_POLICY_REQUEST_CODE 'android.support.v4.app.FragmentActivity  PRIVACY_POLICY_URL 'android.support.v4.app.FragmentActivity  Pair 'android.support.v4.app.FragmentActivity  PrivacyAuditLogger 'android.support.v4.app.FragmentActivity  PrivacyPolicyActivity 'android.support.v4.app.FragmentActivity  R 'android.support.v4.app.FragmentActivity  RESULT_CANCELED 'android.support.v4.app.FragmentActivity  	RESULT_OK 'android.support.v4.app.FragmentActivity  SpannableString 'android.support.v4.app.FragmentActivity  Spanned 'android.support.v4.app.FragmentActivity  String 'android.support.v4.app.FragmentActivity  System 'android.support.v4.app.FragmentActivity  TAG 'android.support.v4.app.FragmentActivity  Triple 'android.support.v4.app.FragmentActivity  Uri 'android.support.v4.app.FragmentActivity  View 'android.support.v4.app.FragmentActivity  android 'android.support.v4.app.FragmentActivity  any 'android.support.v4.app.FragmentActivity  applyChoice 'android.support.v4.app.FragmentActivity  
checkGameOver 'android.support.v4.app.FragmentActivity  coerceAtMost 'android.support.v4.app.FragmentActivity  
component1 'android.support.v4.app.FragmentActivity  
component2 'android.support.v4.app.FragmentActivity  contains 'android.support.v4.app.FragmentActivity  	emptyList 'android.support.v4.app.FragmentActivity  emptyMap 'android.support.v4.app.FragmentActivity  filter 'android.support.v4.app.FragmentActivity  forEachIndexed 'android.support.v4.app.FragmentActivity  generateComplianceReport 'android.support.v4.app.FragmentActivity  getEvent 'android.support.v4.app.FragmentActivity  getEventMapStatistics 'android.support.v4.app.FragmentActivity  getGameOverReason 'android.support.v4.app.FragmentActivity  giveResourceReward 'android.support.v4.app.FragmentActivity  indexOf 'android.support.v4.app.FragmentActivity  indices 'android.support.v4.app.FragmentActivity  initializeGame 'android.support.v4.app.FragmentActivity  isChoiceAvailable 'android.support.v4.app.FragmentActivity  
isNotEmpty 'android.support.v4.app.FragmentActivity  
isNullOrEmpty 'android.support.v4.app.FragmentActivity  	isVictory 'android.support.v4.app.FragmentActivity  java 'android.support.v4.app.FragmentActivity  joinToString 'android.support.v4.app.FragmentActivity  let 'android.support.v4.app.FragmentActivity  listOf 'android.support.v4.app.FragmentActivity  
loadEvents 'android.support.v4.app.FragmentActivity  mapOf 'android.support.v4.app.FragmentActivity  
mutableListOf 'android.support.v4.app.FragmentActivity  mutableMapOf 'android.support.v4.app.FragmentActivity  onActivityResult 'android.support.v4.app.FragmentActivity  onResume 'android.support.v4.app.FragmentActivity  openPrivacyPolicyUrl 'android.support.v4.app.FragmentActivity  performNightPhase 'android.support.v4.app.FragmentActivity  preloadRewardVideo 'android.support.v4.app.FragmentActivity  	resetGame 'android.support.v4.app.FragmentActivity  reviveCharacter 'android.support.v4.app.FragmentActivity  run 'android.support.v4.app.FragmentActivity  set 'android.support.v4.app.FragmentActivity  setGameState 'android.support.v4.app.FragmentActivity  showAdErrorDialog 'android.support.v4.app.FragmentActivity  showReviveAd 'android.support.v4.app.FragmentActivity  showReviveSuccessDialog 'android.support.v4.app.FragmentActivity  showRewardDialog 'android.support.v4.app.FragmentActivity  showRewardVideoForResource 'android.support.v4.app.FragmentActivity  split 'android.support.v4.app.FragmentActivity  startActivityForResult 'android.support.v4.app.FragmentActivity  take 'android.support.v4.app.FragmentActivity  testGameLoop 'android.support.v4.app.FragmentActivity  to 'android.support.v4.app.FragmentActivity  toIntOrNull 'android.support.v4.app.FragmentActivity  toList 'android.support.v4.app.FragmentActivity  toMutableMap 'android.support.v4.app.FragmentActivity  toMutableSet 'android.support.v4.app.FragmentActivity  trim 'android.support.v4.app.FragmentActivity  
trimIndent 'android.support.v4.app.FragmentActivity  validateEventMap 'android.support.v4.app.FragmentActivity  
AdCallback 1android.support.v4.app.FragmentActivity.AdManager  ResourceType 1android.support.v4.app.FragmentActivity.AdManager  ReviveAdCallback 1android.support.v4.app.FragmentActivity.AdManager  ChoiceResult 3android.support.v4.app.FragmentActivity.GameManager  ActivityMainBinding &android.support.v4.app.SupportActivity  ActivityPrivacyPolicyBinding &android.support.v4.app.SupportActivity  ActivityStartBinding &android.support.v4.app.SupportActivity  	AdManager &android.support.v4.app.SupportActivity  AlertDialog &android.support.v4.app.SupportActivity  Choice &android.support.v4.app.SupportActivity  
ClickableSpan &android.support.v4.app.SupportActivity  Double &android.support.v4.app.SupportActivity  	Exception &android.support.v4.app.SupportActivity  Float &android.support.v4.app.SupportActivity  ForegroundColorSpan &android.support.v4.app.SupportActivity  
GameEngine &android.support.v4.app.SupportActivity  GameEventUtils &android.support.v4.app.SupportActivity  GameLoopTest &android.support.v4.app.SupportActivity  GameManager &android.support.v4.app.SupportActivity  	GameState &android.support.v4.app.SupportActivity  GameUIState &android.support.v4.app.SupportActivity  Int &android.support.v4.app.SupportActivity  Intent &android.support.v4.app.SupportActivity  LinkMovementMethod &android.support.v4.app.SupportActivity  Log &android.support.v4.app.SupportActivity  Long &android.support.v4.app.SupportActivity  MODE_PRIVATE &android.support.v4.app.SupportActivity  MainActivity &android.support.v4.app.SupportActivity  MountainSurvivalApplication &android.support.v4.app.SupportActivity  PRIVACY_POLICY_REQUEST_CODE &android.support.v4.app.SupportActivity  PRIVACY_POLICY_URL &android.support.v4.app.SupportActivity  Pair &android.support.v4.app.SupportActivity  PrivacyAuditLogger &android.support.v4.app.SupportActivity  PrivacyPolicyActivity &android.support.v4.app.SupportActivity  R &android.support.v4.app.SupportActivity  RESULT_CANCELED &android.support.v4.app.SupportActivity  	RESULT_OK &android.support.v4.app.SupportActivity  SpannableString &android.support.v4.app.SupportActivity  Spanned &android.support.v4.app.SupportActivity  String &android.support.v4.app.SupportActivity  System &android.support.v4.app.SupportActivity  TAG &android.support.v4.app.SupportActivity  Triple &android.support.v4.app.SupportActivity  Uri &android.support.v4.app.SupportActivity  View &android.support.v4.app.SupportActivity  android &android.support.v4.app.SupportActivity  any &android.support.v4.app.SupportActivity  applyChoice &android.support.v4.app.SupportActivity  
checkGameOver &android.support.v4.app.SupportActivity  coerceAtMost &android.support.v4.app.SupportActivity  
component1 &android.support.v4.app.SupportActivity  
component2 &android.support.v4.app.SupportActivity  contains &android.support.v4.app.SupportActivity  	emptyList &android.support.v4.app.SupportActivity  emptyMap &android.support.v4.app.SupportActivity  filter &android.support.v4.app.SupportActivity  forEachIndexed &android.support.v4.app.SupportActivity  generateComplianceReport &android.support.v4.app.SupportActivity  getEvent &android.support.v4.app.SupportActivity  getEventMapStatistics &android.support.v4.app.SupportActivity  getGameOverReason &android.support.v4.app.SupportActivity  giveResourceReward &android.support.v4.app.SupportActivity  indexOf &android.support.v4.app.SupportActivity  indices &android.support.v4.app.SupportActivity  initializeGame &android.support.v4.app.SupportActivity  isChoiceAvailable &android.support.v4.app.SupportActivity  
isNotEmpty &android.support.v4.app.SupportActivity  
isNullOrEmpty &android.support.v4.app.SupportActivity  	isVictory &android.support.v4.app.SupportActivity  java &android.support.v4.app.SupportActivity  joinToString &android.support.v4.app.SupportActivity  let &android.support.v4.app.SupportActivity  listOf &android.support.v4.app.SupportActivity  
loadEvents &android.support.v4.app.SupportActivity  mapOf &android.support.v4.app.SupportActivity  
mutableListOf &android.support.v4.app.SupportActivity  mutableMapOf &android.support.v4.app.SupportActivity  openPrivacyPolicyUrl &android.support.v4.app.SupportActivity  performNightPhase &android.support.v4.app.SupportActivity  preloadRewardVideo &android.support.v4.app.SupportActivity  	resetGame &android.support.v4.app.SupportActivity  reviveCharacter &android.support.v4.app.SupportActivity  run &android.support.v4.app.SupportActivity  set &android.support.v4.app.SupportActivity  setGameState &android.support.v4.app.SupportActivity  showAdErrorDialog &android.support.v4.app.SupportActivity  showReviveAd &android.support.v4.app.SupportActivity  showReviveSuccessDialog &android.support.v4.app.SupportActivity  showRewardDialog &android.support.v4.app.SupportActivity  showRewardVideoForResource &android.support.v4.app.SupportActivity  split &android.support.v4.app.SupportActivity  take &android.support.v4.app.SupportActivity  testGameLoop &android.support.v4.app.SupportActivity  to &android.support.v4.app.SupportActivity  toIntOrNull &android.support.v4.app.SupportActivity  toList &android.support.v4.app.SupportActivity  toMutableMap &android.support.v4.app.SupportActivity  toMutableSet &android.support.v4.app.SupportActivity  trim &android.support.v4.app.SupportActivity  
trimIndent &android.support.v4.app.SupportActivity  validateEventMap &android.support.v4.app.SupportActivity  
AdCallback 0android.support.v4.app.SupportActivity.AdManager  ResourceType 0android.support.v4.app.SupportActivity.AdManager  ReviveAdCallback 0android.support.v4.app.SupportActivity.AdManager  ChoiceResult 2android.support.v4.app.SupportActivity.GameManager  	ActionBar android.support.v7.app  AlertDialog android.support.v7.app  AppCompatActivity android.support.v7.app  hide  android.support.v7.app.ActionBar  Builder "android.support.v7.app.AlertDialog  dismiss "android.support.v7.app.AlertDialog  create *android.support.v7.app.AlertDialog.Builder  
setCancelable *android.support.v7.app.AlertDialog.Builder  
setMessage *android.support.v7.app.AlertDialog.Builder  setPositiveButton *android.support.v7.app.AlertDialog.Builder  setTitle *android.support.v7.app.AlertDialog.Builder  show *android.support.v7.app.AlertDialog.Builder  ActivityMainBinding (android.support.v7.app.AppCompatActivity  ActivityPrivacyPolicyBinding (android.support.v7.app.AppCompatActivity  ActivityStartBinding (android.support.v7.app.AppCompatActivity  	AdManager (android.support.v7.app.AppCompatActivity  AlertDialog (android.support.v7.app.AppCompatActivity  Choice (android.support.v7.app.AppCompatActivity  
ClickableSpan (android.support.v7.app.AppCompatActivity  Double (android.support.v7.app.AppCompatActivity  	Exception (android.support.v7.app.AppCompatActivity  Float (android.support.v7.app.AppCompatActivity  ForegroundColorSpan (android.support.v7.app.AppCompatActivity  
GameEngine (android.support.v7.app.AppCompatActivity  GameEventUtils (android.support.v7.app.AppCompatActivity  GameLoopTest (android.support.v7.app.AppCompatActivity  GameManager (android.support.v7.app.AppCompatActivity  	GameState (android.support.v7.app.AppCompatActivity  GameUIState (android.support.v7.app.AppCompatActivity  Int (android.support.v7.app.AppCompatActivity  Intent (android.support.v7.app.AppCompatActivity  LinkMovementMethod (android.support.v7.app.AppCompatActivity  Log (android.support.v7.app.AppCompatActivity  Long (android.support.v7.app.AppCompatActivity  MODE_PRIVATE (android.support.v7.app.AppCompatActivity  MainActivity (android.support.v7.app.AppCompatActivity  MountainSurvivalApplication (android.support.v7.app.AppCompatActivity  PRIVACY_POLICY_REQUEST_CODE (android.support.v7.app.AppCompatActivity  PRIVACY_POLICY_URL (android.support.v7.app.AppCompatActivity  Pair (android.support.v7.app.AppCompatActivity  PrivacyAuditLogger (android.support.v7.app.AppCompatActivity  PrivacyPolicyActivity (android.support.v7.app.AppCompatActivity  R (android.support.v7.app.AppCompatActivity  RESULT_CANCELED (android.support.v7.app.AppCompatActivity  	RESULT_OK (android.support.v7.app.AppCompatActivity  SpannableString (android.support.v7.app.AppCompatActivity  Spanned (android.support.v7.app.AppCompatActivity  String (android.support.v7.app.AppCompatActivity  System (android.support.v7.app.AppCompatActivity  TAG (android.support.v7.app.AppCompatActivity  Triple (android.support.v7.app.AppCompatActivity  Uri (android.support.v7.app.AppCompatActivity  View (android.support.v7.app.AppCompatActivity  android (android.support.v7.app.AppCompatActivity  any (android.support.v7.app.AppCompatActivity  applyChoice (android.support.v7.app.AppCompatActivity  
checkGameOver (android.support.v7.app.AppCompatActivity  coerceAtMost (android.support.v7.app.AppCompatActivity  
component1 (android.support.v7.app.AppCompatActivity  
component2 (android.support.v7.app.AppCompatActivity  contains (android.support.v7.app.AppCompatActivity  	emptyList (android.support.v7.app.AppCompatActivity  emptyMap (android.support.v7.app.AppCompatActivity  filter (android.support.v7.app.AppCompatActivity  forEachIndexed (android.support.v7.app.AppCompatActivity  generateComplianceReport (android.support.v7.app.AppCompatActivity  getEvent (android.support.v7.app.AppCompatActivity  getEventMapStatistics (android.support.v7.app.AppCompatActivity  getGameOverReason (android.support.v7.app.AppCompatActivity  giveResourceReward (android.support.v7.app.AppCompatActivity  indexOf (android.support.v7.app.AppCompatActivity  indices (android.support.v7.app.AppCompatActivity  initializeGame (android.support.v7.app.AppCompatActivity  isChoiceAvailable (android.support.v7.app.AppCompatActivity  
isNotEmpty (android.support.v7.app.AppCompatActivity  
isNullOrEmpty (android.support.v7.app.AppCompatActivity  	isVictory (android.support.v7.app.AppCompatActivity  java (android.support.v7.app.AppCompatActivity  joinToString (android.support.v7.app.AppCompatActivity  let (android.support.v7.app.AppCompatActivity  listOf (android.support.v7.app.AppCompatActivity  
loadEvents (android.support.v7.app.AppCompatActivity  mapOf (android.support.v7.app.AppCompatActivity  
mutableListOf (android.support.v7.app.AppCompatActivity  mutableMapOf (android.support.v7.app.AppCompatActivity  onActivityResult (android.support.v7.app.AppCompatActivity  onCreate (android.support.v7.app.AppCompatActivity  onResume (android.support.v7.app.AppCompatActivity  openPrivacyPolicyUrl (android.support.v7.app.AppCompatActivity  performNightPhase (android.support.v7.app.AppCompatActivity  preloadRewardVideo (android.support.v7.app.AppCompatActivity  	resetGame (android.support.v7.app.AppCompatActivity  	resources (android.support.v7.app.AppCompatActivity  reviveCharacter (android.support.v7.app.AppCompatActivity  run (android.support.v7.app.AppCompatActivity  set (android.support.v7.app.AppCompatActivity  setContentView (android.support.v7.app.AppCompatActivity  setGameState (android.support.v7.app.AppCompatActivity  showAdErrorDialog (android.support.v7.app.AppCompatActivity  showReviveAd (android.support.v7.app.AppCompatActivity  showReviveSuccessDialog (android.support.v7.app.AppCompatActivity  showRewardDialog (android.support.v7.app.AppCompatActivity  showRewardVideoForResource (android.support.v7.app.AppCompatActivity  split (android.support.v7.app.AppCompatActivity  supportActionBar (android.support.v7.app.AppCompatActivity  take (android.support.v7.app.AppCompatActivity  testGameLoop (android.support.v7.app.AppCompatActivity  to (android.support.v7.app.AppCompatActivity  toIntOrNull (android.support.v7.app.AppCompatActivity  toList (android.support.v7.app.AppCompatActivity  toMutableMap (android.support.v7.app.AppCompatActivity  toMutableSet (android.support.v7.app.AppCompatActivity  trim (android.support.v7.app.AppCompatActivity  
trimIndent (android.support.v7.app.AppCompatActivity  validateEventMap (android.support.v7.app.AppCompatActivity  
AdCallback 2android.support.v7.app.AppCompatActivity.AdManager  ResourceType 2android.support.v7.app.AppCompatActivity.AdManager  ReviveAdCallback 2android.support.v7.app.AppCompatActivity.AdManager  ChoiceResult 4android.support.v7.app.AppCompatActivity.GameManager  SpannableString android.text  Spanned android.text  setSpan android.text.SpannableString  SPAN_EXCLUSIVE_EXCLUSIVE android.text.Spanned  LinkMovementMethod android.text.method  getInstance &android.text.method.LinkMovementMethod  
ClickableSpan android.text.style  ForegroundColorSpan android.text.style  openPrivacyPolicyUrl !android.text.style.CharacterStyle  openPrivacyPolicyUrl  android.text.style.ClickableSpan  Log android.util  d android.util.Log  e android.util.Log  i android.util.Log  w android.util.Log  LayoutInflater android.view  View android.view  ActivityMainBinding  android.view.ContextThemeWrapper  ActivityPrivacyPolicyBinding  android.view.ContextThemeWrapper  ActivityStartBinding  android.view.ContextThemeWrapper  	AdManager  android.view.ContextThemeWrapper  AlertDialog  android.view.ContextThemeWrapper  Choice  android.view.ContextThemeWrapper  
ClickableSpan  android.view.ContextThemeWrapper  Double  android.view.ContextThemeWrapper  	Exception  android.view.ContextThemeWrapper  Float  android.view.ContextThemeWrapper  ForegroundColorSpan  android.view.ContextThemeWrapper  
GameEngine  android.view.ContextThemeWrapper  GameEventUtils  android.view.ContextThemeWrapper  GameLoopTest  android.view.ContextThemeWrapper  GameManager  android.view.ContextThemeWrapper  	GameState  android.view.ContextThemeWrapper  GameUIState  android.view.ContextThemeWrapper  Int  android.view.ContextThemeWrapper  Intent  android.view.ContextThemeWrapper  LinkMovementMethod  android.view.ContextThemeWrapper  Log  android.view.ContextThemeWrapper  Long  android.view.ContextThemeWrapper  MODE_PRIVATE  android.view.ContextThemeWrapper  MainActivity  android.view.ContextThemeWrapper  MountainSurvivalApplication  android.view.ContextThemeWrapper  PRIVACY_POLICY_REQUEST_CODE  android.view.ContextThemeWrapper  PRIVACY_POLICY_URL  android.view.ContextThemeWrapper  Pair  android.view.ContextThemeWrapper  PrivacyAuditLogger  android.view.ContextThemeWrapper  PrivacyPolicyActivity  android.view.ContextThemeWrapper  R  android.view.ContextThemeWrapper  RESULT_CANCELED  android.view.ContextThemeWrapper  	RESULT_OK  android.view.ContextThemeWrapper  SpannableString  android.view.ContextThemeWrapper  Spanned  android.view.ContextThemeWrapper  String  android.view.ContextThemeWrapper  System  android.view.ContextThemeWrapper  TAG  android.view.ContextThemeWrapper  Triple  android.view.ContextThemeWrapper  Uri  android.view.ContextThemeWrapper  View  android.view.ContextThemeWrapper  android  android.view.ContextThemeWrapper  any  android.view.ContextThemeWrapper  applyChoice  android.view.ContextThemeWrapper  
checkGameOver  android.view.ContextThemeWrapper  coerceAtMost  android.view.ContextThemeWrapper  
component1  android.view.ContextThemeWrapper  
component2  android.view.ContextThemeWrapper  contains  android.view.ContextThemeWrapper  	emptyList  android.view.ContextThemeWrapper  emptyMap  android.view.ContextThemeWrapper  filter  android.view.ContextThemeWrapper  forEachIndexed  android.view.ContextThemeWrapper  generateComplianceReport  android.view.ContextThemeWrapper  getEvent  android.view.ContextThemeWrapper  getEventMapStatistics  android.view.ContextThemeWrapper  getGameOverReason  android.view.ContextThemeWrapper  giveResourceReward  android.view.ContextThemeWrapper  indexOf  android.view.ContextThemeWrapper  indices  android.view.ContextThemeWrapper  initializeGame  android.view.ContextThemeWrapper  isChoiceAvailable  android.view.ContextThemeWrapper  
isNotEmpty  android.view.ContextThemeWrapper  
isNullOrEmpty  android.view.ContextThemeWrapper  	isVictory  android.view.ContextThemeWrapper  java  android.view.ContextThemeWrapper  joinToString  android.view.ContextThemeWrapper  let  android.view.ContextThemeWrapper  listOf  android.view.ContextThemeWrapper  
loadEvents  android.view.ContextThemeWrapper  mapOf  android.view.ContextThemeWrapper  
mutableListOf  android.view.ContextThemeWrapper  mutableMapOf  android.view.ContextThemeWrapper  openPrivacyPolicyUrl  android.view.ContextThemeWrapper  performNightPhase  android.view.ContextThemeWrapper  preloadRewardVideo  android.view.ContextThemeWrapper  	resetGame  android.view.ContextThemeWrapper  reviveCharacter  android.view.ContextThemeWrapper  run  android.view.ContextThemeWrapper  set  android.view.ContextThemeWrapper  setGameState  android.view.ContextThemeWrapper  showAdErrorDialog  android.view.ContextThemeWrapper  showReviveAd  android.view.ContextThemeWrapper  showReviveSuccessDialog  android.view.ContextThemeWrapper  showRewardDialog  android.view.ContextThemeWrapper  showRewardVideoForResource  android.view.ContextThemeWrapper  split  android.view.ContextThemeWrapper  take  android.view.ContextThemeWrapper  testGameLoop  android.view.ContextThemeWrapper  to  android.view.ContextThemeWrapper  toIntOrNull  android.view.ContextThemeWrapper  toList  android.view.ContextThemeWrapper  toMutableMap  android.view.ContextThemeWrapper  toMutableSet  android.view.ContextThemeWrapper  trim  android.view.ContextThemeWrapper  
trimIndent  android.view.ContextThemeWrapper  validateEventMap  android.view.ContextThemeWrapper  
AdCallback *android.view.ContextThemeWrapper.AdManager  ResourceType *android.view.ContextThemeWrapper.AdManager  ReviveAdCallback *android.view.ContextThemeWrapper.AdManager  ChoiceResult ,android.view.ContextThemeWrapper.GameManager  GONE android.view.View  OnClickListener android.view.View  VISIBLE android.view.View  hasOnClickListeners android.view.View  	isEnabled android.view.View  setOnClickListener android.view.View  
visibility android.view.View  <SAM-CONSTRUCTOR> !android.view.View.OnClickListener  WebView android.webkit  setDataDirectorySuffix android.webkit.WebView  Button android.widget  FrameLayout android.widget  LinearLayout android.widget  TextView android.widget  hasOnClickListeners android.widget.Button  	isEnabled android.widget.Button  setOnClickListener android.widget.Button  text android.widget.Button  
visibility android.widget.Button  movementMethod android.widget.TextView  setOnClickListener android.widget.TextView  text android.widget.TextView  APP_KEY com.ainative.mountainsurvival  Activity com.ainative.mountainsurvival  ActivityMainBinding com.ainative.mountainsurvival  ActivityPrivacyPolicyBinding com.ainative.mountainsurvival  ActivityStartBinding com.ainative.mountainsurvival  
AdCallback com.ainative.mountainsurvival  	AdManager com.ainative.mountainsurvival  AlertDialog com.ainative.mountainsurvival  Any com.ainative.mountainsurvival  AppCompatActivity com.ainative.mountainsurvival  Boolean com.ainative.mountainsurvival  Build com.ainative.mountainsurvival  Bundle com.ainative.mountainsurvival  	ByteArray com.ainative.mountainsurvival  Charsets com.ainative.mountainsurvival  Choice com.ainative.mountainsurvival  ChoiceResult com.ainative.mountainsurvival  
ClickableSpan com.ainative.mountainsurvival  Context com.ainative.mountainsurvival  
DayTestResult com.ainative.mountainsurvival  Double com.ainative.mountainsurvival  	EatResult com.ainative.mountainsurvival  EventContainer com.ainative.mountainsurvival  EventEngine com.ainative.mountainsurvival  EventFlowTestResult com.ainative.mountainsurvival  EventTestResult com.ainative.mountainsurvival  
EventTestStep com.ainative.mountainsurvival  	Exception com.ainative.mountainsurvival  Float com.ainative.mountainsurvival  ForegroundColorSpan com.ainative.mountainsurvival  
GameEngine com.ainative.mountainsurvival  GameEngineTest com.ainative.mountainsurvival  	GameEvent com.ainative.mountainsurvival  GameEventUtils com.ainative.mountainsurvival  GameLoopTest com.ainative.mountainsurvival  GameLoopTestResult com.ainative.mountainsurvival  GameManager com.ainative.mountainsurvival  GameOverResult com.ainative.mountainsurvival  GameOverTestResult com.ainative.mountainsurvival  	GameState com.ainative.mountainsurvival  GameStateSnapshot com.ainative.mountainsurvival  GameStatistics com.ainative.mountainsurvival  GameSystemTest com.ainative.mountainsurvival  GameUIState com.ainative.mountainsurvival  Gson com.ainative.mountainsurvival  IOException com.ainative.mountainsurvival  Int com.ainative.mountainsurvival  Intent com.ainative.mountainsurvival  JsonSyntaxException com.ainative.mountainsurvival  LinkMovementMethod com.ainative.mountainsurvival  List com.ainative.mountainsurvival  Log com.ainative.mountainsurvival  Long com.ainative.mountainsurvival  MODE_PRIVATE com.ainative.mountainsurvival  MainActivity com.ainative.mountainsurvival  Map com.ainative.mountainsurvival  MountainSurvivalApplication com.ainative.mountainsurvival  MultiDexApplication com.ainative.mountainsurvival  
MutableMap com.ainative.mountainsurvival  
MutableSet com.ainative.mountainsurvival  NightPhaseResult com.ainative.mountainsurvival  NightSettlementResult com.ainative.mountainsurvival  OSETCustomController com.ainative.mountainsurvival  OSETInitListener com.ainative.mountainsurvival  OSETRewardAd com.ainative.mountainsurvival  OSETRewardAdLoadListener com.ainative.mountainsurvival  OSETRewardListener com.ainative.mountainsurvival  OSETRewardVideo com.ainative.mountainsurvival  OSETSDK com.ainative.mountainsurvival  PRIVACY_POLICY_REQUEST_CODE com.ainative.mountainsurvival  PRIVACY_POLICY_URL com.ainative.mountainsurvival  Pair com.ainative.mountainsurvival  PerformanceTestResult com.ainative.mountainsurvival  PrivacyAuditLogger com.ainative.mountainsurvival  PrivacyComplianceManager com.ainative.mountainsurvival  PrivacyPolicyActivity com.ainative.mountainsurvival  R com.ainative.mountainsurvival  RESULT_CANCELED com.ainative.mountainsurvival  	RESULT_OK com.ainative.mountainsurvival  REWARD_VIDEO_AD_ID com.ainative.mountainsurvival  RandomChoice com.ainative.mountainsurvival  ResourceType com.ainative.mountainsurvival  ReviveAdCallback com.ainative.mountainsurvival  SpannableString com.ainative.mountainsurvival  Spanned com.ainative.mountainsurvival  
StartActivity com.ainative.mountainsurvival  String com.ainative.mountainsurvival  
StringBuilder com.ainative.mountainsurvival  System com.ainative.mountainsurvival  SystemTestResult com.ainative.mountainsurvival  TAG com.ainative.mountainsurvival  
TestResult com.ainative.mountainsurvival  Triple com.ainative.mountainsurvival  Uri com.ainative.mountainsurvival  View com.ainative.mountainsurvival  WebView com.ainative.mountainsurvival  android com.ainative.mountainsurvival  any com.ainative.mountainsurvival  applyChoice com.ainative.mountainsurvival  average com.ainative.mountainsurvival  
checkGameOver com.ainative.mountainsurvival  
coerceAtLeast com.ainative.mountainsurvival  coerceAtMost com.ainative.mountainsurvival  coerceIn com.ainative.mountainsurvival  
component1 com.ainative.mountainsurvival  
component2 com.ainative.mountainsurvival  contains com.ainative.mountainsurvival  count com.ainative.mountainsurvival  createCustomController com.ainative.mountainsurvival  	emptyList com.ainative.mountainsurvival  emptyMap com.ainative.mountainsurvival  filter com.ainative.mountainsurvival  filterIsInstance com.ainative.mountainsurvival  find com.ainative.mountainsurvival  forEach com.ainative.mountainsurvival  forEachIndexed com.ainative.mountainsurvival  generateComplianceReport com.ainative.mountainsurvival  getEvent com.ainative.mountainsurvival  getEventMapStatistics com.ainative.mountainsurvival  getGameOverReason com.ainative.mountainsurvival  getGameStatistics com.ainative.mountainsurvival  getProcessName com.ainative.mountainsurvival  giveResourceReward com.ainative.mountainsurvival  hasEvent com.ainative.mountainsurvival  indexOf com.ainative.mountainsurvival  indices com.ainative.mountainsurvival  initializeGame com.ainative.mountainsurvival  invoke com.ainative.mountainsurvival  isAdSDKInitialized com.ainative.mountainsurvival  isBlank com.ainative.mountainsurvival  isChoiceAvailable com.ainative.mountainsurvival  isEmpty com.ainative.mountainsurvival  
isNotBlank com.ainative.mountainsurvival  
isNotEmpty com.ainative.mountainsurvival  
isNullOrEmpty com.ainative.mountainsurvival  isPrivacyPolicyAgreed com.ainative.mountainsurvival  	isVictory com.ainative.mountainsurvival  java com.ainative.mountainsurvival  joinToString com.ainative.mountainsurvival  let com.ainative.mountainsurvival  listOf com.ainative.mountainsurvival  
loadEvents com.ainative.mountainsurvival  logDeniedInfoAccess com.ainative.mountainsurvival  logPrivacyPolicyStatusChange com.ainative.mountainsurvival  	lowercase com.ainative.mountainsurvival  map com.ainative.mountainsurvival  mapOf com.ainative.mountainsurvival  maxOfOrNull com.ainative.mountainsurvival  	maxOrNull com.ainative.mountainsurvival  minOfOrNull com.ainative.mountainsurvival  	minOrNull com.ainative.mountainsurvival  minusAssign com.ainative.mountainsurvival  
mutableListOf com.ainative.mountainsurvival  mutableMapOf com.ainative.mountainsurvival  mutableSetOf com.ainative.mountainsurvival  openPrivacyPolicyUrl com.ainative.mountainsurvival  performNightPhase com.ainative.mountainsurvival  
plusAssign com.ainative.mountainsurvival  preloadRewardVideo com.ainative.mountainsurvival  repeat com.ainative.mountainsurvival  	resetGame com.ainative.mountainsurvival  reviveCharacter com.ainative.mountainsurvival  run com.ainative.mountainsurvival  set com.ainative.mountainsurvival  setGameState com.ainative.mountainsurvival  showAdErrorDialog com.ainative.mountainsurvival  showReviveAd com.ainative.mountainsurvival  showReviveSuccessDialog com.ainative.mountainsurvival  showRewardDialog com.ainative.mountainsurvival  showRewardVideoForResource com.ainative.mountainsurvival  split com.ainative.mountainsurvival  sumOf com.ainative.mountainsurvival  take com.ainative.mountainsurvival  testGameLoop com.ainative.mountainsurvival  to com.ainative.mountainsurvival  toIntOrNull com.ainative.mountainsurvival  toList com.ainative.mountainsurvival  toMap com.ainative.mountainsurvival  toMutableMap com.ainative.mountainsurvival  toMutableSet com.ainative.mountainsurvival  trim com.ainative.mountainsurvival  
trimIndent com.ainative.mountainsurvival  until com.ainative.mountainsurvival  use com.ainative.mountainsurvival  
validateEvent com.ainative.mountainsurvival  validateEventMap com.ainative.mountainsurvival  Activity 'com.ainative.mountainsurvival.AdManager  
AdCallback 'com.ainative.mountainsurvival.AdManager  	Exception 'com.ainative.mountainsurvival.AdManager  Int 'com.ainative.mountainsurvival.AdManager  Log 'com.ainative.mountainsurvival.AdManager  MountainSurvivalApplication 'com.ainative.mountainsurvival.AdManager  OSETRewardAd 'com.ainative.mountainsurvival.AdManager  OSETRewardAdLoadListener 'com.ainative.mountainsurvival.AdManager  OSETRewardListener 'com.ainative.mountainsurvival.AdManager  OSETRewardVideo 'com.ainative.mountainsurvival.AdManager  ResourceType 'com.ainative.mountainsurvival.AdManager  ReviveAdCallback 'com.ainative.mountainsurvival.AdManager  String 'com.ainative.mountainsurvival.AdManager  TAG 'com.ainative.mountainsurvival.AdManager  preloadRewardVideo 'com.ainative.mountainsurvival.AdManager  showReviveAd 'com.ainative.mountainsurvival.AdManager  showRewardVideoForResource 'com.ainative.mountainsurvival.AdManager  
onAdClosed 2com.ainative.mountainsurvival.AdManager.AdCallback  onAdLoadFailed 2com.ainative.mountainsurvival.AdManager.AdCallback  onAdLoadSuccess 2com.ainative.mountainsurvival.AdManager.AdCallback  onAdRewarded 2com.ainative.mountainsurvival.AdManager.AdCallback  onAdShowFailed 2com.ainative.mountainsurvival.AdManager.AdCallback  onAdShowSuccess 2com.ainative.mountainsurvival.AdManager.AdCallback  FIREWOOD 4com.ainative.mountainsurvival.AdManager.ResourceType  FOOD 4com.ainative.mountainsurvival.AdManager.ResourceType  STAMINA 4com.ainative.mountainsurvival.AdManager.ResourceType  WARMTH 4com.ainative.mountainsurvival.AdManager.ResourceType  displayName 4com.ainative.mountainsurvival.AdManager.ResourceType  rewardAmount 4com.ainative.mountainsurvival.AdManager.ResourceType  
onAdClosed 8com.ainative.mountainsurvival.AdManager.ReviveAdCallback  onAdLoadFailed 8com.ainative.mountainsurvival.AdManager.ReviveAdCallback  onAdLoadSuccess 8com.ainative.mountainsurvival.AdManager.ReviveAdCallback  onAdShowFailed 8com.ainative.mountainsurvival.AdManager.ReviveAdCallback  onAdShowSuccess 8com.ainative.mountainsurvival.AdManager.ReviveAdCallback  onReviveSuccess 8com.ainative.mountainsurvival.AdManager.ReviveAdCallback  effects $com.ainative.mountainsurvival.Choice  let $com.ainative.mountainsurvival.Choice  nextEventId $com.ainative.mountainsurvival.Choice  requirements $com.ainative.mountainsurvival.Choice  
resultText $com.ainative.mountainsurvival.Choice  specialEffects $com.ainative.mountainsurvival.Choice  specialItemGained $com.ainative.mountainsurvival.Choice  specialItemReward $com.ainative.mountainsurvival.Choice  specialItemUsed $com.ainative.mountainsurvival.Choice  text $com.ainative.mountainsurvival.Choice  events ,com.ainative.mountainsurvival.EventContainer  	ByteArray )com.ainative.mountainsurvival.EventEngine  Charsets )com.ainative.mountainsurvival.EventEngine  EventContainer )com.ainative.mountainsurvival.EventEngine  GameEventUtils )com.ainative.mountainsurvival.EventEngine  Gson )com.ainative.mountainsurvival.EventEngine  String )com.ainative.mountainsurvival.EventEngine  any )com.ainative.mountainsurvival.EventEngine  contains )com.ainative.mountainsurvival.EventEngine  context )com.ainative.mountainsurvival.EventEngine  count )com.ainative.mountainsurvival.EventEngine  	emptyList )com.ainative.mountainsurvival.EventEngine  eventContainer )com.ainative.mountainsurvival.EventEngine  filter )com.ainative.mountainsurvival.EventEngine  find )com.ainative.mountainsurvival.EventEngine  getAllEvents )com.ainative.mountainsurvival.EventEngine  getEvent )com.ainative.mountainsurvival.EventEngine  gson )com.ainative.mountainsurvival.EventEngine  hasEvent )com.ainative.mountainsurvival.EventEngine  invoke )com.ainative.mountainsurvival.EventEngine  isChoiceAvailable )com.ainative.mountainsurvival.EventEngine  
isNotEmpty )com.ainative.mountainsurvival.EventEngine  
isNullOrEmpty )com.ainative.mountainsurvival.EventEngine  java )com.ainative.mountainsurvival.EventEngine  let )com.ainative.mountainsurvival.EventEngine  loadJsonFromAssets )com.ainative.mountainsurvival.EventEngine  	lowercase )com.ainative.mountainsurvival.EventEngine  mapOf )com.ainative.mountainsurvival.EventEngine  
mutableListOf )com.ainative.mountainsurvival.EventEngine  sumOf )com.ainative.mountainsurvival.EventEngine  to )com.ainative.mountainsurvival.EventEngine  
validateEvent )com.ainative.mountainsurvival.EventEngine  	ByteArray (com.ainative.mountainsurvival.GameEngine  Charsets (com.ainative.mountainsurvival.GameEngine  EVENTS_FILE_NAME (com.ainative.mountainsurvival.GameEngine  EventContainer (com.ainative.mountainsurvival.GameEngine  Gson (com.ainative.mountainsurvival.GameEngine  Log (com.ainative.mountainsurvival.GameEngine  String (com.ainative.mountainsurvival.GameEngine  TAG (com.ainative.mountainsurvival.GameEngine  convertEventsToMap (com.ainative.mountainsurvival.GameEngine  count (com.ainative.mountainsurvival.GameEngine  emptyMap (com.ainative.mountainsurvival.GameEngine  getEvent (com.ainative.mountainsurvival.GameEngine  getEventMapStatistics (com.ainative.mountainsurvival.GameEngine  gson (com.ainative.mountainsurvival.GameEngine  hasEvent (com.ainative.mountainsurvival.GameEngine  invoke (com.ainative.mountainsurvival.GameEngine  isBlank (com.ainative.mountainsurvival.GameEngine  isEmpty (com.ainative.mountainsurvival.GameEngine  
isNotBlank (com.ainative.mountainsurvival.GameEngine  
isNullOrEmpty (com.ainative.mountainsurvival.GameEngine  java (com.ainative.mountainsurvival.GameEngine  let (com.ainative.mountainsurvival.GameEngine  
loadEvents (com.ainative.mountainsurvival.GameEngine  logEventStatistics (com.ainative.mountainsurvival.GameEngine  mapOf (com.ainative.mountainsurvival.GameEngine  
mutableListOf (com.ainative.mountainsurvival.GameEngine  mutableMapOf (com.ainative.mountainsurvival.GameEngine  parseJsonToEventContainer (com.ainative.mountainsurvival.GameEngine  readJsonFromAssets (com.ainative.mountainsurvival.GameEngine  set (com.ainative.mountainsurvival.GameEngine  sumOf (com.ainative.mountainsurvival.GameEngine  to (com.ainative.mountainsurvival.GameEngine  toMap (com.ainative.mountainsurvival.GameEngine  use (com.ainative.mountainsurvival.GameEngine  validateEventMap (com.ainative.mountainsurvival.GameEngine  Any ,com.ainative.mountainsurvival.GameEngineTest  Boolean ,com.ainative.mountainsurvival.GameEngineTest  Context ,com.ainative.mountainsurvival.GameEngineTest  Double ,com.ainative.mountainsurvival.GameEngineTest  EventTestResult ,com.ainative.mountainsurvival.GameEngineTest  
GameEngine ,com.ainative.mountainsurvival.GameEngineTest  	GameEvent ,com.ainative.mountainsurvival.GameEngineTest  Int ,com.ainative.mountainsurvival.GameEngineTest  List ,com.ainative.mountainsurvival.GameEngineTest  Log ,com.ainative.mountainsurvival.GameEngineTest  Long ,com.ainative.mountainsurvival.GameEngineTest  Map ,com.ainative.mountainsurvival.GameEngineTest  Pair ,com.ainative.mountainsurvival.GameEngineTest  PerformanceTestResult ,com.ainative.mountainsurvival.GameEngineTest  String ,com.ainative.mountainsurvival.GameEngineTest  System ,com.ainative.mountainsurvival.GameEngineTest  TAG ,com.ainative.mountainsurvival.GameEngineTest  
TestResult ,com.ainative.mountainsurvival.GameEngineTest  average ,com.ainative.mountainsurvival.GameEngineTest  	emptyList ,com.ainative.mountainsurvival.GameEngineTest  emptyMap ,com.ainative.mountainsurvival.GameEngineTest  forEachIndexed ,com.ainative.mountainsurvival.GameEngineTest  getEvent ,com.ainative.mountainsurvival.GameEngineTest  getEventMapStatistics ,com.ainative.mountainsurvival.GameEngineTest  hasEvent ,com.ainative.mountainsurvival.GameEngineTest  isBlank ,com.ainative.mountainsurvival.GameEngineTest  let ,com.ainative.mountainsurvival.GameEngineTest  listOf ,com.ainative.mountainsurvival.GameEngineTest  
loadEvents ,com.ainative.mountainsurvival.GameEngineTest  map ,com.ainative.mountainsurvival.GameEngineTest  	maxOrNull ,com.ainative.mountainsurvival.GameEngineTest  	minOrNull ,com.ainative.mountainsurvival.GameEngineTest  
mutableListOf ,com.ainative.mountainsurvival.GameEngineTest  repeat ,com.ainative.mountainsurvival.GameEngineTest  to ,com.ainative.mountainsurvival.GameEngineTest  validateEventMap ,com.ainative.mountainsurvival.GameEngineTest  choices 'com.ainative.mountainsurvival.GameEvent  conditionalEvents 'com.ainative.mountainsurvival.GameEvent  effects 'com.ainative.mountainsurvival.GameEvent  id 'com.ainative.mountainsurvival.GameEvent  let 'com.ainative.mountainsurvival.GameEvent  text 'com.ainative.mountainsurvival.GameEvent  android ,com.ainative.mountainsurvival.GameEventUtils  
component1 ,com.ainative.mountainsurvival.GameEventUtils  
component2 ,com.ainative.mountainsurvival.GameEventUtils  convertToInt ,com.ainative.mountainsurvival.GameEventUtils  	emptyList ,com.ainative.mountainsurvival.GameEventUtils  filterIsInstance ,com.ainative.mountainsurvival.GameEventUtils  isBlank ,com.ainative.mountainsurvival.GameEventUtils  isChoiceAvailable ,com.ainative.mountainsurvival.GameEventUtils  listOf ,com.ainative.mountainsurvival.GameEventUtils  toIntOrNull ,com.ainative.mountainsurvival.GameEventUtils  
validateEvent ,com.ainative.mountainsurvival.GameEventUtils  Boolean *com.ainative.mountainsurvival.GameLoopTest  Choice *com.ainative.mountainsurvival.GameLoopTest  
DayTestResult *com.ainative.mountainsurvival.GameLoopTest  	Exception *com.ainative.mountainsurvival.GameLoopTest  GameLoopTestResult *com.ainative.mountainsurvival.GameLoopTest  GameManager *com.ainative.mountainsurvival.GameLoopTest  GameOverTestResult *com.ainative.mountainsurvival.GameLoopTest  	GameState *com.ainative.mountainsurvival.GameLoopTest  Int *com.ainative.mountainsurvival.GameLoopTest  List *com.ainative.mountainsurvival.GameLoopTest  Log *com.ainative.mountainsurvival.GameLoopTest  Long *com.ainative.mountainsurvival.GameLoopTest  String *com.ainative.mountainsurvival.GameLoopTest  System *com.ainative.mountainsurvival.GameLoopTest  TAG *com.ainative.mountainsurvival.GameLoopTest  applyChoice *com.ainative.mountainsurvival.GameLoopTest  
checkGameOver *com.ainative.mountainsurvival.GameLoopTest  getGameOverReason *com.ainative.mountainsurvival.GameLoopTest  initializeGame *com.ainative.mountainsurvival.GameLoopTest  	isVictory *com.ainative.mountainsurvival.GameLoopTest  mapOf *com.ainative.mountainsurvival.GameLoopTest  
mutableListOf *com.ainative.mountainsurvival.GameLoopTest  performNightPhase *com.ainative.mountainsurvival.GameLoopTest  repeat *com.ainative.mountainsurvival.GameLoopTest  testGameLoop *com.ainative.mountainsurvival.GameLoopTest  to *com.ainative.mountainsurvival.GameLoopTest  issues =com.ainative.mountainsurvival.GameLoopTest.GameLoopTestResult  success =com.ainative.mountainsurvival.GameLoopTest.GameLoopTestResult  NightPhaseResult 6com.ainative.mountainsurvival.GameLoopTest.GameManager  Any )com.ainative.mountainsurvival.GameManager  Boolean )com.ainative.mountainsurvival.GameManager  Choice )com.ainative.mountainsurvival.GameManager  ChoiceResult )com.ainative.mountainsurvival.GameManager  Double )com.ainative.mountainsurvival.GameManager  	EatResult )com.ainative.mountainsurvival.GameManager  Float )com.ainative.mountainsurvival.GameManager  GameOverResult )com.ainative.mountainsurvival.GameManager  	GameState )com.ainative.mountainsurvival.GameManager  GameStateSnapshot )com.ainative.mountainsurvival.GameManager  GameStatistics )com.ainative.mountainsurvival.GameManager  Int )com.ainative.mountainsurvival.GameManager  List )com.ainative.mountainsurvival.GameManager  Log )com.ainative.mountainsurvival.GameManager  Long )com.ainative.mountainsurvival.GameManager  Map )com.ainative.mountainsurvival.GameManager  NightPhaseResult )com.ainative.mountainsurvival.GameManager  NightSettlementResult )com.ainative.mountainsurvival.GameManager  Pair )com.ainative.mountainsurvival.GameManager  String )com.ainative.mountainsurvival.GameManager  System )com.ainative.mountainsurvival.GameManager  TAG )com.ainative.mountainsurvival.GameManager  applyChoice )com.ainative.mountainsurvival.GameManager  calculateTotalFirewoodCollected )com.ainative.mountainsurvival.GameManager  calculateTotalFoodConsumed )com.ainative.mountainsurvival.GameManager  
checkGameOver )com.ainative.mountainsurvival.GameManager  
component1 )com.ainative.mountainsurvival.GameManager  
component2 )com.ainative.mountainsurvival.GameManager  convertToInt )com.ainative.mountainsurvival.GameManager  emptyMap )com.ainative.mountainsurvival.GameManager  gameHistory )com.ainative.mountainsurvival.GameManager  	gameState )com.ainative.mountainsurvival.GameManager  getGameOverReason )com.ainative.mountainsurvival.GameManager  getGameOverResult )com.ainative.mountainsurvival.GameManager  getGameStatistics )com.ainative.mountainsurvival.GameManager  getPropertyValue )com.ainative.mountainsurvival.GameManager  initializeGame )com.ainative.mountainsurvival.GameManager  
isInitialized )com.ainative.mountainsurvival.GameManager  	isVictory )com.ainative.mountainsurvival.GameManager  let )com.ainative.mountainsurvival.GameManager  mapOf )com.ainative.mountainsurvival.GameManager  maxOfOrNull )com.ainative.mountainsurvival.GameManager  minOfOrNull )com.ainative.mountainsurvival.GameManager  
mutableListOf )com.ainative.mountainsurvival.GameManager  mutableMapOf )com.ainative.mountainsurvival.GameManager  mutableSetOf )com.ainative.mountainsurvival.GameManager  performNightPhase )com.ainative.mountainsurvival.GameManager  
plusAssign )com.ainative.mountainsurvival.GameManager  	resetGame )com.ainative.mountainsurvival.GameManager  saveStateSnapshot )com.ainative.mountainsurvival.GameManager  set )com.ainative.mountainsurvival.GameManager  setGameState )com.ainative.mountainsurvival.GameManager  to )com.ainative.mountainsurvival.GameManager  toIntOrNull )com.ainative.mountainsurvival.GameManager  toList )com.ainative.mountainsurvival.GameManager  until )com.ainative.mountainsurvival.GameManager  gameOverResult 6com.ainative.mountainsurvival.GameManager.ChoiceResult  message 6com.ainative.mountainsurvival.GameManager.ChoiceResult  specialItemGained 6com.ainative.mountainsurvival.GameManager.ChoiceResult  stateChanges 6com.ainative.mountainsurvival.GameManager.ChoiceResult  success 6com.ainative.mountainsurvival.GameManager.ChoiceResult  state ;com.ainative.mountainsurvival.GameManager.GameStateSnapshot  totalChoicesMade 8com.ainative.mountainsurvival.GameManager.GameStatistics  dayAfter :com.ainative.mountainsurvival.GameManager.NightPhaseResult  
firewoodAfter :com.ainative.mountainsurvival.GameManager.NightPhaseResult  firewoodBefore :com.ainative.mountainsurvival.GameManager.NightPhaseResult  firewoodUsed :com.ainative.mountainsurvival.GameManager.NightPhaseResult  hadEnoughFirewood :com.ainative.mountainsurvival.GameManager.NightPhaseResult  roofLeaking :com.ainative.mountainsurvival.GameManager.NightPhaseResult  roofLeakingWarmthLoss :com.ainative.mountainsurvival.GameManager.NightPhaseResult  staminaAfter :com.ainative.mountainsurvival.GameManager.NightPhaseResult  
staminaBefore :com.ainative.mountainsurvival.GameManager.NightPhaseResult  
staminaChange :com.ainative.mountainsurvival.GameManager.NightPhaseResult  warmthAfter :com.ainative.mountainsurvival.GameManager.NightPhaseResult  warmthBefore :com.ainative.mountainsurvival.GameManager.NightPhaseResult  warmthChange :com.ainative.mountainsurvival.GameManager.NightPhaseResult  Pair 'com.ainative.mountainsurvival.GameState  applyEffects 'com.ainative.mountainsurvival.GameState  cabinIntegrity 'com.ainative.mountainsurvival.GameState  
checkGameOver 'com.ainative.mountainsurvival.GameState  
coerceAtLeast 'com.ainative.mountainsurvival.GameState  coerceAtMost 'com.ainative.mountainsurvival.GameState  coerceIn 'com.ainative.mountainsurvival.GameState  
component1 'com.ainative.mountainsurvival.GameState  
component2 'com.ainative.mountainsurvival.GameState  copy 'com.ainative.mountainsurvival.GameState  
currentDay 'com.ainative.mountainsurvival.GameState  eat 'com.ainative.mountainsurvival.GameState  firewood 'com.ainative.mountainsurvival.GameState  food 'com.ainative.mountainsurvival.GameState  
getStatusText 'com.ainative.mountainsurvival.GameState  getVictoryType 'com.ainative.mountainsurvival.GameState  hasSpecialState 'com.ainative.mountainsurvival.GameState  hope 'com.ainative.mountainsurvival.GameState  let 'com.ainative.mountainsurvival.GameState  minusAssign 'com.ainative.mountainsurvival.GameState  nightTimeSettlement 'com.ainative.mountainsurvival.GameState  roofLeaking 'com.ainative.mountainsurvival.GameState  set 'com.ainative.mountainsurvival.GameState  setSpecialState 'com.ainative.mountainsurvival.GameState  specialItems 'com.ainative.mountainsurvival.GameState  
specialStates 'com.ainative.mountainsurvival.GameState  stamina 'com.ainative.mountainsurvival.GameState  warmth 'com.ainative.mountainsurvival.GameState  Boolean ,com.ainative.mountainsurvival.GameSystemTest  Choice ,com.ainative.mountainsurvival.GameSystemTest  Context ,com.ainative.mountainsurvival.GameSystemTest  EventFlowTestResult ,com.ainative.mountainsurvival.GameSystemTest  
EventTestStep ,com.ainative.mountainsurvival.GameSystemTest  	Exception ,com.ainative.mountainsurvival.GameSystemTest  
GameEngine ,com.ainative.mountainsurvival.GameSystemTest  GameManager ,com.ainative.mountainsurvival.GameSystemTest  	GameState ,com.ainative.mountainsurvival.GameSystemTest  Int ,com.ainative.mountainsurvival.GameSystemTest  List ,com.ainative.mountainsurvival.GameSystemTest  Log ,com.ainative.mountainsurvival.GameSystemTest  Long ,com.ainative.mountainsurvival.GameSystemTest  String ,com.ainative.mountainsurvival.GameSystemTest  System ,com.ainative.mountainsurvival.GameSystemTest  SystemTestResult ,com.ainative.mountainsurvival.GameSystemTest  TAG ,com.ainative.mountainsurvival.GameSystemTest  applyChoice ,com.ainative.mountainsurvival.GameSystemTest  getEvent ,com.ainative.mountainsurvival.GameSystemTest  getGameStatistics ,com.ainative.mountainsurvival.GameSystemTest  initializeGame ,com.ainative.mountainsurvival.GameSystemTest  
isNotEmpty ,com.ainative.mountainsurvival.GameSystemTest  let ,com.ainative.mountainsurvival.GameSystemTest  
loadEvents ,com.ainative.mountainsurvival.GameSystemTest  map ,com.ainative.mountainsurvival.GameSystemTest  mapOf ,com.ainative.mountainsurvival.GameSystemTest  
mutableListOf ,com.ainative.mountainsurvival.GameSystemTest  to ,com.ainative.mountainsurvival.GameSystemTest  validateEventMap ,com.ainative.mountainsurvival.GameSystemTest  GameStatistics 8com.ainative.mountainsurvival.GameSystemTest.GameManager  ActivityMainBinding *com.ainative.mountainsurvival.MainActivity  	AdManager *com.ainative.mountainsurvival.MainActivity  AlertDialog *com.ainative.mountainsurvival.MainActivity  Any *com.ainative.mountainsurvival.MainActivity  Boolean *com.ainative.mountainsurvival.MainActivity  Bundle *com.ainative.mountainsurvival.MainActivity  Choice *com.ainative.mountainsurvival.MainActivity  	Companion *com.ainative.mountainsurvival.MainActivity  Double *com.ainative.mountainsurvival.MainActivity  	Exception *com.ainative.mountainsurvival.MainActivity  Float *com.ainative.mountainsurvival.MainActivity  
GameEngine *com.ainative.mountainsurvival.MainActivity  	GameEvent *com.ainative.mountainsurvival.MainActivity  GameEventUtils *com.ainative.mountainsurvival.MainActivity  GameLoopTest *com.ainative.mountainsurvival.MainActivity  GameManager *com.ainative.mountainsurvival.MainActivity  	GameState *com.ainative.mountainsurvival.MainActivity  GameUIState *com.ainative.mountainsurvival.MainActivity  Int *com.ainative.mountainsurvival.MainActivity  List *com.ainative.mountainsurvival.MainActivity  Log *com.ainative.mountainsurvival.MainActivity  Long *com.ainative.mountainsurvival.MainActivity  Map *com.ainative.mountainsurvival.MainActivity  Pair *com.ainative.mountainsurvival.MainActivity  String *com.ainative.mountainsurvival.MainActivity  TAG *com.ainative.mountainsurvival.MainActivity  Triple *com.ainative.mountainsurvival.MainActivity  View *com.ainative.mountainsurvival.MainActivity  any *com.ainative.mountainsurvival.MainActivity  applyChoice *com.ainative.mountainsurvival.MainActivity  areButtonListenersSet *com.ainative.mountainsurvival.MainActivity  backupValidGameState *com.ainative.mountainsurvival.MainActivity  binding *com.ainative.mountainsurvival.MainActivity  
checkGameOver *com.ainative.mountainsurvival.MainActivity  coerceAtMost *com.ainative.mountainsurvival.MainActivity  
component1 *com.ainative.mountainsurvival.MainActivity  
component2 *com.ainative.mountainsurvival.MainActivity  contains *com.ainative.mountainsurvival.MainActivity  convertToInt *com.ainative.mountainsurvival.MainActivity  currentChoices *com.ainative.mountainsurvival.MainActivity  currentEvent *com.ainative.mountainsurvival.MainActivity  displayEvent *com.ainative.mountainsurvival.MainActivity  	emptyList *com.ainative.mountainsurvival.MainActivity  emptyMap *com.ainative.mountainsurvival.MainActivity  ensureButtonsAreClickable *com.ainative.mountainsurvival.MainActivity  evaluateCondition *com.ainative.mountainsurvival.MainActivity  evaluateConditionalEvent *com.ainative.mountainsurvival.MainActivity  eventMap *com.ainative.mountainsurvival.MainActivity  filter *com.ainative.mountainsurvival.MainActivity  finish *com.ainative.mountainsurvival.MainActivity  forEachIndexed *com.ainative.mountainsurvival.MainActivity  forceNightPhase *com.ainative.mountainsurvival.MainActivity  gameUIState *com.ainative.mountainsurvival.MainActivity  generateNightSettlementText *com.ainative.mountainsurvival.MainActivity  generateStateChangesText *com.ainative.mountainsurvival.MainActivity  getAvailableChoices *com.ainative.mountainsurvival.MainActivity  getEvent *com.ainative.mountainsurvival.MainActivity  getEventMapStatistics *com.ainative.mountainsurvival.MainActivity  getGameOverReason *com.ainative.mountainsurvival.MainActivity  giveResourceReward *com.ainative.mountainsurvival.MainActivity  handleChoiceClick *com.ainative.mountainsurvival.MainActivity  handleContinueClick *com.ainative.mountainsurvival.MainActivity  handleFoodChoice *com.ainative.mountainsurvival.MainActivity  handleFoodContinueClick *com.ainative.mountainsurvival.MainActivity  handleNightContinueClick *com.ainative.mountainsurvival.MainActivity  handleNormalChoice *com.ainative.mountainsurvival.MainActivity  indices *com.ainative.mountainsurvival.MainActivity  initializeGame *com.ainative.mountainsurvival.MainActivity  initializeStateTransitionDP *com.ainative.mountainsurvival.MainActivity  isChoiceAvailable *com.ainative.mountainsurvival.MainActivity  isNightEvent *com.ainative.mountainsurvival.MainActivity  
isNotEmpty *com.ainative.mountainsurvival.MainActivity  
isNullOrEmpty *com.ainative.mountainsurvival.MainActivity  	isVictory *com.ainative.mountainsurvival.MainActivity  joinToString *com.ainative.mountainsurvival.MainActivity  lastValidCurrentChoices *com.ainative.mountainsurvival.MainActivity  lastValidCurrentEvent *com.ainative.mountainsurvival.MainActivity  lastValidGameState *com.ainative.mountainsurvival.MainActivity  lastValidGameUIState *com.ainative.mountainsurvival.MainActivity  layoutInflater *com.ainative.mountainsurvival.MainActivity  let *com.ainative.mountainsurvival.MainActivity  listOf *com.ainative.mountainsurvival.MainActivity  
loadEvents *com.ainative.mountainsurvival.MainActivity  mapOf *com.ainative.mountainsurvival.MainActivity  
mutableListOf *com.ainative.mountainsurvival.MainActivity  mutableMapOf *com.ainative.mountainsurvival.MainActivity  
pendingChoice *com.ainative.mountainsurvival.MainActivity  pendingNightEventId *com.ainative.mountainsurvival.MainActivity  performNightPhase *com.ainative.mountainsurvival.MainActivity  performNightPhaseAndCheck *com.ainative.mountainsurvival.MainActivity  preloadRewardVideo *com.ainative.mountainsurvival.MainActivity  proceedToNextDay *com.ainative.mountainsurvival.MainActivity  processNextStep *com.ainative.mountainsurvival.MainActivity  	resetGame *com.ainative.mountainsurvival.MainActivity  restartGame *com.ainative.mountainsurvival.MainActivity  reviveCharacter *com.ainative.mountainsurvival.MainActivity  run *com.ainative.mountainsurvival.MainActivity  saveGameStateForRevive *com.ainative.mountainsurvival.MainActivity  savedCurrentChoices *com.ainative.mountainsurvival.MainActivity  savedCurrentEvent *com.ainative.mountainsurvival.MainActivity  savedGameState *com.ainative.mountainsurvival.MainActivity  savedGameUIState *com.ainative.mountainsurvival.MainActivity  set *com.ainative.mountainsurvival.MainActivity  setContentView *com.ainative.mountainsurvival.MainActivity  setGameState *com.ainative.mountainsurvival.MainActivity  setupChoiceButtons *com.ainative.mountainsurvival.MainActivity  setupClickListeners *com.ainative.mountainsurvival.MainActivity  shouldEnterFoodPhase *com.ainative.mountainsurvival.MainActivity  showAdErrorDialog *com.ainative.mountainsurvival.MainActivity  showAdForResource *com.ainative.mountainsurvival.MainActivity  showContinueButton *com.ainative.mountainsurvival.MainActivity  showFailureButtons *com.ainative.mountainsurvival.MainActivity  showFailureScreen *com.ainative.mountainsurvival.MainActivity  showFoodChoicePhase *com.ainative.mountainsurvival.MainActivity  showGameOverDialog *com.ainative.mountainsurvival.MainActivity  showNightPhaseResult *com.ainative.mountainsurvival.MainActivity  showReviveAd *com.ainative.mountainsurvival.MainActivity  showReviveSuccessDialog *com.ainative.mountainsurvival.MainActivity  showRewardDialog *com.ainative.mountainsurvival.MainActivity  showRewardVideoForResource *com.ainative.mountainsurvival.MainActivity  showVictoryButtons *com.ainative.mountainsurvival.MainActivity  showVictoryScreen *com.ainative.mountainsurvival.MainActivity  split *com.ainative.mountainsurvival.MainActivity  stateTransitionDP *com.ainative.mountainsurvival.MainActivity  take *com.ainative.mountainsurvival.MainActivity  testGameLoop *com.ainative.mountainsurvival.MainActivity  to *com.ainative.mountainsurvival.MainActivity  toIntOrNull *com.ainative.mountainsurvival.MainActivity  toList *com.ainative.mountainsurvival.MainActivity  toMutableMap *com.ainative.mountainsurvival.MainActivity  toMutableSet *com.ainative.mountainsurvival.MainActivity  transitionState *com.ainative.mountainsurvival.MainActivity  trim *com.ainative.mountainsurvival.MainActivity  
trimIndent *com.ainative.mountainsurvival.MainActivity  updateUI *com.ainative.mountainsurvival.MainActivity  validateEventMap *com.ainative.mountainsurvival.MainActivity  
AdCallback 4com.ainative.mountainsurvival.MainActivity.AdManager  ResourceType 4com.ainative.mountainsurvival.MainActivity.AdManager  ReviveAdCallback 4com.ainative.mountainsurvival.MainActivity.AdManager  ActivityMainBinding 4com.ainative.mountainsurvival.MainActivity.Companion  	AdManager 4com.ainative.mountainsurvival.MainActivity.Companion  AlertDialog 4com.ainative.mountainsurvival.MainActivity.Companion  Choice 4com.ainative.mountainsurvival.MainActivity.Companion  
GameEngine 4com.ainative.mountainsurvival.MainActivity.Companion  GameEventUtils 4com.ainative.mountainsurvival.MainActivity.Companion  GameLoopTest 4com.ainative.mountainsurvival.MainActivity.Companion  GameManager 4com.ainative.mountainsurvival.MainActivity.Companion  	GameState 4com.ainative.mountainsurvival.MainActivity.Companion  GameUIState 4com.ainative.mountainsurvival.MainActivity.Companion  Log 4com.ainative.mountainsurvival.MainActivity.Companion  Pair 4com.ainative.mountainsurvival.MainActivity.Companion  TAG 4com.ainative.mountainsurvival.MainActivity.Companion  Triple 4com.ainative.mountainsurvival.MainActivity.Companion  View 4com.ainative.mountainsurvival.MainActivity.Companion  any 4com.ainative.mountainsurvival.MainActivity.Companion  applyChoice 4com.ainative.mountainsurvival.MainActivity.Companion  
checkGameOver 4com.ainative.mountainsurvival.MainActivity.Companion  coerceAtMost 4com.ainative.mountainsurvival.MainActivity.Companion  
component1 4com.ainative.mountainsurvival.MainActivity.Companion  
component2 4com.ainative.mountainsurvival.MainActivity.Companion  contains 4com.ainative.mountainsurvival.MainActivity.Companion  	emptyList 4com.ainative.mountainsurvival.MainActivity.Companion  emptyMap 4com.ainative.mountainsurvival.MainActivity.Companion  filter 4com.ainative.mountainsurvival.MainActivity.Companion  forEachIndexed 4com.ainative.mountainsurvival.MainActivity.Companion  getEvent 4com.ainative.mountainsurvival.MainActivity.Companion  getEventMapStatistics 4com.ainative.mountainsurvival.MainActivity.Companion  getGameOverReason 4com.ainative.mountainsurvival.MainActivity.Companion  giveResourceReward 4com.ainative.mountainsurvival.MainActivity.Companion  indices 4com.ainative.mountainsurvival.MainActivity.Companion  initializeGame 4com.ainative.mountainsurvival.MainActivity.Companion  isChoiceAvailable 4com.ainative.mountainsurvival.MainActivity.Companion  
isNotEmpty 4com.ainative.mountainsurvival.MainActivity.Companion  
isNullOrEmpty 4com.ainative.mountainsurvival.MainActivity.Companion  	isVictory 4com.ainative.mountainsurvival.MainActivity.Companion  joinToString 4com.ainative.mountainsurvival.MainActivity.Companion  let 4com.ainative.mountainsurvival.MainActivity.Companion  listOf 4com.ainative.mountainsurvival.MainActivity.Companion  
loadEvents 4com.ainative.mountainsurvival.MainActivity.Companion  mapOf 4com.ainative.mountainsurvival.MainActivity.Companion  
mutableListOf 4com.ainative.mountainsurvival.MainActivity.Companion  mutableMapOf 4com.ainative.mountainsurvival.MainActivity.Companion  performNightPhase 4com.ainative.mountainsurvival.MainActivity.Companion  preloadRewardVideo 4com.ainative.mountainsurvival.MainActivity.Companion  	resetGame 4com.ainative.mountainsurvival.MainActivity.Companion  reviveCharacter 4com.ainative.mountainsurvival.MainActivity.Companion  run 4com.ainative.mountainsurvival.MainActivity.Companion  set 4com.ainative.mountainsurvival.MainActivity.Companion  setGameState 4com.ainative.mountainsurvival.MainActivity.Companion  showAdErrorDialog 4com.ainative.mountainsurvival.MainActivity.Companion  showReviveAd 4com.ainative.mountainsurvival.MainActivity.Companion  showReviveSuccessDialog 4com.ainative.mountainsurvival.MainActivity.Companion  showRewardDialog 4com.ainative.mountainsurvival.MainActivity.Companion  showRewardVideoForResource 4com.ainative.mountainsurvival.MainActivity.Companion  split 4com.ainative.mountainsurvival.MainActivity.Companion  take 4com.ainative.mountainsurvival.MainActivity.Companion  testGameLoop 4com.ainative.mountainsurvival.MainActivity.Companion  to 4com.ainative.mountainsurvival.MainActivity.Companion  toIntOrNull 4com.ainative.mountainsurvival.MainActivity.Companion  toList 4com.ainative.mountainsurvival.MainActivity.Companion  toMutableMap 4com.ainative.mountainsurvival.MainActivity.Companion  toMutableSet 4com.ainative.mountainsurvival.MainActivity.Companion  trim 4com.ainative.mountainsurvival.MainActivity.Companion  
trimIndent 4com.ainative.mountainsurvival.MainActivity.Companion  validateEventMap 4com.ainative.mountainsurvival.MainActivity.Companion  ChoiceResult 6com.ainative.mountainsurvival.MainActivity.GameManager  NightPhaseResult 6com.ainative.mountainsurvival.MainActivity.GameManager  FOOD_CHOICE 6com.ainative.mountainsurvival.MainActivity.GameUIState  FOOD_WAITING_CONTINUE 6com.ainative.mountainsurvival.MainActivity.GameUIState  	GAME_OVER 6com.ainative.mountainsurvival.MainActivity.GameUIState  NIGHT_PHASE 6com.ainative.mountainsurvival.MainActivity.GameUIState  NIGHT_WAITING_CONTINUE 6com.ainative.mountainsurvival.MainActivity.GameUIState  
NORMAL_CHOICE 6com.ainative.mountainsurvival.MainActivity.GameUIState  WAITING_CONTINUE 6com.ainative.mountainsurvival.MainActivity.GameUIState  APP_KEY 9com.ainative.mountainsurvival.MountainSurvivalApplication  Boolean 9com.ainative.mountainsurvival.MountainSurvivalApplication  Build 9com.ainative.mountainsurvival.MountainSurvivalApplication  	Companion 9com.ainative.mountainsurvival.MountainSurvivalApplication  	Exception 9com.ainative.mountainsurvival.MountainSurvivalApplication  Log 9com.ainative.mountainsurvival.MountainSurvivalApplication  OSETInitListener 9com.ainative.mountainsurvival.MountainSurvivalApplication  OSETSDK 9com.ainative.mountainsurvival.MountainSurvivalApplication  PrivacyComplianceManager 9com.ainative.mountainsurvival.MountainSurvivalApplication  REWARD_VIDEO_AD_ID 9com.ainative.mountainsurvival.MountainSurvivalApplication  String 9com.ainative.mountainsurvival.MountainSurvivalApplication  TAG 9com.ainative.mountainsurvival.MountainSurvivalApplication  WebView 9com.ainative.mountainsurvival.MountainSurvivalApplication  android 9com.ainative.mountainsurvival.MountainSurvivalApplication  createCustomController 9com.ainative.mountainsurvival.MountainSurvivalApplication  getProcessName 9com.ainative.mountainsurvival.MountainSurvivalApplication  	initAdSDK 9com.ainative.mountainsurvival.MountainSurvivalApplication  isAdSDKInitialized 9com.ainative.mountainsurvival.MountainSurvivalApplication  APP_KEY Ccom.ainative.mountainsurvival.MountainSurvivalApplication.Companion  Build Ccom.ainative.mountainsurvival.MountainSurvivalApplication.Companion  Log Ccom.ainative.mountainsurvival.MountainSurvivalApplication.Companion  OSETSDK Ccom.ainative.mountainsurvival.MountainSurvivalApplication.Companion  PrivacyComplianceManager Ccom.ainative.mountainsurvival.MountainSurvivalApplication.Companion  REWARD_VIDEO_AD_ID Ccom.ainative.mountainsurvival.MountainSurvivalApplication.Companion  TAG Ccom.ainative.mountainsurvival.MountainSurvivalApplication.Companion  WebView Ccom.ainative.mountainsurvival.MountainSurvivalApplication.Companion  createCustomController Ccom.ainative.mountainsurvival.MountainSurvivalApplication.Companion  getProcessName Ccom.ainative.mountainsurvival.MountainSurvivalApplication.Companion  isAdSDKInitialized Ccom.ainative.mountainsurvival.MountainSurvivalApplication.Companion  content Acom.ainative.mountainsurvival.MountainSurvivalApplication.android  Context Icom.ainative.mountainsurvival.MountainSurvivalApplication.android.content  Log 0com.ainative.mountainsurvival.PrivacyAuditLogger  PrivacyComplianceManager 0com.ainative.mountainsurvival.PrivacyAuditLogger  
StringBuilder 0com.ainative.mountainsurvival.PrivacyAuditLogger  TAG 0com.ainative.mountainsurvival.PrivacyAuditLogger  generateComplianceReport 0com.ainative.mountainsurvival.PrivacyAuditLogger  isPrivacyPolicyAgreed 0com.ainative.mountainsurvival.PrivacyAuditLogger  logDeniedInfoAccess 0com.ainative.mountainsurvival.PrivacyAuditLogger  logPrivacyPolicyStatusChange 0com.ainative.mountainsurvival.PrivacyAuditLogger  Context 6com.ainative.mountainsurvival.PrivacyComplianceManager  Log 6com.ainative.mountainsurvival.PrivacyComplianceManager  PrivacyAuditLogger 6com.ainative.mountainsurvival.PrivacyComplianceManager  TAG 6com.ainative.mountainsurvival.PrivacyComplianceManager  createCustomController 6com.ainative.mountainsurvival.PrivacyComplianceManager  isPrivacyPolicyAgreed 6com.ainative.mountainsurvival.PrivacyComplianceManager  logDeniedInfoAccess 6com.ainative.mountainsurvival.PrivacyComplianceManager  logPrivacyPolicyStatusChange 6com.ainative.mountainsurvival.PrivacyComplianceManager  ActivityPrivacyPolicyBinding 3com.ainative.mountainsurvival.PrivacyPolicyActivity  Bundle 3com.ainative.mountainsurvival.PrivacyPolicyActivity  
ClickableSpan 3com.ainative.mountainsurvival.PrivacyPolicyActivity  	Companion 3com.ainative.mountainsurvival.PrivacyPolicyActivity  	Exception 3com.ainative.mountainsurvival.PrivacyPolicyActivity  ForegroundColorSpan 3com.ainative.mountainsurvival.PrivacyPolicyActivity  Intent 3com.ainative.mountainsurvival.PrivacyPolicyActivity  LinkMovementMethod 3com.ainative.mountainsurvival.PrivacyPolicyActivity  Log 3com.ainative.mountainsurvival.PrivacyPolicyActivity  MODE_PRIVATE 3com.ainative.mountainsurvival.PrivacyPolicyActivity  PRIVACY_POLICY_URL 3com.ainative.mountainsurvival.PrivacyPolicyActivity  R 3com.ainative.mountainsurvival.PrivacyPolicyActivity  RESULT_CANCELED 3com.ainative.mountainsurvival.PrivacyPolicyActivity  	RESULT_OK 3com.ainative.mountainsurvival.PrivacyPolicyActivity  SpannableString 3com.ainative.mountainsurvival.PrivacyPolicyActivity  Spanned 3com.ainative.mountainsurvival.PrivacyPolicyActivity  System 3com.ainative.mountainsurvival.PrivacyPolicyActivity  TAG 3com.ainative.mountainsurvival.PrivacyPolicyActivity  Uri 3com.ainative.mountainsurvival.PrivacyPolicyActivity  View 3com.ainative.mountainsurvival.PrivacyPolicyActivity  agreePrivacyPolicy 3com.ainative.mountainsurvival.PrivacyPolicyActivity  android 3com.ainative.mountainsurvival.PrivacyPolicyActivity  binding 3com.ainative.mountainsurvival.PrivacyPolicyActivity  disagreePrivacyPolicy 3com.ainative.mountainsurvival.PrivacyPolicyActivity  finish 3com.ainative.mountainsurvival.PrivacyPolicyActivity  getSharedPreferences 3com.ainative.mountainsurvival.PrivacyPolicyActivity  	getString 3com.ainative.mountainsurvival.PrivacyPolicyActivity  indexOf 3com.ainative.mountainsurvival.PrivacyPolicyActivity  layoutInflater 3com.ainative.mountainsurvival.PrivacyPolicyActivity  openPrivacyPolicyUrl 3com.ainative.mountainsurvival.PrivacyPolicyActivity  	resources 3com.ainative.mountainsurvival.PrivacyPolicyActivity  setContentView 3com.ainative.mountainsurvival.PrivacyPolicyActivity  	setResult 3com.ainative.mountainsurvival.PrivacyPolicyActivity  setupClickListeners 3com.ainative.mountainsurvival.PrivacyPolicyActivity  setupPrivacyPolicyContent 3com.ainative.mountainsurvival.PrivacyPolicyActivity  
startActivity 3com.ainative.mountainsurvival.PrivacyPolicyActivity  supportActionBar 3com.ainative.mountainsurvival.PrivacyPolicyActivity  ActivityPrivacyPolicyBinding =com.ainative.mountainsurvival.PrivacyPolicyActivity.Companion  ForegroundColorSpan =com.ainative.mountainsurvival.PrivacyPolicyActivity.Companion  Intent =com.ainative.mountainsurvival.PrivacyPolicyActivity.Companion  LinkMovementMethod =com.ainative.mountainsurvival.PrivacyPolicyActivity.Companion  Log =com.ainative.mountainsurvival.PrivacyPolicyActivity.Companion  MODE_PRIVATE =com.ainative.mountainsurvival.PrivacyPolicyActivity.Companion  PRIVACY_POLICY_URL =com.ainative.mountainsurvival.PrivacyPolicyActivity.Companion  R =com.ainative.mountainsurvival.PrivacyPolicyActivity.Companion  RESULT_CANCELED =com.ainative.mountainsurvival.PrivacyPolicyActivity.Companion  	RESULT_OK =com.ainative.mountainsurvival.PrivacyPolicyActivity.Companion  SpannableString =com.ainative.mountainsurvival.PrivacyPolicyActivity.Companion  Spanned =com.ainative.mountainsurvival.PrivacyPolicyActivity.Companion  System =com.ainative.mountainsurvival.PrivacyPolicyActivity.Companion  TAG =com.ainative.mountainsurvival.PrivacyPolicyActivity.Companion  Uri =com.ainative.mountainsurvival.PrivacyPolicyActivity.Companion  android =com.ainative.mountainsurvival.PrivacyPolicyActivity.Companion  indexOf =com.ainative.mountainsurvival.PrivacyPolicyActivity.Companion  openPrivacyPolicyUrl =com.ainative.mountainsurvival.PrivacyPolicyActivity.Companion  privacy_policy_content &com.ainative.mountainsurvival.R.string  privacy_policy_link_text &com.ainative.mountainsurvival.R.string  ActivityStartBinding +com.ainative.mountainsurvival.StartActivity  Boolean +com.ainative.mountainsurvival.StartActivity  Bundle +com.ainative.mountainsurvival.StartActivity  	Exception +com.ainative.mountainsurvival.StartActivity  Int +com.ainative.mountainsurvival.StartActivity  Intent +com.ainative.mountainsurvival.StartActivity  Log +com.ainative.mountainsurvival.StartActivity  MODE_PRIVATE +com.ainative.mountainsurvival.StartActivity  MainActivity +com.ainative.mountainsurvival.StartActivity  MountainSurvivalApplication +com.ainative.mountainsurvival.StartActivity  PRIVACY_POLICY_REQUEST_CODE +com.ainative.mountainsurvival.StartActivity  PrivacyAuditLogger +com.ainative.mountainsurvival.StartActivity  PrivacyPolicyActivity +com.ainative.mountainsurvival.StartActivity  	RESULT_OK +com.ainative.mountainsurvival.StartActivity  TAG +com.ainative.mountainsurvival.StartActivity  application +com.ainative.mountainsurvival.StartActivity  binding +com.ainative.mountainsurvival.StartActivity  checkPrivacyPolicyAgreed +com.ainative.mountainsurvival.StartActivity  exitGame +com.ainative.mountainsurvival.StartActivity  finish +com.ainative.mountainsurvival.StartActivity  generateComplianceReport +com.ainative.mountainsurvival.StartActivity  getSharedPreferences +com.ainative.mountainsurvival.StartActivity  initializeAdSDK +com.ainative.mountainsurvival.StartActivity  initializeStartScreen +com.ainative.mountainsurvival.StartActivity  java +com.ainative.mountainsurvival.StartActivity  layoutInflater +com.ainative.mountainsurvival.StartActivity  setContentView +com.ainative.mountainsurvival.StartActivity  setupClickListeners +com.ainative.mountainsurvival.StartActivity  showPrivacyPolicyDialog +com.ainative.mountainsurvival.StartActivity  
startActivity +com.ainative.mountainsurvival.StartActivity  startActivityForResult +com.ainative.mountainsurvival.StartActivity  	startGame +com.ainative.mountainsurvival.StartActivity  supportActionBar +com.ainative.mountainsurvival.StartActivity  ActivityStartBinding 5com.ainative.mountainsurvival.StartActivity.Companion  Intent 5com.ainative.mountainsurvival.StartActivity.Companion  Log 5com.ainative.mountainsurvival.StartActivity.Companion  MODE_PRIVATE 5com.ainative.mountainsurvival.StartActivity.Companion  MainActivity 5com.ainative.mountainsurvival.StartActivity.Companion  PRIVACY_POLICY_REQUEST_CODE 5com.ainative.mountainsurvival.StartActivity.Companion  PrivacyAuditLogger 5com.ainative.mountainsurvival.StartActivity.Companion  PrivacyPolicyActivity 5com.ainative.mountainsurvival.StartActivity.Companion  	RESULT_OK 5com.ainative.mountainsurvival.StartActivity.Companion  TAG 5com.ainative.mountainsurvival.StartActivity.Companion  generateComplianceReport 5com.ainative.mountainsurvival.StartActivity.Companion  java 5com.ainative.mountainsurvival.StartActivity.Companion  content %com.ainative.mountainsurvival.android  location %com.ainative.mountainsurvival.android  Context -com.ainative.mountainsurvival.android.content  Location .com.ainative.mountainsurvival.android.location  ActivityMainBinding )com.ainative.mountainsurvival.databinding  ActivityPrivacyPolicyBinding )com.ainative.mountainsurvival.databinding  ActivityStartBinding )com.ainative.mountainsurvival.databinding  
choice1Button =com.ainative.mountainsurvival.databinding.ActivityMainBinding  
choice2Button =com.ainative.mountainsurvival.databinding.ActivityMainBinding  
choice3Button =com.ainative.mountainsurvival.databinding.ActivityMainBinding  
choice4Button =com.ainative.mountainsurvival.databinding.ActivityMainBinding  firewoodPlusButton =com.ainative.mountainsurvival.databinding.ActivityMainBinding  firewoodTextView =com.ainative.mountainsurvival.databinding.ActivityMainBinding  foodPlusButton =com.ainative.mountainsurvival.databinding.ActivityMainBinding  foodTextView =com.ainative.mountainsurvival.databinding.ActivityMainBinding  inflate =com.ainative.mountainsurvival.databinding.ActivityMainBinding  root =com.ainative.mountainsurvival.databinding.ActivityMainBinding  staminaPlusButton =com.ainative.mountainsurvival.databinding.ActivityMainBinding  staminaTextView =com.ainative.mountainsurvival.databinding.ActivityMainBinding  
storyTextView =com.ainative.mountainsurvival.databinding.ActivityMainBinding  warmthPlusButton =com.ainative.mountainsurvival.databinding.ActivityMainBinding  warmthTextView =com.ainative.mountainsurvival.databinding.ActivityMainBinding  agreeButton Fcom.ainative.mountainsurvival.databinding.ActivityPrivacyPolicyBinding  disagreeButton Fcom.ainative.mountainsurvival.databinding.ActivityPrivacyPolicyBinding  inflate Fcom.ainative.mountainsurvival.databinding.ActivityPrivacyPolicyBinding  privacyContentTextView Fcom.ainative.mountainsurvival.databinding.ActivityPrivacyPolicyBinding  root Fcom.ainative.mountainsurvival.databinding.ActivityPrivacyPolicyBinding  exitGameButton >com.ainative.mountainsurvival.databinding.ActivityStartBinding  inflate >com.ainative.mountainsurvival.databinding.ActivityStartBinding  root >com.ainative.mountainsurvival.databinding.ActivityStartBinding  startGameButton >com.ainative.mountainsurvival.databinding.ActivityStartBinding  Gson com.google.gson  JsonSyntaxException com.google.gson  fromJson com.google.gson.Gson  	TypeToken com.google.gson.reflect  isUsable $com.kc.openset.ad.base.BaseOSETAdImp  OSETRewardAdLoadListener com.kc.openset.ad.listener  OSETRewardListener com.kc.openset.ad.listener  OSETRewardAd com.kc.openset.ad.reward  OSETRewardVideo com.kc.openset.ad.reward  isUsable %com.kc.openset.ad.reward.OSETRewardAd  showAd %com.kc.openset.ad.reward.OSETRewardAd  getInstance (com.kc.openset.ad.reward.OSETRewardVideo  loadAd (com.kc.openset.ad.reward.OSETRewardVideo  
setContext (com.kc.openset.ad.reward.OSETRewardVideo  setPosId (com.kc.openset.ad.reward.OSETRewardVideo  	setUserId (com.kc.openset.ad.reward.OSETRewardVideo  	startLoad (com.kc.openset.ad.reward.OSETRewardVideo  OSETSDK com.kc.openset.config  getInstance com.kc.openset.config.OSETSDK  init com.kc.openset.config.OSETSDK  setCustomController com.kc.openset.config.OSETSDK  	setUserId com.kc.openset.config.OSETSDK  OSETCustomController  com.kc.openset.config.controller  Log 5com.kc.openset.config.controller.OSETCustomController  PrivacyAuditLogger 5com.kc.openset.config.controller.OSETCustomController  TAG 5com.kc.openset.config.controller.OSETCustomController  logDeniedInfoAccess 5com.kc.openset.config.controller.OSETCustomController  OSETInitListener com.kc.openset.listener  IOException java.io  InputStream java.io  printStackTrace java.io.IOException  	available java.io.InputStream  close java.io.InputStream  read java.io.InputStream  use java.io.InputStream  Class 	java.lang  	Exception 	java.lang  
StringBuilder 	java.lang  message java.lang.Exception  printStackTrace java.lang.Exception  append java.lang.StringBuilder  toString java.lang.StringBuilder  currentTimeMillis java.lang.System  
BigDecimal 	java.math  
BigInteger 	java.math  Charset java.nio.charset  	ByteArray kotlin  CharSequence kotlin  	Function1 kotlin  	Function2 kotlin  Nothing kotlin  Pair kotlin  Result kotlin  String kotlin  Triple kotlin  let kotlin  map kotlin  repeat kotlin  run kotlin  to kotlin  toList kotlin  use kotlin  not kotlin.Boolean  isEmpty kotlin.CharSequence  div 
kotlin.Double  toInt 
kotlin.Double  Int kotlin.Enum  String kotlin.Enum  toInt kotlin.Float  
coerceAtLeast 
kotlin.Int  coerceAtMost 
kotlin.Int  coerceIn 
kotlin.Int  	compareTo 
kotlin.Int  inc 
kotlin.Int  minus 
kotlin.Int  minusAssign 
kotlin.Int  plus 
kotlin.Int  
plusAssign 
kotlin.Int  rangeTo 
kotlin.Int  times 
kotlin.Int  toDouble 
kotlin.Int  
unaryMinus 
kotlin.Int  minus kotlin.Long  toInt kotlin.Long  
component1 kotlin.Pair  
component2 kotlin.Pair  	Companion 
kotlin.String  contains 
kotlin.String  indexOf 
kotlin.String  invoke 
kotlin.String  isBlank 
kotlin.String  isEmpty 
kotlin.String  
isNotBlank 
kotlin.String  
isNotEmpty 
kotlin.String  length 
kotlin.String  let 
kotlin.String  	lowercase 
kotlin.String  split 
kotlin.String  take 
kotlin.String  to 
kotlin.String  toIntOrNull 
kotlin.String  trim 
kotlin.String  
trimIndent 
kotlin.String  invoke kotlin.String.Companion  message kotlin.Throwable  printStackTrace kotlin.Throwable  
component1 
kotlin.Triple  
component2 
kotlin.Triple  
component3 
kotlin.Triple  
Collection kotlin.collections  IntIterator kotlin.collections  List kotlin.collections  Map kotlin.collections  MutableList kotlin.collections  
MutableMap kotlin.collections  
MutableSet kotlin.collections  Set kotlin.collections  any kotlin.collections  average kotlin.collections  
component1 kotlin.collections  
component2 kotlin.collections  contains kotlin.collections  count kotlin.collections  	emptyList kotlin.collections  emptyMap kotlin.collections  filter kotlin.collections  filterIsInstance kotlin.collections  find kotlin.collections  forEach kotlin.collections  forEachIndexed kotlin.collections  indexOf kotlin.collections  indices kotlin.collections  isEmpty kotlin.collections  
isNotEmpty kotlin.collections  
isNullOrEmpty kotlin.collections  joinToString kotlin.collections  listOf kotlin.collections  map kotlin.collections  mapOf kotlin.collections  maxOfOrNull kotlin.collections  	maxOrNull kotlin.collections  minOfOrNull kotlin.collections  	minOrNull kotlin.collections  minusAssign kotlin.collections  
mutableListOf kotlin.collections  mutableMapOf kotlin.collections  mutableSetOf kotlin.collections  
plusAssign kotlin.collections  set kotlin.collections  sumOf kotlin.collections  sumOfInt kotlin.collections  take kotlin.collections  toList kotlin.collections  toMap kotlin.collections  toMutableMap kotlin.collections  toMutableSet kotlin.collections  count kotlin.collections.Collection  sumOf kotlin.collections.Collection  hasNext kotlin.collections.IntIterator  next kotlin.collections.IntIterator  any kotlin.collections.List  contains kotlin.collections.List  count kotlin.collections.List  filter kotlin.collections.List  filterIsInstance kotlin.collections.List  find kotlin.collections.List  forEachIndexed kotlin.collections.List  get kotlin.collections.List  indices kotlin.collections.List  isEmpty kotlin.collections.List  
isNotEmpty kotlin.collections.List  
isNullOrEmpty kotlin.collections.List  map kotlin.collections.List  size kotlin.collections.List  sumOf kotlin.collections.List  toList kotlin.collections.List  Entry kotlin.collections.Map  containsKey kotlin.collections.Map  get kotlin.collections.Map  isEmpty kotlin.collections.Map  
isNotEmpty kotlin.collections.Map  
isNullOrEmpty kotlin.collections.Map  keys kotlin.collections.Map  let kotlin.collections.Map  size kotlin.collections.Map  values kotlin.collections.Map  
component1 kotlin.collections.Map.Entry  
component2 kotlin.collections.Map.Entry  add kotlin.collections.MutableList  addAll kotlin.collections.MutableList  average kotlin.collections.MutableList  clear kotlin.collections.MutableList  get kotlin.collections.MutableList  isEmpty kotlin.collections.MutableList  
isNotEmpty kotlin.collections.MutableList  joinToString kotlin.collections.MutableList  maxOfOrNull kotlin.collections.MutableList  	maxOrNull kotlin.collections.MutableList  minOfOrNull kotlin.collections.MutableList  	minOrNull kotlin.collections.MutableList  removeAt kotlin.collections.MutableList  size kotlin.collections.MutableList  toList kotlin.collections.MutableList  containsKey kotlin.collections.MutableMap  get kotlin.collections.MutableMap  set kotlin.collections.MutableMap  size kotlin.collections.MutableMap  toMap kotlin.collections.MutableMap  toMutableMap kotlin.collections.MutableMap  add kotlin.collections.MutableSet  contains kotlin.collections.MutableSet  remove kotlin.collections.MutableSet  size kotlin.collections.MutableSet  toMutableSet kotlin.collections.MutableSet  contains kotlin.collections.Set  use 	kotlin.io  java 
kotlin.jvm  	CharRange 
kotlin.ranges  IntRange 
kotlin.ranges  	LongRange 
kotlin.ranges  	UIntRange 
kotlin.ranges  
ULongRange 
kotlin.ranges  
coerceAtLeast 
kotlin.ranges  coerceAtMost 
kotlin.ranges  coerceIn 
kotlin.ranges  contains 
kotlin.ranges  until 
kotlin.ranges  iterator kotlin.ranges.IntProgression  iterator kotlin.ranges.IntRange  java kotlin.reflect.KClass  Sequence kotlin.sequences  any kotlin.sequences  average kotlin.sequences  contains kotlin.sequences  count kotlin.sequences  filter kotlin.sequences  filterIsInstance kotlin.sequences  find kotlin.sequences  forEach kotlin.sequences  forEachIndexed kotlin.sequences  indexOf kotlin.sequences  joinToString kotlin.sequences  map kotlin.sequences  maxOfOrNull kotlin.sequences  	maxOrNull kotlin.sequences  minOfOrNull kotlin.sequences  	minOrNull kotlin.sequences  sumOf kotlin.sequences  take kotlin.sequences  toList kotlin.sequences  toMutableSet kotlin.sequences  Charsets kotlin.text  String kotlin.text  any kotlin.text  contains kotlin.text  count kotlin.text  filter kotlin.text  find kotlin.text  forEach kotlin.text  forEachIndexed kotlin.text  indexOf kotlin.text  indices kotlin.text  isBlank kotlin.text  isEmpty kotlin.text  
isNotBlank kotlin.text  
isNotEmpty kotlin.text  
isNullOrEmpty kotlin.text  	lowercase kotlin.text  map kotlin.text  maxOfOrNull kotlin.text  	maxOrNull kotlin.text  minOfOrNull kotlin.text  	minOrNull kotlin.text  repeat kotlin.text  set kotlin.text  split kotlin.text  sumOf kotlin.text  take kotlin.text  toIntOrNull kotlin.text  toList kotlin.text  trim kotlin.text  
trimIndent kotlin.text  UTF_8 kotlin.text.Charsets  MediaPlayer android.app.Activity  MediaPlayer android.content.Context  MediaPlayer android.content.ContextWrapper  MediaPlayer 
android.media  create android.media.MediaPlayer  	isLooping android.media.MediaPlayer  	isPlaying android.media.MediaPlayer  let android.media.MediaPlayer  pause android.media.MediaPlayer  release android.media.MediaPlayer  start android.media.MediaPlayer  stop android.media.MediaPlayer  MediaPlayer 'android.support.v4.app.FragmentActivity  onPause 'android.support.v4.app.FragmentActivity  MediaPlayer &android.support.v4.app.SupportActivity  MediaPlayer (android.support.v7.app.AppCompatActivity  	onDestroy (android.support.v7.app.AppCompatActivity  onPause (android.support.v7.app.AppCompatActivity  MediaPlayer  android.view.ContextThemeWrapper  MediaPlayer com.ainative.mountainsurvival  winter #com.ainative.mountainsurvival.R.raw  MediaPlayer +com.ainative.mountainsurvival.StartActivity  R +com.ainative.mountainsurvival.StartActivity  initializeBackgroundMusic +com.ainative.mountainsurvival.StartActivity  isMusicEnabled +com.ainative.mountainsurvival.StartActivity  let +com.ainative.mountainsurvival.StartActivity  mediaPlayer +com.ainative.mountainsurvival.StartActivity  stopBackgroundMusic +com.ainative.mountainsurvival.StartActivity  toggleMusic +com.ainative.mountainsurvival.StartActivity  MediaPlayer 5com.ainative.mountainsurvival.StartActivity.Companion  R 5com.ainative.mountainsurvival.StartActivity.Companion  let 5com.ainative.mountainsurvival.StartActivity.Companion  musicToggleButton >com.ainative.mountainsurvival.databinding.ActivityStartBinding  MusicManager android.app.Activity  getMusicStatusText android.app.Activity  
initialize android.app.Activity  pauseBackgroundMusic android.app.Activity  resumeBackgroundMusic android.app.Activity  startBackgroundMusic android.app.Activity  stopBackgroundMusic android.app.Activity  toggleMusic android.app.Activity  SharedPreferences android.content  MusicManager android.content.Context  getMusicStatusText android.content.Context  
initialize android.content.Context  pauseBackgroundMusic android.content.Context  resumeBackgroundMusic android.content.Context  startBackgroundMusic android.content.Context  stopBackgroundMusic android.content.Context  toggleMusic android.content.Context  MusicManager android.content.ContextWrapper  getMusicStatusText android.content.ContextWrapper  
initialize android.content.ContextWrapper  pauseBackgroundMusic android.content.ContextWrapper  resumeBackgroundMusic android.content.ContextWrapper  startBackgroundMusic android.content.ContextWrapper  stopBackgroundMusic android.content.ContextWrapper  toggleMusic android.content.ContextWrapper  MusicManager 'android.support.v4.app.FragmentActivity  getMusicStatusText 'android.support.v4.app.FragmentActivity  
initialize 'android.support.v4.app.FragmentActivity  pauseBackgroundMusic 'android.support.v4.app.FragmentActivity  resumeBackgroundMusic 'android.support.v4.app.FragmentActivity  startBackgroundMusic 'android.support.v4.app.FragmentActivity  stopBackgroundMusic 'android.support.v4.app.FragmentActivity  toggleMusic 'android.support.v4.app.FragmentActivity  MusicManager &android.support.v4.app.SupportActivity  getMusicStatusText &android.support.v4.app.SupportActivity  
initialize &android.support.v4.app.SupportActivity  pauseBackgroundMusic &android.support.v4.app.SupportActivity  resumeBackgroundMusic &android.support.v4.app.SupportActivity  startBackgroundMusic &android.support.v4.app.SupportActivity  stopBackgroundMusic &android.support.v4.app.SupportActivity  toggleMusic &android.support.v4.app.SupportActivity  MusicManager (android.support.v7.app.AppCompatActivity  getMusicStatusText (android.support.v7.app.AppCompatActivity  
initialize (android.support.v7.app.AppCompatActivity  pauseBackgroundMusic (android.support.v7.app.AppCompatActivity  resumeBackgroundMusic (android.support.v7.app.AppCompatActivity  startBackgroundMusic (android.support.v7.app.AppCompatActivity  stopBackgroundMusic (android.support.v7.app.AppCompatActivity  toggleMusic (android.support.v7.app.AppCompatActivity  MusicManager  android.view.ContextThemeWrapper  getMusicStatusText  android.view.ContextThemeWrapper  
initialize  android.view.ContextThemeWrapper  pauseBackgroundMusic  android.view.ContextThemeWrapper  resumeBackgroundMusic  android.view.ContextThemeWrapper  startBackgroundMusic  android.view.ContextThemeWrapper  stopBackgroundMusic  android.view.ContextThemeWrapper  toggleMusic  android.view.ContextThemeWrapper  MusicManager com.ainative.mountainsurvival  getMusicStatusText com.ainative.mountainsurvival  
initialize com.ainative.mountainsurvival  pauseBackgroundMusic com.ainative.mountainsurvival  resumeBackgroundMusic com.ainative.mountainsurvival  startBackgroundMusic com.ainative.mountainsurvival  stopBackgroundMusic com.ainative.mountainsurvival  toggleMusic com.ainative.mountainsurvival  MusicManager *com.ainative.mountainsurvival.MainActivity  
initialize *com.ainative.mountainsurvival.MainActivity  pauseBackgroundMusic *com.ainative.mountainsurvival.MainActivity  resumeBackgroundMusic *com.ainative.mountainsurvival.MainActivity  startBackgroundMusic *com.ainative.mountainsurvival.MainActivity  stopBackgroundMusic *com.ainative.mountainsurvival.MainActivity  MusicManager 4com.ainative.mountainsurvival.MainActivity.Companion  
initialize 4com.ainative.mountainsurvival.MainActivity.Companion  pauseBackgroundMusic 4com.ainative.mountainsurvival.MainActivity.Companion  resumeBackgroundMusic 4com.ainative.mountainsurvival.MainActivity.Companion  startBackgroundMusic 4com.ainative.mountainsurvival.MainActivity.Companion  stopBackgroundMusic 4com.ainative.mountainsurvival.MainActivity.Companion  Context *com.ainative.mountainsurvival.MusicManager  KEY_MUSIC_ENABLED *com.ainative.mountainsurvival.MusicManager  Log *com.ainative.mountainsurvival.MusicManager  MediaPlayer *com.ainative.mountainsurvival.MusicManager  
PREFS_NAME *com.ainative.mountainsurvival.MusicManager  R *com.ainative.mountainsurvival.MusicManager  TAG *com.ainative.mountainsurvival.MusicManager  getMusicStatusText *com.ainative.mountainsurvival.MusicManager  
initialize *com.ainative.mountainsurvival.MusicManager  
isInitialized *com.ainative.mountainsurvival.MusicManager  isMusicEnabled *com.ainative.mountainsurvival.MusicManager  let *com.ainative.mountainsurvival.MusicManager  mediaPlayer *com.ainative.mountainsurvival.MusicManager  pauseBackgroundMusic *com.ainative.mountainsurvival.MusicManager  resumeBackgroundMusic *com.ainative.mountainsurvival.MusicManager  startBackgroundMusic *com.ainative.mountainsurvival.MusicManager  stopBackgroundMusic *com.ainative.mountainsurvival.MusicManager  toggleMusic *com.ainative.mountainsurvival.MusicManager  MusicManager +com.ainative.mountainsurvival.StartActivity  getMusicStatusText +com.ainative.mountainsurvival.StartActivity  
initialize +com.ainative.mountainsurvival.StartActivity  pauseBackgroundMusic +com.ainative.mountainsurvival.StartActivity  resumeBackgroundMusic +com.ainative.mountainsurvival.StartActivity  startBackgroundMusic +com.ainative.mountainsurvival.StartActivity  updateMusicButtonText +com.ainative.mountainsurvival.StartActivity  MusicManager 5com.ainative.mountainsurvival.StartActivity.Companion  getMusicStatusText 5com.ainative.mountainsurvival.StartActivity.Companion  
initialize 5com.ainative.mountainsurvival.StartActivity.Companion  pauseBackgroundMusic 5com.ainative.mountainsurvival.StartActivity.Companion  resumeBackgroundMusic 5com.ainative.mountainsurvival.StartActivity.Companion  startBackgroundMusic 5com.ainative.mountainsurvival.StartActivity.Companion  stopBackgroundMusic 5com.ainative.mountainsurvival.StartActivity.Companion  toggleMusic 5com.ainative.mountainsurvival.StartActivity.Companion  
isInitialized android.app.Activity  
isInitialized android.content.Context  
isInitialized android.content.ContextWrapper  
isInitialized 'android.support.v4.app.FragmentActivity  
isInitialized &android.support.v4.app.SupportActivity  
isInitialized (android.support.v7.app.AppCompatActivity  
isInitialized  android.view.ContextThemeWrapper  
isInitialized com.ainative.mountainsurvival  
isInitialized +com.ainative.mountainsurvival.StartActivity  
isInitialized 5com.ainative.mountainsurvival.StartActivity.Companion  
isInitialized kotlin  KMutableProperty0 kotlin.reflect  
isInitialized  kotlin.reflect.KMutableProperty0  android +com.ainative.mountainsurvival.StartActivity  android 5com.ainative.mountainsurvival.StartActivity.Companion  LanguageManager android.app.Activity  getCurrentLanguage android.app.Activity  getLanguageDisplayName android.app.Activity  getSupportedLanguages android.app.Activity  initializeLanguage android.app.Activity  isMusicEnabled android.app.Activity  map android.app.Activity  recreate android.app.Activity  setLanguage android.app.Activity  toTypedArray android.app.Activity  LanguageManager android.app.Application  getCurrentLanguage android.app.Application  initializeLanguage android.app.Application  let android.app.Application  LanguageManager android.content.Context  getCurrentLanguage android.content.Context  getLanguageDisplayName android.content.Context  getSupportedLanguages android.content.Context  initializeLanguage android.content.Context  isMusicEnabled android.content.Context  map android.content.Context  	resources android.content.Context  setLanguage android.content.Context  toTypedArray android.content.Context  LanguageManager android.content.ContextWrapper  getCurrentLanguage android.content.ContextWrapper  getLanguageDisplayName android.content.ContextWrapper  getSupportedLanguages android.content.ContextWrapper  initializeLanguage android.content.ContextWrapper  isMusicEnabled android.content.ContextWrapper  map android.content.ContextWrapper  setLanguage android.content.ContextWrapper  toTypedArray android.content.ContextWrapper  	getString !android.content.SharedPreferences  	putString (android.content.SharedPreferences.Editor  
Configuration android.content.res  	setLocale !android.content.res.Configuration  
configuration android.content.res.Resources  displayMetrics android.content.res.Resources  updateConfiguration android.content.res.Resources  LanguageManager ,android.support.multidex.MultiDexApplication  getCurrentLanguage ,android.support.multidex.MultiDexApplication  initializeLanguage ,android.support.multidex.MultiDexApplication  let ,android.support.multidex.MultiDexApplication  LanguageManager 'android.support.v4.app.FragmentActivity  getCurrentLanguage 'android.support.v4.app.FragmentActivity  getLanguageDisplayName 'android.support.v4.app.FragmentActivity  getSupportedLanguages 'android.support.v4.app.FragmentActivity  initializeLanguage 'android.support.v4.app.FragmentActivity  isMusicEnabled 'android.support.v4.app.FragmentActivity  map 'android.support.v4.app.FragmentActivity  setLanguage 'android.support.v4.app.FragmentActivity  toTypedArray 'android.support.v4.app.FragmentActivity  LanguageManager &android.support.v4.app.SupportActivity  getCurrentLanguage &android.support.v4.app.SupportActivity  getLanguageDisplayName &android.support.v4.app.SupportActivity  getSupportedLanguages &android.support.v4.app.SupportActivity  initializeLanguage &android.support.v4.app.SupportActivity  isMusicEnabled &android.support.v4.app.SupportActivity  map &android.support.v4.app.SupportActivity  setLanguage &android.support.v4.app.SupportActivity  toTypedArray &android.support.v4.app.SupportActivity  setNegativeButton *android.support.v7.app.AlertDialog.Builder  setSingleChoiceItems *android.support.v7.app.AlertDialog.Builder  LanguageManager (android.support.v7.app.AppCompatActivity  getCurrentLanguage (android.support.v7.app.AppCompatActivity  getLanguageDisplayName (android.support.v7.app.AppCompatActivity  getSupportedLanguages (android.support.v7.app.AppCompatActivity  initializeLanguage (android.support.v7.app.AppCompatActivity  isMusicEnabled (android.support.v7.app.AppCompatActivity  map (android.support.v7.app.AppCompatActivity  setLanguage (android.support.v7.app.AppCompatActivity  toTypedArray (android.support.v7.app.AppCompatActivity  LanguageManager  android.view.ContextThemeWrapper  getCurrentLanguage  android.view.ContextThemeWrapper  getLanguageDisplayName  android.view.ContextThemeWrapper  getSupportedLanguages  android.view.ContextThemeWrapper  initializeLanguage  android.view.ContextThemeWrapper  isMusicEnabled  android.view.ContextThemeWrapper  map  android.view.ContextThemeWrapper  setLanguage  android.view.ContextThemeWrapper  toTypedArray  android.view.ContextThemeWrapper  
Configuration com.ainative.mountainsurvival  LanguageManager com.ainative.mountainsurvival  Locale com.ainative.mountainsurvival  getCurrentLanguage com.ainative.mountainsurvival  getLanguageDisplayName com.ainative.mountainsurvival  getSupportedLanguages com.ainative.mountainsurvival  initializeLanguage com.ainative.mountainsurvival  isMusicEnabled com.ainative.mountainsurvival  setLanguage com.ainative.mountainsurvival  toTypedArray com.ainative.mountainsurvival  
Configuration -com.ainative.mountainsurvival.LanguageManager  Context -com.ainative.mountainsurvival.LanguageManager  DEFAULT_LANGUAGE -com.ainative.mountainsurvival.LanguageManager  KEY_LANGUAGE -com.ainative.mountainsurvival.LanguageManager  LANGUAGE_ENGLISH -com.ainative.mountainsurvival.LanguageManager  LANGUAGE_SIMPLIFIED_CHINESE -com.ainative.mountainsurvival.LanguageManager  LANGUAGE_TRADITIONAL_CHINESE -com.ainative.mountainsurvival.LanguageManager  Locale -com.ainative.mountainsurvival.LanguageManager  Log -com.ainative.mountainsurvival.LanguageManager  
PREFS_NAME -com.ainative.mountainsurvival.LanguageManager  R -com.ainative.mountainsurvival.LanguageManager  TAG -com.ainative.mountainsurvival.LanguageManager  
applyLanguage -com.ainative.mountainsurvival.LanguageManager  getCurrentLanguage -com.ainative.mountainsurvival.LanguageManager  getLanguageDisplayName -com.ainative.mountainsurvival.LanguageManager  getSupportedLanguages -com.ainative.mountainsurvival.LanguageManager  initializeLanguage -com.ainative.mountainsurvival.LanguageManager  listOf -com.ainative.mountainsurvival.LanguageManager  setLanguage -com.ainative.mountainsurvival.LanguageManager  LanguageManager *com.ainative.mountainsurvival.MainActivity  initializeLanguage *com.ainative.mountainsurvival.MainActivity  LanguageManager 4com.ainative.mountainsurvival.MainActivity.Companion  initializeLanguage 4com.ainative.mountainsurvival.MainActivity.Companion  LanguageManager 9com.ainative.mountainsurvival.MountainSurvivalApplication  getCurrentLanguage 9com.ainative.mountainsurvival.MountainSurvivalApplication  initializeLanguage 9com.ainative.mountainsurvival.MountainSurvivalApplication  let 9com.ainative.mountainsurvival.MountainSurvivalApplication  LanguageManager Ccom.ainative.mountainsurvival.MountainSurvivalApplication.Companion  getCurrentLanguage Ccom.ainative.mountainsurvival.MountainSurvivalApplication.Companion  initializeLanguage Ccom.ainative.mountainsurvival.MountainSurvivalApplication.Companion  let Ccom.ainative.mountainsurvival.MountainSurvivalApplication.Companion  language_cancel &com.ainative.mountainsurvival.R.string  language_english &com.ainative.mountainsurvival.R.string  language_selection_title &com.ainative.mountainsurvival.R.string  language_simplified_chinese &com.ainative.mountainsurvival.R.string  language_traditional_chinese &com.ainative.mountainsurvival.R.string  	music_off &com.ainative.mountainsurvival.R.string  music_on &com.ainative.mountainsurvival.R.string  AlertDialog +com.ainative.mountainsurvival.StartActivity  LanguageManager +com.ainative.mountainsurvival.StartActivity  getCurrentLanguage +com.ainative.mountainsurvival.StartActivity  getLanguageDisplayName +com.ainative.mountainsurvival.StartActivity  	getString +com.ainative.mountainsurvival.StartActivity  getSupportedLanguages +com.ainative.mountainsurvival.StartActivity  initializeLanguage +com.ainative.mountainsurvival.StartActivity  map +com.ainative.mountainsurvival.StartActivity  recreate +com.ainative.mountainsurvival.StartActivity  setLanguage +com.ainative.mountainsurvival.StartActivity  showLanguageSelectionDialog +com.ainative.mountainsurvival.StartActivity  toTypedArray +com.ainative.mountainsurvival.StartActivity  AlertDialog 5com.ainative.mountainsurvival.StartActivity.Companion  LanguageManager 5com.ainative.mountainsurvival.StartActivity.Companion  getCurrentLanguage 5com.ainative.mountainsurvival.StartActivity.Companion  getLanguageDisplayName 5com.ainative.mountainsurvival.StartActivity.Companion  getSupportedLanguages 5com.ainative.mountainsurvival.StartActivity.Companion  initializeLanguage 5com.ainative.mountainsurvival.StartActivity.Companion  isMusicEnabled 5com.ainative.mountainsurvival.StartActivity.Companion  map 5com.ainative.mountainsurvival.StartActivity.Companion  setLanguage 5com.ainative.mountainsurvival.StartActivity.Companion  toTypedArray 5com.ainative.mountainsurvival.StartActivity.Companion  languageButton >com.ainative.mountainsurvival.databinding.ActivityStartBinding  Boolean 	java.util  
Configuration 	java.util  Context 	java.util  	Exception 	java.util  List 	java.util  Locale 	java.util  Log 	java.util  R 	java.util  String 	java.util  listOf 	java.util  ENGLISH java.util.Locale  SIMPLIFIED_CHINESE java.util.Locale  TRADITIONAL_CHINESE java.util.Locale  displayName java.util.Locale  Array kotlin  toTypedArray kotlin.collections  indexOf kotlin.collections.List  toTypedArray kotlin.collections.List  	getString android.app.Activity  	getString android.content.ContextWrapper  	getString 'android.support.v4.app.FragmentActivity  	getString &android.support.v4.app.SupportActivity  	getString (android.support.v7.app.AppCompatActivity  	getString  android.view.ContextThemeWrapper  	ImageView android.widget  setImageResource android.widget.ImageView  	getString com.ainative.mountainsurvival  R *com.ainative.mountainsurvival.MainActivity  	getString *com.ainative.mountainsurvival.MainActivity  R 4com.ainative.mountainsurvival.MainActivity.Companion  	getString 4com.ainative.mountainsurvival.MainActivity.Companion  title (com.ainative.mountainsurvival.R.drawable  title_en (com.ainative.mountainsurvival.R.drawable  ad_load_failed_message &com.ainative.mountainsurvival.R.string  ad_load_failed_title &com.ainative.mountainsurvival.R.string  ad_loading_message &com.ainative.mountainsurvival.R.string  ad_loading_title &com.ainative.mountainsurvival.R.string  	ad_revive &com.ainative.mountainsurvival.R.string  ad_show_failed_message &com.ainative.mountainsurvival.R.string  ad_show_failed_title &com.ainative.mountainsurvival.R.string  
cabin_damaged &com.ainative.mountainsurvival.R.string  cabin_good_condition &com.ainative.mountainsurvival.R.string  cabin_improved &com.ainative.mountainsurvival.R.string  cabin_poor_condition &com.ainative.mountainsurvival.R.string  challenge_again &com.ainative.mountainsurvival.R.string  choice_effect_text &com.ainative.mountainsurvival.R.string  confirm &com.ainative.mountainsurvival.R.string  continue_button &com.ainative.mountainsurvival.R.string  
continue_game &com.ainative.mountainsurvival.R.string  eat_food &com.ainative.mountainsurvival.R.string  eat_food_result &com.ainative.mountainsurvival.R.string  environment_effects &com.ainative.mountainsurvival.R.string  event_effect &com.ainative.mountainsurvival.R.string  event_not_found &com.ainative.mountainsurvival.R.string  
firewood_name &com.ainative.mountainsurvival.R.string  	food_name &com.ainative.mountainsurvival.R.string  food_phase_description &com.ainative.mountainsurvival.R.string  food_phase_title &com.ainative.mountainsurvival.R.string  game_data_load_failed &com.ainative.mountainsurvival.R.string  high_morale_effect &com.ainative.mountainsurvival.R.string  hope_greatly_decreased &com.ainative.mountainsurvival.R.string  hope_greatly_increased &com.ainative.mountainsurvival.R.string  hope_slightly_decreased &com.ainative.mountainsurvival.R.string  hope_slightly_increased &com.ainative.mountainsurvival.R.string  leave_mountain &com.ainative.mountainsurvival.R.string  low_morale_effect &com.ainative.mountainsurvival.R.string  new_day_generic &com.ainative.mountainsurvival.R.string  night_phase_title &com.ainative.mountainsurvival.R.string  night_settlement &com.ainative.mountainsurvival.R.string  night_with_firewood &com.ainative.mountainsurvival.R.string  night_without_firewood &com.ainative.mountainsurvival.R.string  no_choice_continue &com.ainative.mountainsurvival.R.string  rest_indoors &com.ainative.mountainsurvival.R.string  restart_challenge &com.ainative.mountainsurvival.R.string  revive_ad_load_failed_message &com.ainative.mountainsurvival.R.string  revive_ad_loading_message &com.ainative.mountainsurvival.R.string  revive_ad_loading_title &com.ainative.mountainsurvival.R.string  revive_ad_show_failed_message &com.ainative.mountainsurvival.R.string  revive_success_generic &com.ainative.mountainsurvival.R.string  revive_success_message &com.ainative.mountainsurvival.R.string  revive_success_title &com.ainative.mountainsurvival.R.string  reward_message &com.ainative.mountainsurvival.R.string  reward_title &com.ainative.mountainsurvival.R.string  	save_food &com.ainative.mountainsurvival.R.string  save_food_result &com.ainative.mountainsurvival.R.string  search_resources &com.ainative.mountainsurvival.R.string  stamina_name &com.ainative.mountainsurvival.R.string  status_change_prefix &com.ainative.mountainsurvival.R.string  warmth_name &com.ainative.mountainsurvival.R.string  updateTitleImage +com.ainative.mountainsurvival.StartActivity  titleImageView >com.ainative.mountainsurvival.databinding.ActivityStartBinding  plus 
kotlin.String                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               