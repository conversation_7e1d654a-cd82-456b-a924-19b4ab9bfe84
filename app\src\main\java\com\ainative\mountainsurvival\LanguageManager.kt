package com.ainative.mountainsurvival

import android.content.Context
import android.content.SharedPreferences
import android.content.res.Configuration
import android.util.Log
import java.util.*

/**
 * 语言管理器
 * 负责管理应用的多语言设置和切换
 */
object LanguageManager {
    
    private const val TAG = "LanguageManager"
    private const val PREFS_NAME = "language_settings"
    private const val KEY_LANGUAGE = "selected_language"
    
    // 支持的语言代码
    const val LANGUAGE_ENGLISH = "en"
    const val LANGUAGE_SIMPLIFIED_CHINESE = "zh"
    const val LANGUAGE_TRADITIONAL_CHINESE = "zh-rTW"
    
    // 默认语言为英文
    private const val DEFAULT_LANGUAGE = LANGUAGE_ENGLISH
    
    /**
     * 获取当前设置的语言
     * @param context 上下文
     * @return 语言代码
     */
    fun getCurrentLanguage(context: Context): String {
        val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        return prefs.getString(KEY_LANGUAGE, DEFAULT_LANGUAGE) ?: DEFAULT_LANGUAGE
    }
    
    /**
     * 设置应用语言
     * @param context 上下文
     * @param languageCode 语言代码
     */
    fun setLanguage(context: Context, languageCode: String) {
        Log.d(TAG, "设置语言为: $languageCode")
        
        // 保存语言设置
        val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        prefs.edit().putString(KEY_LANGUAGE, languageCode).apply()
        
        // 应用语言设置
        applyLanguage(context, languageCode)
    }
    
    /**
     * 应用语言设置到当前上下文
     * @param context 上下文
     * @param languageCode 语言代码
     */
    private fun applyLanguage(context: Context, languageCode: String) {
        try {
            val locale = when (languageCode) {
                LANGUAGE_ENGLISH -> Locale.ENGLISH
                LANGUAGE_SIMPLIFIED_CHINESE -> Locale.SIMPLIFIED_CHINESE
                LANGUAGE_TRADITIONAL_CHINESE -> Locale.TRADITIONAL_CHINESE
                else -> Locale.ENGLISH
            }
            
            val config = Configuration(context.resources.configuration)
            config.setLocale(locale)
            
            context.resources.updateConfiguration(config, context.resources.displayMetrics)
            
            Log.d(TAG, "语言设置已应用: ${locale.displayName}")
        } catch (e: Exception) {
            Log.e(TAG, "应用语言设置失败", e)
        }
    }
    
    /**
     * 初始化语言设置
     * 在Application或Activity启动时调用
     * @param context 上下文
     */
    fun initializeLanguage(context: Context) {
        val currentLanguage = getCurrentLanguage(context)
        Log.d(TAG, "初始化语言设置: $currentLanguage")
        applyLanguage(context, currentLanguage)
    }
    
    /**
     * 获取语言显示名称
     * @param context 上下文
     * @param languageCode 语言代码
     * @return 语言显示名称
     */
    fun getLanguageDisplayName(context: Context, languageCode: String): String {
        return when (languageCode) {
            LANGUAGE_ENGLISH -> context.getString(R.string.language_english)
            LANGUAGE_SIMPLIFIED_CHINESE -> context.getString(R.string.language_simplified_chinese)
            LANGUAGE_TRADITIONAL_CHINESE -> context.getString(R.string.language_traditional_chinese)
            else -> context.getString(R.string.language_english)
        }
    }
    
    /**
     * 获取所有支持的语言列表
     * @return 语言代码列表
     */
    fun getSupportedLanguages(): List<String> {
        return listOf(
            LANGUAGE_ENGLISH,
            LANGUAGE_SIMPLIFIED_CHINESE,
            LANGUAGE_TRADITIONAL_CHINESE
        )
    }
    
    /**
     * 检查是否为默认语言
     * @param languageCode 语言代码
     * @return 是否为默认语言
     */
    fun isDefaultLanguage(languageCode: String): Boolean {
        return languageCode == DEFAULT_LANGUAGE
    }
    
    /**
     * 重置为默认语言
     * @param context 上下文
     */
    fun resetToDefaultLanguage(context: Context) {
        setLanguage(context, DEFAULT_LANGUAGE)
    }
}
