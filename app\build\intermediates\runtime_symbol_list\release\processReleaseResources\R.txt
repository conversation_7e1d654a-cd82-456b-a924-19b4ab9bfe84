int anim abc_fade_in 0x7f010000
int anim abc_fade_out 0x7f010001
int anim abc_grow_fade_in_from_bottom 0x7f010002
int anim abc_popup_enter 0x7f010003
int anim abc_popup_exit 0x7f010004
int anim abc_shrink_fade_out_from_bottom 0x7f010005
int anim abc_slide_in_bottom 0x7f010006
int anim abc_slide_in_top 0x7f010007
int anim abc_slide_out_bottom 0x7f010008
int anim abc_slide_out_top 0x7f010009
int anim abc_tooltip_enter 0x7f01000a
int anim abc_tooltip_exit 0x7f01000b
int anim bullet_bottom_dialog_enter 0x7f01000c
int anim bullet_bottom_dialog_exit 0x7f01000d
int anim cj_pay_activity_add_in_animation 0x7f01000e
int anim cj_pay_activity_fade_in_animation 0x7f01000f
int anim cj_pay_activity_fade_out_animation 0x7f010010
int anim cj_pay_activity_remove_out_animation 0x7f010011
int anim cj_pay_expo_easeout_interpolator 0x7f010012
int anim cj_pay_fragment_down_out_animation 0x7f010013
int anim cj_pay_fragment_up_in_animation 0x7f010014
int anim cj_pay_quadratic_easein_interpolator 0x7f010015
int anim cj_pay_slide_in_from_bottom_with_bezier 0x7f010016
int anim cj_pay_slide_out_to_bottom_with_bezier 0x7f010017
int anim cj_pay_slide_right_in 0x7f010018
int anim design_bottom_sheet_slide_in 0x7f010019
int anim design_bottom_sheet_slide_out 0x7f01001a
int anim design_snackbar_in 0x7f01001b
int anim design_snackbar_out 0x7f01001c
int anim download_confirm_dialog_slide_right_in 0x7f01001d
int anim download_confirm_dialog_slide_up 0x7f01001e
int anim ec_alpha_in 0x7f01001f
int anim ec_alpha_out 0x7f010020
int anim ec_base_enter 0x7f010021
int anim ec_base_exit 0x7f010022
int anim ec_bottom_in 0x7f010023
int anim ec_bottom_out 0x7f010024
int anim ec_commerce_activity_in 0x7f010025
int anim ec_commerce_activity_out 0x7f010026
int anim ec_commerce_pre_out 0x7f010027
int anim ec_pop_bottom_in 0x7f010028
int anim ec_pop_bottom_out 0x7f010029
int anim ec_pop_slide_in 0x7f01002a
int anim ec_pop_slide_out 0x7f01002b
int anim ec_slide_in 0x7f01002c
int anim ec_slide_out 0x7f01002d
int anim ec_zoom_in 0x7f01002e
int anim ec_zoom_out 0x7f01002f
int anim oset_dialog_enter 0x7f010030
int anim oset_dialog_exit 0x7f010031
int anim shopping_popup_fade_in 0x7f010032
int anim shopping_popup_fade_out 0x7f010033
int anim sig_dialog_slide_in_bottom 0x7f010034
int anim sig_dialog_slide_out_bottom 0x7f010035
int anim slide_right_in 0x7f010036
int anim slide_up 0x7f010037
int anim tt_text_animation_x_in 0x7f010038
int anim tt_text_animation_y_in 0x7f010039
int anim tt_text_animation_y_out 0x7f01003a
int animator design_appbar_state_list_animator 0x7f020000
int animator design_fab_hide_motion_spec 0x7f020001
int animator design_fab_show_motion_spec 0x7f020002
int animator mtrl_btn_state_list_anim 0x7f020003
int animator mtrl_btn_unelevated_state_list_anim 0x7f020004
int animator mtrl_chip_state_list_anim 0x7f020005
int animator mtrl_fab_hide_motion_spec 0x7f020006
int animator mtrl_fab_show_motion_spec 0x7f020007
int animator mtrl_fab_transformation_sheet_collapse_spec 0x7f020008
int animator mtrl_fab_transformation_sheet_expand_spec 0x7f020009
int attr OSETbackColor 0x7f030000
int attr OSETbackWidth 0x7f030001
int attr OSETprogColor 0x7f030002
int attr OSETprogFirstColor 0x7f030003
int attr OSETprogStartColor 0x7f030004
int attr OSETprogWidth 0x7f030005
int attr OSETprogress 0x7f030006
int attr actionBarDivider 0x7f030007
int attr actionBarItemBackground 0x7f030008
int attr actionBarPopupTheme 0x7f030009
int attr actionBarSize 0x7f03000a
int attr actionBarSplitStyle 0x7f03000b
int attr actionBarStyle 0x7f03000c
int attr actionBarTabBarStyle 0x7f03000d
int attr actionBarTabStyle 0x7f03000e
int attr actionBarTabTextStyle 0x7f03000f
int attr actionBarTheme 0x7f030010
int attr actionBarWidgetTheme 0x7f030011
int attr actionButtonStyle 0x7f030012
int attr actionDropDownStyle 0x7f030013
int attr actionLayout 0x7f030014
int attr actionMenuTextAppearance 0x7f030015
int attr actionMenuTextColor 0x7f030016
int attr actionModeBackground 0x7f030017
int attr actionModeCloseButtonStyle 0x7f030018
int attr actionModeCloseDrawable 0x7f030019
int attr actionModeCopyDrawable 0x7f03001a
int attr actionModeCutDrawable 0x7f03001b
int attr actionModeFindDrawable 0x7f03001c
int attr actionModePasteDrawable 0x7f03001d
int attr actionModePopupWindowStyle 0x7f03001e
int attr actionModeSelectAllDrawable 0x7f03001f
int attr actionModeShareDrawable 0x7f030020
int attr actionModeSplitBackground 0x7f030021
int attr actionModeStyle 0x7f030022
int attr actionModeWebSearchDrawable 0x7f030023
int attr actionOverflowButtonStyle 0x7f030024
int attr actionOverflowMenuStyle 0x7f030025
int attr actionProviderClass 0x7f030026
int attr actionViewClass 0x7f030027
int attr activityChooserViewStyle 0x7f030028
int attr adScopeCircleColor 0x7f030029
int attr adScopeRadius 0x7f03002a
int attr adScopeRingBgColor 0x7f03002b
int attr adScopeRingColor 0x7f03002c
int attr adScopeStrokeWidth 0x7f03002d
int attr adScopeTextColor 0x7f03002e
int attr adSetMaxHeight 0x7f03002f
int attr adSetMaxWidth 0x7f030030
int attr alertDialogButtonGroupStyle 0x7f030031
int attr alertDialogCenterButtons 0x7f030032
int attr alertDialogStyle 0x7f030033
int attr alertDialogTheme 0x7f030034
int attr allowStacking 0x7f030035
int attr alpha 0x7f030036
int attr alphabeticModifiers 0x7f030037
int attr arrowHeadLength 0x7f030038
int attr arrowShaftLength 0x7f030039
int attr autoCompleteTextViewStyle 0x7f03003a
int attr autoSizeMaxTextSize 0x7f03003b
int attr autoSizeMinTextSize 0x7f03003c
int attr autoSizePresetSizes 0x7f03003d
int attr autoSizeStepGranularity 0x7f03003e
int attr autoSizeTextType 0x7f03003f
int attr auto_refresh_interval 0x7f030040
int attr background 0x7f030041
int attr backgroundSplit 0x7f030042
int attr backgroundStacked 0x7f030043
int attr backgroundTint 0x7f030044
int attr backgroundTintMode 0x7f030045
int attr barLength 0x7f030046
int attr barrierAllowsGoneWidgets 0x7f030047
int attr barrierDirection 0x7f030048
int attr behavior_autoHide 0x7f030049
int attr behavior_fitToContents 0x7f03004a
int attr behavior_hideable 0x7f03004b
int attr behavior_overlapTop 0x7f03004c
int attr behavior_peekHeight 0x7f03004d
int attr behavior_skipCollapsed 0x7f03004e
int attr beizi_adSize 0x7f03004f
int attr beizi_adSizes 0x7f030050
int attr beizi_adUnitId 0x7f030051
int attr beizi_bav_arrow_style 0x7f030052
int attr beizi_bav_color 0x7f030053
int attr beizi_bav_stroke_width 0x7f030054
int attr borderWidth 0x7f030055
int attr borderlessButtonStyle 0x7f030056
int attr bottomAppBarStyle 0x7f030057
int attr bottomNavigationStyle 0x7f030058
int attr bottomSheetDialogTheme 0x7f030059
int attr bottomSheetStyle 0x7f03005a
int attr boxBackgroundColor 0x7f03005b
int attr boxBackgroundMode 0x7f03005c
int attr boxCollapsedPaddingTop 0x7f03005d
int attr boxCornerRadiusBottomEnd 0x7f03005e
int attr boxCornerRadiusBottomStart 0x7f03005f
int attr boxCornerRadiusTopEnd 0x7f030060
int attr boxCornerRadiusTopStart 0x7f030061
int attr boxStrokeColor 0x7f030062
int attr boxStrokeWidth 0x7f030063
int attr buttonBarButtonStyle 0x7f030064
int attr buttonBarNegativeButtonStyle 0x7f030065
int attr buttonBarNeutralButtonStyle 0x7f030066
int attr buttonBarPositiveButtonStyle 0x7f030067
int attr buttonBarStyle 0x7f030068
int attr buttonGravity 0x7f030069
int attr buttonIconDimen 0x7f03006a
int attr buttonPanelSideLayout 0x7f03006b
int attr buttonStyle 0x7f03006c
int attr buttonStyleSmall 0x7f03006d
int attr buttonTint 0x7f03006e
int attr buttonTintMode 0x7f03006f
int attr cardBackgroundColor 0x7f030070
int attr cardCornerRadius 0x7f030071
int attr cardElevation 0x7f030072
int attr cardMaxElevation 0x7f030073
int attr cardPreventCornerOverlap 0x7f030074
int attr cardUseCompatPadding 0x7f030075
int attr cardViewStyle 0x7f030076
int attr chainUseRtl 0x7f030077
int attr checkboxStyle 0x7f030078
int attr checkedChip 0x7f030079
int attr checkedIcon 0x7f03007a
int attr checkedIconEnabled 0x7f03007b
int attr checkedIconVisible 0x7f03007c
int attr checkedTextViewStyle 0x7f03007d
int attr chipBackgroundColor 0x7f03007e
int attr chipCornerRadius 0x7f03007f
int attr chipEndPadding 0x7f030080
int attr chipGroupStyle 0x7f030081
int attr chipIcon 0x7f030082
int attr chipIconEnabled 0x7f030083
int attr chipIconSize 0x7f030084
int attr chipIconTint 0x7f030085
int attr chipIconVisible 0x7f030086
int attr chipMinHeight 0x7f030087
int attr chipSpacing 0x7f030088
int attr chipSpacingHorizontal 0x7f030089
int attr chipSpacingVertical 0x7f03008a
int attr chipStandaloneStyle 0x7f03008b
int attr chipStartPadding 0x7f03008c
int attr chipStrokeColor 0x7f03008d
int attr chipStrokeWidth 0x7f03008e
int attr chipStyle 0x7f03008f
int attr closeIcon 0x7f030090
int attr closeIconEnabled 0x7f030091
int attr closeIconEndPadding 0x7f030092
int attr closeIconSize 0x7f030093
int attr closeIconStartPadding 0x7f030094
int attr closeIconTint 0x7f030095
int attr closeIconVisible 0x7f030096
int attr closeItemLayout 0x7f030097
int attr collapseContentDescription 0x7f030098
int attr collapseIcon 0x7f030099
int attr collapsedTitleGravity 0x7f03009a
int attr collapsedTitleTextAppearance 0x7f03009b
int attr color 0x7f03009c
int attr colorAccent 0x7f03009d
int attr colorBackgroundFloating 0x7f03009e
int attr colorButtonNormal 0x7f03009f
int attr colorControlActivated 0x7f0300a0
int attr colorControlHighlight 0x7f0300a1
int attr colorControlNormal 0x7f0300a2
int attr colorError 0x7f0300a3
int attr colorPrimary 0x7f0300a4
int attr colorPrimaryDark 0x7f0300a5
int attr colorSecondary 0x7f0300a6
int attr colorSwitchThumbNormal 0x7f0300a7
int attr commitIcon 0x7f0300a8
int attr constraintSet 0x7f0300a9
int attr constraint_referenced_ids 0x7f0300aa
int attr content 0x7f0300ab
int attr contentDescription 0x7f0300ac
int attr contentInsetEnd 0x7f0300ad
int attr contentInsetEndWithActions 0x7f0300ae
int attr contentInsetLeft 0x7f0300af
int attr contentInsetRight 0x7f0300b0
int attr contentInsetStart 0x7f0300b1
int attr contentInsetStartWithNavigation 0x7f0300b2
int attr contentPadding 0x7f0300b3
int attr contentPaddingBottom 0x7f0300b4
int attr contentPaddingLeft 0x7f0300b5
int attr contentPaddingRight 0x7f0300b6
int attr contentPaddingTop 0x7f0300b7
int attr contentScrim 0x7f0300b8
int attr controlBackground 0x7f0300b9
int attr coordinatorLayoutStyle 0x7f0300ba
int attr cornerRadius 0x7f0300bb
int attr counterEnabled 0x7f0300bc
int attr counterMaxLength 0x7f0300bd
int attr counterOverflowTextAppearance 0x7f0300be
int attr counterTextAppearance 0x7f0300bf
int attr customNavigationLayout 0x7f0300c0
int attr defaultQueryHint 0x7f0300c1
int attr dialogCornerRadius 0x7f0300c2
int attr dialogPreferredPadding 0x7f0300c3
int attr dialogTheme 0x7f0300c4
int attr displayOptions 0x7f0300c5
int attr divider 0x7f0300c6
int attr dividerHorizontal 0x7f0300c7
int attr dividerPadding 0x7f0300c8
int attr dividerVertical 0x7f0300c9
int attr drawableSize 0x7f0300ca
int attr drawerArrowStyle 0x7f0300cb
int attr dropDownListViewStyle 0x7f0300cc
int attr dropdownListPreferredItemHeight 0x7f0300cd
int attr editTextBackground 0x7f0300ce
int attr editTextColor 0x7f0300cf
int attr editTextStyle 0x7f0300d0
int attr elevation 0x7f0300d1
int attr emptyVisibility 0x7f0300d2
int attr enforceMaterialTheme 0x7f0300d3
int attr enforceTextAppearance 0x7f0300d4
int attr errorEnabled 0x7f0300d5
int attr errorTextAppearance 0x7f0300d6
int attr expandActivityOverflowButtonDrawable 0x7f0300d7
int attr expanded 0x7f0300d8
int attr expandedTitleGravity 0x7f0300d9
int attr expandedTitleMargin 0x7f0300da
int attr expandedTitleMarginBottom 0x7f0300db
int attr expandedTitleMarginEnd 0x7f0300dc
int attr expandedTitleMarginStart 0x7f0300dd
int attr expandedTitleMarginTop 0x7f0300de
int attr expandedTitleTextAppearance 0x7f0300df
int attr expands_to_fit_screen_width 0x7f0300e0
int attr fabAlignmentMode 0x7f0300e1
int attr fabCradleMargin 0x7f0300e2
int attr fabCradleRoundedCornerRadius 0x7f0300e3
int attr fabCradleVerticalOffset 0x7f0300e4
int attr fabCustomSize 0x7f0300e5
int attr fabSize 0x7f0300e6
int attr fastScrollEnabled 0x7f0300e7
int attr fastScrollHorizontalThumbDrawable 0x7f0300e8
int attr fastScrollHorizontalTrackDrawable 0x7f0300e9
int attr fastScrollVerticalThumbDrawable 0x7f0300ea
int attr fastScrollVerticalTrackDrawable 0x7f0300eb
int attr firstBaselineToTopHeight 0x7f0300ec
int attr floatingActionButtonStyle 0x7f0300ed
int attr font 0x7f0300ee
int attr fontFamily 0x7f0300ef
int attr fontProviderAuthority 0x7f0300f0
int attr fontProviderCerts 0x7f0300f1
int attr fontProviderFetchStrategy 0x7f0300f2
int attr fontProviderFetchTimeout 0x7f0300f3
int attr fontProviderPackage 0x7f0300f4
int attr fontProviderQuery 0x7f0300f5
int attr fontStyle 0x7f0300f6
int attr fontVariationSettings 0x7f0300f7
int attr fontWeight 0x7f0300f8
int attr foregroundInsidePadding 0x7f0300f9
int attr gapBetweenBars 0x7f0300fa
int attr goIcon 0x7f0300fb
int attr headerLayout 0x7f0300fc
int attr height 0x7f0300fd
int attr helperText 0x7f0300fe
int attr helperTextEnabled 0x7f0300ff
int attr helperTextTextAppearance 0x7f030100
int attr hideMotionSpec 0x7f030101
int attr hideOnContentScroll 0x7f030102
int attr hideOnScroll 0x7f030103
int attr hintAnimationEnabled 0x7f030104
int attr hintEnabled 0x7f030105
int attr hintTextAppearance 0x7f030106
int attr homeAsUpIndicator 0x7f030107
int attr homeLayout 0x7f030108
int attr hoveredFocusedTranslationZ 0x7f030109
int attr icon 0x7f03010a
int attr iconEndPadding 0x7f03010b
int attr iconGravity 0x7f03010c
int attr iconPadding 0x7f03010d
int attr iconSize 0x7f03010e
int attr iconStartPadding 0x7f03010f
int attr iconTint 0x7f030110
int attr iconTintMode 0x7f030111
int attr iconifiedByDefault 0x7f030112
int attr imageButtonStyle 0x7f030113
int attr indeterminateProgressStyle 0x7f030114
int attr initialActivityCount 0x7f030115
int attr insetForeground 0x7f030116
int attr isLightTheme 0x7f030117
int attr itemBackground 0x7f030118
int attr itemHorizontalPadding 0x7f030119
int attr itemHorizontalTranslationEnabled 0x7f03011a
int attr itemIconPadding 0x7f03011b
int attr itemIconSize 0x7f03011c
int attr itemIconTint 0x7f03011d
int attr itemPadding 0x7f03011e
int attr itemSpacing 0x7f03011f
int attr itemTextAppearance 0x7f030120
int attr itemTextAppearanceActive 0x7f030121
int attr itemTextAppearanceInactive 0x7f030122
int attr itemTextColor 0x7f030123
int attr keylines 0x7f030124
int attr ksad_SeekBarBackground 0x7f030125
int attr ksad_SeekBarDefaultIndicator 0x7f030126
int attr ksad_SeekBarDefaultIndicatorPass 0x7f030127
int attr ksad_SeekBarDisplayProgressText 0x7f030128
int attr ksad_SeekBarHeight 0x7f030129
int attr ksad_SeekBarLimitProgressText100 0x7f03012a
int attr ksad_SeekBarPaddingBottom 0x7f03012b
int attr ksad_SeekBarPaddingLeft 0x7f03012c
int attr ksad_SeekBarPaddingRight 0x7f03012d
int attr ksad_SeekBarPaddingTop 0x7f03012e
int attr ksad_SeekBarProgress 0x7f03012f
int attr ksad_SeekBarProgressTextColor 0x7f030130
int attr ksad_SeekBarProgressTextMargin 0x7f030131
int attr ksad_SeekBarProgressTextSize 0x7f030132
int attr ksad_SeekBarRadius 0x7f030133
int attr ksad_SeekBarSecondProgress 0x7f030134
int attr ksad_SeekBarShowProgressText 0x7f030135
int attr ksad_SeekBarThumb 0x7f030136
int attr ksad_SeekBarWidth 0x7f030137
int attr ksad_autoStartMarquee 0x7f030138
int attr ksad_backgroundDrawable 0x7f030139
int attr ksad_bottomLeftCorner 0x7f03013a
int attr ksad_clickable 0x7f03013b
int attr ksad_clipBackground 0x7f03013c
int attr ksad_color 0x7f03013d
int attr ksad_dashGap 0x7f03013e
int attr ksad_dashLength 0x7f03013f
int attr ksad_dashThickness 0x7f030140
int attr ksad_default_color 0x7f030141
int attr ksad_dot_distance 0x7f030142
int attr ksad_dot_height 0x7f030143
int attr ksad_dot_selected_width 0x7f030144
int attr ksad_dot_unselected_width 0x7f030145
int attr ksad_downloadLeftTextColor 0x7f030146
int attr ksad_downloadRightTextColor 0x7f030147
int attr ksad_downloadTextColor 0x7f030148
int attr ksad_downloadTextSize 0x7f030149
int attr ksad_downloadingFormat 0x7f03014a
int attr ksad_enableBottomShadow 0x7f03014b
int attr ksad_enableLeftShadow 0x7f03014c
int attr ksad_enableRightShadow 0x7f03014d
int attr ksad_enableTopShadow 0x7f03014e
int attr ksad_halfstart 0x7f03014f
int attr ksad_height_color 0x7f030150
int attr ksad_innerCirclePadding 0x7f030151
int attr ksad_innerCircleStrokeColor 0x7f030152
int attr ksad_innerCircleStrokeWidth 0x7f030153
int attr ksad_is_left_slide 0x7f030154
int attr ksad_labelRadius 0x7f030155
int attr ksad_leftTopCorner 0x7f030156
int attr ksad_marqueeSpeed 0x7f030157
int attr ksad_orientation 0x7f030158
int attr ksad_outerRadius 0x7f030159
int attr ksad_outerStrokeColor 0x7f03015a
int attr ksad_outerStrokeWidth 0x7f03015b
int attr ksad_privacy_color 0x7f03015c
int attr ksad_progressDrawable 0x7f03015d
int attr ksad_radius 0x7f03015e
int attr ksad_ratio 0x7f03015f
int attr ksad_rightBottomCorner 0x7f030160
int attr ksad_shadowColor 0x7f030161
int attr ksad_shadowSize 0x7f030162
int attr ksad_shakeIcon 0x7f030163
int attr ksad_shakeViewStyle 0x7f030164
int attr ksad_show_clickable_underline 0x7f030165
int attr ksad_sideRadius 0x7f030166
int attr ksad_solidColor 0x7f030167
int attr ksad_starCount 0x7f030168
int attr ksad_starEmpty 0x7f030169
int attr ksad_starFill 0x7f03016a
int attr ksad_starHalf 0x7f03016b
int attr ksad_starImageHeight 0x7f03016c
int attr ksad_starImagePadding 0x7f03016d
int attr ksad_starImageWidth 0x7f03016e
int attr ksad_strokeColor 0x7f03016f
int attr ksad_strokeSize 0x7f030170
int attr ksad_text 0x7f030171
int attr ksad_textAppearance 0x7f030172
int attr ksad_textColor 0x7f030173
int attr ksad_textDrawable 0x7f030174
int attr ksad_textIsSelected 0x7f030175
int attr ksad_textLeftBottomRadius 0x7f030176
int attr ksad_textLeftTopRadius 0x7f030177
int attr ksad_textNoBottomStroke 0x7f030178
int attr ksad_textNoLeftStroke 0x7f030179
int attr ksad_textNoRightStroke 0x7f03017a
int attr ksad_textNoTopStroke 0x7f03017b
int attr ksad_textNormalSolidColor 0x7f03017c
int attr ksad_textNormalTextColor 0x7f03017d
int attr ksad_textPressedSolidColor 0x7f03017e
int attr ksad_textRadius 0x7f03017f
int attr ksad_textRightBottomRadius 0x7f030180
int attr ksad_textRightTopRadius 0x7f030181
int attr ksad_textSelectedTextColor 0x7f030182
int attr ksad_textSize 0x7f030183
int attr ksad_textStrokeColor 0x7f030184
int attr ksad_textStrokeWidth 0x7f030185
int attr ksad_textStyle 0x7f030186
int attr ksad_topRightCorner 0x7f030187
int attr ksad_totalStarCount 0x7f030188
int attr ksad_typeface 0x7f030189
int attr ksad_verticalRadius 0x7f03018a
int attr ksad_width_in_landscape 0x7f03018b
int attr labelVisibilityMode 0x7f03018c
int attr lastBaselineToBottomHeight 0x7f03018d
int attr layout 0x7f03018e
int attr layoutManager 0x7f03018f
int attr layout_anchor 0x7f030190
int attr layout_anchorGravity 0x7f030191
int attr layout_behavior 0x7f030192
int attr layout_collapseMode 0x7f030193
int attr layout_collapseParallaxMultiplier 0x7f030194
int attr layout_constrainedHeight 0x7f030195
int attr layout_constrainedWidth 0x7f030196
int attr layout_constraintBaseline_creator 0x7f030197
int attr layout_constraintBaseline_toBaselineOf 0x7f030198
int attr layout_constraintBottom_creator 0x7f030199
int attr layout_constraintBottom_toBottomOf 0x7f03019a
int attr layout_constraintBottom_toTopOf 0x7f03019b
int attr layout_constraintCircle 0x7f03019c
int attr layout_constraintCircleAngle 0x7f03019d
int attr layout_constraintCircleRadius 0x7f03019e
int attr layout_constraintDimensionRatio 0x7f03019f
int attr layout_constraintEnd_toEndOf 0x7f0301a0
int attr layout_constraintEnd_toStartOf 0x7f0301a1
int attr layout_constraintGuide_begin 0x7f0301a2
int attr layout_constraintGuide_end 0x7f0301a3
int attr layout_constraintGuide_percent 0x7f0301a4
int attr layout_constraintHeight_default 0x7f0301a5
int attr layout_constraintHeight_max 0x7f0301a6
int attr layout_constraintHeight_min 0x7f0301a7
int attr layout_constraintHeight_percent 0x7f0301a8
int attr layout_constraintHorizontal_bias 0x7f0301a9
int attr layout_constraintHorizontal_chainStyle 0x7f0301aa
int attr layout_constraintHorizontal_weight 0x7f0301ab
int attr layout_constraintLeft_creator 0x7f0301ac
int attr layout_constraintLeft_toLeftOf 0x7f0301ad
int attr layout_constraintLeft_toRightOf 0x7f0301ae
int attr layout_constraintRight_creator 0x7f0301af
int attr layout_constraintRight_toLeftOf 0x7f0301b0
int attr layout_constraintRight_toRightOf 0x7f0301b1
int attr layout_constraintStart_toEndOf 0x7f0301b2
int attr layout_constraintStart_toStartOf 0x7f0301b3
int attr layout_constraintTop_creator 0x7f0301b4
int attr layout_constraintTop_toBottomOf 0x7f0301b5
int attr layout_constraintTop_toTopOf 0x7f0301b6
int attr layout_constraintVertical_bias 0x7f0301b7
int attr layout_constraintVertical_chainStyle 0x7f0301b8
int attr layout_constraintVertical_weight 0x7f0301b9
int attr layout_constraintWidth_default 0x7f0301ba
int attr layout_constraintWidth_max 0x7f0301bb
int attr layout_constraintWidth_min 0x7f0301bc
int attr layout_constraintWidth_percent 0x7f0301bd
int attr layout_dodgeInsetEdges 0x7f0301be
int attr layout_editor_absoluteX 0x7f0301bf
int attr layout_editor_absoluteY 0x7f0301c0
int attr layout_goneMarginBottom 0x7f0301c1
int attr layout_goneMarginEnd 0x7f0301c2
int attr layout_goneMarginLeft 0x7f0301c3
int attr layout_goneMarginRight 0x7f0301c4
int attr layout_goneMarginStart 0x7f0301c5
int attr layout_goneMarginTop 0x7f0301c6
int attr layout_insetEdge 0x7f0301c7
int attr layout_keyline 0x7f0301c8
int attr layout_optimizationLevel 0x7f0301c9
int attr layout_scrollFlags 0x7f0301ca
int attr layout_scrollInterpolator 0x7f0301cb
int attr liftOnScroll 0x7f0301cc
int attr lineHeight 0x7f0301cd
int attr lineSpacing 0x7f0301ce
int attr listChoiceBackgroundIndicator 0x7f0301cf
int attr listDividerAlertDialog 0x7f0301d0
int attr listItemLayout 0x7f0301d1
int attr listLayout 0x7f0301d2
int attr listMenuViewStyle 0x7f0301d3
int attr listPopupWindowStyle 0x7f0301d4
int attr listPreferredItemHeight 0x7f0301d5
int attr listPreferredItemHeightLarge 0x7f0301d6
int attr listPreferredItemHeightSmall 0x7f0301d7
int attr listPreferredItemPaddingLeft 0x7f0301d8
int attr listPreferredItemPaddingRight 0x7f0301d9
int attr load_landing_page_in_background 0x7f0301da
int attr logo 0x7f0301db
int attr logoDescription 0x7f0301dc
int attr materialButtonStyle 0x7f0301dd
int attr materialCardViewStyle 0x7f0301de
int attr maxActionInlineWidth 0x7f0301df
int attr maxButtonHeight 0x7f0301e0
int attr maxImageSize 0x7f0301e1
int attr measureWithLargestChild 0x7f0301e2
int attr menu 0x7f0301e3
int attr multiChoiceItemLayout 0x7f0301e4
int attr navigationContentDescription 0x7f0301e5
int attr navigationIcon 0x7f0301e6
int attr navigationMode 0x7f0301e7
int attr navigationViewStyle 0x7f0301e8
int attr numericModifiers 0x7f0301e9
int attr opens_native_browser 0x7f0301ea
int attr oset_riv_border_color 0x7f0301eb
int attr oset_riv_border_width 0x7f0301ec
int attr oset_riv_corner_radius 0x7f0301ed
int attr oset_riv_corner_radius_bottom_left 0x7f0301ee
int attr oset_riv_corner_radius_bottom_right 0x7f0301ef
int attr oset_riv_corner_radius_top_left 0x7f0301f0
int attr oset_riv_corner_radius_top_right 0x7f0301f1
int attr oset_riv_mutate_background 0x7f0301f2
int attr oset_riv_oval 0x7f0301f3
int attr oset_riv_tile_mode 0x7f0301f4
int attr oset_riv_tile_mode_x 0x7f0301f5
int attr oset_riv_tile_mode_y 0x7f0301f6
int attr overlapAnchor 0x7f0301f7
int attr paddingBottomNoButtons 0x7f0301f8
int attr paddingEnd 0x7f0301f9
int attr paddingStart 0x7f0301fa
int attr paddingTopNoTitle 0x7f0301fb
int attr panelBackground 0x7f0301fc
int attr panelMenuListTheme 0x7f0301fd
int attr panelMenuListWidth 0x7f0301fe
int attr passwordToggleContentDescription 0x7f0301ff
int attr passwordToggleDrawable 0x7f030200
int attr passwordToggleEnabled 0x7f030201
int attr passwordToggleTint 0x7f030202
int attr passwordToggleTintMode 0x7f030203
int attr popupMenuStyle 0x7f030204
int attr popupTheme 0x7f030205
int attr popupWindowStyle 0x7f030206
int attr preserveIconSpacing 0x7f030207
int attr pressedTranslationZ 0x7f030208
int attr progressBarPadding 0x7f030209
int attr progressBarStyle 0x7f03020a
int attr queryBackground 0x7f03020b
int attr queryHint 0x7f03020c
int attr radioButtonStyle 0x7f03020d
int attr ratingBarStyle 0x7f03020e
int attr ratingBarStyleIndicator 0x7f03020f
int attr ratingBarStyleSmall 0x7f030210
int attr resize_ad_to_fit_container 0x7f030211
int attr reverseLayout 0x7f030212
int attr rippleColor 0x7f030213
int attr scrimAnimationDuration 0x7f030214
int attr scrimBackground 0x7f030215
int attr scrimVisibleHeightTrigger 0x7f030216
int attr searchHintIcon 0x7f030217
int attr searchIcon 0x7f030218
int attr searchViewStyle 0x7f030219
int attr seekBarStyle 0x7f03021a
int attr selectableItemBackground 0x7f03021b
int attr selectableItemBackgroundBorderless 0x7f03021c
int attr should_reload_on_resume 0x7f03021d
int attr showAsAction 0x7f03021e
int attr showDividers 0x7f03021f
int attr showMotionSpec 0x7f030220
int attr showText 0x7f030221
int attr showTitle 0x7f030222
int attr show_loading_indicator 0x7f030223
int attr sig_isSmall 0x7f030224
int attr singleChoiceItemLayout 0x7f030225
int attr singleLine 0x7f030226
int attr singleSelection 0x7f030227
int attr snackbarButtonStyle 0x7f030228
int attr snackbarStyle 0x7f030229
int attr spanCount 0x7f03022a
int attr spinBars 0x7f03022b
int attr spinnerDropDownItemStyle 0x7f03022c
int attr spinnerStyle 0x7f03022d
int attr splitTrack 0x7f03022e
int attr srcCompat 0x7f03022f
int attr stackFromEnd 0x7f030230
int attr state_above_anchor 0x7f030231
int attr state_collapsed 0x7f030232
int attr state_collapsible 0x7f030233
int attr state_liftable 0x7f030234
int attr state_lifted 0x7f030235
int attr statusBarBackground 0x7f030236
int attr statusBarScrim 0x7f030237
int attr strokeColor 0x7f030238
int attr strokeWidth 0x7f030239
int attr subMenuArrow 0x7f03023a
int attr submitBackground 0x7f03023b
int attr subtitle 0x7f03023c
int attr subtitleTextAppearance 0x7f03023d
int attr subtitleTextColor 0x7f03023e
int attr subtitleTextStyle 0x7f03023f
int attr suggestionRowLayout 0x7f030240
int attr switchMinWidth 0x7f030241
int attr switchPadding 0x7f030242
int attr switchStyle 0x7f030243
int attr switchTextAppearance 0x7f030244
int attr tabBackground 0x7f030245
int attr tabContentStart 0x7f030246
int attr tabGravity 0x7f030247
int attr tabIconTint 0x7f030248
int attr tabIconTintMode 0x7f030249
int attr tabIndicator 0x7f03024a
int attr tabIndicatorAnimationDuration 0x7f03024b
int attr tabIndicatorColor 0x7f03024c
int attr tabIndicatorFullWidth 0x7f03024d
int attr tabIndicatorGravity 0x7f03024e
int attr tabIndicatorHeight 0x7f03024f
int attr tabInlineLabel 0x7f030250
int attr tabMaxWidth 0x7f030251
int attr tabMinWidth 0x7f030252
int attr tabMode 0x7f030253
int attr tabPadding 0x7f030254
int attr tabPaddingBottom 0x7f030255
int attr tabPaddingEnd 0x7f030256
int attr tabPaddingStart 0x7f030257
int attr tabPaddingTop 0x7f030258
int attr tabRippleColor 0x7f030259
int attr tabSelectedTextColor 0x7f03025a
int attr tabStyle 0x7f03025b
int attr tabTextAppearance 0x7f03025c
int attr tabTextColor 0x7f03025d
int attr tabUnboundedRipple 0x7f03025e
int attr test 0x7f03025f
int attr textAllCaps 0x7f030260
int attr textAppearanceBody1 0x7f030261
int attr textAppearanceBody2 0x7f030262
int attr textAppearanceButton 0x7f030263
int attr textAppearanceCaption 0x7f030264
int attr textAppearanceHeadline1 0x7f030265
int attr textAppearanceHeadline2 0x7f030266
int attr textAppearanceHeadline3 0x7f030267
int attr textAppearanceHeadline4 0x7f030268
int attr textAppearanceHeadline5 0x7f030269
int attr textAppearanceHeadline6 0x7f03026a
int attr textAppearanceLargePopupMenu 0x7f03026b
int attr textAppearanceListItem 0x7f03026c
int attr textAppearanceListItemSecondary 0x7f03026d
int attr textAppearanceListItemSmall 0x7f03026e
int attr textAppearanceOverline 0x7f03026f
int attr textAppearancePopupMenuHeader 0x7f030270
int attr textAppearanceSearchResultSubtitle 0x7f030271
int attr textAppearanceSearchResultTitle 0x7f030272
int attr textAppearanceSmallPopupMenu 0x7f030273
int attr textAppearanceSubtitle1 0x7f030274
int attr textAppearanceSubtitle2 0x7f030275
int attr textColorAlertDialogListItem 0x7f030276
int attr textColorSearchUrl 0x7f030277
int attr textEndPadding 0x7f030278
int attr textInputStyle 0x7f030279
int attr textStartPadding 0x7f03027a
int attr theme 0x7f03027b
int attr thickness 0x7f03027c
int attr thumbTextPadding 0x7f03027d
int attr thumbTint 0x7f03027e
int attr thumbTintMode 0x7f03027f
int attr tickMark 0x7f030280
int attr tickMarkTint 0x7f030281
int attr tickMarkTintMode 0x7f030282
int attr tint 0x7f030283
int attr tintMode 0x7f030284
int attr title 0x7f030285
int attr titleEnabled 0x7f030286
int attr titleMargin 0x7f030287
int attr titleMarginBottom 0x7f030288
int attr titleMarginEnd 0x7f030289
int attr titleMarginStart 0x7f03028a
int attr titleMarginTop 0x7f03028b
int attr titleMargins 0x7f03028c
int attr titleTextAppearance 0x7f03028d
int attr titleTextColor 0x7f03028e
int attr titleTextStyle 0x7f03028f
int attr toolbarId 0x7f030290
int attr toolbarNavigationButtonStyle 0x7f030291
int attr toolbarStyle 0x7f030292
int attr tooltipForegroundColor 0x7f030293
int attr tooltipFrameBackground 0x7f030294
int attr tooltipText 0x7f030295
int attr track 0x7f030296
int attr trackTint 0x7f030297
int attr trackTintMode 0x7f030298
int attr transition_direction 0x7f030299
int attr transition_duration 0x7f03029a
int attr transition_type 0x7f03029b
int attr ttcIndex 0x7f03029c
int attr useCompatPadding 0x7f03029d
int attr video_scale_type 0x7f03029e
int attr viewInflaterClass 0x7f03029f
int attr voiceIcon 0x7f0302a0
int attr windowActionBar 0x7f0302a1
int attr windowActionBarOverlay 0x7f0302a2
int attr windowActionModeOverlay 0x7f0302a3
int attr windowFixedHeightMajor 0x7f0302a4
int attr windowFixedHeightMinor 0x7f0302a5
int attr windowFixedWidthMajor 0x7f0302a6
int attr windowFixedWidthMinor 0x7f0302a7
int attr windowMinWidthMajor 0x7f0302a8
int attr windowMinWidthMinor 0x7f0302a9
int attr windowNoTitle 0x7f0302aa
int bool abc_action_bar_embed_tabs 0x7f040000
int bool abc_allow_stacked_button_bar 0x7f040001
int bool abc_config_actionMenuItemAllCaps 0x7f040002
int bool mtrl_btn_textappearance_all_caps 0x7f040003
int color abc_background_cache_hint_selector_material_dark 0x7f050000
int color abc_background_cache_hint_selector_material_light 0x7f050001
int color abc_btn_colored_borderless_text_material 0x7f050002
int color abc_btn_colored_text_material 0x7f050003
int color abc_color_highlight_material 0x7f050004
int color abc_hint_foreground_material_dark 0x7f050005
int color abc_hint_foreground_material_light 0x7f050006
int color abc_input_method_navigation_guard 0x7f050007
int color abc_primary_text_disable_only_material_dark 0x7f050008
int color abc_primary_text_disable_only_material_light 0x7f050009
int color abc_primary_text_material_dark 0x7f05000a
int color abc_primary_text_material_light 0x7f05000b
int color abc_search_url_text 0x7f05000c
int color abc_search_url_text_normal 0x7f05000d
int color abc_search_url_text_pressed 0x7f05000e
int color abc_search_url_text_selected 0x7f05000f
int color abc_secondary_text_material_dark 0x7f050010
int color abc_secondary_text_material_light 0x7f050011
int color abc_tint_btn_checkable 0x7f050012
int color abc_tint_default 0x7f050013
int color abc_tint_edittext 0x7f050014
int color abc_tint_seek_thumb 0x7f050015
int color abc_tint_spinner 0x7f050016
int color abc_tint_switch_track 0x7f050017
int color accent_material_dark 0x7f050018
int color accent_material_light 0x7f050019
int color accent_orange 0x7f05001a
int color appinfo_tab_selected_color 0x7f05001b
int color appinfo_tab_unselected_color 0x7f05001c
int color background_floating_material_dark 0x7f05001d
int color background_floating_material_light 0x7f05001e
int color background_light 0x7f05001f
int color background_material_dark 0x7f050020
int color background_material_light 0x7f050021
int color black 0x7f050022
int color bright_foreground_disabled_material_dark 0x7f050023
int color bright_foreground_disabled_material_light 0x7f050024
int color bright_foreground_inverse_material_dark 0x7f050025
int color bright_foreground_inverse_material_light 0x7f050026
int color bright_foreground_material_dark 0x7f050027
int color bright_foreground_material_light 0x7f050028
int color button_material_dark 0x7f050029
int color button_material_light 0x7f05002a
int color button_text_selector 0x7f05002b
int color cardview_dark_background 0x7f05002c
int color cardview_light_background 0x7f05002d
int color cardview_shadow_end_color 0x7f05002e
int color cardview_shadow_start_color 0x7f05002f
int color design_bottom_navigation_shadow_color 0x7f050030
int color design_default_color_primary 0x7f050031
int color design_default_color_primary_dark 0x7f050032
int color design_error 0x7f050033
int color design_fab_shadow_end_color 0x7f050034
int color design_fab_shadow_mid_color 0x7f050035
int color design_fab_shadow_start_color 0x7f050036
int color design_fab_stroke_end_inner_color 0x7f050037
int color design_fab_stroke_end_outer_color 0x7f050038
int color design_fab_stroke_top_inner_color 0x7f050039
int color design_fab_stroke_top_outer_color 0x7f05003a
int color design_snackbar_background_color 0x7f05003b
int color design_tint_password_toggle 0x7f05003c
int color dim_foreground_disabled_material_dark 0x7f05003d
int color dim_foreground_disabled_material_light 0x7f05003e
int color dim_foreground_material_dark 0x7f05003f
int color dim_foreground_material_light 0x7f050040
int color ec_store_window_background 0x7f050041
int color error_color_material_dark 0x7f050042
int color error_color_material_light 0x7f050043
int color firewood_color 0x7f050044
int color food_color 0x7f050045
int color foreground_material_dark 0x7f050046
int color foreground_material_light 0x7f050047
int color highlighted_text_material_dark 0x7f050048
int color highlighted_text_material_light 0x7f050049
int color ic_launcher_background 0x7f05004a
int color ksad_88_white 0x7f05004b
int color ksad_99_black 0x7f05004c
int color ksad_99_white 0x7f05004d
int color ksad_black_6c 0x7f05004e
int color ksad_black_alpha100 0x7f05004f
int color ksad_black_alpha20 0x7f050050
int color ksad_black_alpha35 0x7f050051
int color ksad_black_alpha50 0x7f050052
int color ksad_default_dialog_bg_color 0x7f050053
int color ksad_default_privacy_link_color 0x7f050054
int color ksad_default_shake_btn_bg_color 0x7f050055
int color ksad_feed_main_color 0x7f050056
int color ksad_for_liteapi 0x7f050057
int color ksad_gray_9c 0x7f050058
int color ksad_jinniu_end_origin_color 0x7f050059
int color ksad_no_title_common_dialog_negativebtn_color 0x7f05005a
int color ksad_no_title_common_dialog_positivebtn_color 0x7f05005b
int color ksad_play_again_horizontal_bg 0x7f05005c
int color ksad_play_again_horizontal_bg_light 0x7f05005d
int color ksad_playable_pre_tips_icon_bg 0x7f05005e
int color ksad_reward_main_color 0x7f05005f
int color ksad_reward_original_price 0x7f050060
int color ksad_reward_undone_color 0x7f050061
int color ksad_secondary_btn_color 0x7f050062
int color ksad_shake_icon_bg_start_color 0x7f050063
int color ksad_splash_endcard_appdesc_color 0x7f050064
int color ksad_splash_endcard_appversion_color 0x7f050065
int color ksad_splash_endcard_bg_color 0x7f050066
int color ksad_splash_endcard_developer_color 0x7f050067
int color ksad_splash_endcard_name_color 0x7f050068
int color ksad_splash_endcard_ompliance_color 0x7f050069
int color ksad_splash_endcard_title_color 0x7f05006a
int color ksad_text_black_222 0x7f05006b
int color ksad_translucent 0x7f05006c
int color ksad_white 0x7f05006d
int color ksad_white_alpha_20 0x7f05006e
int color material_blue_grey_800 0x7f05006f
int color material_blue_grey_900 0x7f050070
int color material_blue_grey_950 0x7f050071
int color material_deep_teal_200 0x7f050072
int color material_deep_teal_500 0x7f050073
int color material_grey_100 0x7f050074
int color material_grey_300 0x7f050075
int color material_grey_50 0x7f050076
int color material_grey_600 0x7f050077
int color material_grey_800 0x7f050078
int color material_grey_850 0x7f050079
int color material_grey_900 0x7f05007a
int color mtrl_bottom_nav_colored_item_tint 0x7f05007b
int color mtrl_bottom_nav_item_tint 0x7f05007c
int color mtrl_btn_bg_color_disabled 0x7f05007d
int color mtrl_btn_bg_color_selector 0x7f05007e
int color mtrl_btn_ripple_color 0x7f05007f
int color mtrl_btn_stroke_color_selector 0x7f050080
int color mtrl_btn_text_btn_ripple_color 0x7f050081
int color mtrl_btn_text_color_disabled 0x7f050082
int color mtrl_btn_text_color_selector 0x7f050083
int color mtrl_btn_transparent_bg_color 0x7f050084
int color mtrl_chip_background_color 0x7f050085
int color mtrl_chip_close_icon_tint 0x7f050086
int color mtrl_chip_ripple_color 0x7f050087
int color mtrl_chip_text_color 0x7f050088
int color mtrl_fab_ripple_color 0x7f050089
int color mtrl_scrim_color 0x7f05008a
int color mtrl_tabs_colored_ripple_color 0x7f05008b
int color mtrl_tabs_icon_color_selector 0x7f05008c
int color mtrl_tabs_icon_color_selector_colored 0x7f05008d
int color mtrl_tabs_legacy_text_color_selector 0x7f05008e
int color mtrl_tabs_ripple_color 0x7f05008f
int color mtrl_text_btn_text_color_selector 0x7f050090
int color mtrl_textinput_default_box_stroke_color 0x7f050091
int color mtrl_textinput_disabled_color 0x7f050092
int color mtrl_textinput_filled_box_default_background_color 0x7f050093
int color mtrl_textinput_hovered_box_stroke_color 0x7f050094
int color notification_action_color_filter 0x7f050095
int color notification_icon_bg_color 0x7f050096
int color notification_material_background_media_default_color 0x7f050097
int color oset_colorAccent 0x7f050098
int color oset_colorPrimary 0x7f050099
int color oset_colorPrimaryDark 0x7f05009a
int color oset_colorWhite 0x7f05009b
int color oset_progress_red 0x7f05009c
int color oset_text_select 0x7f05009d
int color oset_text_type_select 0x7f05009e
int color oset_text_type_unselect 0x7f05009f
int color oset_text_unselect 0x7f0500a0
int color oset_weather_theme 0x7f0500a1
int color oset_yd_video_bg 0x7f0500a2
int color primary_blue 0x7f0500a3
int color primary_blue_dark 0x7f0500a4
int color primary_dark_material_dark 0x7f0500a5
int color primary_dark_material_light 0x7f0500a6
int color primary_material_dark 0x7f0500a7
int color primary_material_light 0x7f0500a8
int color primary_text_default_material_dark 0x7f0500a9
int color primary_text_default_material_light 0x7f0500aa
int color primary_text_disabled_material_dark 0x7f0500ab
int color primary_text_disabled_material_light 0x7f0500ac
int color purple_200 0x7f0500ad
int color purple_500 0x7f0500ae
int color purple_700 0x7f0500af
int color ripple_material_dark 0x7f0500b0
int color ripple_material_light 0x7f0500b1
int color secondary_text_default_material_dark 0x7f0500b2
int color secondary_text_default_material_light 0x7f0500b3
int color secondary_text_disabled_material_dark 0x7f0500b4
int color secondary_text_disabled_material_light 0x7f0500b5
int color stamina_color 0x7f0500b6
int color switch_thumb_disabled_material_dark 0x7f0500b7
int color switch_thumb_disabled_material_light 0x7f0500b8
int color switch_thumb_material_dark 0x7f0500b9
int color switch_thumb_material_light 0x7f0500ba
int color switch_thumb_normal_material_dark 0x7f0500bb
int color switch_thumb_normal_material_light 0x7f0500bc
int color teal_200 0x7f0500bd
int color teal_700 0x7f0500be
int color text_primary 0x7f0500bf
int color text_secondary 0x7f0500c0
int color tooltip_background_dark 0x7f0500c1
int color tooltip_background_light 0x7f0500c2
int color tt_adx_logo_des_bg 0x7f0500c3
int color tt_adx_logo_desc 0x7f0500c4
int color tt_app_detail_line_bg 0x7f0500c5
int color tt_app_detail_privacy_text_bg 0x7f0500c6
int color tt_app_detail_stroke_bg 0x7f0500c7
int color tt_app_tag_background 0x7f0500c8
int color tt_app_tag_text_color 0x7f0500c9
int color tt_appdownloader_notification_material_background_color 0x7f0500ca
int color tt_appdownloader_notification_title_color 0x7f0500cb
int color tt_appdownloader_s1 0x7f0500cc
int color tt_appdownloader_s13 0x7f0500cd
int color tt_appdownloader_s18 0x7f0500ce
int color tt_appdownloader_s4 0x7f0500cf
int color tt_appdownloader_s8 0x7f0500d0
int color tt_cancle_bg 0x7f0500d1
int color tt_dislike_dialog_background 0x7f0500d2
int color tt_dislike_transparent 0x7f0500d3
int color tt_divider 0x7f0500d4
int color tt_download_app_name 0x7f0500d5
int color tt_download_bar_background 0x7f0500d6
int color tt_download_bar_background_new 0x7f0500d7
int color tt_download_text_background 0x7f0500d8
int color tt_draw_btn_back 0x7f0500d9
int color tt_full_background 0x7f0500da
int color tt_full_interaction_bar_background 0x7f0500db
int color tt_full_interaction_dialog_background 0x7f0500dc
int color tt_full_screen_skip_bg 0x7f0500dd
int color tt_full_status_bar_color 0x7f0500de
int color tt_header_font 0x7f0500df
int color tt_heise3 0x7f0500e0
int color tt_listview 0x7f0500e1
int color tt_listview_press 0x7f0500e2
int color tt_mediation_transparent 0x7f0500e3
int color tt_rating_comment 0x7f0500e4
int color tt_rating_comment_vertical 0x7f0500e5
int color tt_rating_star 0x7f0500e6
int color tt_reward_live_dialog_bg 0x7f0500e7
int color tt_reward_slide_up_bg 0x7f0500e8
int color tt_skip_red 0x7f0500e9
int color tt_splash_click_bar_text_shadow 0x7f0500ea
int color tt_ssxinbaise4 0x7f0500eb
int color tt_ssxinbaise4_press 0x7f0500ec
int color tt_ssxinheihui3 0x7f0500ed
int color tt_ssxinhongse1 0x7f0500ee
int color tt_ssxinmian1 0x7f0500ef
int color tt_ssxinmian11 0x7f0500f0
int color tt_ssxinmian15 0x7f0500f1
int color tt_ssxinmian6 0x7f0500f2
int color tt_ssxinmian7 0x7f0500f3
int color tt_ssxinmian8 0x7f0500f4
int color tt_ssxinxian11 0x7f0500f5
int color tt_ssxinxian11_selected 0x7f0500f6
int color tt_ssxinxian3 0x7f0500f7
int color tt_ssxinxian3_press 0x7f0500f8
int color tt_ssxinzi12 0x7f0500f9
int color tt_ssxinzi15 0x7f0500fa
int color tt_ssxinzi4 0x7f0500fb
int color tt_ssxinzi9 0x7f0500fc
int color tt_text_font 0x7f0500fd
int color tt_titlebar_background_dark 0x7f0500fe
int color tt_titlebar_background_ffffff 0x7f0500ff
int color tt_titlebar_background_light 0x7f050100
int color tt_trans_black 0x7f050101
int color tt_trans_half_black 0x7f050102
int color tt_transparent 0x7f050103
int color tt_video_player_text 0x7f050104
int color tt_video_player_text_withoutnight 0x7f050105
int color tt_video_shadow_color 0x7f050106
int color tt_video_shaoow_color_fullscreen 0x7f050107
int color tt_video_time_color 0x7f050108
int color tt_video_traffic_tip_background_color 0x7f050109
int color tt_video_transparent 0x7f05010a
int color tt_white 0x7f05010b
int color ttdownloader_transparent 0x7f05010c
int color view_divider_bg 0x7f05010d
int color warmth_color 0x7f05010e
int color white 0x7f05010f
int dimen abc_action_bar_content_inset_material 0x7f060000
int dimen abc_action_bar_content_inset_with_nav 0x7f060001
int dimen abc_action_bar_default_height_material 0x7f060002
int dimen abc_action_bar_default_padding_end_material 0x7f060003
int dimen abc_action_bar_default_padding_start_material 0x7f060004
int dimen abc_action_bar_elevation_material 0x7f060005
int dimen abc_action_bar_icon_vertical_padding_material 0x7f060006
int dimen abc_action_bar_overflow_padding_end_material 0x7f060007
int dimen abc_action_bar_overflow_padding_start_material 0x7f060008
int dimen abc_action_bar_stacked_max_height 0x7f060009
int dimen abc_action_bar_stacked_tab_max_width 0x7f06000a
int dimen abc_action_bar_subtitle_bottom_margin_material 0x7f06000b
int dimen abc_action_bar_subtitle_top_margin_material 0x7f06000c
int dimen abc_action_button_min_height_material 0x7f06000d
int dimen abc_action_button_min_width_material 0x7f06000e
int dimen abc_action_button_min_width_overflow_material 0x7f06000f
int dimen abc_alert_dialog_button_bar_height 0x7f060010
int dimen abc_alert_dialog_button_dimen 0x7f060011
int dimen abc_button_inset_horizontal_material 0x7f060012
int dimen abc_button_inset_vertical_material 0x7f060013
int dimen abc_button_padding_horizontal_material 0x7f060014
int dimen abc_button_padding_vertical_material 0x7f060015
int dimen abc_cascading_menus_min_smallest_width 0x7f060016
int dimen abc_config_prefDialogWidth 0x7f060017
int dimen abc_control_corner_material 0x7f060018
int dimen abc_control_inset_material 0x7f060019
int dimen abc_control_padding_material 0x7f06001a
int dimen abc_dialog_corner_radius_material 0x7f06001b
int dimen abc_dialog_fixed_height_major 0x7f06001c
int dimen abc_dialog_fixed_height_minor 0x7f06001d
int dimen abc_dialog_fixed_width_major 0x7f06001e
int dimen abc_dialog_fixed_width_minor 0x7f06001f
int dimen abc_dialog_list_padding_bottom_no_buttons 0x7f060020
int dimen abc_dialog_list_padding_top_no_title 0x7f060021
int dimen abc_dialog_min_width_major 0x7f060022
int dimen abc_dialog_min_width_minor 0x7f060023
int dimen abc_dialog_padding_material 0x7f060024
int dimen abc_dialog_padding_top_material 0x7f060025
int dimen abc_dialog_title_divider_material 0x7f060026
int dimen abc_disabled_alpha_material_dark 0x7f060027
int dimen abc_disabled_alpha_material_light 0x7f060028
int dimen abc_dropdownitem_icon_width 0x7f060029
int dimen abc_dropdownitem_text_padding_left 0x7f06002a
int dimen abc_dropdownitem_text_padding_right 0x7f06002b
int dimen abc_edit_text_inset_bottom_material 0x7f06002c
int dimen abc_edit_text_inset_horizontal_material 0x7f06002d
int dimen abc_edit_text_inset_top_material 0x7f06002e
int dimen abc_floating_window_z 0x7f06002f
int dimen abc_list_item_padding_horizontal_material 0x7f060030
int dimen abc_panel_menu_list_width 0x7f060031
int dimen abc_progress_bar_height_material 0x7f060032
int dimen abc_search_view_preferred_height 0x7f060033
int dimen abc_search_view_preferred_width 0x7f060034
int dimen abc_seekbar_track_background_height_material 0x7f060035
int dimen abc_seekbar_track_progress_height_material 0x7f060036
int dimen abc_select_dialog_padding_start_material 0x7f060037
int dimen abc_switch_padding 0x7f060038
int dimen abc_text_size_body_1_material 0x7f060039
int dimen abc_text_size_body_2_material 0x7f06003a
int dimen abc_text_size_button_material 0x7f06003b
int dimen abc_text_size_caption_material 0x7f06003c
int dimen abc_text_size_display_1_material 0x7f06003d
int dimen abc_text_size_display_2_material 0x7f06003e
int dimen abc_text_size_display_3_material 0x7f06003f
int dimen abc_text_size_display_4_material 0x7f060040
int dimen abc_text_size_headline_material 0x7f060041
int dimen abc_text_size_large_material 0x7f060042
int dimen abc_text_size_medium_material 0x7f060043
int dimen abc_text_size_menu_header_material 0x7f060044
int dimen abc_text_size_menu_material 0x7f060045
int dimen abc_text_size_small_material 0x7f060046
int dimen abc_text_size_subhead_material 0x7f060047
int dimen abc_text_size_subtitle_material_toolbar 0x7f060048
int dimen abc_text_size_title_material 0x7f060049
int dimen abc_text_size_title_material_toolbar 0x7f06004a
int dimen cardview_compat_inset_shadow 0x7f06004b
int dimen cardview_default_elevation 0x7f06004c
int dimen cardview_default_radius 0x7f06004d
int dimen compat_button_inset_horizontal_material 0x7f06004e
int dimen compat_button_inset_vertical_material 0x7f06004f
int dimen compat_button_padding_horizontal_material 0x7f060050
int dimen compat_button_padding_vertical_material 0x7f060051
int dimen compat_control_corner_material 0x7f060052
int dimen compat_notification_large_icon_max_height 0x7f060053
int dimen compat_notification_large_icon_max_width 0x7f060054
int dimen design_appbar_elevation 0x7f060055
int dimen design_bottom_navigation_active_item_max_width 0x7f060056
int dimen design_bottom_navigation_active_item_min_width 0x7f060057
int dimen design_bottom_navigation_active_text_size 0x7f060058
int dimen design_bottom_navigation_elevation 0x7f060059
int dimen design_bottom_navigation_height 0x7f06005a
int dimen design_bottom_navigation_icon_size 0x7f06005b
int dimen design_bottom_navigation_item_max_width 0x7f06005c
int dimen design_bottom_navigation_item_min_width 0x7f06005d
int dimen design_bottom_navigation_margin 0x7f06005e
int dimen design_bottom_navigation_shadow_height 0x7f06005f
int dimen design_bottom_navigation_text_size 0x7f060060
int dimen design_bottom_sheet_modal_elevation 0x7f060061
int dimen design_bottom_sheet_peek_height_min 0x7f060062
int dimen design_fab_border_width 0x7f060063
int dimen design_fab_elevation 0x7f060064
int dimen design_fab_image_size 0x7f060065
int dimen design_fab_size_mini 0x7f060066
int dimen design_fab_size_normal 0x7f060067
int dimen design_fab_translation_z_hovered_focused 0x7f060068
int dimen design_fab_translation_z_pressed 0x7f060069
int dimen design_navigation_elevation 0x7f06006a
int dimen design_navigation_icon_padding 0x7f06006b
int dimen design_navigation_icon_size 0x7f06006c
int dimen design_navigation_item_horizontal_padding 0x7f06006d
int dimen design_navigation_item_icon_padding 0x7f06006e
int dimen design_navigation_max_width 0x7f06006f
int dimen design_navigation_padding_bottom 0x7f060070
int dimen design_navigation_separator_vertical_padding 0x7f060071
int dimen design_snackbar_action_inline_max_width 0x7f060072
int dimen design_snackbar_background_corner_radius 0x7f060073
int dimen design_snackbar_elevation 0x7f060074
int dimen design_snackbar_extra_spacing_horizontal 0x7f060075
int dimen design_snackbar_max_width 0x7f060076
int dimen design_snackbar_min_width 0x7f060077
int dimen design_snackbar_padding_horizontal 0x7f060078
int dimen design_snackbar_padding_vertical 0x7f060079
int dimen design_snackbar_padding_vertical_2lines 0x7f06007a
int dimen design_snackbar_text_size 0x7f06007b
int dimen design_tab_max_width 0x7f06007c
int dimen design_tab_scrollable_min_width 0x7f06007d
int dimen design_tab_text_size 0x7f06007e
int dimen design_tab_text_size_2line 0x7f06007f
int dimen design_textinput_caption_translate_y 0x7f060080
int dimen disabled_alpha_material_dark 0x7f060081
int dimen disabled_alpha_material_light 0x7f060082
int dimen fastscroll_default_thickness 0x7f060083
int dimen fastscroll_margin 0x7f060084
int dimen fastscroll_minimum_range 0x7f060085
int dimen highlight_alpha_material_colored 0x7f060086
int dimen highlight_alpha_material_dark 0x7f060087
int dimen highlight_alpha_material_light 0x7f060088
int dimen hint_alpha_material_dark 0x7f060089
int dimen hint_alpha_material_light 0x7f06008a
int dimen hint_pressed_alpha_material_dark 0x7f06008b
int dimen hint_pressed_alpha_material_light 0x7f06008c
int dimen item_touch_helper_max_drag_scroll_per_frame 0x7f06008d
int dimen item_touch_helper_swipe_escape_max_velocity 0x7f06008e
int dimen item_touch_helper_swipe_escape_velocity 0x7f06008f
int dimen ksad_action_bar_height 0x7f060090
int dimen ksad_activity_title_bar_height 0x7f060091
int dimen ksad_coupon_dialog_height 0x7f060092
int dimen ksad_coupon_dialog_value_prefix_text_size 0x7f060093
int dimen ksad_coupon_dialog_width 0x7f060094
int dimen ksad_draw_author_end_icon_width 0x7f060095
int dimen ksad_draw_author_icon_stroke_width 0x7f060096
int dimen ksad_draw_author_icon_width 0x7f060097
int dimen ksad_fullscreen_shake_center_hand_size 0x7f060098
int dimen ksad_fullscreen_shake_center_icon_size 0x7f060099
int dimen ksad_fullscreen_shake_center_tips_height 0x7f06009a
int dimen ksad_fullscreen_shake_center_tips_start_width 0x7f06009b
int dimen ksad_fullscreen_shake_center_tips_width 0x7f06009c
int dimen ksad_fullscreen_shake_tips_height 0x7f06009d
int dimen ksad_fullscreen_shake_tips_icon_live_shop_marginBottom 0x7f06009e
int dimen ksad_fullscreen_shake_tips_icon_marginBottom 0x7f06009f
int dimen ksad_fullscreen_shake_tips_icon_marginLeft 0x7f0600a0
int dimen ksad_fullscreen_shake_tips_icon_padding 0x7f0600a1
int dimen ksad_fullscreen_shake_tips_icon_size 0x7f0600a2
int dimen ksad_fullscreen_shake_tips_icon_stroke_size 0x7f0600a3
int dimen ksad_fullscreen_shake_tips_title_live_shop_marginBottom 0x7f0600a4
int dimen ksad_fullscreen_shake_tips_title_marginBottom 0x7f0600a5
int dimen ksad_fullscreen_shake_tips_width 0x7f0600a6
int dimen ksad_hand_slide_hand_height 0x7f0600a7
int dimen ksad_hand_slide_height 0x7f0600a8
int dimen ksad_hand_slide_tail_height_end 0x7f0600a9
int dimen ksad_hand_slide_tail_height_start 0x7f0600aa
int dimen ksad_hand_slide_tail_shadow_width 0x7f0600ab
int dimen ksad_hand_slide_tail_width 0x7f0600ac
int dimen ksad_hand_slide_up 0x7f0600ad
int dimen ksad_hand_slide_width 0x7f0600ae
int dimen ksad_image_player_sweep_wave_height_end 0x7f0600af
int dimen ksad_image_player_sweep_wave_height_start 0x7f0600b0
int dimen ksad_image_player_sweep_wave_width_end 0x7f0600b1
int dimen ksad_image_player_sweep_wave_width_start 0x7f0600b2
int dimen ksad_install_tips_bottom_height 0x7f0600b3
int dimen ksad_install_tips_bottom_margin_bottom 0x7f0600b4
int dimen ksad_install_tips_bottom_margin_left 0x7f0600b5
int dimen ksad_install_tips_card_elevation 0x7f0600b6
int dimen ksad_install_tips_card_height 0x7f0600b7
int dimen ksad_install_tips_card_margin 0x7f0600b8
int dimen ksad_install_tips_card_padding_left 0x7f0600b9
int dimen ksad_install_tips_card_padding_right 0x7f0600ba
int dimen ksad_interstitial_card_radius 0x7f0600bb
int dimen ksad_interstitial_download_bar_height 0x7f0600bc
int dimen ksad_interstitial_icon_radius 0x7f0600bd
int dimen ksad_jinniu_light_sweep_margin_left 0x7f0600be
int dimen ksad_jinniu_light_sweep_width 0x7f0600bf
int dimen ksad_live_base_card_full_height 0x7f0600c0
int dimen ksad_live_card_tips_animation_y 0x7f0600c1
int dimen ksad_live_card_tips_height 0x7f0600c2
int dimen ksad_live_card_tips_margin_bottom 0x7f0600c3
int dimen ksad_live_card_tips_margin_left 0x7f0600c4
int dimen ksad_live_origin_dialog_height 0x7f0600c5
int dimen ksad_live_shop_card_full_height 0x7f0600c6
int dimen ksad_live_subscribe_card_count_area_margin_top 0x7f0600c7
int dimen ksad_live_subscribe_card_count_area_trans_y 0x7f0600c8
int dimen ksad_live_subscribe_card_follower_avatar_size 0x7f0600c9
int dimen ksad_live_subscribe_card_full_height 0x7f0600ca
int dimen ksad_live_subscribe_card_height 0x7f0600cb
int dimen ksad_live_subscribe_card_logo_margin_bottom 0x7f0600cc
int dimen ksad_live_subscribe_card_margin 0x7f0600cd
int dimen ksad_live_subscribe_card_width_horizontal 0x7f0600ce
int dimen ksad_live_subscribe_dialog_height 0x7f0600cf
int dimen ksad_live_subscribe_dialog_icon_size 0x7f0600d0
int dimen ksad_live_subscribe_dialog_width 0x7f0600d1
int dimen ksad_live_subscribe_end_dialog_height 0x7f0600d2
int dimen ksad_live_subscribe_end_dialog_icon_size 0x7f0600d3
int dimen ksad_live_subscribe_end_dialog_width 0x7f0600d4
int dimen ksad_play_again_dialog_btn_height 0x7f0600d5
int dimen ksad_play_again_end_icon_size 0x7f0600d6
int dimen ksad_play_again_end_icon_size_horizontal 0x7f0600d7
int dimen ksad_playable_action_btn_height 0x7f0600d8
int dimen ksad_playable_end_btn_margin_top 0x7f0600d9
int dimen ksad_playable_end_btn_margin_top_small 0x7f0600da
int dimen ksad_playable_end_content_width 0x7f0600db
int dimen ksad_playable_end_desc_margin_top 0x7f0600dc
int dimen ksad_playable_end_desc_margin_top_small 0x7f0600dd
int dimen ksad_reward_apk_info_card_actionbar_text_size 0x7f0600de
int dimen ksad_reward_apk_info_card_height 0x7f0600df
int dimen ksad_reward_apk_info_card_icon_size 0x7f0600e0
int dimen ksad_reward_apk_info_card_margin 0x7f0600e1
int dimen ksad_reward_apk_info_card_step_area_height 0x7f0600e2
int dimen ksad_reward_apk_info_card_step_divider_height 0x7f0600e3
int dimen ksad_reward_apk_info_card_step_icon_radius 0x7f0600e4
int dimen ksad_reward_apk_info_card_step_icon_size 0x7f0600e5
int dimen ksad_reward_apk_info_card_step_icon_text_size 0x7f0600e6
int dimen ksad_reward_apk_info_card_tags_height 0x7f0600e7
int dimen ksad_reward_apk_info_card_width 0x7f0600e8
int dimen ksad_reward_author_height 0x7f0600e9
int dimen ksad_reward_author_icon_anim_start 0x7f0600ea
int dimen ksad_reward_author_icon_inner_width 0x7f0600eb
int dimen ksad_reward_author_icon_stroke_width 0x7f0600ec
int dimen ksad_reward_author_icon_width 0x7f0600ed
int dimen ksad_reward_author_width 0x7f0600ee
int dimen ksad_reward_follow_author_icon_margin_bottom 0x7f0600ef
int dimen ksad_reward_follow_card_height 0x7f0600f0
int dimen ksad_reward_follow_card_margin 0x7f0600f1
int dimen ksad_reward_follow_card_width_horizontal 0x7f0600f2
int dimen ksad_reward_follow_dialog_card_height 0x7f0600f3
int dimen ksad_reward_follow_dialog_height 0x7f0600f4
int dimen ksad_reward_follow_dialog_icon_size 0x7f0600f5
int dimen ksad_reward_follow_dialog_width 0x7f0600f6
int dimen ksad_reward_follow_end_card_height 0x7f0600f7
int dimen ksad_reward_follow_end_height 0x7f0600f8
int dimen ksad_reward_follow_end_width 0x7f0600f9
int dimen ksad_reward_follow_logo_margin_bottom 0x7f0600fa
int dimen ksad_reward_followed_card_height 0x7f0600fb
int dimen ksad_reward_followed_card_width 0x7f0600fc
int dimen ksad_reward_jinniu_card_btn_height 0x7f0600fd
int dimen ksad_reward_jinniu_card_height 0x7f0600fe
int dimen ksad_reward_jinniu_card_height_full 0x7f0600ff
int dimen ksad_reward_jinniu_card_icon_size 0x7f060100
int dimen ksad_reward_jinniu_card_margin 0x7f060101
int dimen ksad_reward_jinniu_card_padding 0x7f060102
int dimen ksad_reward_jinniu_dialog_close_size 0x7f060103
int dimen ksad_reward_jinniu_dialog_height 0x7f060104
int dimen ksad_reward_jinniu_dialog_icon_size 0x7f060105
int dimen ksad_reward_jinniu_dialog_width 0x7f060106
int dimen ksad_reward_jinniu_end_height 0x7f060107
int dimen ksad_reward_jinniu_end_icon_size 0x7f060108
int dimen ksad_reward_jinniu_end_max_width 0x7f060109
int dimen ksad_reward_jinniu_end_origin_text_size 0x7f06010a
int dimen ksad_reward_jinniu_logo_margin_bottom 0x7f06010b
int dimen ksad_reward_js_actionbar_height 0x7f06010c
int dimen ksad_reward_middle_end_card_logo_view_height 0x7f06010d
int dimen ksad_reward_middle_end_card_logo_view_margin_bottom 0x7f06010e
int dimen ksad_reward_native_normal_actionbar_height 0x7f06010f
int dimen ksad_reward_order_card_coupon_height 0x7f060110
int dimen ksad_reward_order_card_height 0x7f060111
int dimen ksad_reward_order_card_icon_size 0x7f060112
int dimen ksad_reward_order_card_margin 0x7f060113
int dimen ksad_reward_order_card_padding 0x7f060114
int dimen ksad_reward_order_coupon_divider 0x7f060115
int dimen ksad_reward_order_dialog_height 0x7f060116
int dimen ksad_reward_order_dialog_icon_size 0x7f060117
int dimen ksad_reward_order_dialog_width 0x7f060118
int dimen ksad_reward_order_end_dialog_height 0x7f060119
int dimen ksad_reward_order_end_dialog_width 0x7f06011a
int dimen ksad_reward_order_logo_margin_bottom 0x7f06011b
int dimen ksad_reward_order_original_price_size 0x7f06011c
int dimen ksad_reward_order_price_size 0x7f06011d
int dimen ksad_reward_playable_pre_tips_default_margin_bottom 0x7f06011e
int dimen ksad_reward_playable_pre_tips_height 0x7f06011f
int dimen ksad_reward_playable_pre_tips_icon_padding 0x7f060120
int dimen ksad_reward_playable_pre_tips_icon_size 0x7f060121
int dimen ksad_reward_playable_pre_tips_margin_bottom 0x7f060122
int dimen ksad_reward_playable_pre_tips_margin_bottom_without_actionbar 0x7f060123
int dimen ksad_reward_playable_pre_tips_margin_right 0x7f060124
int dimen ksad_reward_playable_pre_tips_transx 0x7f060125
int dimen ksad_reward_playable_pre_tips_width 0x7f060126
int dimen ksad_reward_shake_center_hand_size 0x7f060127
int dimen ksad_reward_shake_center_icon_size 0x7f060128
int dimen ksad_reward_shake_center_tips_height 0x7f060129
int dimen ksad_reward_shake_center_tips_start_width 0x7f06012a
int dimen ksad_reward_shake_center_tips_width 0x7f06012b
int dimen ksad_reward_shake_tips_height 0x7f06012c
int dimen ksad_reward_shake_tips_icon_live_shop_marginBottom 0x7f06012d
int dimen ksad_reward_shake_tips_icon_marginBottom 0x7f06012e
int dimen ksad_reward_shake_tips_icon_marginLeft 0x7f06012f
int dimen ksad_reward_shake_tips_icon_padding 0x7f060130
int dimen ksad_reward_shake_tips_icon_size 0x7f060131
int dimen ksad_reward_shake_tips_icon_stroke_size 0x7f060132
int dimen ksad_reward_shake_tips_title_live_shop_marginBottom 0x7f060133
int dimen ksad_reward_shake_tips_title_marginBottom 0x7f060134
int dimen ksad_reward_shake_tips_width 0x7f060135
int dimen ksad_reward_task_dialog_height 0x7f060136
int dimen ksad_reward_task_dialog_width 0x7f060137
int dimen ksad_seek_bar_progress_text_margin 0x7f060138
int dimen ksad_skip_view_divider_height 0x7f060139
int dimen ksad_skip_view_divider_margin_horizontal 0x7f06013a
int dimen ksad_skip_view_divider_margin_left 0x7f06013b
int dimen ksad_skip_view_divider_margin_vertical 0x7f06013c
int dimen ksad_skip_view_divider_width 0x7f06013d
int dimen ksad_skip_view_height 0x7f06013e
int dimen ksad_skip_view_padding_horizontal 0x7f06013f
int dimen ksad_skip_view_radius 0x7f060140
int dimen ksad_skip_view_text_size 0x7f060141
int dimen ksad_skip_view_width 0x7f060142
int dimen ksad_splash_actionbar_height 0x7f060143
int dimen ksad_splash_actionbar_margin_bottom 0x7f060144
int dimen ksad_splash_actionbar_width 0x7f060145
int dimen ksad_splash_endcard_ab_subtitle_text_sp 0x7f060146
int dimen ksad_splash_endcard_ab_subtitle_text_sp_land 0x7f060147
int dimen ksad_splash_endcard_ab_title_text_sp 0x7f060148
int dimen ksad_splash_endcard_ab_title_text_sp_land 0x7f060149
int dimen ksad_splash_endcard_actionbar_iconh 0x7f06014a
int dimen ksad_splash_endcard_actionbar_iconh_land 0x7f06014b
int dimen ksad_splash_endcard_actionbar_iconw 0x7f06014c
int dimen ksad_splash_endcard_actionbar_iconw_land 0x7f06014d
int dimen ksad_splash_endcard_app_iconh 0x7f06014e
int dimen ksad_splash_endcard_app_iconh_land 0x7f06014f
int dimen ksad_splash_endcard_app_iconw 0x7f060150
int dimen ksad_splash_endcard_app_iconw_land 0x7f060151
int dimen ksad_splash_endcard_app_margin_top 0x7f060152
int dimen ksad_splash_endcard_app_margin_top_land 0x7f060153
int dimen ksad_splash_endcard_appdesc_h 0x7f060154
int dimen ksad_splash_endcard_appdesc_h_land 0x7f060155
int dimen ksad_splash_endcard_appdesc_margin_top 0x7f060156
int dimen ksad_splash_endcard_appdesc_margin_top_land 0x7f060157
int dimen ksad_splash_endcard_appdesc_text_sp 0x7f060158
int dimen ksad_splash_endcard_appdesc_text_sp_land 0x7f060159
int dimen ksad_splash_endcard_appname_h 0x7f06015a
int dimen ksad_splash_endcard_appname_h_land 0x7f06015b
int dimen ksad_splash_endcard_appname_margin_top 0x7f06015c
int dimen ksad_splash_endcard_appname_margin_top_land 0x7f06015d
int dimen ksad_splash_endcard_appname_text_sp 0x7f06015e
int dimen ksad_splash_endcard_appname_text_sp_land 0x7f06015f
int dimen ksad_splash_endcard_appver_h 0x7f060160
int dimen ksad_splash_endcard_appver_h_land 0x7f060161
int dimen ksad_splash_endcard_appver_text_sp 0x7f060162
int dimen ksad_splash_endcard_appver_text_sp_land 0x7f060163
int dimen ksad_splash_endcard_close_root_h 0x7f060164
int dimen ksad_splash_endcard_close_root_h_land 0x7f060165
int dimen ksad_splash_endcard_close_root_margin_top 0x7f060166
int dimen ksad_splash_endcard_close_root_margin_top_land 0x7f060167
int dimen ksad_splash_endcard_gift_iconh 0x7f060168
int dimen ksad_splash_endcard_gift_iconh_land 0x7f060169
int dimen ksad_splash_endcard_gift_iconw 0x7f06016a
int dimen ksad_splash_endcard_gift_iconw_land 0x7f06016b
int dimen ksad_splash_endcard_title_iconh 0x7f06016c
int dimen ksad_splash_endcard_title_iconh_land 0x7f06016d
int dimen ksad_splash_endcard_title_iconw 0x7f06016e
int dimen ksad_splash_endcard_title_iconw_land 0x7f06016f
int dimen ksad_splash_hand_bgh 0x7f060170
int dimen ksad_splash_hand_bgw 0x7f060171
int dimen ksad_splash_rotate_view_height 0x7f060172
int dimen ksad_splash_rotate_view_margin_bottom 0x7f060173
int dimen ksad_splash_rotate_view_margin_top 0x7f060174
int dimen ksad_splash_rotate_view_width 0x7f060175
int dimen ksad_splash_shake_animator_height 0x7f060176
int dimen ksad_splash_shake_view_height 0x7f060177
int dimen ksad_splash_shake_view_margin_bottom 0x7f060178
int dimen ksad_splash_shake_view_margin_top 0x7f060179
int dimen ksad_splash_shake_view_width 0x7f06017a
int dimen ksad_title_bar_height 0x7f06017b
int dimen mtrl_bottomappbar_fabOffsetEndMode 0x7f06017c
int dimen mtrl_bottomappbar_fab_cradle_margin 0x7f06017d
int dimen mtrl_bottomappbar_fab_cradle_rounded_corner_radius 0x7f06017e
int dimen mtrl_bottomappbar_fab_cradle_vertical_offset 0x7f06017f
int dimen mtrl_bottomappbar_height 0x7f060180
int dimen mtrl_btn_corner_radius 0x7f060181
int dimen mtrl_btn_dialog_btn_min_width 0x7f060182
int dimen mtrl_btn_disabled_elevation 0x7f060183
int dimen mtrl_btn_disabled_z 0x7f060184
int dimen mtrl_btn_elevation 0x7f060185
int dimen mtrl_btn_focused_z 0x7f060186
int dimen mtrl_btn_hovered_z 0x7f060187
int dimen mtrl_btn_icon_btn_padding_left 0x7f060188
int dimen mtrl_btn_icon_padding 0x7f060189
int dimen mtrl_btn_inset 0x7f06018a
int dimen mtrl_btn_letter_spacing 0x7f06018b
int dimen mtrl_btn_padding_bottom 0x7f06018c
int dimen mtrl_btn_padding_left 0x7f06018d
int dimen mtrl_btn_padding_right 0x7f06018e
int dimen mtrl_btn_padding_top 0x7f06018f
int dimen mtrl_btn_pressed_z 0x7f060190
int dimen mtrl_btn_stroke_size 0x7f060191
int dimen mtrl_btn_text_btn_icon_padding 0x7f060192
int dimen mtrl_btn_text_btn_padding_left 0x7f060193
int dimen mtrl_btn_text_btn_padding_right 0x7f060194
int dimen mtrl_btn_text_size 0x7f060195
int dimen mtrl_btn_z 0x7f060196
int dimen mtrl_card_elevation 0x7f060197
int dimen mtrl_card_spacing 0x7f060198
int dimen mtrl_chip_pressed_translation_z 0x7f060199
int dimen mtrl_chip_text_size 0x7f06019a
int dimen mtrl_fab_elevation 0x7f06019b
int dimen mtrl_fab_translation_z_hovered_focused 0x7f06019c
int dimen mtrl_fab_translation_z_pressed 0x7f06019d
int dimen mtrl_navigation_elevation 0x7f06019e
int dimen mtrl_navigation_item_horizontal_padding 0x7f06019f
int dimen mtrl_navigation_item_icon_padding 0x7f0601a0
int dimen mtrl_snackbar_background_corner_radius 0x7f0601a1
int dimen mtrl_snackbar_margin 0x7f0601a2
int dimen mtrl_textinput_box_bottom_offset 0x7f0601a3
int dimen mtrl_textinput_box_corner_radius_medium 0x7f0601a4
int dimen mtrl_textinput_box_corner_radius_small 0x7f0601a5
int dimen mtrl_textinput_box_label_cutout_padding 0x7f0601a6
int dimen mtrl_textinput_box_padding_end 0x7f0601a7
int dimen mtrl_textinput_box_stroke_width_default 0x7f0601a8
int dimen mtrl_textinput_box_stroke_width_focused 0x7f0601a9
int dimen mtrl_textinput_outline_box_expanded_padding 0x7f0601aa
int dimen mtrl_toolbar_default_height 0x7f0601ab
int dimen notification_action_icon_size 0x7f0601ac
int dimen notification_action_text_size 0x7f0601ad
int dimen notification_big_circle_margin 0x7f0601ae
int dimen notification_content_margin_start 0x7f0601af
int dimen notification_large_icon_height 0x7f0601b0
int dimen notification_large_icon_width 0x7f0601b1
int dimen notification_main_column_padding_top 0x7f0601b2
int dimen notification_media_narrow_margin 0x7f0601b3
int dimen notification_right_icon_size 0x7f0601b4
int dimen notification_right_side_padding_top 0x7f0601b5
int dimen notification_small_icon_background_padding 0x7f0601b6
int dimen notification_small_icon_size_as_large 0x7f0601b7
int dimen notification_subtext_size 0x7f0601b8
int dimen notification_top_pad 0x7f0601b9
int dimen notification_top_pad_large_text 0x7f0601ba
int dimen subtitle_corner_radius 0x7f0601bb
int dimen subtitle_outline_width 0x7f0601bc
int dimen subtitle_shadow_offset 0x7f0601bd
int dimen subtitle_shadow_radius 0x7f0601be
int dimen tooltip_corner_radius 0x7f0601bf
int dimen tooltip_horizontal_padding 0x7f0601c0
int dimen tooltip_margin 0x7f0601c1
int dimen tooltip_precise_anchor_extra_offset 0x7f0601c2
int dimen tooltip_precise_anchor_threshold 0x7f0601c3
int dimen tooltip_vertical_padding 0x7f0601c4
int dimen tooltip_y_offset_non_touch 0x7f0601c5
int dimen tooltip_y_offset_touch 0x7f0601c6
int dimen tt_download_dialog_marginHorizontal 0x7f0601c7
int dimen tt_video_container_maxheight 0x7f0601c8
int dimen tt_video_container_minheight 0x7f0601c9
int dimen tt_video_cover_padding_horizon 0x7f0601ca
int dimen tt_video_cover_padding_vertical 0x7f0601cb
int drawable abc_ab_share_pack_mtrl_alpha 0x7f070007
int drawable abc_action_bar_item_background_material 0x7f070008
int drawable abc_btn_borderless_material 0x7f070009
int drawable abc_btn_check_material 0x7f07000a
int drawable abc_btn_check_to_on_mtrl_000 0x7f07000b
int drawable abc_btn_check_to_on_mtrl_015 0x7f07000c
int drawable abc_btn_colored_material 0x7f07000d
int drawable abc_btn_default_mtrl_shape 0x7f07000e
int drawable abc_btn_radio_material 0x7f07000f
int drawable abc_btn_radio_to_on_mtrl_000 0x7f070010
int drawable abc_btn_radio_to_on_mtrl_015 0x7f070011
int drawable abc_btn_switch_to_on_mtrl_00001 0x7f070012
int drawable abc_btn_switch_to_on_mtrl_00012 0x7f070013
int drawable abc_cab_background_internal_bg 0x7f070014
int drawable abc_cab_background_top_material 0x7f070015
int drawable abc_cab_background_top_mtrl_alpha 0x7f070016
int drawable abc_control_background_material 0x7f070017
int drawable abc_dialog_material_background 0x7f070018
int drawable abc_edit_text_material 0x7f070019
int drawable abc_ic_ab_back_material 0x7f07001a
int drawable abc_ic_arrow_drop_right_black_24dp 0x7f07001b
int drawable abc_ic_clear_material 0x7f07001c
int drawable abc_ic_commit_search_api_mtrl_alpha 0x7f07001d
int drawable abc_ic_go_search_api_material 0x7f07001e
int drawable abc_ic_menu_copy_mtrl_am_alpha 0x7f07001f
int drawable abc_ic_menu_cut_mtrl_alpha 0x7f070020
int drawable abc_ic_menu_overflow_material 0x7f070021
int drawable abc_ic_menu_paste_mtrl_am_alpha 0x7f070022
int drawable abc_ic_menu_selectall_mtrl_alpha 0x7f070023
int drawable abc_ic_menu_share_mtrl_alpha 0x7f070024
int drawable abc_ic_search_api_material 0x7f070025
int drawable abc_ic_star_black_16dp 0x7f070026
int drawable abc_ic_star_black_36dp 0x7f070027
int drawable abc_ic_star_black_48dp 0x7f070028
int drawable abc_ic_star_half_black_16dp 0x7f070029
int drawable abc_ic_star_half_black_36dp 0x7f07002a
int drawable abc_ic_star_half_black_48dp 0x7f07002b
int drawable abc_ic_voice_search_api_material 0x7f07002c
int drawable abc_item_background_holo_dark 0x7f07002d
int drawable abc_item_background_holo_light 0x7f07002e
int drawable abc_list_divider_material 0x7f07002f
int drawable abc_list_divider_mtrl_alpha 0x7f070030
int drawable abc_list_focused_holo 0x7f070031
int drawable abc_list_longpressed_holo 0x7f070032
int drawable abc_list_pressed_holo_dark 0x7f070033
int drawable abc_list_pressed_holo_light 0x7f070034
int drawable abc_list_selector_background_transition_holo_dark 0x7f070035
int drawable abc_list_selector_background_transition_holo_light 0x7f070036
int drawable abc_list_selector_disabled_holo_dark 0x7f070037
int drawable abc_list_selector_disabled_holo_light 0x7f070038
int drawable abc_list_selector_holo_dark 0x7f070039
int drawable abc_list_selector_holo_light 0x7f07003a
int drawable abc_menu_hardkey_panel_mtrl_mult 0x7f07003b
int drawable abc_popup_background_mtrl_mult 0x7f07003c
int drawable abc_ratingbar_indicator_material 0x7f07003d
int drawable abc_ratingbar_material 0x7f07003e
int drawable abc_ratingbar_small_material 0x7f07003f
int drawable abc_scrubber_control_off_mtrl_alpha 0x7f070040
int drawable abc_scrubber_control_to_pressed_mtrl_000 0x7f070041
int drawable abc_scrubber_control_to_pressed_mtrl_005 0x7f070042
int drawable abc_scrubber_primary_mtrl_alpha 0x7f070043
int drawable abc_scrubber_track_mtrl_alpha 0x7f070044
int drawable abc_seekbar_thumb_material 0x7f070045
int drawable abc_seekbar_tick_mark_material 0x7f070046
int drawable abc_seekbar_track_material 0x7f070047
int drawable abc_spinner_mtrl_am_alpha 0x7f070048
int drawable abc_spinner_textfield_background_material 0x7f070049
int drawable abc_switch_thumb_material 0x7f07004a
int drawable abc_switch_track_mtrl_alpha 0x7f07004b
int drawable abc_tab_indicator_material 0x7f07004c
int drawable abc_tab_indicator_mtrl_alpha 0x7f07004d
int drawable abc_text_cursor_material 0x7f07004e
int drawable abc_text_select_handle_left_mtrl_dark 0x7f07004f
int drawable abc_text_select_handle_left_mtrl_light 0x7f070050
int drawable abc_text_select_handle_middle_mtrl_dark 0x7f070051
int drawable abc_text_select_handle_middle_mtrl_light 0x7f070052
int drawable abc_text_select_handle_right_mtrl_dark 0x7f070053
int drawable abc_text_select_handle_right_mtrl_light 0x7f070054
int drawable abc_textfield_activated_mtrl_alpha 0x7f070055
int drawable abc_textfield_default_mtrl_alpha 0x7f070056
int drawable abc_textfield_search_activated_mtrl_alpha 0x7f070057
int drawable abc_textfield_search_default_mtrl_alpha 0x7f070058
int drawable abc_textfield_search_material 0x7f070059
int drawable abc_vector_test 0x7f07005a
int drawable anim_shake 0x7f07005b
int drawable anim_shake_download 0x7f07005c
int drawable avd_hide_password 0x7f07005d
int drawable avd_show_password 0x7f07005e
int drawable background1 0x7f07005f
int drawable background2 0x7f070060
int drawable banner_da_close 0x7f070061
int drawable beizi_ad_action_bg 0x7f070062
int drawable beizi_bg_circle 0x7f070063
int drawable beizi_bg_operate_button 0x7f070064
int drawable beizi_blue_corner 0x7f070065
int drawable beizi_close 0x7f070066
int drawable beizi_close_two 0x7f070067
int drawable beizi_complaint_button_disable_shape 0x7f070068
int drawable beizi_complaint_button_enable_shape 0x7f070069
int drawable beizi_complaint_dialog_close 0x7f07006a
int drawable beizi_complaint_dialog_shape 0x7f07006b
int drawable beizi_complaint_edit_suggest 0x7f07006c
int drawable beizi_complaint_edittext_bg 0x7f07006d
int drawable beizi_complaint_edittext_cursor 0x7f07006e
int drawable beizi_complaint_edittext_normal 0x7f07006f
int drawable beizi_custom_dialog_shape 0x7f070070
int drawable beizi_divider_dotted_line 0x7f070071
int drawable beizi_download_button_shape 0x7f070072
int drawable beizi_download_dialog_shape 0x7f070073
int drawable beizi_euler_angle 0x7f070074
int drawable beizi_icon_checkbox 0x7f070075
int drawable beizi_icon_shake_native 0x7f070076
int drawable beizi_icon_shake_native_download 0x7f070077
int drawable beizi_progress_bg 0x7f070078
int drawable beizi_twist_roll 0x7f070079
int drawable beizi_twist_roll_one 0x7f07007a
int drawable beizi_twist_roll_two 0x7f07007b
int drawable beizi_twist_roo_go_image 0x7f07007c
int drawable beizi_white_corner 0x7f07007d
int drawable bg_round_blue 0x7f07007e
int drawable bg_round_white 0x7f07007f
int drawable button_background 0x7f070080
int drawable button_background_black 0x7f070081
int drawable button_background_gray 0x7f070082
int drawable button_background_secondary 0x7f070083
int drawable button_black_frame 0x7f070084
int drawable button_close_background 0x7f070085
int drawable button_count_down_background 0x7f070086
int drawable button_count_down_interstitial_background 0x7f070087
int drawable click_arrow 0x7f070088
int drawable design_bottom_navigation_item_background 0x7f070089
int drawable design_fab_background 0x7f07008a
int drawable design_ic_visibility 0x7f07008b
int drawable design_ic_visibility_off 0x7f07008c
int drawable design_password_eye 0x7f07008d
int drawable design_snackbar_background 0x7f07008e
int drawable dialog_background 0x7f07008f
int drawable download_confirm_background_confirm 0x7f070090
int drawable download_confirm_background_landscape 0x7f070091
int drawable download_confirm_background_portrait 0x7f070092
int drawable game_overlay 0x7f070093
int drawable gdt_ic_express_pause 0x7f070094
int drawable gdt_ic_express_play 0x7f070095
int drawable hand 0x7f070096
int drawable ic_download_confirm_close 0x7f070097
int drawable ic_launcher_background 0x7f070098
int drawable ic_launcher_foreground 0x7f070099
int drawable ic_mtrl_chip_checked_black 0x7f07009a
int drawable ic_mtrl_chip_checked_circle 0x7f07009b
int drawable ic_mtrl_chip_close_circle 0x7f07009c
int drawable ic_white_close 0x7f07009d
int drawable icon_back_left 0x7f07009e
int drawable interstitial_close 0x7f07009f
int drawable jad_btn_skip_background_beizi 0x7f0700a0
int drawable ks_logo_background_beizi 0x7f0700a1
int drawable ksad_ad_dislike_bottom 0x7f0700a2
int drawable ksad_ad_dislike_gray 0x7f0700a3
int drawable ksad_ad_hand 0x7f0700a4
int drawable ksad_ad_icon 0x7f0700a5
int drawable ksad_ad_live_end 0x7f0700a6
int drawable ksad_api_default_app_icon 0x7f0700a7
int drawable ksad_arrow_left 0x7f0700a8
int drawable ksad_author_circle 0x7f0700a9
int drawable ksad_author_icon_bg 0x7f0700aa
int drawable ksad_button_bg 0x7f0700ab
int drawable ksad_click_wave_bg 0x7f0700ac
int drawable ksad_close_bg 0x7f0700ad
int drawable ksad_compliance_view_bg 0x7f0700ae
int drawable ksad_compliance_white_bg 0x7f0700af
int drawable ksad_coupon_dialog_action_btn_bg 0x7f0700b0
int drawable ksad_coupon_dialog_bg 0x7f0700b1
int drawable ksad_default_app_icon 0x7f0700b2
int drawable ksad_download_progress_mask_bg 0x7f0700b3
int drawable ksad_draw_bottom_bg 0x7f0700b4
int drawable ksad_draw_card_close 0x7f0700b5
int drawable ksad_draw_card_white_bg 0x7f0700b6
int drawable ksad_draw_concert_light_bg 0x7f0700b7
int drawable ksad_draw_convert_light_press 0x7f0700b8
int drawable ksad_draw_convert_light_unpress 0x7f0700b9
int drawable ksad_draw_convert_normal_bg 0x7f0700ba
int drawable ksad_draw_download_progress 0x7f0700bb
int drawable ksad_draw_follow_arrow_down 0x7f0700bc
int drawable ksad_draw_follow_btn_bg 0x7f0700bd
int drawable ksad_draw_live_actionbar_shop_bg 0x7f0700be
int drawable ksad_draw_live_bottom_base_bg 0x7f0700bf
int drawable ksad_draw_live_button_bg 0x7f0700c0
int drawable ksad_feed_actionbar_before_bg 0x7f0700c1
int drawable ksad_feed_actionbar_cover_bg 0x7f0700c2
int drawable ksad_feed_actionbar_cover_normal 0x7f0700c3
int drawable ksad_feed_actionbar_cover_pressed 0x7f0700c4
int drawable ksad_feed_actionbar_h5_cover 0x7f0700c5
int drawable ksad_feed_app_download_before_bg 0x7f0700c6
int drawable ksad_feed_app_h5_before_bg 0x7f0700c7
int drawable ksad_feed_biserial_bg 0x7f0700c8
int drawable ksad_feed_download_progress 0x7f0700c9
int drawable ksad_feed_download_progress_novel 0x7f0700ca
int drawable ksad_feed_immerse_image_bg 0x7f0700cb
int drawable ksad_feed_immerse_video_bg 0x7f0700cc
int drawable ksad_feed_novel_bg 0x7f0700cd
int drawable ksad_feed_novel_bottom_bg 0x7f0700ce
int drawable ksad_feed_shake_bg 0x7f0700cf
int drawable ksad_feed_webview_bg 0x7f0700d0
int drawable ksad_ic_arrow_right 0x7f0700d1
int drawable ksad_ic_arrow_right_main_color 0x7f0700d2
int drawable ksad_ic_clock 0x7f0700d3
int drawable ksad_ic_clock_grey 0x7f0700d4
int drawable ksad_ic_default_user_avatar 0x7f0700d5
int drawable ksad_ic_fire 0x7f0700d6
int drawable ksad_ic_reflux_recommend 0x7f0700d7
int drawable ksad_ic_rotate_line 0x7f0700d8
int drawable ksad_ic_rotate_phone 0x7f0700d9
int drawable ksad_ic_shake_combo_hand 0x7f0700da
int drawable ksad_ic_shake_hand 0x7f0700db
int drawable ksad_ic_shake_phone 0x7f0700dc
int drawable ksad_icon_auto_close 0x7f0700dd
int drawable ksad_image_player_sweep1 0x7f0700de
int drawable ksad_image_player_sweep2 0x7f0700df
int drawable ksad_install_dialog_bg 0x7f0700e0
int drawable ksad_install_tips_bg 0x7f0700e1
int drawable ksad_install_tips_bottom_bg 0x7f0700e2
int drawable ksad_install_tips_btn_install_bg 0x7f0700e3
int drawable ksad_install_tips_btn_install_bottom_bg 0x7f0700e4
int drawable ksad_install_tips_ic_close 0x7f0700e5
int drawable ksad_interstitial_actionbar_app_progress 0x7f0700e6
int drawable ksad_interstitial_btn_bg 0x7f0700e7
int drawable ksad_interstitial_btn_voice 0x7f0700e8
int drawable ksad_interstitial_btn_watch_continue_bg 0x7f0700e9
int drawable ksad_interstitial_close 0x7f0700ea
int drawable ksad_interstitial_intercept_dialog_bg 0x7f0700eb
int drawable ksad_interstitial_left_arrow 0x7f0700ec
int drawable ksad_interstitial_left_slide_bg 0x7f0700ed
int drawable ksad_interstitial_mute 0x7f0700ee
int drawable ksad_interstitial_playable_timer_bg 0x7f0700ef
int drawable ksad_interstitial_right_arrow 0x7f0700f0
int drawable ksad_interstitial_right_slide_bg 0x7f0700f1
int drawable ksad_interstitial_toast_bg 0x7f0700f2
int drawable ksad_interstitial_toast_logo 0x7f0700f3
int drawable ksad_interstitial_unmute 0x7f0700f4
int drawable ksad_interstitial_video_play 0x7f0700f5
int drawable ksad_jinniu_light_sweep 0x7f0700f6
int drawable ksad_ksad_reward_btn_blue_bg 0x7f0700f7
int drawable ksad_ksad_reward_follow_btn_follow_bg 0x7f0700f8
int drawable ksad_ksad_reward_follow_btn_follow_unchecked_bg 0x7f0700f9
int drawable ksad_live_icon_corner_badge_bg 0x7f0700fa
int drawable ksad_live_top_back 0x7f0700fb
int drawable ksad_logo_bg_big_radius 0x7f0700fc
int drawable ksad_logo_gray 0x7f0700fd
int drawable ksad_logo_white 0x7f0700fe
int drawable ksad_main_color_card_bg 0x7f0700ff
int drawable ksad_message_toast_2_bg 0x7f070100
int drawable ksad_message_toast_bg 0x7f070101
int drawable ksad_native_rotate_alpha_phone 0x7f070102
int drawable ksad_native_rotate_circle 0x7f070103
int drawable ksad_native_rotate_line 0x7f070104
int drawable ksad_native_rotate_phone 0x7f070105
int drawable ksad_native_video_duration_bg 0x7f070106
int drawable ksad_navi_back_selector 0x7f070107
int drawable ksad_navi_close_selector 0x7f070108
int drawable ksad_navigation_back 0x7f070109
int drawable ksad_navigation_back_pressed 0x7f07010a
int drawable ksad_navigation_close 0x7f07010b
int drawable ksad_navigation_close_pressed 0x7f07010c
int drawable ksad_notification_control_btn_bg_checked 0x7f07010d
int drawable ksad_notification_control_btn_bg_unchecked 0x7f07010e
int drawable ksad_notification_default_icon 0x7f07010f
int drawable ksad_notification_install_bg 0x7f070110
int drawable ksad_notification_progress 0x7f070111
int drawable ksad_notification_small_icon 0x7f070112
int drawable ksad_page_close 0x7f070113
int drawable ksad_photo_video_play_icon_2 0x7f070114
int drawable ksad_play_again_dialog_img 0x7f070115
int drawable ksad_play_again_dialog_img_bg 0x7f070116
int drawable ksad_playable_pre_tips_bg 0x7f070117
int drawable ksad_reward_apk_stars_divider 0x7f070118
int drawable ksad_reward_apk_tags_divider 0x7f070119
int drawable ksad_reward_call_bg 0x7f07011a
int drawable ksad_reward_card_bg 0x7f07011b
int drawable ksad_reward_card_close 0x7f07011c
int drawable ksad_reward_card_tag_bg 0x7f07011d
int drawable ksad_reward_card_tag_white_bg 0x7f07011e
int drawable ksad_reward_deep_task_icon_bg 0x7f07011f
int drawable ksad_reward_deep_task_view_bg 0x7f070120
int drawable ksad_reward_follow_add 0x7f070121
int drawable ksad_reward_follow_arrow_down 0x7f070122
int drawable ksad_reward_gift 0x7f070123
int drawable ksad_reward_install_btn_bg 0x7f070124
int drawable ksad_reward_jinniu_close 0x7f070125
int drawable ksad_reward_live_action_bottom_bg 0x7f070126
int drawable ksad_reward_live_app_download_bg 0x7f070127
int drawable ksad_reward_live_download_progress 0x7f070128
int drawable ksad_reward_live_end_bottom_action_btn_bg 0x7f070129
int drawable ksad_reward_live_end_bottom_bg 0x7f07012a
int drawable ksad_reward_live_end_bottom_des_btn_bg 0x7f07012b
int drawable ksad_reward_open_land_page_time_bg 0x7f07012c
int drawable ksad_reward_order_card_coupon_divider 0x7f07012d
int drawable ksad_reward_origrin_live_actionbar_bg 0x7f07012e
int drawable ksad_reward_origrin_live_button_bg 0x7f07012f
int drawable ksad_reward_preview_bottom_bg 0x7f070130
int drawable ksad_reward_preview_close 0x7f070131
int drawable ksad_reward_preview_top_gift 0x7f070132
int drawable ksad_reward_preview_topbar_progress 0x7f070133
int drawable ksad_reward_red_right_arrow 0x7f070134
int drawable ksad_reward_reflux_recommand 0x7f070135
int drawable ksad_reward_reflux_title_close 0x7f070136
int drawable ksad_reward_step_big_icon_forground 0x7f070137
int drawable ksad_reward_step_icon_bg_unchecked 0x7f070138
int drawable ksad_reward_step_icon_checked 0x7f070139
int drawable ksad_reward_task_dialog_bg 0x7f07013a
int drawable ksad_sdk_logo 0x7f07013b
int drawable ksad_seconed_confirm_bg 0x7f07013c
int drawable ksad_seekbar_btn_slider 0x7f07013d
int drawable ksad_seekbar_btn_slider_gray 0x7f07013e
int drawable ksad_shake_center_bg 0x7f07013f
int drawable ksad_shake_layout_bg 0x7f070140
int drawable ksad_shake_tips_bg 0x7f070141
int drawable ksad_shake_tips_icon_bg 0x7f070142
int drawable ksad_skip_view_bg 0x7f070143
int drawable ksad_slide_hand 0x7f070144
int drawable ksad_slide_hand_bg 0x7f070145
int drawable ksad_slide_square_bg_shape 0x7f070146
int drawable ksad_splash_actionbar_bg 0x7f070147
int drawable ksad_splash_base_arrows 0x7f070148
int drawable ksad_splash_bg_slide 0x7f070149
int drawable ksad_splash_default_bgimg 0x7f07014a
int drawable ksad_splash_default_icon 0x7f07014b
int drawable ksad_splash_down_highlight_arrow 0x7f07014c
int drawable ksad_splash_endcard_btn_bg 0x7f07014d
int drawable ksad_splash_endcard_close 0x7f07014e
int drawable ksad_splash_endcard_close_bg 0x7f07014f
int drawable ksad_splash_endcard_giftbox 0x7f070150
int drawable ksad_splash_endcard_title 0x7f070151
int drawable ksad_splash_float_white_bg 0x7f070152
int drawable ksad_splash_hand 0x7f070153
int drawable ksad_splash_hand_lb 0x7f070154
int drawable ksad_splash_hand_lt 0x7f070155
int drawable ksad_splash_hand_rb 0x7f070156
int drawable ksad_splash_hand_rt 0x7f070157
int drawable ksad_splash_left_highlight_arrow 0x7f070158
int drawable ksad_splash_logo 0x7f070159
int drawable ksad_splash_logo_bg 0x7f07015a
int drawable ksad_splash_mute 0x7f07015b
int drawable ksad_splash_mute_pressed 0x7f07015c
int drawable ksad_splash_right_highlight_arrow 0x7f07015d
int drawable ksad_splash_rotate_combo_left_arrow 0x7f07015e
int drawable ksad_splash_rotate_combo_phone 0x7f07015f
int drawable ksad_splash_rotate_combo_right_arrow 0x7f070160
int drawable ksad_splash_rotate_type_two 0x7f070161
int drawable ksad_splash_shake_combo_border 0x7f070162
int drawable ksad_splash_shake_combo_button 0x7f070163
int drawable ksad_splash_side_bg 0x7f070164
int drawable ksad_splash_slide_animation_hand 0x7f070165
int drawable ksad_splash_slide_round_bg 0x7f070166
int drawable ksad_splash_slide_round_white_bg 0x7f070167
int drawable ksad_splash_slide_square_bg 0x7f070168
int drawable ksad_splash_slide_tag 0x7f070169
int drawable ksad_splash_sound_selector 0x7f07016a
int drawable ksad_splash_unmute 0x7f07016b
int drawable ksad_splash_unmute_pressed 0x7f07016c
int drawable ksad_splash_up_highlight_arrow 0x7f07016d
int drawable ksad_splash_vplus_close 0x7f07016e
int drawable ksad_split_mini_video_close_btn 0x7f07016f
int drawable ksad_star_checked 0x7f070170
int drawable ksad_star_half 0x7f070171
int drawable ksad_star_unchecked 0x7f070172
int drawable ksad_tips_card_bg 0x7f070173
int drawable ksad_toast_corner_bg 0x7f070174
int drawable ksad_toast_text 0x7f070175
int drawable ksad_video_actionbar_app_progress 0x7f070176
int drawable ksad_video_actionbar_cover_bg 0x7f070177
int drawable ksad_video_actionbar_cover_normal 0x7f070178
int drawable ksad_video_actionbar_cover_pressed 0x7f070179
int drawable ksad_video_actionbar_h5_bg 0x7f07017a
int drawable ksad_video_app_12_bg 0x7f07017b
int drawable ksad_video_app_16_bg 0x7f07017c
int drawable ksad_video_app_20_bg 0x7f07017d
int drawable ksad_video_btn_bg 0x7f07017e
int drawable ksad_video_closedialog_bg 0x7f07017f
int drawable ksad_video_install_bg 0x7f070180
int drawable ksad_video_play_165 0x7f070181
int drawable ksad_video_play_176 0x7f070182
int drawable ksad_video_player_back_btn 0x7f070183
int drawable ksad_video_player_exit_fullscreen_btn 0x7f070184
int drawable ksad_video_player_fullscreen_btn 0x7f070185
int drawable ksad_video_player_pause_btn 0x7f070186
int drawable ksad_video_player_pause_center 0x7f070187
int drawable ksad_video_player_play_btn 0x7f070188
int drawable ksad_video_progress 0x7f070189
int drawable ksad_video_progress_normal 0x7f07018a
int drawable ksad_video_reward_deep_task_icon 0x7f07018b
int drawable ksad_video_reward_icon 0x7f07018c
int drawable ksad_video_skip_icon 0x7f07018d
int drawable ksad_video_sound_close 0x7f07018e
int drawable ksad_video_sound_open 0x7f07018f
int drawable ksad_video_sound_selector 0x7f070190
int drawable ksad_web_exit_intercept_dialog_bg 0x7f070191
int drawable ksad_web_exit_intercept_negative_btn_bg 0x7f070192
int drawable ksad_web_exit_intercept_positive_btn_bg 0x7f070193
int drawable ksad_web_reward_task_img 0x7f070194
int drawable ksad_web_reward_task_text_bg 0x7f070195
int drawable ksad_web_tip_bar_close_button 0x7f070196
int drawable live_loading_anim 0x7f070197
int drawable live_loading_background 0x7f070198
int drawable logo_text_background 0x7f070199
int drawable mtrl_snackbar_background 0x7f07019a
int drawable mtrl_tabs_default_indicator 0x7f07019b
int drawable navigation_empty_icon 0x7f07019c
int drawable notification_action_background 0x7f07019d
int drawable notification_bg 0x7f07019e
int drawable notification_bg_low 0x7f07019f
int drawable notification_bg_low_normal 0x7f0701a0
int drawable notification_bg_low_pressed 0x7f0701a1
int drawable notification_bg_normal 0x7f0701a2
int drawable notification_bg_normal_pressed 0x7f0701a3
int drawable notification_icon_background 0x7f0701a4
int drawable notification_template_icon_bg 0x7f0701a5
int drawable notification_template_icon_low_bg 0x7f0701a6
int drawable notification_tile_bg 0x7f0701a7
int drawable notify_panel_notification_icon_bg 0x7f0701a8
int drawable oset_ad_logo_bg_black_translucent 0x7f0701a9
int drawable oset_api_download_btn_bg 0x7f0701aa
int drawable oset_banner_close 0x7f0701ab
int drawable oset_bg_black_gradient 0x7f0701ac
int drawable oset_bg_black_radius 0x7f0701ad
int drawable oset_bg_black_radius_5 0x7f0701ae
int drawable oset_bg_black_top_to_bottom 0x7f0701af
int drawable oset_bg_black_translucent 0x7f0701b0
int drawable oset_bg_dialog_rule 0x7f0701b1
int drawable oset_btn_bg_creative 0x7f0701b2
int drawable oset_bz_btn_act_sub_bg 0x7f0701b3
int drawable oset_bz_media_bg 0x7f0701b4
int drawable oset_dial_back 0x7f0701b5
int drawable oset_dial_dialog_dial_close 0x7f0701b6
int drawable oset_gdt_ad_info_bg 0x7f0701b7
int drawable oset_gdt_btn_bg 0x7f0701b8
int drawable oset_gm_btn_act_sub_bg 0x7f0701b9
int drawable oset_gm_media_bg 0x7f0701ba
int drawable oset_image_back 0x7f0701bb
int drawable oset_ks_btn_act_sub_bg 0x7f0701bc
int drawable oset_ks_btn_act_sub_bg_multiple_version 0x7f0701bd
int drawable oset_ks_media_bg 0x7f0701be
int drawable oset_ks_media_bg_multiple_version 0x7f0701bf
int drawable oset_ks_native_ad_bg 0x7f0701c0
int drawable oset_ks_native_item_btn_bg 0x7f0701c1
int drawable oset_od_splash_click_bg 0x7f0701c2
int drawable oset_sg_btn_act_sub_bg 0x7f0701c3
int drawable oset_sg_media_bg 0x7f0701c4
int drawable oset_sigmob_native_btn_bg 0x7f0701c5
int drawable oset_video_seekbar 0x7f0701c6
int drawable progress 0x7f0701c7
int drawable scrollbar 0x7f0701c8
int drawable shake_one 0x7f0701c9
int drawable shake_one_download 0x7f0701ca
int drawable shake_two 0x7f0701cb
int drawable shake_two_download 0x7f0701cc
int drawable sig_circle_gray 0x7f0701cd
int drawable sig_cta_bg 0x7f0701ce
int drawable sig_dislike_layout_background 0x7f0701cf
int drawable sig_dislike_suggest_ev 0x7f0701d0
int drawable sig_image_shake 0x7f0701d1
int drawable sig_image_shake_new 0x7f0701d2
int drawable sig_image_video_back_left 0x7f0701d3
int drawable sig_image_video_back_left_black 0x7f0701d4
int drawable sig_image_video_back_right 0x7f0701d5
int drawable sig_image_video_background_block 0x7f0701d6
int drawable sig_image_video_background_white 0x7f0701d7
int drawable sig_image_video_fullscreen 0x7f0701d8
int drawable sig_image_video_mute 0x7f0701d9
int drawable sig_image_video_play 0x7f0701da
int drawable sig_image_video_replay 0x7f0701db
int drawable sig_image_video_small 0x7f0701dc
int drawable sig_image_video_unmute 0x7f0701dd
int drawable sig_interstitial_cta_bg 0x7f0701de
int drawable sig_layout_background_left_right_radius 0x7f0701df
int drawable sig_layout_background_radius 0x7f0701e0
int drawable sig_shake_layout_background 0x7f0701e1
int drawable sig_video_bottom_progress 0x7f0701e2
int drawable slide_down_close_ad 0x7f0701e3
int drawable slide_down_one 0x7f0701e4
int drawable slide_down_three 0x7f0701e5
int drawable slide_down_two 0x7f0701e6
int drawable start_overlay 0x7f0701e7
int drawable title 0x7f0701e8
int drawable title_en 0x7f0701e9
int drawable tooltip_frame_dark 0x7f0701ea
int drawable tooltip_frame_light 0x7f0701eb
int drawable tt_ad_download_progress_bar_horizontal 0x7f0701ec
int drawable tt_ad_logo 0x7f0701ed
int drawable tt_ad_logo_backup 0x7f0701ee
int drawable tt_ad_logo_new 0x7f0701ef
int drawable tt_ad_logo_small 0x7f0701f0
int drawable tt_ad_skip_btn_bg 0x7f0701f1
int drawable tt_adapter_baidu_logo 0x7f0701f2
int drawable tt_adapter_gdt_logo 0x7f0701f3
int drawable tt_adapter_ks_logo 0x7f0701f4
int drawable tt_app_detail_back_btn 0x7f0701f5
int drawable tt_app_detail_black 0x7f0701f6
int drawable tt_app_detail_info 0x7f0701f7
int drawable tt_appdownloader_action_bg 0x7f0701f8
int drawable tt_appdownloader_action_new_bg 0x7f0701f9
int drawable tt_appdownloader_ad_detail_download_progress 0x7f0701fa
int drawable tt_appdownloader_detail_download_success_bg 0x7f0701fb
int drawable tt_appdownloader_download_progress_bar_horizontal 0x7f0701fc
int drawable tt_appdownloader_download_progress_bar_horizontal_new 0x7f0701fd
int drawable tt_appdownloader_download_progress_bar_horizontal_night 0x7f0701fe
int drawable tt_arrow_down 0x7f0701ff
int drawable tt_arrow_left 0x7f070200
int drawable tt_arrow_right 0x7f070201
int drawable tt_arrow_up 0x7f070202
int drawable tt_back_video 0x7f070203
int drawable tt_blue_hand 0x7f070204
int drawable tt_browser_progress_style 0x7f070205
int drawable tt_close_btn 0x7f070206
int drawable tt_close_move_details_normal 0x7f070207
int drawable tt_copy_privacy_url_btn 0x7f070208
int drawable tt_coupon_bg 0x7f070209
int drawable tt_dislike_icon 0x7f07020a
int drawable tt_dislike_icon2 0x7f07020b
int drawable tt_dislike_icon_inter_night 0x7f07020c
int drawable tt_dislike_icon_night 0x7f07020d
int drawable tt_download_btn_bg 0x7f07020e
int drawable tt_download_dialog_btn_bg 0x7f07020f
int drawable tt_ecomm_page_backup_img 0x7f070210
int drawable tt_ecomm_page_btn_bg 0x7f070211
int drawable tt_ecomm_page_line 0x7f070212
int drawable tt_enlarge_video 0x7f070213
int drawable tt_forward_video 0x7f070214
int drawable tt_ic_back_light 0x7f070215
int drawable tt_ic_top_again_bg 0x7f070216
int drawable tt_ic_top_arrow_right 0x7f070217
int drawable tt_icon_live_video 0x7f070218
int drawable tt_im_fs_handle 0x7f070219
int drawable tt_im_fs_tip 0x7f07021a
int drawable tt_live_ad_loading_btn_status 0x7f07021b
int drawable tt_live_ad_status_icon 0x7f07021c
int drawable tt_live_avatar_bg 0x7f07021d
int drawable tt_live_feed_status_icon 0x7f07021e
int drawable tt_live_icon_red 0x7f07021f
int drawable tt_live_loading 0x7f070220
int drawable tt_live_video_loading_progress 0x7f070221
int drawable tt_lu_backup_img 0x7f070222
int drawable tt_mute 0x7f070223
int drawable tt_new_pause_video 0x7f070224
int drawable tt_new_play_video 0x7f070225
int drawable tt_normalscreen_loading 0x7f070226
int drawable tt_open_app_detail_download_btn_bg 0x7f070227
int drawable tt_open_app_detail_list_item 0x7f070228
int drawable tt_play_movebar_textpage 0x7f070229
int drawable tt_playable_btn_bk 0x7f07022a
int drawable tt_playable_game_icon 0x7f07022b
int drawable tt_ratingbar_empty_star2 0x7f07022c
int drawable tt_ratingbar_full_star2 0x7f07022d
int drawable tt_refreshing_video_textpage 0x7f07022e
int drawable tt_refreshing_video_textpage_normal 0x7f07022f
int drawable tt_refreshing_video_textpage_pressed 0x7f070230
int drawable tt_retain_gift 0x7f070231
int drawable tt_reward_again_gift 0x7f070232
int drawable tt_reward_auth_gold_icon 0x7f070233
int drawable tt_reward_box_time_bg 0x7f070234
int drawable tt_reward_browse_multi_icon 0x7f070235
int drawable tt_reward_chest_box 0x7f070236
int drawable tt_reward_chest_btn_bg 0x7f070237
int drawable tt_reward_chest_gift2 0x7f070238
int drawable tt_reward_chest_gift_open2 0x7f070239
int drawable tt_reward_chest_tip 0x7f07023a
int drawable tt_reward_coin 0x7f07023b
int drawable tt_reward_dislike_icon 0x7f07023c
int drawable tt_reward_full_feedback 0x7f07023d
int drawable tt_reward_full_mute 0x7f07023e
int drawable tt_reward_full_unmute 0x7f07023f
int drawable tt_right_arrow 0x7f070240
int drawable tt_saas_close 0x7f070241
int drawable tt_shadow_fullscreen_top 0x7f070242
int drawable tt_shop_page_red_bag 0x7f070243
int drawable tt_shop_page_return 0x7f070244
int drawable tt_shrink_fullscreen 0x7f070245
int drawable tt_shrink_video 0x7f070246
int drawable tt_skip_btn 0x7f070247
int drawable tt_splash_ad_backup_bg 0x7f070248
int drawable tt_splash_ad_backup_btn_bg 0x7f070249
int drawable tt_splash_arrow 0x7f07024a
int drawable tt_splash_card_btn_bg 0x7f07024b
int drawable tt_splash_card_close 0x7f07024c
int drawable tt_splash_card_feedback_bg 0x7f07024d
int drawable tt_splash_card_shake 0x7f07024e
int drawable tt_splash_click_bar_go 0x7f07024f
int drawable tt_splash_hand 0x7f070250
int drawable tt_splash_hand2 0x7f070251
int drawable tt_splash_hand3 0x7f070252
int drawable tt_splash_mute 0x7f070253
int drawable tt_splash_rock 0x7f070254
int drawable tt_splash_shake_hand 0x7f070255
int drawable tt_splash_slide_right_bg 0x7f070256
int drawable tt_splash_slide_right_circle 0x7f070257
int drawable tt_splash_slide_up_10 0x7f070258
int drawable tt_splash_slide_up_arrow 0x7f070259
int drawable tt_splash_slide_up_bg 0x7f07025a
int drawable tt_splash_slide_up_circle 0x7f07025b
int drawable tt_splash_slide_up_finger 0x7f07025c
int drawable tt_splash_twist 0x7f07025d
int drawable tt_splash_unlock_btn_bg 0x7f07025e
int drawable tt_splash_unlock_icon_empty 0x7f07025f
int drawable tt_splash_unlock_image_arrow 0x7f070260
int drawable tt_splash_unlock_image_go 0x7f070261
int drawable tt_splash_unmute 0x7f070262
int drawable tt_star 0x7f070263
int drawable tt_star_empty_bg 0x7f070264
int drawable tt_star_full_bg 0x7f070265
int drawable tt_star_thick 0x7f070266
int drawable tt_stop_movebar_textpage 0x7f070267
int drawable tt_suggestion_logo 0x7f070268
int drawable tt_tooltip_people 0x7f070269
int drawable tt_ugen_back 0x7f07026a
int drawable tt_ugen_close 0x7f07026b
int drawable tt_ugen_logo 0x7f07026c
int drawable tt_ugen_muted 0x7f07026d
int drawable tt_ugen_rating_star 0x7f07026e
int drawable tt_ugen_unmuted 0x7f07026f
int drawable tt_unmute 0x7f070270
int drawable tt_user 0x7f070271
int drawable tt_video_close_drawable 0x7f070272
int drawable tt_video_loading_progress_bar 0x7f070273
int drawable tt_white_hand 0x7f070274
int drawable tt_white_righterbackicon_titlebar 0x7f070275
int drawable tt_white_slide_up 0x7f070276
int drawable tt_wriggle_union 0x7f070277
int drawable tt_wriggle_union_white 0x7f070278
int drawable ttdownloader_bg_appinfo_btn 0x7f070279
int drawable ttdownloader_bg_appinfo_dialog 0x7f07027a
int drawable ttdownloader_bg_button_blue_corner 0x7f07027b
int drawable ttdownloader_bg_kllk_btn1 0x7f07027c
int drawable ttdownloader_bg_kllk_btn2 0x7f07027d
int drawable ttdownloader_bg_transparent 0x7f07027e
int drawable ttdownloader_bg_white_corner 0x7f07027f
int drawable ttdownloader_dash_line 0x7f070280
int drawable ttdownloader_icon_back_arrow 0x7f070281
int drawable ttdownloader_icon_download 0x7f070282
int drawable ttdownloader_icon_yes 0x7f070283
int drawable voice_off 0x7f070284
int drawable voice_on 0x7f070285
int id ALT 0x7f080000
int id CTRL 0x7f080001
int id FUNCTION 0x7f080002
int id META 0x7f080003
int id SHIFT 0x7f080004
int id SYM 0x7f080005
int id action0 0x7f080006
int id action_bar 0x7f080007
int id action_bar_activity_content 0x7f080008
int id action_bar_container 0x7f080009
int id action_bar_root 0x7f08000a
int id action_bar_spinner 0x7f08000b
int id action_bar_subtitle 0x7f08000c
int id action_bar_title 0x7f08000d
int id action_container 0x7f08000e
int id action_context_bar 0x7f08000f
int id action_divider 0x7f080010
int id action_image 0x7f080011
int id action_menu_divider 0x7f080012
int id action_menu_presenter 0x7f080013
int id action_mode_bar 0x7f080014
int id action_mode_bar_stub 0x7f080015
int id action_mode_close_button 0x7f080016
int id action_text 0x7f080017
int id actions 0x7f080018
int id activity_chooser_view_content 0x7f080019
int id ad_layout 0x7f08001a
int id add 0x7f08001b
int id agreeButton 0x7f08001c
int id alertTitle 0x7f08001d
int id all 0x7f08001e
int id always 0x7f08001f
int id async 0x7f080020
int id auto 0x7f080021
int id banner_full_tk_card_view 0x7f080022
int id barrier 0x7f080023
int id beginning 0x7f080024
int id beizi_ad_action 0x7f080025
int id beizi_ad_container 0x7f080026
int id beizi_ad_cover_image_container 0x7f080027
int id beizi_ad_cover_image_container_parent 0x7f080028
int id beizi_ad_logo 0x7f080029
int id beizi_addeci_content_tv 0x7f08002a
int id beizi_addeci_content_wb 0x7f08002b
int id beizi_addep_fold_iv 0x7f08002c
int id beizi_addep_item_divider_view 0x7f08002d
int id beizi_addep_title_tv 0x7f08002e
int id beizi_appinfo_intro_below_line 0x7f08002f
int id beizi_appinfo_intro_layout 0x7f080030
int id beizi_appinfo_intro_textview 0x7f080031
int id beizi_appinfo_permission_below_line 0x7f080032
int id beizi_appinfo_permission_layout 0x7f080033
int id beizi_appinfo_permission_textview 0x7f080034
int id beizi_appinfo_privacy_below_line 0x7f080035
int id beizi_appinfo_privacy_layout 0x7f080036
int id beizi_appinfo_privacy_textview 0x7f080037
int id beizi_close 0x7f080038
int id beizi_close_view 0x7f080039
int id beizi_dislike_item_multi_one_title 0x7f08003a
int id beizi_dislike_item_multi_two_recycleview 0x7f08003b
int id beizi_dislike_item_multi_two_recycleview_item 0x7f08003c
int id beizi_dislike_item_multi_two_title 0x7f08003d
int id beizi_dislike_reasons_list_recycleview 0x7f08003e
int id beizi_download_appinfo_back 0x7f08003f
int id beizi_download_appinfo_divider_tablayout 0x7f080040
int id beizi_download_appinfo_intro_content_scrollview 0x7f080041
int id beizi_download_appinfo_intro_content_textview 0x7f080042
int id beizi_download_appinfo_intro_content_webview 0x7f080043
int id beizi_download_appinfo_persmission_content_scrollview 0x7f080044
int id beizi_download_appinfo_persmission_content_textview 0x7f080045
int id beizi_download_appinfo_persmission_content_webview 0x7f080046
int id beizi_download_appinfo_privacy_content_scrollview 0x7f080047
int id beizi_download_appinfo_privacy_content_textview 0x7f080048
int id beizi_download_appinfo_privacy_content_webview 0x7f080049
int id beizi_download_appinfo_tablayout 0x7f08004a
int id beizi_download_appinfo_title 0x7f08004b
int id beizi_download_dialog_close_iv 0x7f08004c
int id beizi_download_dialog_container_rl 0x7f08004d
int id beizi_download_dialog_developer_tv 0x7f08004e
int id beizi_download_dialog_download_container_ll 0x7f08004f
int id beizi_download_dialog_download_ll 0x7f080050
int id beizi_download_dialog_expand_lv 0x7f080051
int id beizi_download_dialog_icon_iv 0x7f080052
int id beizi_download_dialog_market_cancel_tv 0x7f080053
int id beizi_download_dialog_market_confirm_tv 0x7f080054
int id beizi_download_dialog_market_container_ll 0x7f080055
int id beizi_download_dialog_name_tv 0x7f080056
int id beizi_download_dialog_version_tv 0x7f080057
int id beizi_interstitial_ad_app_download_info_tv 0x7f080058
int id beizi_interstitial_ad_app_icon_iv 0x7f080059
int id beizi_interstitial_ad_close_container_rl 0x7f08005a
int id beizi_interstitial_ad_close_iv 0x7f08005b
int id beizi_interstitial_ad_close_text_container_ll 0x7f08005c
int id beizi_interstitial_ad_complain_tv 0x7f08005d
int id beizi_interstitial_ad_container_ll 0x7f08005e
int id beizi_interstitial_ad_content_rl 0x7f08005f
int id beizi_interstitial_ad_countdown_tv 0x7f080060
int id beizi_interstitial_ad_divide_view 0x7f080061
int id beizi_interstitial_ad_img_iv 0x7f080062
int id beizi_interstitial_ad_interaction_container_landscape_rl 0x7f080063
int id beizi_interstitial_ad_interaction_container_portrait_rl 0x7f080064
int id beizi_interstitial_ad_logo_container_fl 0x7f080065
int id beizi_interstitial_ad_material_container_rl 0x7f080066
int id beizi_interstitial_ad_subtitle_tv 0x7f080067
int id beizi_interstitial_ad_title_container_ll 0x7f080068
int id beizi_interstitial_ad_title_divider_view 0x7f080069
int id beizi_interstitial_ad_title_tv 0x7f08006a
int id beizi_interstitial_ad_video_replay_container_rl 0x7f08006b
int id beizi_interstitial_ad_video_replay_iv 0x7f08006c
int id beizi_interstitial_ad_video_vv 0x7f08006d
int id beizi_interstitial_ad_voice_iv 0x7f08006e
int id beizi_material_design 0x7f08006f
int id beizi_media_view 0x7f080070
int id beizi_reward_video_ad_app_download_info_tv 0x7f080071
int id beizi_reward_video_ad_app_icon_iv 0x7f080072
int id beizi_reward_video_ad_app_interaction_container_ll 0x7f080073
int id beizi_reward_video_ad_app_subtitle_tv 0x7f080074
int id beizi_reward_video_ad_app_title_tv 0x7f080075
int id beizi_reward_video_ad_container_rl 0x7f080076
int id beizi_reward_video_ad_detainment_app_icon_iv 0x7f080077
int id beizi_reward_video_ad_detainment_app_subtitle_tv 0x7f080078
int id beizi_reward_video_ad_detainment_app_title_tv 0x7f080079
int id beizi_reward_video_ad_detainment_container_rl 0x7f08007a
int id beizi_reward_video_ad_detainment_image_iv 0x7f08007b
int id beizi_reward_video_ad_detainment_interaction_container_ll 0x7f08007c
int id beizi_reward_video_ad_detainment_interaction_iv 0x7f08007d
int id beizi_reward_video_ad_detainment_interaction_title_tv 0x7f08007e
int id beizi_reward_video_ad_detainment_logo_container_fl 0x7f08007f
int id beizi_reward_video_ad_get_rewards_close_tv 0x7f080080
int id beizi_reward_video_ad_get_rewards_container_ll 0x7f080081
int id beizi_reward_video_ad_get_rewards_countdown_tv 0x7f080082
int id beizi_reward_video_ad_gift_iv 0x7f080083
int id beizi_reward_video_ad_interaction_container_ll 0x7f080084
int id beizi_reward_video_ad_interaction_iv 0x7f080085
int id beizi_reward_video_ad_interaction_title_tv 0x7f080086
int id beizi_reward_video_ad_logo_container_fl 0x7f080087
int id beizi_reward_video_ad_video_vv 0x7f080088
int id beizi_reward_video_ad_voice_iv 0x7f080089
int id beizi_reward_video_ad_voice_outline_view 0x7f08008a
int id beizi_reward_video_cancel_privilege_dialog_tv 0x7f08008b
int id beizi_reward_video_complaint_tv 0x7f08008c
int id beizi_reward_video_privilege_dialog_app_icon_iv 0x7f08008d
int id beizi_reward_video_privilege_dialog_app_info_ll 0x7f08008e
int id beizi_reward_video_privilege_dialog_app_subtitle_tv 0x7f08008f
int id beizi_reward_video_privilege_dialog_app_title_tv 0x7f080090
int id beizi_reward_video_privilege_dialog_container_ll 0x7f080091
int id beizi_reward_video_privilege_dialog_content_container_ll 0x7f080092
int id beizi_reward_video_privilege_dialog_countdown_tv 0x7f080093
int id beizi_reward_video_privilege_dialog_in_background_time_tv 0x7f080094
int id beizi_reward_video_privilege_dialog_interaction_container_ll 0x7f080095
int id beizi_reward_video_privilege_dialog_interaction_iv 0x7f080096
int id beizi_reward_video_privilege_dialog_interaction_title_tv 0x7f080097
int id beizi_reward_video_privilege_dialog_tip_sub_title_tv 0x7f080098
int id beizi_reward_video_progress_bar 0x7f080099
int id beizi_reward_video_quit_dialog_close_ad_tv 0x7f08009a
int id beizi_reward_video_quit_dialog_container_rl 0x7f08009b
int id beizi_reward_video_quit_dialog_content_container_ll 0x7f08009c
int id beizi_reward_video_quit_dialog_continue_play_tv 0x7f08009d
int id beizi_reward_video_quit_dialog_get_reward_tv 0x7f08009e
int id beizi_right_view 0x7f08009f
int id beizi_root_container 0x7f0800a0
int id beizi_twist_describe_text 0x7f0800a1
int id beizi_twist_go_imageview 0x7f0800a2
int id beizi_twist_right_first_image 0x7f0800a3
int id beizi_twist_right_second_image 0x7f0800a4
int id beizi_twist_right_third_image 0x7f0800a5
int id beizi_twist_right_total_layout 0x7f0800a6
int id beizi_twist_shake_total_layout 0x7f0800a7
int id beizi_twist_title_text 0x7f0800a8
int id beizi_twist_top_view 0x7f0800a9
int id beizi_twist_total_layout 0x7f0800aa
int id beizi_wechat_design 0x7f0800ab
int id blocking 0x7f0800ac
int id bold 0x7f0800ad
int id bottom 0x7f0800ae
int id btn_action 0x7f0800af
int id btn_download 0x7f0800b0
int id btn_gm_banner_act 0x7f0800b1
int id btn_ks_banner_act 0x7f0800b2
int id btn_sg_banner_act 0x7f0800b3
int id btn_sigmob_native_act 0x7f0800b4
int id buttonPanel 0x7f0800b5
int id bz_eav_container_ll 0x7f0800b6
int id bz_eav_img_container_rl 0x7f0800b7
int id bz_eav_img_iv 0x7f0800b8
int id bz_eav_sav_iv 0x7f0800b9
int id bz_eav_subtitle_tv 0x7f0800ba
int id bz_eav_title_tv 0x7f0800bb
int id cancel_action 0x7f0800bc
int id cancel_tv 0x7f0800bd
int id center 0x7f0800be
int id centerBottom 0x7f0800bf
int id centerBottomCrop 0x7f0800c0
int id centerCrop 0x7f0800c1
int id centerInside 0x7f0800c2
int id centerTop 0x7f0800c3
int id centerTopCrop 0x7f0800c4
int id center_horizontal 0x7f0800c5
int id center_vertical 0x7f0800c6
int id chains 0x7f0800c7
int id checkbox 0x7f0800c8
int id choice1Button 0x7f0800c9
int id choice2Button 0x7f0800ca
int id choice3Button 0x7f0800cb
int id choice4Button 0x7f0800cc
int id choicesLayout 0x7f0800cd
int id chronometer 0x7f0800ce
int id clamp 0x7f0800cf
int id click_container 0x7f0800d0
int id clip_horizontal 0x7f0800d1
int id clip_vertical 0x7f0800d2
int id close_iv 0x7f0800d3
int id collapseActionView 0x7f0800d4
int id complaint_dialog_close_view 0x7f0800d5
int id complaint_input_other_divider 0x7f0800d6
int id complaint_input_other_edittext 0x7f0800d7
int id complaint_normal_ui 0x7f0800d8
int id complaint_other_suggest_layout 0x7f0800d9
int id complaint_other_suggest_number_textview 0x7f0800da
int id complaint_other_suggest_submit 0x7f0800db
int id complaint_other_suggest_title 0x7f0800dc
int id complaint_other_suggest_view 0x7f0800dd
int id complaint_reason_item_divider 0x7f0800de
int id confirm_tv 0x7f0800df
int id container 0x7f0800e0
int id content 0x7f0800e1
int id contentPanel 0x7f0800e2
int id coordinator 0x7f0800e3
int id countdown_view 0x7f0800e4
int id cpv 0x7f0800e5
int id custom 0x7f0800e6
int id customPanel 0x7f0800e7
int id dash_line 0x7f0800e8
int id decor_content_parent 0x7f0800e9
int id default_activity_button 0x7f0800ea
int id delete_tv 0x7f0800eb
int id design_bottom_sheet 0x7f0800ec
int id design_menu_item_action_area 0x7f0800ed
int id design_menu_item_action_area_stub 0x7f0800ee
int id design_menu_item_text 0x7f0800ef
int id design_navigation_view 0x7f0800f0
int id details 0x7f0800f1
int id dialog_root 0x7f0800f2
int id dialog_text 0x7f0800f3
int id dimensions 0x7f0800f4
int id direct 0x7f0800f5
int id disableHome 0x7f0800f6
int id disagreeButton 0x7f0800f7
int id dislike_item_multi_one_title 0x7f0800f8
int id dislike_reasons_list_recycleview 0x7f0800f9
int id down 0x7f0800fa
int id download_confirm_close 0x7f0800fb
int id download_confirm_confirm 0x7f0800fc
int id download_confirm_content 0x7f0800fd
int id download_confirm_holder 0x7f0800fe
int id download_confirm_progress_bar 0x7f0800ff
int id download_confirm_reload_button 0x7f080100
int id download_confirm_root 0x7f080101
int id edit_query 0x7f080102
int id end 0x7f080103
int id endInside 0x7f080104
int id end_padder 0x7f080105
int id enterAlways 0x7f080106
int id enterAlwaysCollapsed 0x7f080107
int id exitGameButton 0x7f080108
int id exitUntilCollapsed 0x7f080109
int id expand_activities_button 0x7f08010a
int id expanded_menu 0x7f08010b
int id fade 0x7f08010c
int id fill 0x7f08010d
int id fill_horizontal 0x7f08010e
int id fill_vertical 0x7f08010f
int id filled 0x7f080110
int id firewoodPlusButton 0x7f080111
int id firewoodTextView 0x7f080112
int id fitCenter 0x7f080113
int id fitEnd 0x7f080114
int id fitStart 0x7f080115
int id fitXY 0x7f080116
int id fixed 0x7f080117
int id fl_bz_banner_video 0x7f080118
int id fl_container 0x7f080119
int id fl_container_mask 0x7f08011a
int id fl_event_container 0x7f08011b
int id fl_gm_banner_video 0x7f08011c
int id fl_gm_media 0x7f08011d
int id fl_img_container 0x7f08011e
int id fl_ks_banner_video 0x7f08011f
int id fl_material 0x7f080120
int id fl_media 0x7f080121
int id fl_root 0x7f080122
int id fl_sg_banner_video 0x7f080123
int id foodPlusButton 0x7f080124
int id foodTextView 0x7f080125
int id forever 0x7f080126
int id ghost_view 0x7f080127
int id gone 0x7f080128
int id group_divider 0x7f080129
int id groups 0x7f08012a
int id hand 0x7f08012b
int id home 0x7f08012c
int id homeAsUp 0x7f08012d
int id horizontal 0x7f08012e
int id icon 0x7f08012f
int id icon_group 0x7f080130
int id ifRoom 0x7f080131
int id image 0x7f080132
int id info 0x7f080133
int id invisible 0x7f080134
int id italic 0x7f080135
int id item_touch_helper_previous_elevation 0x7f080136
int id iv_ad_logo 0x7f080137
int id iv_ad_source_img 0x7f080138
int id iv_app_icon 0x7f080139
int id iv_back 0x7f08013a
int id iv_bz_banner_act 0x7f08013b
int id iv_bz_banner_close 0x7f08013c
int id iv_close 0x7f08013d
int id iv_cover_image 0x7f08013e
int id iv_cover_img 0x7f08013f
int id iv_detail_back 0x7f080140
int id iv_gm_banner_close 0x7f080141
int id iv_gm_banner_img 0x7f080142
int id iv_icon 0x7f080143
int id iv_image 0x7f080144
int id iv_image_blur 0x7f080145
int id iv_imageview 0x7f080146
int id iv_ks_banner_close 0x7f080147
int id iv_ks_banner_img 0x7f080148
int id iv_privacy_back 0x7f080149
int id iv_replay 0x7f08014a
int id iv_sg_banner_close 0x7f08014b
int id iv_sg_banner_img 0x7f08014c
int id iv_shake 0x7f08014d
int id iv_sigmob_native_close 0x7f08014e
int id iv_sigmob_native_icon 0x7f08014f
int id iv_sigmob_native_video 0x7f080150
int id iv_slide_down_arrow 0x7f080151
int id iv_voice 0x7f080152
int id jad_logo 0x7f080153
int id jad_splash_container 0x7f080154
int id jad_splash_image 0x7f080155
int id jad_splash_skip_btn 0x7f080156
int id ksad_activity_apk_info_area_native 0x7f080157
int id ksad_ad_biserial_info_container 0x7f080158
int id ksad_ad_btn_sub_title 0x7f080159
int id ksad_ad_btn_title 0x7f08015a
int id ksad_ad_cover 0x7f08015b
int id ksad_ad_desc 0x7f08015c
int id ksad_ad_desc_layout 0x7f08015d
int id ksad_ad_developer_text 0x7f08015e
int id ksad_ad_dislike 0x7f08015f
int id ksad_ad_dislike_logo 0x7f080160
int id ksad_ad_download_container 0x7f080161
int id ksad_ad_endcard_appdesc 0x7f080162
int id ksad_ad_endcard_appname 0x7f080163
int id ksad_ad_endcard_appversion 0x7f080164
int id ksad_ad_endcard_close_root 0x7f080165
int id ksad_ad_endcard_icon 0x7f080166
int id ksad_ad_endcard_line 0x7f080167
int id ksad_ad_endcard_logo 0x7f080168
int id ksad_ad_endcard_second 0x7f080169
int id ksad_ad_endcard_title_view 0x7f08016a
int id ksad_ad_h5_container 0x7f08016b
int id ksad_ad_image 0x7f08016c
int id ksad_ad_image_left 0x7f08016d
int id ksad_ad_image_mid 0x7f08016e
int id ksad_ad_image_right 0x7f08016f
int id ksad_ad_info 0x7f080170
int id ksad_ad_interstitial_logo 0x7f080171
int id ksad_ad_label_play_bar 0x7f080172
int id ksad_ad_land_page_native 0x7f080173
int id ksad_ad_light_convert_btn 0x7f080174
int id ksad_ad_normal_container 0x7f080175
int id ksad_ad_normal_convert_btn 0x7f080176
int id ksad_ad_normal_des 0x7f080177
int id ksad_ad_normal_logo 0x7f080178
int id ksad_ad_normal_title 0x7f080179
int id ksad_ad_novel_container 0x7f08017a
int id ksad_ad_title 0x7f08017b
int id ksad_app_ad_desc 0x7f08017c
int id ksad_app_container 0x7f08017d
int id ksad_app_desc 0x7f08017e
int id ksad_app_download 0x7f08017f
int id ksad_app_download_btn 0x7f080180
int id ksad_app_download_count 0x7f080181
int id ksad_app_icon 0x7f080182
int id ksad_app_introduce 0x7f080183
int id ksad_app_name 0x7f080184
int id ksad_app_score 0x7f080185
int id ksad_app_title 0x7f080186
int id ksad_author_animator 0x7f080187
int id ksad_author_animator2 0x7f080188
int id ksad_author_arrow_down 0x7f080189
int id ksad_author_btn_follow 0x7f08018a
int id ksad_author_icon 0x7f08018b
int id ksad_author_icon_frame 0x7f08018c
int id ksad_author_icon_layout 0x7f08018d
int id ksad_author_icon_outer 0x7f08018e
int id ksad_author_name_txt 0x7f08018f
int id ksad_author_name_txt_landscape 0x7f080190
int id ksad_auto_close_btn 0x7f080191
int id ksad_auto_close_text 0x7f080192
int id ksad_back_icon 0x7f080193
int id ksad_banner_base_content 0x7f080194
int id ksad_banner_button_base 0x7f080195
int id ksad_banner_item_button 0x7f080196
int id ksad_banner_item_close 0x7f080197
int id ksad_banner_item_content 0x7f080198
int id ksad_banner_item_des 0x7f080199
int id ksad_banner_item_image 0x7f08019a
int id ksad_banner_item_image_bg 0x7f08019b
int id ksad_banner_item_info 0x7f08019c
int id ksad_banner_item_title 0x7f08019d
int id ksad_banner_logo 0x7f08019e
int id ksad_blur_end_cover 0x7f08019f
int id ksad_blur_video_cover 0x7f0801a0
int id ksad_card_ad_desc 0x7f0801a1
int id ksad_card_app_close 0x7f0801a2
int id ksad_card_app_container 0x7f0801a3
int id ksad_card_app_desc 0x7f0801a4
int id ksad_card_app_download_btn 0x7f0801a5
int id ksad_card_app_download_count 0x7f0801a6
int id ksad_card_app_icon 0x7f0801a7
int id ksad_card_app_name 0x7f0801a8
int id ksad_card_app_score 0x7f0801a9
int id ksad_card_app_score_container 0x7f0801aa
int id ksad_card_close 0x7f0801ab
int id ksad_card_h5_container 0x7f0801ac
int id ksad_card_h5_open_btn 0x7f0801ad
int id ksad_card_logo 0x7f0801ae
int id ksad_card_tips_root 0x7f0801af
int id ksad_card_tips_title 0x7f0801b0
int id ksad_card_tips_view 0x7f0801b1
int id ksad_center 0x7f0801b2
int id ksad_click_mask 0x7f0801b3
int id ksad_close_btn 0x7f0801b4
int id ksad_common_app_action 0x7f0801b5
int id ksad_common_app_card_land_stub 0x7f0801b6
int id ksad_common_app_card_root 0x7f0801b7
int id ksad_common_app_card_stub 0x7f0801b8
int id ksad_common_app_desc 0x7f0801b9
int id ksad_common_app_desc2 0x7f0801ba
int id ksad_common_app_icon 0x7f0801bb
int id ksad_common_app_install_container 0x7f0801bc
int id ksad_common_app_logo 0x7f0801bd
int id ksad_common_app_name 0x7f0801be
int id ksad_common_app_tags 0x7f0801bf
int id ksad_compliance_left_view 0x7f0801c0
int id ksad_compliance_right_view 0x7f0801c1
int id ksad_compliance_splash_endcard 0x7f0801c2
int id ksad_compliance_view 0x7f0801c3
int id ksad_container 0x7f0801c4
int id ksad_continue_btn 0x7f0801c5
int id ksad_coupon_dialog_bg 0x7f0801c6
int id ksad_coupon_dialog_btn_action 0x7f0801c7
int id ksad_coupon_dialog_card 0x7f0801c8
int id ksad_coupon_dialog_content 0x7f0801c9
int id ksad_coupon_dialog_desc 0x7f0801ca
int id ksad_coupon_dialog_title 0x7f0801cb
int id ksad_data_flow_container 0x7f0801cc
int id ksad_data_flow_play_btn 0x7f0801cd
int id ksad_data_flow_play_tip 0x7f0801ce
int id ksad_detail_call_btn 0x7f0801cf
int id ksad_detail_close_btn 0x7f0801d0
int id ksad_detail_reward_deep_task_view 0x7f0801d1
int id ksad_detail_reward_deep_task_view_playend 0x7f0801d2
int id ksad_detail_reward_icon 0x7f0801d3
int id ksad_download_bar 0x7f0801d4
int id ksad_download_bar_cover 0x7f0801d5
int id ksad_download_container 0x7f0801d6
int id ksad_download_control_bg_image 0x7f0801d7
int id ksad_download_control_btn 0x7f0801d8
int id ksad_download_control_view 0x7f0801d9
int id ksad_download_icon 0x7f0801da
int id ksad_download_install 0x7f0801db
int id ksad_download_name 0x7f0801dc
int id ksad_download_percent_num 0x7f0801dd
int id ksad_download_progress 0x7f0801de
int id ksad_download_progress_cover 0x7f0801df
int id ksad_download_size 0x7f0801e0
int id ksad_download_status 0x7f0801e1
int id ksad_download_tips_web_card_webView 0x7f0801e2
int id ksad_download_title_view 0x7f0801e3
int id ksad_draw_author_icon 0x7f0801e4
int id ksad_draw_h5_logo 0x7f0801e5
int id ksad_draw_live_base_stub 0x7f0801e6
int id ksad_draw_live_card_bg 0x7f0801e7
int id ksad_draw_live_end 0x7f0801e8
int id ksad_draw_live_end_app_name 0x7f0801e9
int id ksad_draw_live_end_text 0x7f0801ea
int id ksad_draw_live_frame_bg 0x7f0801eb
int id ksad_draw_live_kwai_logo 0x7f0801ec
int id ksad_draw_live_shop_stub 0x7f0801ed
int id ksad_draw_origin_live_base1 0x7f0801ee
int id ksad_draw_origin_live_relative 0x7f0801ef
int id ksad_draw_origin_live_root 0x7f0801f0
int id ksad_draw_tailframe_logo 0x7f0801f1
int id ksad_draw_tk_container 0x7f0801f2
int id ksad_draw_video_container 0x7f0801f3
int id ksad_end_close_btn 0x7f0801f4
int id ksad_end_left_call_btn 0x7f0801f5
int id ksad_end_reward_icon 0x7f0801f6
int id ksad_end_reward_icon_layout 0x7f0801f7
int id ksad_end_right_call_btn 0x7f0801f8
int id ksad_exit_intercept_content 0x7f0801f9
int id ksad_exit_intercept_content_layout 0x7f0801fa
int id ksad_exit_intercept_dialog_layout 0x7f0801fb
int id ksad_fans_count 0x7f0801fc
int id ksad_fans_hot_icon 0x7f0801fd
int id ksad_feed_ad_label 0x7f0801fe
int id ksad_feed_ad_video_container 0x7f0801ff
int id ksad_feed_biserial_image 0x7f080200
int id ksad_feed_biserial_video 0x7f080201
int id ksad_feed_bottombar_container 0x7f080202
int id ksad_feed_logo 0x7f080203
int id ksad_feed_novel_image 0x7f080204
int id ksad_feed_novel_video 0x7f080205
int id ksad_feed_shake_bg 0x7f080206
int id ksad_feed_shake_icon 0x7f080207
int id ksad_feed_video_container 0x7f080208
int id ksad_foreground_cover 0x7f080209
int id ksad_h5_ad_desc 0x7f08020a
int id ksad_h5_container 0x7f08020b
int id ksad_h5_desc 0x7f08020c
int id ksad_h5_open 0x7f08020d
int id ksad_h5_open_btn 0x7f08020e
int id ksad_h5_open_cover 0x7f08020f
int id ksad_hand 0x7f080210
int id ksad_hand_slide_hand 0x7f080211
int id ksad_hand_slide_tail 0x7f080212
int id ksad_image_container 0x7f080213
int id ksad_info_container 0x7f080214
int id ksad_inside_circle 0x7f080215
int id ksad_install_cancel 0x7f080216
int id ksad_install_tips_close 0x7f080217
int id ksad_install_tips_content 0x7f080218
int id ksad_install_tips_icon 0x7f080219
int id ksad_install_tips_install 0x7f08021a
int id ksad_install_tv 0x7f08021b
int id ksad_interstitial_aggregate_convert 0x7f08021c
int id ksad_interstitial_aggregate_cut 0x7f08021d
int id ksad_interstitial_aggregate_refresh 0x7f08021e
int id ksad_interstitial_auto_close 0x7f08021f
int id ksad_interstitial_close_outer 0x7f080220
int id ksad_interstitial_count_down 0x7f080221
int id ksad_interstitial_desc 0x7f080222
int id ksad_interstitial_download_btn 0x7f080223
int id ksad_interstitial_full_bg 0x7f080224
int id ksad_interstitial_intercept_app_icon 0x7f080225
int id ksad_interstitial_intercept_app_title 0x7f080226
int id ksad_interstitial_intercept_dialog_btn_continue 0x7f080227
int id ksad_interstitial_intercept_dialog_btn_deny 0x7f080228
int id ksad_interstitial_intercept_dialog_desc 0x7f080229
int id ksad_interstitial_intercept_dialog_detail 0x7f08022a
int id ksad_interstitial_logo 0x7f08022b
int id ksad_interstitial_mute 0x7f08022c
int id ksad_interstitial_name 0x7f08022d
int id ksad_interstitial_native 0x7f08022e
int id ksad_interstitial_native_container 0x7f08022f
int id ksad_interstitial_native_video_container 0x7f080230
int id ksad_interstitial_play_end 0x7f080231
int id ksad_interstitial_playable_timer 0x7f080232
int id ksad_interstitial_playing 0x7f080233
int id ksad_interstitial_tail_frame 0x7f080234
int id ksad_interstitial_toast_container 0x7f080235
int id ksad_interstitial_video_blur 0x7f080236
int id ksad_js_container 0x7f080237
int id ksad_js_full_card 0x7f080238
int id ksad_js_interact 0x7f080239
int id ksad_js_live_card 0x7f08023a
int id ksad_js_reward_card 0x7f08023b
int id ksad_js_reward_image_card 0x7f08023c
int id ksad_js_task 0x7f08023d
int id ksad_js_tk_back_dialog 0x7f08023e
int id ksad_js_topfloor 0x7f08023f
int id ksad_kwad_titlebar 0x7f080240
int id ksad_kwad_titlebar_title 0x7f080241
int id ksad_kwad_web_navi_back 0x7f080242
int id ksad_kwad_web_navi_close 0x7f080243
int id ksad_kwad_web_title_bar 0x7f080244
int id ksad_land_page_logo 0x7f080245
int id ksad_land_page_root 0x7f080246
int id ksad_landing_page_container 0x7f080247
int id ksad_landing_page_root 0x7f080248
int id ksad_landing_page_webview 0x7f080249
int id ksad_live_actionbar_btn 0x7f08024a
int id ksad_live_author_icon 0x7f08024b
int id ksad_live_bg_img 0x7f08024c
int id ksad_live_container 0x7f08024d
int id ksad_live_end_bg_mantle 0x7f08024e
int id ksad_live_end_bottom_action_btn 0x7f08024f
int id ksad_live_end_bottom_action_btn_landscape 0x7f080250
int id ksad_live_end_bottom_des_btn 0x7f080251
int id ksad_live_end_bottom_des_btn_landscape 0x7f080252
int id ksad_live_end_bottom_divider 0x7f080253
int id ksad_live_end_bottom_divider_landscape 0x7f080254
int id ksad_live_end_bottom_title 0x7f080255
int id ksad_live_end_bottom_title_landscape 0x7f080256
int id ksad_live_end_detail_layout 0x7f080257
int id ksad_live_end_detail_layout_landscape 0x7f080258
int id ksad_live_end_detail_like_person_count 0x7f080259
int id ksad_live_end_detail_like_person_count_landscape 0x7f08025a
int id ksad_live_end_detail_watch_person_count 0x7f08025b
int id ksad_live_end_detail_watch_person_count_landscape 0x7f08025c
int id ksad_live_end_detail_watch_time 0x7f08025d
int id ksad_live_end_detail_watch_time_landscape 0x7f08025e
int id ksad_live_end_page_author_icon 0x7f08025f
int id ksad_live_end_page_author_icon_landscape 0x7f080260
int id ksad_live_end_page_bg 0x7f080261
int id ksad_live_end_page_bg_landscape 0x7f080262
int id ksad_live_end_page_close_btn 0x7f080263
int id ksad_live_end_page_layout_root 0x7f080264
int id ksad_live_end_page_layout_root_landscape 0x7f080265
int id ksad_live_end_text 0x7f080266
int id ksad_live_end_top_divider 0x7f080267
int id ksad_live_end_top_divider_landscape 0x7f080268
int id ksad_live_end_txt 0x7f080269
int id ksad_live_end_txt_landscape 0x7f08026a
int id ksad_live_subscribe_dialog_btn_close 0x7f08026b
int id ksad_live_subscribe_dialog_btn_continue 0x7f08026c
int id ksad_live_subscribe_dialog_btn_deny 0x7f08026d
int id ksad_live_subscribe_dialog_content 0x7f08026e
int id ksad_live_subscribe_dialog_content_txt 0x7f08026f
int id ksad_live_subscribe_dialog_icon 0x7f080270
int id ksad_live_subscribe_dialog_title 0x7f080271
int id ksad_live_subscribe_dialog_vide_detail 0x7f080272
int id ksad_live_subscribe_end_btn_close 0x7f080273
int id ksad_live_subscribe_end_btn_subscribe 0x7f080274
int id ksad_live_subscribe_end_icon 0x7f080275
int id ksad_live_subscribe_end_root 0x7f080276
int id ksad_live_subscribe_end_start_time 0x7f080277
int id ksad_live_subscribe_end_subscribe_count 0x7f080278
int id ksad_live_subscribe_end_title 0x7f080279
int id ksad_live_video_container 0x7f08027a
int id ksad_logo_container 0x7f08027b
int id ksad_logo_icon 0x7f08027c
int id ksad_logo_text 0x7f08027d
int id ksad_manual_tips_view 0x7f08027e
int id ksad_message_toast_txt 0x7f08027f
int id ksad_middle_end_card 0x7f080280
int id ksad_middle_end_card_webview_container 0x7f080281
int id ksad_multi_ad_container 0x7f080282
int id ksad_multi_ad_indicator 0x7f080283
int id ksad_native_container_stub 0x7f080284
int id ksad_no_title_common_content_layout 0x7f080285
int id ksad_no_title_common_content_text 0x7f080286
int id ksad_no_title_common_dialog_layout 0x7f080287
int id ksad_no_title_common_negative_btn 0x7f080288
int id ksad_no_title_common_positive_btn 0x7f080289
int id ksad_normal_text 0x7f08028a
int id ksad_origin_live_bottom_layout 0x7f08028b
int id ksad_origin_live_bottom_text 0x7f08028c
int id ksad_outside_circle 0x7f08028d
int id ksad_play_detail_top_toolbar 0x7f08028e
int id ksad_play_end_top_toolbar 0x7f08028f
int id ksad_play_end_web_card_container 0x7f080290
int id ksad_play_right_area 0x7f080291
int id ksad_play_right_area_bg_img 0x7f080292
int id ksad_play_right_area_container 0x7f080293
int id ksad_play_web_card_webView 0x7f080294
int id ksad_playabale_end_blur_img 0x7f080295
int id ksad_playabale_end_btn_action 0x7f080296
int id ksad_playabale_end_btn_container 0x7f080297
int id ksad_playabale_end_card 0x7f080298
int id ksad_playabale_end_content 0x7f080299
int id ksad_playabale_end_desc 0x7f08029a
int id ksad_playabale_end_icon 0x7f08029b
int id ksad_playabale_end_title 0x7f08029c
int id ksad_playabale_logo 0x7f08029d
int id ksad_playabale_middle_divider 0x7f08029e
int id ksad_playabale_try 0x7f08029f
int id ksad_playabel_pre_tips_icon 0x7f0802a0
int id ksad_playabel_pre_tips_text 0x7f0802a1
int id ksad_playable_activity_root 0x7f0802a2
int id ksad_playable_end_stub 0x7f0802a3
int id ksad_playable_end_tags 0x7f0802a4
int id ksad_playable_pre_tips_root 0x7f0802a5
int id ksad_playable_pre_tips_stub 0x7f0802a6
int id ksad_playable_webview 0x7f0802a7
int id ksad_playend_native_container 0x7f0802a8
int id ksad_playend_native_jinniu 0x7f0802a9
int id ksad_pre_form_card 0x7f0802aa
int id ksad_preload_left_container 0x7f0802ab
int id ksad_preload_right_container 0x7f0802ac
int id ksad_preview_topbar_close 0x7f0802ad
int id ksad_preview_topbar_progress 0x7f0802ae
int id ksad_preview_topbar_reward_count 0x7f0802af
int id ksad_preview_topbar_reward_gift_icon 0x7f0802b0
int id ksad_preview_topbar_reward_tips 0x7f0802b1
int id ksad_preview_webview_container 0x7f0802b2
int id ksad_product_price 0x7f0802b3
int id ksad_progress_bar 0x7f0802b4
int id ksad_progress_bg 0x7f0802b5
int id ksad_push_ad_contaiber 0x7f0802b6
int id ksad_recycler_container 0x7f0802b7
int id ksad_recycler_view 0x7f0802b8
int id ksad_reward_apk_info_card_h5 0x7f0802b9
int id ksad_reward_apk_info_card_native_container 0x7f0802ba
int id ksad_reward_apk_info_card_root 0x7f0802bb
int id ksad_reward_apk_info_desc 0x7f0802bc
int id ksad_reward_apk_info_desc_2 0x7f0802bd
int id ksad_reward_apk_info_icon 0x7f0802be
int id ksad_reward_apk_info_install_action 0x7f0802bf
int id ksad_reward_apk_info_install_container 0x7f0802c0
int id ksad_reward_apk_info_install_start 0x7f0802c1
int id ksad_reward_apk_info_name 0x7f0802c2
int id ksad_reward_apk_info_score 0x7f0802c3
int id ksad_reward_apk_info_stub 0x7f0802c4
int id ksad_reward_apk_info_tags 0x7f0802c5
int id ksad_reward_app_download_btn 0x7f0802c6
int id ksad_reward_btn_for_live_cover 0x7f0802c7
int id ksad_reward_close_extend_dialog_btn_continue 0x7f0802c8
int id ksad_reward_close_extend_dialog_btn_deny 0x7f0802c9
int id ksad_reward_close_extend_dialog_gift 0x7f0802ca
int id ksad_reward_close_extend_dialog_play_time_tips 0x7f0802cb
int id ksad_reward_deep_task_count_down 0x7f0802cc
int id ksad_reward_deep_task_count_down_playend 0x7f0802cd
int id ksad_reward_deep_task_sound_switch 0x7f0802ce
int id ksad_reward_jinniu_btn_buy 0x7f0802cf
int id ksad_reward_jinniu_card 0x7f0802d0
int id ksad_reward_jinniu_coupon 0x7f0802d1
int id ksad_reward_jinniu_coupon_layout 0x7f0802d2
int id ksad_reward_jinniu_coupon_prefix 0x7f0802d3
int id ksad_reward_jinniu_desc 0x7f0802d4
int id ksad_reward_jinniu_dialog_btn_close 0x7f0802d5
int id ksad_reward_jinniu_dialog_btn_continue 0x7f0802d6
int id ksad_reward_jinniu_dialog_btn_deny 0x7f0802d7
int id ksad_reward_jinniu_dialog_desc 0x7f0802d8
int id ksad_reward_jinniu_dialog_detail 0x7f0802d9
int id ksad_reward_jinniu_dialog_icon 0x7f0802da
int id ksad_reward_jinniu_dialog_play_time_tips 0x7f0802db
int id ksad_reward_jinniu_dialog_title 0x7f0802dc
int id ksad_reward_jinniu_end_btn_buy 0x7f0802dd
int id ksad_reward_jinniu_end_btn_vide_detail 0x7f0802de
int id ksad_reward_jinniu_end_card 0x7f0802df
int id ksad_reward_jinniu_end_card_root 0x7f0802e0
int id ksad_reward_jinniu_end_desc 0x7f0802e1
int id ksad_reward_jinniu_end_icon 0x7f0802e2
int id ksad_reward_jinniu_end_price 0x7f0802e3
int id ksad_reward_jinniu_end_title 0x7f0802e4
int id ksad_reward_jinniu_icon 0x7f0802e5
int id ksad_reward_jinniu_light_sweep 0x7f0802e6
int id ksad_reward_jinniu_price 0x7f0802e7
int id ksad_reward_jinniu_price_layout 0x7f0802e8
int id ksad_reward_jinniu_right_label 0x7f0802e9
int id ksad_reward_jinniu_root 0x7f0802ea
int id ksad_reward_jinniu_text_area 0x7f0802eb
int id ksad_reward_jinniu_title 0x7f0802ec
int id ksad_reward_land_page_open_colon 0x7f0802ed
int id ksad_reward_land_page_open_desc 0x7f0802ee
int id ksad_reward_land_page_open_minute 0x7f0802ef
int id ksad_reward_land_page_open_second 0x7f0802f0
int id ksad_reward_land_page_open_tip 0x7f0802f1
int id ksad_reward_live_kwai_logo 0x7f0802f2
int id ksad_reward_live_subscribe_badge 0x7f0802f3
int id ksad_reward_live_subscribe_btn_follow 0x7f0802f4
int id ksad_reward_live_subscribe_count 0x7f0802f5
int id ksad_reward_live_subscribe_desc 0x7f0802f6
int id ksad_reward_live_subscribe_follower_area 0x7f0802f7
int id ksad_reward_live_subscribe_follower_icon1 0x7f0802f8
int id ksad_reward_live_subscribe_follower_icon2 0x7f0802f9
int id ksad_reward_live_subscribe_follower_icon3 0x7f0802fa
int id ksad_reward_live_subscribe_icon 0x7f0802fb
int id ksad_reward_live_subscribe_kwai_logo 0x7f0802fc
int id ksad_reward_live_subscribe_name 0x7f0802fd
int id ksad_reward_live_subscribe_right 0x7f0802fe
int id ksad_reward_live_subscribe_root 0x7f0802ff
int id ksad_reward_live_subscribe_stub 0x7f080300
int id ksad_reward_order_btn_buy 0x7f080301
int id ksad_reward_order_card 0x7f080302
int id ksad_reward_order_coupon 0x7f080303
int id ksad_reward_order_coupon_list 0x7f080304
int id ksad_reward_order_dialog_btn_close 0x7f080305
int id ksad_reward_order_dialog_btn_deny 0x7f080306
int id ksad_reward_order_dialog_btn_view_detail 0x7f080307
int id ksad_reward_order_dialog_desc 0x7f080308
int id ksad_reward_order_dialog_icon 0x7f080309
int id ksad_reward_order_end_btn_buy 0x7f08030a
int id ksad_reward_order_end_btn_close 0x7f08030b
int id ksad_reward_order_end_card 0x7f08030c
int id ksad_reward_order_end_card_root 0x7f08030d
int id ksad_reward_order_end_desc 0x7f08030e
int id ksad_reward_order_end_icon 0x7f08030f
int id ksad_reward_order_end_price 0x7f080310
int id ksad_reward_order_end_title 0x7f080311
int id ksad_reward_order_icon 0x7f080312
int id ksad_reward_order_kwai_logo 0x7f080313
int id ksad_reward_order_price 0x7f080314
int id ksad_reward_order_root 0x7f080315
int id ksad_reward_order_text_area 0x7f080316
int id ksad_reward_order_title 0x7f080317
int id ksad_reward_origin_live_base1 0x7f080318
int id ksad_reward_origin_live_base_stub 0x7f080319
int id ksad_reward_origin_live_end_page_stub 0x7f08031a
int id ksad_reward_origin_live_end_page_stub_landscape 0x7f08031b
int id ksad_reward_origin_live_relative 0x7f08031c
int id ksad_reward_origin_live_root 0x7f08031d
int id ksad_reward_origin_live_shop_stub 0x7f08031e
int id ksad_reward_play_layout 0x7f08031f
int id ksad_reward_preview_hand_slide 0x7f080320
int id ksad_reward_preview_hand_slide_container 0x7f080321
int id ksad_reward_preview_logo 0x7f080322
int id ksad_reward_preview_topbar 0x7f080323
int id ksad_reward_right_arrow 0x7f080324
int id ksad_reward_task_dialog_abandon 0x7f080325
int id ksad_reward_task_dialog_continue 0x7f080326
int id ksad_reward_task_dialog_icon 0x7f080327
int id ksad_reward_task_dialog_steps 0x7f080328
int id ksad_reward_task_dialog_title 0x7f080329
int id ksad_reward_task_step_item_icon 0x7f08032a
int id ksad_reward_task_step_item_icon_text 0x7f08032b
int id ksad_reward_task_step_item_text 0x7f08032c
int id ksad_reward_text_aera 0x7f08032d
int id ksad_reward_time_close_dialog_btn_continue 0x7f08032e
int id ksad_reward_time_close_dialog_btn_deny 0x7f08032f
int id ksad_reward_time_close_dialog_desc 0x7f080330
int id ksad_reward_time_close_dialog_detail 0x7f080331
int id ksad_reward_time_close_dialog_icon 0x7f080332
int id ksad_reward_time_close_dialog_play_time_tips 0x7f080333
int id ksad_reward_time_close_dialog_title 0x7f080334
int id ksad_right_area_webview 0x7f080335
int id ksad_right_area_webview_container 0x7f080336
int id ksad_right_close 0x7f080337
int id ksad_root_container 0x7f080338
int id ksad_root_live_container 0x7f080339
int id ksad_rotate_action 0x7f08033a
int id ksad_rotate_combo_action_text 0x7f08033b
int id ksad_rotate_combo_layout 0x7f08033c
int id ksad_rotate_combo_root 0x7f08033d
int id ksad_rotate_combo_rotate_view 0x7f08033e
int id ksad_rotate_combo_slide_action_text 0x7f08033f
int id ksad_rotate_combo_slide_arrow_bottom 0x7f080340
int id ksad_rotate_combo_slide_arrow_top 0x7f080341
int id ksad_rotate_combo_slide_round_img 0x7f080342
int id ksad_rotate_layout 0x7f080343
int id ksad_rotate_root 0x7f080344
int id ksad_rotate_text 0x7f080345
int id ksad_rotate_view 0x7f080346
int id ksad_score_fifth 0x7f080347
int id ksad_score_fourth 0x7f080348
int id ksad_second_confirm_cancle 0x7f080349
int id ksad_second_confirm_content_view 0x7f08034a
int id ksad_second_confirm_ensure 0x7f08034b
int id ksad_second_confirm_root_view 0x7f08034c
int id ksad_shake_action 0x7f08034d
int id ksad_shake_center_circle_area 0x7f08034e
int id ksad_shake_center_circle_area_bg 0x7f08034f
int id ksad_shake_center_icon 0x7f080350
int id ksad_shake_center_root 0x7f080351
int id ksad_shake_center_sub_title 0x7f080352
int id ksad_shake_center_title 0x7f080353
int id ksad_shake_combo_button_background 0x7f080354
int id ksad_shake_combo_button_spread 0x7f080355
int id ksad_shake_combo_layout 0x7f080356
int id ksad_shake_combo_root 0x7f080357
int id ksad_shake_combo_shake_icon 0x7f080358
int id ksad_shake_combo_shake_main_text 0x7f080359
int id ksad_shake_combo_slide_action_text 0x7f08035a
int id ksad_shake_combo_slide_arrow_bottom 0x7f08035b
int id ksad_shake_combo_slide_arrow_top 0x7f08035c
int id ksad_shake_combo_slide_popup_view 0x7f08035d
int id ksad_shake_combo_sub_text 0x7f08035e
int id ksad_shake_layout 0x7f08035f
int id ksad_shake_root 0x7f080360
int id ksad_shake_text 0x7f080361
int id ksad_shake_tips_label 0x7f080362
int id ksad_shake_view 0x7f080363
int id ksad_skip_icon 0x7f080364
int id ksad_skip_view_area 0x7f080365
int id ksad_skip_view_divider 0x7f080366
int id ksad_skip_view_skip 0x7f080367
int id ksad_skip_view_timer 0x7f080368
int id ksad_slide_combo_action_sub_text 0x7f080369
int id ksad_slide_combo_action_text 0x7f08036a
int id ksad_slide_combo_layout 0x7f08036b
int id ksad_slide_combo_root 0x7f08036c
int id ksad_slide_combo_round_bg 0x7f08036d
int id ksad_slide_combo_slide_hand 0x7f08036e
int id ksad_slide_layout 0x7f08036f
int id ksad_space 0x7f080370
int id ksad_splash_actionbar_full_screen 0x7f080371
int id ksad_splash_actionbar_native 0x7f080372
int id ksad_splash_actionbar_native_root 0x7f080373
int id ksad_splash_actionbar_native_stub 0x7f080374
int id ksad_splash_actionbar_text 0x7f080375
int id ksad_splash_background 0x7f080376
int id ksad_splash_circle_skip_left_view 0x7f080377
int id ksad_splash_circle_skip_right_view 0x7f080378
int id ksad_splash_default_desc 0x7f080379
int id ksad_splash_default_icon 0x7f08037a
int id ksad_splash_default_image_view 0x7f08037b
int id ksad_splash_default_image_view_container 0x7f08037c
int id ksad_splash_default_img 0x7f08037d
int id ksad_splash_default_tips 0x7f08037e
int id ksad_splash_default_title 0x7f08037f
int id ksad_splash_end_card_giftbox_view 0x7f080380
int id ksad_splash_end_card_native_bg 0x7f080381
int id ksad_splash_end_card_native_dialog_root 0x7f080382
int id ksad_splash_end_card_native_root 0x7f080383
int id ksad_splash_end_card_native_view 0x7f080384
int id ksad_splash_endcard_actionbar 0x7f080385
int id ksad_splash_endcard_close_img 0x7f080386
int id ksad_splash_endcard_view_stub 0x7f080387
int id ksad_splash_foreground 0x7f080388
int id ksad_splash_left_top_root 0x7f080389
int id ksad_splash_logo_container 0x7f08038a
int id ksad_splash_preload_left_tips 0x7f08038b
int id ksad_splash_preload_right_tips 0x7f08038c
int id ksad_splash_right_top_root 0x7f08038d
int id ksad_splash_root_container 0x7f08038e
int id ksad_splash_skip_left_view 0x7f08038f
int id ksad_splash_skip_right_view 0x7f080390
int id ksad_splash_slideTouchView 0x7f080391
int id ksad_splash_slideView 0x7f080392
int id ksad_splash_slide_actiontext 0x7f080393
int id ksad_splash_slide_title 0x7f080394
int id ksad_splash_slideview_root 0x7f080395
int id ksad_splash_sound 0x7f080396
int id ksad_splash_video_player 0x7f080397
int id ksad_splash_webview_container 0x7f080398
int id ksad_split_land_ad_feed_video 0x7f080399
int id ksad_split_mini_close_btn 0x7f08039a
int id ksad_split_texture 0x7f08039b
int id ksad_status_tv 0x7f08039c
int id ksad_tf_h5_ad_desc 0x7f08039d
int id ksad_tf_h5_open_btn 0x7f08039e
int id ksad_title 0x7f08039f
int id ksad_tk_dialog_container 0x7f0803a0
int id ksad_tk_root_container 0x7f0803a1
int id ksad_tk_view 0x7f0803a2
int id ksad_toast_view 0x7f0803a3
int id ksad_top_flag_layout 0x7f0803a4
int id ksad_top_layout 0x7f0803a5
int id ksad_top_left 0x7f0803a6
int id ksad_top_toolbar_close_tip 0x7f0803a7
int id ksad_total_count_down_text 0x7f0803a8
int id ksad_video_app_tail_frame 0x7f0803a9
int id ksad_video_blur_bg 0x7f0803aa
int id ksad_video_complete_app_container 0x7f0803ab
int id ksad_video_complete_app_icon 0x7f0803ac
int id ksad_video_complete_h5_container 0x7f0803ad
int id ksad_video_container 0x7f0803ae
int id ksad_video_control_button 0x7f0803af
int id ksad_video_control_container 0x7f0803b0
int id ksad_video_control_fullscreen 0x7f0803b1
int id ksad_video_control_fullscreen_container 0x7f0803b2
int id ksad_video_control_fullscreen_title 0x7f0803b3
int id ksad_video_control_play_button 0x7f0803b4
int id ksad_video_control_play_duration 0x7f0803b5
int id ksad_video_control_play_status 0x7f0803b6
int id ksad_video_control_play_total 0x7f0803b7
int id ksad_video_count_down 0x7f0803b8
int id ksad_video_cover 0x7f0803b9
int id ksad_video_cover_image 0x7f0803ba
int id ksad_video_error_container 0x7f0803bb
int id ksad_video_fail_tip 0x7f0803bc
int id ksad_video_first_frame 0x7f0803bd
int id ksad_video_first_frame_container 0x7f0803be
int id ksad_video_h5_tail_frame 0x7f0803bf
int id ksad_video_immerse_text 0x7f0803c0
int id ksad_video_immerse_text_container 0x7f0803c1
int id ksad_video_network_unavailable 0x7f0803c2
int id ksad_video_play_bar_app_landscape 0x7f0803c3
int id ksad_video_play_bar_app_portrait 0x7f0803c4
int id ksad_video_play_bar_h5 0x7f0803c5
int id ksad_video_player 0x7f0803c6
int id ksad_video_progress 0x7f0803c7
int id ksad_video_root_container 0x7f0803c8
int id ksad_video_sound_switch 0x7f0803c9
int id ksad_video_tail_frame 0x7f0803ca
int id ksad_video_tail_frame_container 0x7f0803cb
int id ksad_video_text_below 0x7f0803cc
int id ksad_video_text_below_action_bar 0x7f0803cd
int id ksad_video_text_below_action_icon 0x7f0803ce
int id ksad_video_text_below_action_icon_layout 0x7f0803cf
int id ksad_video_text_below_action_title 0x7f0803d0
int id ksad_video_tf_logo 0x7f0803d1
int id ksad_video_thumb_container 0x7f0803d2
int id ksad_video_thumb_image 0x7f0803d3
int id ksad_video_thumb_img 0x7f0803d4
int id ksad_video_thumb_left 0x7f0803d5
int id ksad_video_thumb_mid 0x7f0803d6
int id ksad_video_thumb_right 0x7f0803d7
int id ksad_video_webView 0x7f0803d8
int id ksad_video_webview 0x7f0803d9
int id ksad_web_bottom_card_webView 0x7f0803da
int id ksad_web_card_container 0x7f0803db
int id ksad_web_card_frame 0x7f0803dc
int id ksad_web_card_webView 0x7f0803dd
int id ksad_web_default_bottom_card_webView 0x7f0803de
int id ksad_web_download_container 0x7f0803df
int id ksad_web_download_progress 0x7f0803e0
int id ksad_web_exit_intercept_negative_btn 0x7f0803e1
int id ksad_web_exit_intercept_positive_btn 0x7f0803e2
int id ksad_web_reward_task_layout 0x7f0803e3
int id ksad_web_reward_task_text 0x7f0803e4
int id ksad_web_tip_bar 0x7f0803e5
int id ksad_web_tip_bar_textview 0x7f0803e6
int id ksad_web_tip_close_btn 0x7f0803e7
int id ksad_web_video_seek_bar 0x7f0803e8
int id kwad_actionbar_des_text 0x7f0803e9
int id kwad_actionbar_title 0x7f0803ea
int id labeled 0x7f0803eb
int id languageButton 0x7f0803ec
int id largeLabel 0x7f0803ed
int id left 0x7f0803ee
int id leftBottom 0x7f0803ef
int id leftBottomCrop 0x7f0803f0
int id leftCenter 0x7f0803f1
int id leftCenterCrop 0x7f0803f2
int id leftTop 0x7f0803f3
int id leftTopCrop 0x7f0803f4
int id line 0x7f0803f5
int id line1 0x7f0803f6
int id line3 0x7f0803f7
int id listMode 0x7f0803f8
int id list_item 0x7f0803f9
int id live_progress_cancel 0x7f0803fa
int id ll_ad_logo 0x7f0803fb
int id ll_app_info 0x7f0803fc
int id ll_container 0x7f0803fd
int id ll_countdown 0x7f0803fe
int id ll_download 0x7f0803ff
int id ll_permission 0x7f080400
int id ll_shake 0x7f080401
int id ll_title 0x7f080402
int id masked 0x7f080403
int id media_actions 0x7f080404
int id message 0x7f080405
int id message_tv 0x7f080406
int id middle 0x7f080407
int id mini 0x7f080408
int id mirror 0x7f080409
int id monospace 0x7f08040a
int id movein 0x7f08040b
int id mtrl_child_content_container 0x7f08040c
int id mtrl_internal_children_alpha_tag 0x7f08040d
int id multiply 0x7f08040e
int id musicToggleButton 0x7f08040f
int id native_ad_ltrg_ad_text_iv 0x7f080410
int id native_ad_ltrg_container_rl 0x7f080411
int id native_ad_ltrg_content_rl 0x7f080412
int id native_ad_ltrg_go_iv 0x7f080413
int id native_ad_ltrg_go_rl 0x7f080414
int id native_ad_ltrg_go_tv 0x7f080415
int id native_ad_ltrg_image_iv 0x7f080416
int id native_ad_ltrg_image_rl 0x7f080417
int id native_ad_ltrg_subtitle_rl 0x7f080418
int id native_ad_ltrg_subtitle_tv 0x7f080419
int id native_ad_ltrg_title_ll 0x7f08041a
int id native_ad_ltrg_title_tv 0x7f08041b
int id native_ad_tpbt_container_ll 0x7f08041c
int id native_ad_tpbt_content_ll 0x7f08041d
int id native_ad_tpbt_go_iv 0x7f08041e
int id native_ad_tpbt_go_rl 0x7f08041f
int id native_ad_tpbt_go_tv 0x7f080420
int id native_ad_tpbt_image_iv 0x7f080421
int id native_ad_tpbt_img_container_fl 0x7f080422
int id native_ad_tpbt_subtitle_tv 0x7f080423
int id native_ad_tpbt_title_ll 0x7f080424
int id native_ad_tpbt_title_tv 0x7f080425
int id navigation_header_container 0x7f080426
int id never 0x7f080427
int id none 0x7f080428
int id normal 0x7f080429
int id notification_background 0x7f08042a
int id notification_main_column 0x7f08042b
int id notification_main_column_container 0x7f08042c
int id oset_ks_ad_actionBar_container 0x7f08042d
int id oset_ks_ad_container 0x7f08042e
int id oset_ks_ad_dislike 0x7f08042f
int id oset_ks_ad_image 0x7f080430
int id oset_ks_ad_image_left 0x7f080431
int id oset_ks_ad_image_mid 0x7f080432
int id oset_ks_ad_image_right 0x7f080433
int id oset_ks_app_desc 0x7f080434
int id oset_ks_app_download_btn 0x7f080435
int id oset_ks_app_icon 0x7f080436
int id oset_ks_app_title 0x7f080437
int id oset_ks_ksad_logo_icon 0x7f080438
int id oset_ks_ksad_logo_text 0x7f080439
int id oset_ks_video_container 0x7f08043a
int id oset_shake_view 0x7f08043b
int id outline 0x7f08043c
int id packed 0x7f08043d
int id parallax 0x7f08043e
int id parent 0x7f08043f
int id parentPanel 0x7f080440
int id parent_matrix 0x7f080441
int id percent 0x7f080442
int id permission_list 0x7f080443
int id pin 0x7f080444
int id privacyContentTextView 0x7f080445
int id privacy_webview 0x7f080446
int id progress_bar 0x7f080447
int id progress_circular 0x7f080448
int id progress_horizontal 0x7f080449
int id push 0x7f08044a
int id ra_btn_cancel 0x7f08044b
int id ra_btn_ok 0x7f08044c
int id ra_pic 0x7f08044d
int id ra_title 0x7f08044e
int id radio 0x7f08044f
int id random 0x7f080450
int id repeat 0x7f080451
int id reveal 0x7f080452
int id right 0x7f080453
int id rightBottom 0x7f080454
int id rightBottomCrop 0x7f080455
int id rightCenter 0x7f080456
int id rightCenterCrop 0x7f080457
int id rightTop 0x7f080458
int id rightTopCrop 0x7f080459
int id right_icon 0x7f08045a
int id right_side 0x7f08045b
int id rl_action 0x7f08045c
int id rl_anim_container 0x7f08045d
int id rl_bg_container 0x7f08045e
int id rl_close 0x7f08045f
int id rl_container 0x7f080460
int id rl_down 0x7f080461
int id rl_media 0x7f080462
int id rl_slide_down_container 0x7f080463
int id root 0x7f080464
int id rotate_alpha_phone 0x7f080465
int id rotate_bg 0x7f080466
int id rotate_line 0x7f080467
int id rotate_phone 0x7f080468
int id sans 0x7f080469
int id save_image_matrix 0x7f08046a
int id save_non_transition_alpha 0x7f08046b
int id save_scale_type 0x7f08046c
int id screen 0x7f08046d
int id scroll 0x7f08046e
int id scrollIndicatorDown 0x7f08046f
int id scrollIndicatorUp 0x7f080470
int id scrollView 0x7f080471
int id scroll_container 0x7f080472
int id scrollable 0x7f080473
int id scrollbar 0x7f080474
int id scrollbar_container 0x7f080475
int id search_badge 0x7f080476
int id search_bar 0x7f080477
int id search_button 0x7f080478
int id search_close_btn 0x7f080479
int id search_edit_frame 0x7f08047a
int id search_go_btn 0x7f08047b
int id search_mag_icon 0x7f08047c
int id search_plate 0x7f08047d
int id search_src_text 0x7f08047e
int id search_voice_btn 0x7f08047f
int id seek_bar 0x7f080480
int id select_dialog_listview 0x7f080481
int id selected 0x7f080482
int id serif 0x7f080483
int id shortcut 0x7f080484
int id showCustom 0x7f080485
int id showHome 0x7f080486
int id showTitle 0x7f080487
int id sig_adInfo_text 0x7f080488
int id sig_ad_close 0x7f080489
int id sig_ad_container 0x7f08048a
int id sig_ad_desc 0x7f08048b
int id sig_ad_header 0x7f08048c
int id sig_ad_privacy_ad_logo 0x7f08048d
int id sig_ad_privacy_ad_text 0x7f08048e
int id sig_ad_privacy_info 0x7f08048f
int id sig_ad_privacy_ll 0x7f080490
int id sig_ad_privacy_name 0x7f080491
int id sig_ad_privacy_permission 0x7f080492
int id sig_ad_privacy_version 0x7f080493
int id sig_ad_privacy_view 0x7f080494
int id sig_ad_rl 0x7f080495
int id sig_ad_rl_1 0x7f080496
int id sig_ad_rl_close 0x7f080497
int id sig_ad_rl_root 0x7f080498
int id sig_ad_sound 0x7f080499
int id sig_ad_timer 0x7f08049a
int id sig_ad_title 0x7f08049b
int id sig_app_cta 0x7f08049c
int id sig_app_icon 0x7f08049d
int id sig_app_info 0x7f08049e
int id sig_app_info_dev 0x7f08049f
int id sig_app_info_name 0x7f0804a0
int id sig_app_info_permissions 0x7f0804a1
int id sig_app_info_privacy 0x7f0804a2
int id sig_app_info_product 0x7f0804a3
int id sig_app_info_ver 0x7f0804a4
int id sig_app_info_ver_rl 0x7f0804a5
int id sig_app_name 0x7f0804a6
int id sig_commit_sl 0x7f0804a7
int id sig_cta_button 0x7f0804a8
int id sig_dislike_ll 0x7f0804a9
int id sig_dislike_tv 0x7f0804aa
int id sig_download_notification_button 0x7f0804ab
int id sig_download_notification_icon 0x7f0804ac
int id sig_download_notification_progressBar 0x7f0804ad
int id sig_download_notification_speed 0x7f0804ae
int id sig_download_notification_status 0x7f0804af
int id sig_download_notification_title 0x7f0804b0
int id sig_endCard_image 0x7f0804b1
int id sig_flow_sl 0x7f0804b2
int id sig_native_video_app_container 0x7f0804b3
int id sig_native_video_back 0x7f0804b4
int id sig_native_video_back_rl 0x7f0804b5
int id sig_native_video_big_replay 0x7f0804b6
int id sig_native_video_blurImageView 0x7f0804b7
int id sig_native_video_bottom_progress 0x7f0804b8
int id sig_native_video_fullscreen_btn 0x7f0804b9
int id sig_native_video_fullscreen_rl 0x7f0804ba
int id sig_native_video_layout_bottom 0x7f0804bb
int id sig_native_video_layout_top 0x7f0804bc
int id sig_native_video_replay_btn 0x7f0804bd
int id sig_native_video_replay_rl 0x7f0804be
int id sig_native_video_sound_btn 0x7f0804bf
int id sig_native_video_sound_rl 0x7f0804c0
int id sig_native_video_start_btn 0x7f0804c1
int id sig_native_video_start_rl 0x7f0804c2
int id sig_native_video_surface_container 0x7f0804c3
int id sig_native_video_thumb 0x7f0804c4
int id sig_not_close_tv 0x7f0804c5
int id sig_not_show_tv 0x7f0804c6
int id sig_shakeDescView 0x7f0804c7
int id sig_shakeImageView 0x7f0804c8
int id sig_shakeTitleView 0x7f0804c9
int id sig_shake_view 0x7f0804ca
int id sig_splash_template_ad_container 0x7f0804cb
int id sig_suggest_et 0x7f0804cc
int id sl_permission 0x7f0804cd
int id smallLabel 0x7f0804ce
int id snackbar_action 0x7f0804cf
int id snackbar_text 0x7f0804d0
int id snap 0x7f0804d1
int id snapMargins 0x7f0804d2
int id spacer 0x7f0804d3
int id splash_end_card_view 0x7f0804d4
int id splash_full_tk_play_card_view 0x7f0804d5
int id splash_play_card_view 0x7f0804d6
int id splash_tk_play_card_view 0x7f0804d7
int id split_action_bar 0x7f0804d8
int id spread 0x7f0804d9
int id spread_inside 0x7f0804da
int id src_atop 0x7f0804db
int id src_in 0x7f0804dc
int id src_over 0x7f0804dd
int id srl_tag 0x7f0804de
int id staminaPlusButton 0x7f0804df
int id staminaTextView 0x7f0804e0
int id standard 0x7f0804e1
int id start 0x7f0804e2
int id startGameButton 0x7f0804e3
int id startInside 0x7f0804e4
int id statusBarLayout 0x7f0804e5
int id status_bar_latest_event_content 0x7f0804e6
int id storyScrollView 0x7f0804e7
int id storyTextView 0x7f0804e8
int id stretch 0x7f0804e9
int id submenuarrow 0x7f0804ea
int id submit_area 0x7f0804eb
int id tabMode 0x7f0804ec
int id tag_transition_group 0x7f0804ed
int id tag_unhandled_key_event_manager 0x7f0804ee
int id tag_unhandled_key_listeners 0x7f0804ef
int id text 0x7f0804f0
int id text2 0x7f0804f1
int id textSpacerNoButtons 0x7f0804f2
int id textSpacerNoTitle 0x7f0804f3
int id textStart 0x7f0804f4
int id text_input_password_toggle 0x7f0804f5
int id textinput_counter 0x7f0804f6
int id textinput_error 0x7f0804f7
int id textinput_helper_text 0x7f0804f8
int id texture_view 0x7f0804f9
int id time 0x7f0804fa
int id title 0x7f0804fb
int id titleDividerNoCustom 0x7f0804fc
int id titleImageView 0x7f0804fd
int id title_bar 0x7f0804fe
int id title_template 0x7f0804ff
int id top 0x7f080500
int id topPanel 0x7f080501
int id touch_outside 0x7f080502
int id transition_current_scene 0x7f080503
int id transition_layout_save 0x7f080504
int id transition_position 0x7f080505
int id transition_scene_layoutid_cache 0x7f080506
int id transition_transform 0x7f080507
int id tt_appdownloader_action 0x7f080508
int id tt_appdownloader_desc 0x7f080509
int id tt_appdownloader_download_progress 0x7f08050a
int id tt_appdownloader_download_progress_new 0x7f08050b
int id tt_appdownloader_download_size 0x7f08050c
int id tt_appdownloader_download_status 0x7f08050d
int id tt_appdownloader_download_success 0x7f08050e
int id tt_appdownloader_download_success_size 0x7f08050f
int id tt_appdownloader_download_success_status 0x7f080510
int id tt_appdownloader_download_text 0x7f080511
int id tt_appdownloader_icon 0x7f080512
int id tt_appdownloader_root 0x7f080513
int id tt_id_render_tag 0x7f080514
int id tt_id_root_web_view 0x7f080515
int id tt_mediation_admob_developer_view_root_tag_key 0x7f080516
int id tt_mediation_admob_developer_view_tag_key 0x7f080517
int id tt_mediation_gdt_developer_view_logo_tag_key 0x7f080518
int id tt_mediation_gdt_developer_view_root_tag_key 0x7f080519
int id tt_mediation_gdt_developer_view_tag_key 0x7f08051a
int id tt_mediation_mtg_ad_choice 0x7f08051b
int id tt_shake_tag_key 0x7f08051c
int id tv_action 0x7f08051d
int id tv_ad_name 0x7f08051e
int id tv_ad_source 0x7f08051f
int id tv_app_auother 0x7f080520
int id tv_app_detail 0x7f080521
int id tv_app_developer 0x7f080522
int id tv_app_function 0x7f080523
int id tv_app_info 0x7f080524
int id tv_app_name 0x7f080525
int id tv_app_permission 0x7f080526
int id tv_app_privacy 0x7f080527
int id tv_app_version 0x7f080528
int id tv_auto_shutdown_time 0x7f080529
int id tv_bz_banner_content 0x7f08052a
int id tv_bz_banner_title 0x7f08052b
int id tv_close 0x7f08052c
int id tv_content 0x7f08052d
int id tv_continue 0x7f08052e
int id tv_countdown 0x7f08052f
int id tv_desc 0x7f080530
int id tv_empty 0x7f080531
int id tv_exit 0x7f080532
int id tv_give_up 0x7f080533
int id tv_gm_banner_content 0x7f080534
int id tv_gm_banner_title 0x7f080535
int id tv_ks_banner_content 0x7f080536
int id tv_ks_banner_title 0x7f080537
int id tv_msg 0x7f080538
int id tv_name 0x7f080539
int id tv_permission_description 0x7f08053a
int id tv_permission_title 0x7f08053b
int id tv_sg_banner_content 0x7f08053c
int id tv_sg_banner_title 0x7f08053d
int id tv_shake 0x7f08053e
int id tv_sigmob_native_content 0x7f08053f
int id tv_sigmob_native_title 0x7f080540
int id tv_skip 0x7f080541
int id tv_slide_down_title 0x7f080542
int id tv_title 0x7f080543
int id twist_right_first_image 0x7f080544
int id uniform 0x7f080545
int id unlabeled 0x7f080546
int id up 0x7f080547
int id useLogo 0x7f080548
int id vertical 0x7f080549
int id video_cover 0x7f08054a
int id view_offset_helper 0x7f08054b
int id view_stub_action_bar 0x7f08054c
int id view_stub_action_bar_landscape 0x7f08054d
int id visible 0x7f08054e
int id warmthPlusButton 0x7f08054f
int id warmthTextView 0x7f080550
int id web 0x7f080551
int id web_view 0x7f080552
int id withText 0x7f080553
int id wrap 0x7f080554
int id wrap_content 0x7f080555
int integer abc_config_activityDefaultDur 0x7f090000
int integer abc_config_activityShortDur 0x7f090001
int integer app_bar_elevation_anim_duration 0x7f090002
int integer bottom_sheet_slide_duration 0x7f090003
int integer cancel_button_image_alpha 0x7f090004
int integer config_tooltipAnimTime 0x7f090005
int integer design_snackbar_text_max_lines 0x7f090006
int integer design_tab_indicator_anim_duration_ms 0x7f090007
int integer hide_password_duration 0x7f090008
int integer min_screen_width_bucket 0x7f090009
int integer mtrl_btn_anim_delay_ms 0x7f09000a
int integer mtrl_btn_anim_duration_ms 0x7f09000b
int integer mtrl_chip_anim_duration 0x7f09000c
int integer mtrl_tab_indicator_anim_duration_ms 0x7f09000d
int integer show_password_duration 0x7f09000e
int integer status_bar_notification_info_maxnum 0x7f09000f
int interpolator mtrl_fast_out_linear_in 0x7f0a0000
int interpolator mtrl_fast_out_slow_in 0x7f0a0001
int interpolator mtrl_linear 0x7f0a0002
int interpolator mtrl_linear_out_slow_in 0x7f0a0003
int layout abc_action_bar_title_item 0x7f0b0000
int layout abc_action_bar_up_container 0x7f0b0001
int layout abc_action_menu_item_layout 0x7f0b0002
int layout abc_action_menu_layout 0x7f0b0003
int layout abc_action_mode_bar 0x7f0b0004
int layout abc_action_mode_close_item_material 0x7f0b0005
int layout abc_activity_chooser_view 0x7f0b0006
int layout abc_activity_chooser_view_list_item 0x7f0b0007
int layout abc_alert_dialog_button_bar_material 0x7f0b0008
int layout abc_alert_dialog_material 0x7f0b0009
int layout abc_alert_dialog_title_material 0x7f0b000a
int layout abc_cascading_menu_item_layout 0x7f0b000b
int layout abc_dialog_title_material 0x7f0b000c
int layout abc_expanded_menu_layout 0x7f0b000d
int layout abc_list_menu_item_checkbox 0x7f0b000e
int layout abc_list_menu_item_icon 0x7f0b000f
int layout abc_list_menu_item_layout 0x7f0b0010
int layout abc_list_menu_item_radio 0x7f0b0011
int layout abc_popup_menu_header_item_layout 0x7f0b0012
int layout abc_popup_menu_item_layout 0x7f0b0013
int layout abc_screen_content_include 0x7f0b0014
int layout abc_screen_simple 0x7f0b0015
int layout abc_screen_simple_overlay_action_mode 0x7f0b0016
int layout abc_screen_toolbar 0x7f0b0017
int layout abc_search_dropdown_item_icons_2line 0x7f0b0018
int layout abc_search_view 0x7f0b0019
int layout abc_select_dialog_material 0x7f0b001a
int layout abc_tooltip 0x7f0b001b
int layout activity_beizi_interstitial 0x7f0b001c
int layout activity_beizi_reward_video 0x7f0b001d
int layout activity_in_app_browser 0x7f0b001e
int layout activity_main 0x7f0b001f
int layout activity_privacy_policy 0x7f0b0020
int layout activity_start 0x7f0b0021
int layout beizi_complaint_dialog 0x7f0b0022
int layout beizi_complaint_item_multi_one 0x7f0b0023
int layout beizi_dislike_dialog 0x7f0b0024
int layout beizi_dislike_item_multi_one 0x7f0b0025
int layout beizi_dislike_item_multi_two 0x7f0b0026
int layout beizi_dislike_item_multi_two_recycle_item 0x7f0b0027
int layout beizi_download_appinfo_activity 0x7f0b0028
int layout beizi_download_dialog 0x7f0b0029
int layout beizi_download_dialog_expand_child_item 0x7f0b002a
int layout beizi_download_dialog_expand_parent_item 0x7f0b002b
int layout beizi_interaction_euler_angle_view 0x7f0b002c
int layout beizi_layout_native_left_text_right_picture_view 0x7f0b002d
int layout beizi_layout_native_top_picture_bottom_text_view 0x7f0b002e
int layout beizi_layout_unified_view 0x7f0b002f
int layout beizi_native_custom_view 0x7f0b0030
int layout beizi_twist_view 0x7f0b0031
int layout design_bottom_navigation_item 0x7f0b0032
int layout design_bottom_sheet_dialog 0x7f0b0033
int layout design_layout_snackbar 0x7f0b0034
int layout design_layout_snackbar_include 0x7f0b0035
int layout design_layout_tab_icon 0x7f0b0036
int layout design_layout_tab_text 0x7f0b0037
int layout design_menu_item_action_area 0x7f0b0038
int layout design_navigation_item 0x7f0b0039
int layout design_navigation_item_header 0x7f0b003a
int layout design_navigation_item_separator 0x7f0b003b
int layout design_navigation_item_subheader 0x7f0b003c
int layout design_navigation_menu 0x7f0b003d
int layout design_navigation_menu_item 0x7f0b003e
int layout design_text_input_password_icon 0x7f0b003f
int layout download_confirm_dialog 0x7f0b0040
int layout ksad_activity_ad_land_page 0x7f0b0041
int layout ksad_activity_ad_video_webview 0x7f0b0042
int layout ksad_activity_ad_webview 0x7f0b0043
int layout ksad_activity_apk_info_landscape 0x7f0b0044
int layout ksad_activity_feed_download 0x7f0b0045
int layout ksad_activity_fullscreen_native 0x7f0b0046
int layout ksad_activity_fullscreen_tk 0x7f0b0047
int layout ksad_activity_fullscreen_video_legacy 0x7f0b0048
int layout ksad_activity_land_page_horizontal 0x7f0b0049
int layout ksad_activity_landpage 0x7f0b004a
int layout ksad_activity_playable 0x7f0b004b
int layout ksad_activity_preview_topbar 0x7f0b004c
int layout ksad_activity_reward_neo 0x7f0b004d
int layout ksad_activity_reward_neo_native 0x7f0b004e
int layout ksad_activity_reward_preview 0x7f0b004f
int layout ksad_activity_reward_video_legacy 0x7f0b0050
int layout ksad_activity_simple_ad_webview 0x7f0b0051
int layout ksad_activity_title_bar 0x7f0b0052
int layout ksad_ad_land_page_native 0x7f0b0053
int layout ksad_ad_landingpage_layout 0x7f0b0054
int layout ksad_ad_web_card_layout 0x7f0b0055
int layout ksad_app_score 0x7f0b0056
int layout ksad_author_icon 0x7f0b0057
int layout ksad_auto_close 0x7f0b0058
int layout ksad_banner_base 0x7f0b0059
int layout ksad_banner_item 0x7f0b005a
int layout ksad_banner_item_land 0x7f0b005b
int layout ksad_card_tips 0x7f0b005c
int layout ksad_common_app_card 0x7f0b005d
int layout ksad_common_app_card_land 0x7f0b005e
int layout ksad_content_alliance_toast 0x7f0b005f
int layout ksad_content_alliance_toast_2 0x7f0b0060
int layout ksad_content_alliance_toast_light 0x7f0b0061
int layout ksad_datail_webview_container 0x7f0b0062
int layout ksad_detail_webview 0x7f0b0063
int layout ksad_download_dialog_layout 0x7f0b0064
int layout ksad_download_progress_bar 0x7f0b0065
int layout ksad_download_progress_biserial_layout 0x7f0b0066
int layout ksad_download_progress_layout 0x7f0b0067
int layout ksad_download_progress_novel_layout 0x7f0b0068
int layout ksad_draw_actionbar_live_base 0x7f0b0069
int layout ksad_draw_actionbar_live_shop 0x7f0b006a
int layout ksad_draw_ad_live_layout 0x7f0b006b
int layout ksad_draw_author_end_icon 0x7f0b006c
int layout ksad_draw_author_icon 0x7f0b006d
int layout ksad_draw_card_app 0x7f0b006e
int layout ksad_draw_card_h5 0x7f0b006f
int layout ksad_draw_download_bar 0x7f0b0070
int layout ksad_draw_layout 0x7f0b0071
int layout ksad_draw_live_end_card 0x7f0b0072
int layout ksad_draw_tk_layout 0x7f0b0073
int layout ksad_draw_video_tailframe 0x7f0b0074
int layout ksad_endcard_close_view 0x7f0b0075
int layout ksad_feed_app_download 0x7f0b0076
int layout ksad_feed_app_download_novel 0x7f0b0077
int layout ksad_feed_biserial_image 0x7f0b0078
int layout ksad_feed_biserial_video 0x7f0b0079
int layout ksad_feed_label_dislike 0x7f0b007a
int layout ksad_feed_novel_regular_image 0x7f0b007b
int layout ksad_feed_novel_regular_video 0x7f0b007c
int layout ksad_feed_open_biserial_h5 0x7f0b007d
int layout ksad_feed_open_h5 0x7f0b007e
int layout ksad_feed_shake 0x7f0b007f
int layout ksad_feed_text_above_group_image 0x7f0b0080
int layout ksad_feed_text_above_image 0x7f0b0081
int layout ksad_feed_text_above_video 0x7f0b0082
int layout ksad_feed_text_below_image 0x7f0b0083
int layout ksad_feed_text_below_video 0x7f0b0084
int layout ksad_feed_text_immerse_image 0x7f0b0085
int layout ksad_feed_text_left_image 0x7f0b0086
int layout ksad_feed_text_right_image 0x7f0b0087
int layout ksad_feed_tkview 0x7f0b0088
int layout ksad_feed_video 0x7f0b0089
int layout ksad_feed_video_palyer_controller 0x7f0b008a
int layout ksad_feed_webview 0x7f0b008b
int layout ksad_fullscreen_detail_top_toolbar 0x7f0b008c
int layout ksad_fullscreen_end_top_toolbar 0x7f0b008d
int layout ksad_hand_slide 0x7f0b008e
int layout ksad_image_player_sweep 0x7f0b008f
int layout ksad_install_dialog 0x7f0b0090
int layout ksad_install_tips 0x7f0b0091
int layout ksad_install_tips_bottom 0x7f0b0092
int layout ksad_interstitial 0x7f0b0093
int layout ksad_interstitial_aggregate_manual_tips 0x7f0b0094
int layout ksad_interstitial_download 0x7f0b0095
int layout ksad_interstitial_exit_intercept_dialog 0x7f0b0096
int layout ksad_interstitial_left_slide_to_next 0x7f0b0097
int layout ksad_interstitial_multi_ad 0x7f0b0098
int layout ksad_interstitial_native 0x7f0b0099
int layout ksad_interstitial_native_above 0x7f0b009a
int layout ksad_interstitial_native_element 0x7f0b009b
int layout ksad_interstitial_right_slide_to_return 0x7f0b009c
int layout ksad_interstitial_toast_layout 0x7f0b009d
int layout ksad_layout_splash_slideview 0x7f0b009e
int layout ksad_live_origin_dialog 0x7f0b009f
int layout ksad_live_subscribe_card 0x7f0b00a0
int layout ksad_live_subscribe_dialog 0x7f0b00a1
int layout ksad_live_subscribe_end_dialog 0x7f0b00a2
int layout ksad_logo_layout 0x7f0b00a3
int layout ksad_native_live_layout 0x7f0b00a4
int layout ksad_native_rotate_layout 0x7f0b00a5
int layout ksad_native_video_layout 0x7f0b00a6
int layout ksad_no_title_common_dialog_content_layout 0x7f0b00a7
int layout ksad_notification_download_completed 0x7f0b00a8
int layout ksad_notification_download_progress_with_control 0x7f0b00a9
int layout ksad_notification_download_progress_without_control 0x7f0b00aa
int layout ksad_play_card_default_info 0x7f0b00ab
int layout ksad_playable_end_info 0x7f0b00ac
int layout ksad_playable_pre_tips 0x7f0b00ad
int layout ksad_promote_ad_click 0x7f0b00ae
int layout ksad_push_ad_container 0x7f0b00af
int layout ksad_reward_actionbar_live_shop 0x7f0b00b0
int layout ksad_reward_actionbar_origin_live_base 0x7f0b00b1
int layout ksad_reward_apk_info_card 0x7f0b00b2
int layout ksad_reward_apk_info_card_native 0x7f0b00b3
int layout ksad_reward_apk_info_card_tag_item 0x7f0b00b4
int layout ksad_reward_apk_info_card_tag_white_item 0x7f0b00b5
int layout ksad_reward_coupon_dialog 0x7f0b00b6
int layout ksad_reward_detail_top_toolbar 0x7f0b00b7
int layout ksad_reward_end_top_toolbar 0x7f0b00b8
int layout ksad_reward_jinniu_dialog 0x7f0b00b9
int layout ksad_reward_jinniu_end 0x7f0b00ba
int layout ksad_reward_live_end_page 0x7f0b00bb
int layout ksad_reward_live_end_page_landscape 0x7f0b00bc
int layout ksad_reward_order_card 0x7f0b00bd
int layout ksad_reward_order_card_coupon 0x7f0b00be
int layout ksad_reward_order_dialog 0x7f0b00bf
int layout ksad_reward_order_end_dialog 0x7f0b00c0
int layout ksad_reward_order_jinniu 0x7f0b00c1
int layout ksad_reward_playend_native 0x7f0b00c2
int layout ksad_reward_task_dialog_dash 0x7f0b00c3
int layout ksad_reward_task_launch_app_dialog 0x7f0b00c4
int layout ksad_reward_task_step_item_checked 0x7f0b00c5
int layout ksad_reward_task_step_item_unchecked 0x7f0b00c6
int layout ksad_reward_time_close_dialog 0x7f0b00c7
int layout ksad_reward_video_area 0x7f0b00c8
int layout ksad_seconed_confirm_dialog_layout 0x7f0b00c9
int layout ksad_shake_center 0x7f0b00ca
int layout ksad_shake_tips_title 0x7f0b00cb
int layout ksad_skip_view 0x7f0b00cc
int layout ksad_splash_action_native 0x7f0b00cd
int layout ksad_splash_bottom_view 0x7f0b00ce
int layout ksad_splash_end_card_area 0x7f0b00cf
int layout ksad_splash_end_card_area_land 0x7f0b00d0
int layout ksad_splash_end_card_native 0x7f0b00d1
int layout ksad_splash_rotate_combo_layout 0x7f0b00d2
int layout ksad_splash_rotate_layout 0x7f0b00d3
int layout ksad_splash_screen_layout 0x7f0b00d4
int layout ksad_splash_shake_combo_layout 0x7f0b00d5
int layout ksad_splash_shake_layout 0x7f0b00d6
int layout ksad_splash_slide_combo_layout 0x7f0b00d7
int layout ksad_splash_slidelayout 0x7f0b00d8
int layout ksad_split_land_page 0x7f0b00d9
int layout ksad_split_mini_video 0x7f0b00da
int layout ksad_tk_page 0x7f0b00db
int layout ksad_toast_corner 0x7f0b00dc
int layout ksad_video_action_bar_landscape_layout 0x7f0b00dd
int layout ksad_video_action_bar_portrait_layout 0x7f0b00de
int layout ksad_video_actionbar_app_landscape 0x7f0b00df
int layout ksad_video_actionbar_app_portrait 0x7f0b00e0
int layout ksad_video_actionbar_h5 0x7f0b00e1
int layout ksad_video_close_dialog 0x7f0b00e2
int layout ksad_video_close_extend_dialog 0x7f0b00e3
int layout ksad_video_play_bar_app_portrait_for_live 0x7f0b00e4
int layout ksad_video_tf_bar_app_landscape 0x7f0b00e5
int layout ksad_video_tf_bar_app_portrait_horizontal 0x7f0b00e6
int layout ksad_video_tf_bar_app_portrait_vertical 0x7f0b00e7
int layout ksad_video_tf_bar_h5_landscape 0x7f0b00e8
int layout ksad_video_tf_bar_h5_portrait_horizontal 0x7f0b00e9
int layout ksad_video_tf_bar_h5_portrait_vertical 0x7f0b00ea
int layout ksad_video_tf_view_landscape_horizontal 0x7f0b00eb
int layout ksad_video_tf_view_landscape_vertical 0x7f0b00ec
int layout ksad_video_tf_view_portrait_horizontal 0x7f0b00ed
int layout ksad_video_tf_view_portrait_vertical 0x7f0b00ee
int layout ksad_video_tk_dialog_layout 0x7f0b00ef
int layout ksad_web_exit_intercept_content_layout 0x7f0b00f0
int layout layout_scrollview_down 0x7f0b00f1
int layout layout_scrollview_left 0x7f0b00f2
int layout layout_scrollview_right 0x7f0b00f3
int layout layout_scrollview_up 0x7f0b00f4
int layout layout_splash_native 0x7f0b00f5
int layout live_init_progress_dialog 0x7f0b00f6
int layout mtrl_layout_snackbar 0x7f0b00f7
int layout mtrl_layout_snackbar_include 0x7f0b00f8
int layout notification_action 0x7f0b00f9
int layout notification_action_tombstone 0x7f0b00fa
int layout notification_media_action 0x7f0b00fb
int layout notification_media_cancel_action 0x7f0b00fc
int layout notification_template_big_media 0x7f0b00fd
int layout notification_template_big_media_custom 0x7f0b00fe
int layout notification_template_big_media_narrow 0x7f0b00ff
int layout notification_template_big_media_narrow_custom 0x7f0b0100
int layout notification_template_custom_big 0x7f0b0101
int layout notification_template_icon_group 0x7f0b0102
int layout notification_template_lines_media 0x7f0b0103
int layout notification_template_media 0x7f0b0104
int layout notification_template_media_custom 0x7f0b0105
int layout notification_template_part_chronometer 0x7f0b0106
int layout notification_template_part_time 0x7f0b0107
int layout oset_activity_native_view_ad_app_info_webview 0x7f0b0108
int layout oset_activity_video_content 0x7f0b0109
int layout oset_activity_webview 0x7f0b010a
int layout oset_api_ad_logo 0x7f0b010b
int layout oset_api_ad_privacy 0x7f0b010c
int layout oset_api_ad_privacy_white 0x7f0b010d
int layout oset_api_banner_ad 0x7f0b010e
int layout oset_api_banner_ad_1 0x7f0b010f
int layout oset_api_interstitial_ad 0x7f0b0110
int layout oset_api_native_ad 0x7f0b0111
int layout oset_api_reward_ad 0x7f0b0112
int layout oset_api_splash_ad 0x7f0b0113
int layout oset_bz_banner 0x7f0b0114
int layout oset_dialog_dl_compliance 0x7f0b0115
int layout oset_dialog_exit 0x7f0b0116
int layout oset_draw_view_gdt_layout 0x7f0b0117
int layout oset_gm_banner 0x7f0b0118
int layout oset_item_native_view_ad_permission 0x7f0b0119
int layout oset_ks_banner 0x7f0b011a
int layout oset_ks_banner_multiple_version 0x7f0b011b
int layout oset_ks_native_item_app_download 0x7f0b011c
int layout oset_ks_native_item_group_image 0x7f0b011d
int layout oset_ks_native_item_single_image 0x7f0b011e
int layout oset_ks_native_item_video 0x7f0b011f
int layout oset_layout_shake 0x7f0b0120
int layout oset_qidian_video_player 0x7f0b0121
int layout oset_sg_banner 0x7f0b0122
int layout oset_sigmob_native_layout 0x7f0b0123
int layout reward_again_dialog 0x7f0b0124
int layout select_dialog_item_material 0x7f0b0125
int layout select_dialog_multichoice_material 0x7f0b0126
int layout select_dialog_singlechoice_material 0x7f0b0127
int layout sig_ad_app_info_layout 0x7f0b0128
int layout sig_ad_app_info_small_layout 0x7f0b0129
int layout sig_ad_privacy_layout 0x7f0b012a
int layout sig_ad_privacy_new_layout 0x7f0b012b
int layout sig_app_info_layout 0x7f0b012c
int layout sig_app_layout 0x7f0b012d
int layout sig_dislike_layout 0x7f0b012e
int layout sig_download_notification_layout 0x7f0b012f
int layout sig_fullscreen_layout 0x7f0b0130
int layout sig_native_express_layout 0x7f0b0131
int layout sig_new_interstitial_endcard_layout 0x7f0b0132
int layout sig_new_interstitial_full_layout 0x7f0b0133
int layout sig_new_interstitial_header_layout 0x7f0b0134
int layout sig_new_interstitial_layout 0x7f0b0135
int layout sig_new_interstitial_small_layout 0x7f0b0136
int layout sig_shake_view_layout 0x7f0b0137
int layout sig_splash_layout 0x7f0b0138
int layout sig_video_player_layout 0x7f0b0139
int layout support_simple_spinner_dropdown_item 0x7f0b013a
int layout tt_appdownloader_notification_layout 0x7f0b013b
int layout ttdownloader_activity_app_detail_info 0x7f0b013c
int layout ttdownloader_activity_app_privacy_policy 0x7f0b013d
int layout ttdownloader_dialog_appinfo 0x7f0b013e
int layout ttdownloader_dialog_select_operation 0x7f0b013f
int layout ttdownloader_item_permission 0x7f0b0140
int mipmap ad_close 0x7f0c0000
int mipmap beizi_bg_reward_video_interaction 0x7f0c0001
int mipmap beizi_icon_arrow_fold 0x7f0c0002
int mipmap beizi_icon_arrow_unfold 0x7f0c0003
int mipmap beizi_icon_close 0x7f0c0004
int mipmap beizi_icon_download 0x7f0c0005
int mipmap beizi_icon_reward_gift 0x7f0c0006
int mipmap beizi_icon_video_replay 0x7f0c0007
int mipmap beizi_interaction_icon_arrow_down 0x7f0c0008
int mipmap beizi_interaction_icon_arrow_left 0x7f0c0009
int mipmap beizi_interaction_icon_arrow_right 0x7f0c000a
int mipmap beizi_interaction_icon_arrow_up 0x7f0c000b
int mipmap beizi_interaction_icon_close 0x7f0c000c
int mipmap beizi_interaction_icon_euler_angle 0x7f0c000d
int mipmap beizi_interaction_icon_shake 0x7f0c000e
int mipmap ic_launcher 0x7f0c000f
int mipmap ic_launcher_foreground 0x7f0c0010
int mipmap ic_launcher_round 0x7f0c0011
int mipmap icon_reward_bg 0x7f0c0012
int mipmap live_loading 0x7f0c0013
int mipmap live_loading_cancel 0x7f0c0014
int mipmap od_mute 0x7f0c0015
int mipmap od_voiced 0x7f0c0016
int mipmap oset_ks_test_app_default_icon 0x7f0c0017
int mipmap oset_od_close 0x7f0c0018
int mipmap oset_od_information_close 0x7f0c0019
int mipmap oset_od_mute 0x7f0c001a
int mipmap oset_od_replay 0x7f0c001b
int mipmap oset_od_voiced 0x7f0c001c
int mipmap oset_splash_shake_phone 0x7f0c001d
int mipmap oset_timeover_img 0x7f0c001e
int mipmap oset_voiced 0x7f0c001f
int raw keep 0x7f0d0000
int raw sig_keep 0x7f0d0001
int raw winter 0x7f0d0002
int raw zeus_keep_res_live 0x7f0d0003
int string abc_action_bar_home_description 0x7f0e0000
int string abc_action_bar_up_description 0x7f0e0001
int string abc_action_menu_overflow_description 0x7f0e0002
int string abc_action_mode_done 0x7f0e0003
int string abc_activity_chooser_view_see_all 0x7f0e0004
int string abc_activitychooserview_choose_application 0x7f0e0005
int string abc_capital_off 0x7f0e0006
int string abc_capital_on 0x7f0e0007
int string abc_font_family_body_1_material 0x7f0e0008
int string abc_font_family_body_2_material 0x7f0e0009
int string abc_font_family_button_material 0x7f0e000a
int string abc_font_family_caption_material 0x7f0e000b
int string abc_font_family_display_1_material 0x7f0e000c
int string abc_font_family_display_2_material 0x7f0e000d
int string abc_font_family_display_3_material 0x7f0e000e
int string abc_font_family_display_4_material 0x7f0e000f
int string abc_font_family_headline_material 0x7f0e0010
int string abc_font_family_menu_material 0x7f0e0011
int string abc_font_family_subhead_material 0x7f0e0012
int string abc_font_family_title_material 0x7f0e0013
int string abc_menu_alt_shortcut_label 0x7f0e0014
int string abc_menu_ctrl_shortcut_label 0x7f0e0015
int string abc_menu_delete_shortcut_label 0x7f0e0016
int string abc_menu_enter_shortcut_label 0x7f0e0017
int string abc_menu_function_shortcut_label 0x7f0e0018
int string abc_menu_meta_shortcut_label 0x7f0e0019
int string abc_menu_shift_shortcut_label 0x7f0e001a
int string abc_menu_space_shortcut_label 0x7f0e001b
int string abc_menu_sym_shortcut_label 0x7f0e001c
int string abc_prepend_shortcut_label 0x7f0e001d
int string abc_search_hint 0x7f0e001e
int string abc_searchview_description_clear 0x7f0e001f
int string abc_searchview_description_query 0x7f0e0020
int string abc_searchview_description_search 0x7f0e0021
int string abc_searchview_description_submit 0x7f0e0022
int string abc_searchview_description_voice 0x7f0e0023
int string abc_shareactionprovider_share_with 0x7f0e0024
int string abc_shareactionprovider_share_with_application 0x7f0e0025
int string abc_toolbar_collapse_description 0x7f0e0026
int string ad_load_failed_message 0x7f0e0027
int string ad_load_failed_title 0x7f0e0028
int string ad_loading_message 0x7f0e0029
int string ad_loading_title 0x7f0e002a
int string ad_revive 0x7f0e002b
int string ad_show_failed_message 0x7f0e002c
int string ad_show_failed_title 0x7f0e002d
int string app_name 0x7f0e002e
int string app_name_en 0x7f0e002f
int string appbar_scrolling_view_behavior 0x7f0e0030
int string beizi_action_cant_be_completed 0x7f0e0031
int string beizi_allow 0x7f0e0032
int string beizi_cancel 0x7f0e0033
int string beizi_confirm 0x7f0e0034
int string beizi_deny 0x7f0e0035
int string beizi_dialog_text_hint 0x7f0e0036
int string beizi_html5_geo_permission_prompt 0x7f0e0037
int string beizi_html5_geo_permission_prompt_title 0x7f0e0038
int string beizi_loading 0x7f0e0039
int string beizi_native_tag 0x7f0e003a
int string beizi_skip_ad 0x7f0e003b
int string beizi_store_picture_accept 0x7f0e003c
int string beizi_store_picture_decline 0x7f0e003d
int string beizi_store_picture_message 0x7f0e003e
int string beizi_store_picture_title 0x7f0e003f
int string bottom_sheet_behavior 0x7f0e0040
int string cabin_damaged 0x7f0e0041
int string cabin_good_condition 0x7f0e0042
int string cabin_improved 0x7f0e0043
int string cabin_poor_condition 0x7f0e0044
int string challenge_again 0x7f0e0045
int string character_counter_content_description 0x7f0e0046
int string character_counter_pattern 0x7f0e0047
int string choice_effect_text 0x7f0e0048
int string confirm 0x7f0e0049
int string continue_button 0x7f0e004a
int string continue_game 0x7f0e004b
int string eat_food 0x7f0e004c
int string eat_food_result 0x7f0e004d
int string environment_effects 0x7f0e004e
int string event_effect 0x7f0e004f
int string event_not_found 0x7f0e0050
int string exit_game 0x7f0e0051
int string fab_transformation_scrim_behavior 0x7f0e0052
int string fab_transformation_sheet_behavior 0x7f0e0053
int string firewood_name 0x7f0e0054
int string food_name 0x7f0e0055
int string food_phase_description 0x7f0e0056
int string food_phase_title 0x7f0e0057
int string game_data_load_failed 0x7f0e0058
int string game_description 0x7f0e0059
int string hide_bottom_view_on_scroll_behavior 0x7f0e005a
int string high_morale_effect 0x7f0e005b
int string hope_greatly_decreased 0x7f0e005c
int string hope_greatly_increased 0x7f0e005d
int string hope_slightly_decreased 0x7f0e005e
int string hope_slightly_increased 0x7f0e005f
int string init 0x7f0e0060
int string interstitialAd 0x7f0e0061
int string interstitialImageAd 0x7f0e0062
int string ksad_ad_default_username_normal 0x7f0e0063
int string ksad_card_tips_interested 0x7f0e0064
int string ksad_card_tips_pre 0x7f0e0065
int string ksad_click_immediate 0x7f0e0066
int string ksad_data_error_toast 0x7f0e0067
int string ksad_deep_link_dialog_content 0x7f0e0068
int string ksad_default_no_more_tip_or_toast_txt 0x7f0e0069
int string ksad_download_kwai_waiting 0x7f0e006a
int string ksad_half_page_loading_error_tip 0x7f0e006b
int string ksad_install_tips 0x7f0e006c
int string ksad_launch_tips 0x7f0e006d
int string ksad_leave_persist 0x7f0e006e
int string ksad_left_slide_to_next 0x7f0e006f
int string ksad_live_end 0x7f0e0070
int string ksad_network_error_toast 0x7f0e0071
int string ksad_no_title_common_dialog_negativebtn_title 0x7f0e0072
int string ksad_no_title_common_dialog_positivebtn_title 0x7f0e0073
int string ksad_page_load_no_more_tip 0x7f0e0074
int string ksad_page_loading_data_error_sub_title 0x7f0e0075
int string ksad_page_loading_data_error_title 0x7f0e0076
int string ksad_page_loading_error_retry 0x7f0e0077
int string ksad_page_loading_network_error_sub_title 0x7f0e0078
int string ksad_page_loading_network_error_title 0x7f0e0079
int string ksad_request_install_content 0x7f0e007a
int string ksad_request_install_nagative 0x7f0e007b
int string ksad_request_install_positive 0x7f0e007c
int string ksad_request_install_title 0x7f0e007d
int string ksad_reward_playable_load_error_toast 0x7f0e007e
int string ksad_reward_success_tip 0x7f0e007f
int string ksad_right_slide_to_return 0x7f0e0080
int string ksad_see_detail 0x7f0e0081
int string ksad_skip_text 0x7f0e0082
int string ksad_splash_preload_tips_text 0x7f0e0083
int string ksad_splash_rotate_combo_rotate_text 0x7f0e0084
int string ksad_splash_shake_combo_action_text 0x7f0e0085
int string ksad_splash_shake_combo_sub_text 0x7f0e0086
int string ksad_splash_slide_combo_guide_text 0x7f0e0087
int string ksad_splash_slide_combo_sub_text 0x7f0e0088
int string ksad_splash_slide_guide_text 0x7f0e0089
int string ksad_watch_continue 0x7f0e008a
int string language 0x7f0e008b
int string language_cancel 0x7f0e008c
int string language_english 0x7f0e008d
int string language_selection_title 0x7f0e008e
int string language_simplified_chinese 0x7f0e008f
int string language_traditional_chinese 0x7f0e0090
int string leave_mountain 0x7f0e0091
int string live_in_loading 0x7f0e0092
int string live_in_loading_failed 0x7f0e0093
int string low_morale_effect 0x7f0e0094
int string mtrl_chip_close_icon_content_description 0x7f0e0095
int string music_off 0x7f0e0096
int string music_on 0x7f0e0097
int string new_day_generic 0x7f0e0098
int string night_phase_title 0x7f0e0099
int string night_settlement 0x7f0e009a
int string night_with_firewood 0x7f0e009b
int string night_without_firewood 0x7f0e009c
int string no_choice_continue 0x7f0e009d
int string oset_function 0x7f0e009e
int string oset_permission 0x7f0e009f
int string oset_privacy 0x7f0e00a0
int string password_toggle_content_description 0x7f0e00a1
int string path_password_eye 0x7f0e00a2
int string path_password_eye_mask_strike_through 0x7f0e00a3
int string path_password_eye_mask_visible 0x7f0e00a4
int string path_password_strike_through 0x7f0e00a5
int string privacy_policy_agree 0x7f0e00a6
int string privacy_policy_content 0x7f0e00a7
int string privacy_policy_disagree 0x7f0e00a8
int string privacy_policy_link_text 0x7f0e00a9
int string privacy_policy_subtitle 0x7f0e00aa
int string privacy_policy_title 0x7f0e00ab
int string privacy_policy_url 0x7f0e00ac
int string rest_indoors 0x7f0e00ad
int string restart_challenge 0x7f0e00ae
int string revive_ad_load_failed_message 0x7f0e00af
int string revive_ad_loading_message 0x7f0e00b0
int string revive_ad_loading_title 0x7f0e00b1
int string revive_ad_show_failed_message 0x7f0e00b2
int string revive_success_generic 0x7f0e00b3
int string revive_success_message 0x7f0e00b4
int string revive_success_title 0x7f0e00b5
int string reward_message 0x7f0e00b6
int string reward_title 0x7f0e00b7
int string rewardedAd 0x7f0e00b8
int string save_food 0x7f0e00b9
int string save_food_result 0x7f0e00ba
int string search_menu_title 0x7f0e00bb
int string search_resources 0x7f0e00bc
int string sig_ad 0x7f0e00bd
int string sig_back 0x7f0e00be
int string sig_close 0x7f0e00bf
int string sig_close_ad_cancel 0x7f0e00c0
int string sig_close_ad_message 0x7f0e00c1
int string sig_close_ad_ok 0x7f0e00c2
int string sig_close_ad_title 0x7f0e00c3
int string sig_close_args 0x7f0e00c4
int string sig_skip_ad_args 0x7f0e00c5
int string sig_skip_args_1 0x7f0e00c6
int string sig_skip_args_2 0x7f0e00c7
int string splash_ad 0x7f0e00c8
int string srl_component_falsify 0x7f0e00c9
int string srl_content_empty 0x7f0e00ca
int string stamina_name 0x7f0e00cb
int string start_game 0x7f0e00cc
int string status_bar_notification_info_overflow 0x7f0e00cd
int string status_change_prefix 0x7f0e00ce
int string title_content_description 0x7f0e00cf
int string tt_00_00 0x7f0e00d0
int string tt_ad 0x7f0e00d1
int string tt_ad_logo_txt 0x7f0e00d2
int string tt_agg_page_close 0x7f0e00d3
int string tt_app_name 0x7f0e00d4
int string tt_app_privacy_dialog_title 0x7f0e00d5
int string tt_appdownloader_button_cancel_download 0x7f0e00d6
int string tt_appdownloader_button_queue_for_wifi 0x7f0e00d7
int string tt_appdownloader_button_start_now 0x7f0e00d8
int string tt_appdownloader_download_percent 0x7f0e00d9
int string tt_appdownloader_download_remaining 0x7f0e00da
int string tt_appdownloader_download_unknown_title 0x7f0e00db
int string tt_appdownloader_duration_hours 0x7f0e00dc
int string tt_appdownloader_duration_minutes 0x7f0e00dd
int string tt_appdownloader_duration_seconds 0x7f0e00de
int string tt_appdownloader_jump_unknown_source 0x7f0e00df
int string tt_appdownloader_label_cancel 0x7f0e00e0
int string tt_appdownloader_label_cancel_directly 0x7f0e00e1
int string tt_appdownloader_label_ok 0x7f0e00e2
int string tt_appdownloader_label_reserve_wifi 0x7f0e00e3
int string tt_appdownloader_notification_download 0x7f0e00e4
int string tt_appdownloader_notification_download_complete_open 0x7f0e00e5
int string tt_appdownloader_notification_download_complete_with_install 0x7f0e00e6
int string tt_appdownloader_notification_download_complete_without_install 0x7f0e00e7
int string tt_appdownloader_notification_download_continue 0x7f0e00e8
int string tt_appdownloader_notification_download_delete 0x7f0e00e9
int string tt_appdownloader_notification_download_failed 0x7f0e00ea
int string tt_appdownloader_notification_download_install 0x7f0e00eb
int string tt_appdownloader_notification_download_open 0x7f0e00ec
int string tt_appdownloader_notification_download_pause 0x7f0e00ed
int string tt_appdownloader_notification_download_restart 0x7f0e00ee
int string tt_appdownloader_notification_download_resume 0x7f0e00ef
int string tt_appdownloader_notification_download_space_failed 0x7f0e00f0
int string tt_appdownloader_notification_download_waiting_net 0x7f0e00f1
int string tt_appdownloader_notification_download_waiting_wifi 0x7f0e00f2
int string tt_appdownloader_notification_downloading 0x7f0e00f3
int string tt_appdownloader_notification_install_finished_open 0x7f0e00f4
int string tt_appdownloader_notification_insufficient_space_error 0x7f0e00f5
int string tt_appdownloader_notification_need_wifi_for_size 0x7f0e00f6
int string tt_appdownloader_notification_no_internet_error 0x7f0e00f7
int string tt_appdownloader_notification_no_wifi_and_in_net 0x7f0e00f8
int string tt_appdownloader_notification_paused_in_background 0x7f0e00f9
int string tt_appdownloader_notification_pausing 0x7f0e00fa
int string tt_appdownloader_notification_prepare 0x7f0e00fb
int string tt_appdownloader_notification_request_btn_no 0x7f0e00fc
int string tt_appdownloader_notification_request_btn_yes 0x7f0e00fd
int string tt_appdownloader_notification_request_message 0x7f0e00fe
int string tt_appdownloader_notification_request_title 0x7f0e00ff
int string tt_appdownloader_notification_waiting_download_complete_handler 0x7f0e0100
int string tt_appdownloader_resume_in_wifi 0x7f0e0101
int string tt_appdownloader_tip 0x7f0e0102
int string tt_appdownloader_wifi_recommended_body 0x7f0e0103
int string tt_appdownloader_wifi_recommended_title 0x7f0e0104
int string tt_appdownloader_wifi_required_body 0x7f0e0105
int string tt_appdownloader_wifi_required_title 0x7f0e0106
int string tt_application_detail 0x7f0e0107
int string tt_auto_play_cancel_text 0x7f0e0108
int string tt_cancel 0x7f0e0109
int string tt_change_other 0x7f0e010a
int string tt_click_replay 0x7f0e010b
int string tt_comment_num 0x7f0e010c
int string tt_comment_num_backup 0x7f0e010d
int string tt_comment_score 0x7f0e010e
int string tt_common_download_app_detail 0x7f0e010f
int string tt_common_download_app_privacy 0x7f0e0110
int string tt_common_download_cancel 0x7f0e0111
int string tt_confirm_download 0x7f0e0112
int string tt_confirm_download_have_app_name 0x7f0e0113
int string tt_dislike_comment_hint 0x7f0e0114
int string tt_dislike_feedback_repeat 0x7f0e0115
int string tt_dislike_feedback_success 0x7f0e0116
int string tt_dislike_header_tv_back 0x7f0e0117
int string tt_dislike_header_tv_title 0x7f0e0118
int string tt_dislike_other_suggest 0x7f0e0119
int string tt_dislike_other_suggest_out 0x7f0e011a
int string tt_dislike_submit 0x7f0e011b
int string tt_download 0x7f0e011c
int string tt_download_finish 0x7f0e011d
int string tt_ecomm_page_reward_acquire 0x7f0e011e
int string tt_ecomm_page_reward_slide_tip 0x7f0e011f
int string tt_ecomm_page_reward_tip 0x7f0e0120
int string tt_ensure_exit 0x7f0e0121
int string tt_feedback 0x7f0e0122
int string tt_full_screen_skip_tx 0x7f0e0123
int string tt_image_download_apk 0x7f0e0124
int string tt_install 0x7f0e0125
int string tt_label_cancel 0x7f0e0126
int string tt_label_ok 0x7f0e0127
int string tt_live_back_btn 0x7f0e0128
int string tt_live_fans_text 0x7f0e0129
int string tt_live_feed_btn 0x7f0e012a
int string tt_live_feed_logo 0x7f0e012b
int string tt_live_finish 0x7f0e012c
int string tt_live_full_reward_btn 0x7f0e012d
int string tt_live_loading_btn 0x7f0e012e
int string tt_live_loading_text 0x7f0e012f
int string tt_live_watch_text 0x7f0e0130
int string tt_logo_cn 0x7f0e0131
int string tt_logo_en 0x7f0e0132
int string tt_mediation_format_adapter_name 0x7f0e0133
int string tt_mediation_format_error_msg 0x7f0e0134
int string tt_mediation_format_no_ad_error_msg 0x7f0e0135
int string tt_mediation_format_setting_error_msg 0x7f0e0136
int string tt_mediation_format_show_success_msg 0x7f0e0137
int string tt_mediation_format_success_msg 0x7f0e0138
int string tt_mediation_label_cancel 0x7f0e0139
int string tt_mediation_label_ok 0x7f0e013a
int string tt_mediation_permission_denied 0x7f0e013b
int string tt_mediation_request_permission_descript_external_storage 0x7f0e013c
int string tt_mediation_request_permission_descript_location 0x7f0e013d
int string tt_mediation_request_permission_descript_read_phone_state 0x7f0e013e
int string tt_no_network 0x7f0e013f
int string tt_open_app_detail_developer 0x7f0e0140
int string tt_open_app_detail_privacy 0x7f0e0141
int string tt_open_app_detail_privacy_list 0x7f0e0142
int string tt_open_app_name 0x7f0e0143
int string tt_open_app_version 0x7f0e0144
int string tt_open_landing_page_app_name 0x7f0e0145
int string tt_permission_denied 0x7f0e0146
int string tt_permission_list 0x7f0e0147
int string tt_privacy_back 0x7f0e0148
int string tt_privacy_policy 0x7f0e0149
int string tt_privacy_start_download 0x7f0e014a
int string tt_quit 0x7f0e014b
int string tt_request_permission_descript_external_storage 0x7f0e014c
int string tt_request_permission_descript_location 0x7f0e014d
int string tt_request_permission_descript_read_phone_state 0x7f0e014e
int string tt_retain_tips_message 0x7f0e014f
int string tt_reward_auto_jump_live 0x7f0e0150
int string tt_reward_empty 0x7f0e0151
int string tt_reward_feedback 0x7f0e0152
int string tt_reward_full_skip_count_down 0x7f0e0153
int string tt_reward_live_dialog_btn_text 0x7f0e0154
int string tt_reward_live_dialog_cancel_count_down_text 0x7f0e0155
int string tt_reward_live_dialog_cancel_text 0x7f0e0156
int string tt_reward_live_grant 0x7f0e0157
int string tt_reward_screen_skip_tx 0x7f0e0158
int string tt_reward_slip_up_lp_tip 0x7f0e0159
int string tt_reward_slip_up_tip 0x7f0e015a
int string tt_reward_slip_up_tip2 0x7f0e015b
int string tt_slide_up_3d 0x7f0e015c
int string tt_splash_backup_ad_btn 0x7f0e015d
int string tt_splash_backup_ad_title 0x7f0e015e
int string tt_splash_brush_mask_hint 0x7f0e015f
int string tt_splash_brush_mask_title 0x7f0e0160
int string tt_splash_click_bar_text 0x7f0e0161
int string tt_splash_default_click_shake 0x7f0e0162
int string tt_splash_rock_desc 0x7f0e0163
int string tt_splash_rock_text 0x7f0e0164
int string tt_splash_rock_top 0x7f0e0165
int string tt_splash_rock_top_text 0x7f0e0166
int string tt_splash_skip_tv_text 0x7f0e0167
int string tt_splash_wriggle_text 0x7f0e0168
int string tt_splash_wriggle_top_text 0x7f0e0169
int string tt_splash_wriggle_top_text_style_17 0x7f0e016a
int string tt_text_privacy_app_version 0x7f0e016b
int string tt_text_privacy_development 0x7f0e016c
int string tt_tip 0x7f0e016d
int string tt_unlike 0x7f0e016e
int string tt_video_bytesize 0x7f0e016f
int string tt_video_bytesize_M 0x7f0e0170
int string tt_video_bytesize_MB 0x7f0e0171
int string tt_video_continue_play 0x7f0e0172
int string tt_video_dial_phone 0x7f0e0173
int string tt_video_dial_replay 0x7f0e0174
int string tt_video_download_apk 0x7f0e0175
int string tt_video_mobile_go_detail 0x7f0e0176
int string tt_video_retry_des_txt 0x7f0e0177
int string tt_video_without_wifi_tips 0x7f0e0178
int string tt_web_title_default 0x7f0e0179
int string tt_will_play 0x7f0e017a
int string version 0x7f0e017b
int string warmth_name 0x7f0e017c
int style AlertDialog_AppCompat 0x7f0f0000
int style AlertDialog_AppCompat_Light 0x7f0f0001
int style AlphaAnimation 0x7f0f0002
int style Animation_AppCompat_Dialog 0x7f0f0003
int style Animation_AppCompat_DropDownUp 0x7f0f0004
int style Animation_AppCompat_Tooltip 0x7f0f0005
int style Animation_Design_BottomSheetDialog 0x7f0f0006
int style AppTheme 0x7f0f0007
int style Base_AlertDialog_AppCompat 0x7f0f0008
int style Base_AlertDialog_AppCompat_Light 0x7f0f0009
int style Base_Animation_AppCompat_Dialog 0x7f0f000a
int style Base_Animation_AppCompat_DropDownUp 0x7f0f000b
int style Base_Animation_AppCompat_Tooltip 0x7f0f000c
int style Base_CardView 0x7f0f000d
int style Base_DialogWindowTitle_AppCompat 0x7f0f000e
int style Base_DialogWindowTitleBackground_AppCompat 0x7f0f000f
int style Base_TextAppearance_AppCompat 0x7f0f0010
int style Base_TextAppearance_AppCompat_Body1 0x7f0f0011
int style Base_TextAppearance_AppCompat_Body2 0x7f0f0012
int style Base_TextAppearance_AppCompat_Button 0x7f0f0013
int style Base_TextAppearance_AppCompat_Caption 0x7f0f0014
int style Base_TextAppearance_AppCompat_Display1 0x7f0f0015
int style Base_TextAppearance_AppCompat_Display2 0x7f0f0016
int style Base_TextAppearance_AppCompat_Display3 0x7f0f0017
int style Base_TextAppearance_AppCompat_Display4 0x7f0f0018
int style Base_TextAppearance_AppCompat_Headline 0x7f0f0019
int style Base_TextAppearance_AppCompat_Inverse 0x7f0f001a
int style Base_TextAppearance_AppCompat_Large 0x7f0f001b
int style Base_TextAppearance_AppCompat_Large_Inverse 0x7f0f001c
int style Base_TextAppearance_AppCompat_Light_Widget_PopupMenu_Large 0x7f0f001d
int style Base_TextAppearance_AppCompat_Light_Widget_PopupMenu_Small 0x7f0f001e
int style Base_TextAppearance_AppCompat_Medium 0x7f0f001f
int style Base_TextAppearance_AppCompat_Medium_Inverse 0x7f0f0020
int style Base_TextAppearance_AppCompat_Menu 0x7f0f0021
int style Base_TextAppearance_AppCompat_SearchResult 0x7f0f0022
int style Base_TextAppearance_AppCompat_SearchResult_Subtitle 0x7f0f0023
int style Base_TextAppearance_AppCompat_SearchResult_Title 0x7f0f0024
int style Base_TextAppearance_AppCompat_Small 0x7f0f0025
int style Base_TextAppearance_AppCompat_Small_Inverse 0x7f0f0026
int style Base_TextAppearance_AppCompat_Subhead 0x7f0f0027
int style Base_TextAppearance_AppCompat_Subhead_Inverse 0x7f0f0028
int style Base_TextAppearance_AppCompat_Title 0x7f0f0029
int style Base_TextAppearance_AppCompat_Title_Inverse 0x7f0f002a
int style Base_TextAppearance_AppCompat_Tooltip 0x7f0f002b
int style Base_TextAppearance_AppCompat_Widget_ActionBar_Menu 0x7f0f002c
int style Base_TextAppearance_AppCompat_Widget_ActionBar_Subtitle 0x7f0f002d
int style Base_TextAppearance_AppCompat_Widget_ActionBar_Subtitle_Inverse 0x7f0f002e
int style Base_TextAppearance_AppCompat_Widget_ActionBar_Title 0x7f0f002f
int style Base_TextAppearance_AppCompat_Widget_ActionBar_Title_Inverse 0x7f0f0030
int style Base_TextAppearance_AppCompat_Widget_ActionMode_Subtitle 0x7f0f0031
int style Base_TextAppearance_AppCompat_Widget_ActionMode_Title 0x7f0f0032
int style Base_TextAppearance_AppCompat_Widget_Button 0x7f0f0033
int style Base_TextAppearance_AppCompat_Widget_Button_Borderless_Colored 0x7f0f0034
int style Base_TextAppearance_AppCompat_Widget_Button_Colored 0x7f0f0035
int style Base_TextAppearance_AppCompat_Widget_Button_Inverse 0x7f0f0036
int style Base_TextAppearance_AppCompat_Widget_DropDownItem 0x7f0f0037
int style Base_TextAppearance_AppCompat_Widget_PopupMenu_Header 0x7f0f0038
int style Base_TextAppearance_AppCompat_Widget_PopupMenu_Large 0x7f0f0039
int style Base_TextAppearance_AppCompat_Widget_PopupMenu_Small 0x7f0f003a
int style Base_TextAppearance_AppCompat_Widget_Switch 0x7f0f003b
int style Base_TextAppearance_AppCompat_Widget_TextView_SpinnerItem 0x7f0f003c
int style Base_TextAppearance_Widget_AppCompat_ExpandedMenu_Item 0x7f0f003d
int style Base_TextAppearance_Widget_AppCompat_Toolbar_Subtitle 0x7f0f003e
int style Base_TextAppearance_Widget_AppCompat_Toolbar_Title 0x7f0f003f
int style Base_Theme_AppCompat 0x7f0f0040
int style Base_Theme_AppCompat_CompactMenu 0x7f0f0041
int style Base_Theme_AppCompat_Dialog 0x7f0f0042
int style Base_Theme_AppCompat_Dialog_Alert 0x7f0f0043
int style Base_Theme_AppCompat_Dialog_FixedSize 0x7f0f0044
int style Base_Theme_AppCompat_Dialog_MinWidth 0x7f0f0045
int style Base_Theme_AppCompat_DialogWhenLarge 0x7f0f0046
int style Base_Theme_AppCompat_Light 0x7f0f0047
int style Base_Theme_AppCompat_Light_DarkActionBar 0x7f0f0048
int style Base_Theme_AppCompat_Light_Dialog 0x7f0f0049
int style Base_Theme_AppCompat_Light_Dialog_Alert 0x7f0f004a
int style Base_Theme_AppCompat_Light_Dialog_FixedSize 0x7f0f004b
int style Base_Theme_AppCompat_Light_Dialog_MinWidth 0x7f0f004c
int style Base_Theme_AppCompat_Light_DialogWhenLarge 0x7f0f004d
int style Base_Theme_MaterialComponents 0x7f0f004e
int style Base_Theme_MaterialComponents_Bridge 0x7f0f004f
int style Base_Theme_MaterialComponents_CompactMenu 0x7f0f0050
int style Base_Theme_MaterialComponents_Dialog 0x7f0f0051
int style Base_Theme_MaterialComponents_Dialog_Alert 0x7f0f0052
int style Base_Theme_MaterialComponents_Dialog_FixedSize 0x7f0f0053
int style Base_Theme_MaterialComponents_Dialog_MinWidth 0x7f0f0054
int style Base_Theme_MaterialComponents_DialogWhenLarge 0x7f0f0055
int style Base_Theme_MaterialComponents_Light 0x7f0f0056
int style Base_Theme_MaterialComponents_Light_Bridge 0x7f0f0057
int style Base_Theme_MaterialComponents_Light_DarkActionBar 0x7f0f0058
int style Base_Theme_MaterialComponents_Light_DarkActionBar_Bridge 0x7f0f0059
int style Base_Theme_MaterialComponents_Light_Dialog 0x7f0f005a
int style Base_Theme_MaterialComponents_Light_Dialog_Alert 0x7f0f005b
int style Base_Theme_MaterialComponents_Light_Dialog_FixedSize 0x7f0f005c
int style Base_Theme_MaterialComponents_Light_Dialog_MinWidth 0x7f0f005d
int style Base_Theme_MaterialComponents_Light_DialogWhenLarge 0x7f0f005e
int style Base_ThemeOverlay_AppCompat 0x7f0f005f
int style Base_ThemeOverlay_AppCompat_ActionBar 0x7f0f0060
int style Base_ThemeOverlay_AppCompat_Dark 0x7f0f0061
int style Base_ThemeOverlay_AppCompat_Dark_ActionBar 0x7f0f0062
int style Base_ThemeOverlay_AppCompat_Dialog 0x7f0f0063
int style Base_ThemeOverlay_AppCompat_Dialog_Alert 0x7f0f0064
int style Base_ThemeOverlay_AppCompat_Light 0x7f0f0065
int style Base_ThemeOverlay_MaterialComponents_Dialog 0x7f0f0066
int style Base_ThemeOverlay_MaterialComponents_Dialog_Alert 0x7f0f0067
int style Base_V14_Theme_MaterialComponents 0x7f0f0068
int style Base_V14_Theme_MaterialComponents_Bridge 0x7f0f0069
int style Base_V14_Theme_MaterialComponents_Dialog 0x7f0f006a
int style Base_V14_Theme_MaterialComponents_Light 0x7f0f006b
int style Base_V14_Theme_MaterialComponents_Light_Bridge 0x7f0f006c
int style Base_V14_Theme_MaterialComponents_Light_DarkActionBar_Bridge 0x7f0f006d
int style Base_V14_Theme_MaterialComponents_Light_Dialog 0x7f0f006e
int style Base_V14_ThemeOverlay_MaterialComponents_Dialog 0x7f0f006f
int style Base_V14_ThemeOverlay_MaterialComponents_Dialog_Alert 0x7f0f0070
int style Base_V21_Theme_AppCompat 0x7f0f0071
int style Base_V21_Theme_AppCompat_Dialog 0x7f0f0072
int style Base_V21_Theme_AppCompat_Light 0x7f0f0073
int style Base_V21_Theme_AppCompat_Light_Dialog 0x7f0f0074
int style Base_V21_ThemeOverlay_AppCompat_Dialog 0x7f0f0075
int style Base_V22_Theme_AppCompat 0x7f0f0076
int style Base_V22_Theme_AppCompat_Light 0x7f0f0077
int style Base_V23_Theme_AppCompat 0x7f0f0078
int style Base_V23_Theme_AppCompat_Light 0x7f0f0079
int style Base_V26_Theme_AppCompat 0x7f0f007a
int style Base_V26_Theme_AppCompat_Light 0x7f0f007b
int style Base_V26_Widget_AppCompat_Toolbar 0x7f0f007c
int style Base_V28_Theme_AppCompat 0x7f0f007d
int style Base_V28_Theme_AppCompat_Light 0x7f0f007e
int style Base_V7_Theme_AppCompat 0x7f0f007f
int style Base_V7_Theme_AppCompat_Dialog 0x7f0f0080
int style Base_V7_Theme_AppCompat_Light 0x7f0f0081
int style Base_V7_Theme_AppCompat_Light_Dialog 0x7f0f0082
int style Base_V7_ThemeOverlay_AppCompat_Dialog 0x7f0f0083
int style Base_V7_Widget_AppCompat_AutoCompleteTextView 0x7f0f0084
int style Base_V7_Widget_AppCompat_EditText 0x7f0f0085
int style Base_V7_Widget_AppCompat_Toolbar 0x7f0f0086
int style Base_Widget_AppCompat_ActionBar 0x7f0f0087
int style Base_Widget_AppCompat_ActionBar_Solid 0x7f0f0088
int style Base_Widget_AppCompat_ActionBar_TabBar 0x7f0f0089
int style Base_Widget_AppCompat_ActionBar_TabText 0x7f0f008a
int style Base_Widget_AppCompat_ActionBar_TabView 0x7f0f008b
int style Base_Widget_AppCompat_ActionButton 0x7f0f008c
int style Base_Widget_AppCompat_ActionButton_CloseMode 0x7f0f008d
int style Base_Widget_AppCompat_ActionButton_Overflow 0x7f0f008e
int style Base_Widget_AppCompat_ActionMode 0x7f0f008f
int style Base_Widget_AppCompat_ActivityChooserView 0x7f0f0090
int style Base_Widget_AppCompat_AutoCompleteTextView 0x7f0f0091
int style Base_Widget_AppCompat_Button 0x7f0f0092
int style Base_Widget_AppCompat_Button_Borderless 0x7f0f0093
int style Base_Widget_AppCompat_Button_Borderless_Colored 0x7f0f0094
int style Base_Widget_AppCompat_Button_ButtonBar_AlertDialog 0x7f0f0095
int style Base_Widget_AppCompat_Button_Colored 0x7f0f0096
int style Base_Widget_AppCompat_Button_Small 0x7f0f0097
int style Base_Widget_AppCompat_ButtonBar 0x7f0f0098
int style Base_Widget_AppCompat_ButtonBar_AlertDialog 0x7f0f0099
int style Base_Widget_AppCompat_CompoundButton_CheckBox 0x7f0f009a
int style Base_Widget_AppCompat_CompoundButton_RadioButton 0x7f0f009b
int style Base_Widget_AppCompat_CompoundButton_Switch 0x7f0f009c
int style Base_Widget_AppCompat_DrawerArrowToggle 0x7f0f009d
int style Base_Widget_AppCompat_DrawerArrowToggle_Common 0x7f0f009e
int style Base_Widget_AppCompat_DropDownItem_Spinner 0x7f0f009f
int style Base_Widget_AppCompat_EditText 0x7f0f00a0
int style Base_Widget_AppCompat_ImageButton 0x7f0f00a1
int style Base_Widget_AppCompat_Light_ActionBar 0x7f0f00a2
int style Base_Widget_AppCompat_Light_ActionBar_Solid 0x7f0f00a3
int style Base_Widget_AppCompat_Light_ActionBar_TabBar 0x7f0f00a4
int style Base_Widget_AppCompat_Light_ActionBar_TabText 0x7f0f00a5
int style Base_Widget_AppCompat_Light_ActionBar_TabText_Inverse 0x7f0f00a6
int style Base_Widget_AppCompat_Light_ActionBar_TabView 0x7f0f00a7
int style Base_Widget_AppCompat_Light_PopupMenu 0x7f0f00a8
int style Base_Widget_AppCompat_Light_PopupMenu_Overflow 0x7f0f00a9
int style Base_Widget_AppCompat_ListMenuView 0x7f0f00aa
int style Base_Widget_AppCompat_ListPopupWindow 0x7f0f00ab
int style Base_Widget_AppCompat_ListView 0x7f0f00ac
int style Base_Widget_AppCompat_ListView_DropDown 0x7f0f00ad
int style Base_Widget_AppCompat_ListView_Menu 0x7f0f00ae
int style Base_Widget_AppCompat_PopupMenu 0x7f0f00af
int style Base_Widget_AppCompat_PopupMenu_Overflow 0x7f0f00b0
int style Base_Widget_AppCompat_PopupWindow 0x7f0f00b1
int style Base_Widget_AppCompat_ProgressBar 0x7f0f00b2
int style Base_Widget_AppCompat_ProgressBar_Horizontal 0x7f0f00b3
int style Base_Widget_AppCompat_RatingBar 0x7f0f00b4
int style Base_Widget_AppCompat_RatingBar_Indicator 0x7f0f00b5
int style Base_Widget_AppCompat_RatingBar_Small 0x7f0f00b6
int style Base_Widget_AppCompat_SearchView 0x7f0f00b7
int style Base_Widget_AppCompat_SearchView_ActionBar 0x7f0f00b8
int style Base_Widget_AppCompat_SeekBar 0x7f0f00b9
int style Base_Widget_AppCompat_SeekBar_Discrete 0x7f0f00ba
int style Base_Widget_AppCompat_Spinner 0x7f0f00bb
int style Base_Widget_AppCompat_Spinner_Underlined 0x7f0f00bc
int style Base_Widget_AppCompat_TextView_SpinnerItem 0x7f0f00bd
int style Base_Widget_AppCompat_Toolbar 0x7f0f00be
int style Base_Widget_AppCompat_Toolbar_Button_Navigation 0x7f0f00bf
int style Base_Widget_Design_TabLayout 0x7f0f00c0
int style Base_Widget_MaterialComponents_Chip 0x7f0f00c1
int style Base_Widget_MaterialComponents_TextInputEditText 0x7f0f00c2
int style Base_Widget_MaterialComponents_TextInputLayout 0x7f0f00c3
int style BeiZiAlertDialogStyle 0x7f0f00c4
int style BeiZiDialogStyle 0x7f0f00c5
int style BeiZiDownloadConfirmDialogAnimationRight 0x7f0f00c6
int style BeiZiDownloadConfirmDialogAnimationUp 0x7f0f00c7
int style BeiZiDownloadConfirmDialogFullScreen 0x7f0f00c8
int style BeiZiTheme 0x7f0f00c9
int style BeiZiTheme_Transparent 0x7f0f00ca
int style BottomAnimation 0x7f0f00cb
int style Bullet_Bottom_Dialog_Animation 0x7f0f00cc
int style CardView 0x7f0f00cd
int style CardView_Dark 0x7f0f00ce
int style CardView_Light 0x7f0f00cf
int style Dialog_BottomSheet_Transparent 0x7f0f00d0
int style DialogAnimationRight 0x7f0f00d1
int style DialogAnimationUp 0x7f0f00d2
int style DialogFullScreen 0x7f0f00d3
int style EC_Widget_Design_BottomSheet_Modal 0x7f0f00d4
int style ECBaseDialogFragmentAnimation 0x7f0f00d5
int style ECBottomInWindowAnimation 0x7f0f00d6
int style ECBottomOutWindowAnimation 0x7f0f00d7
int style ECHalfScreenAnchorV4Anime 0x7f0f00d8
int style ECSlideInWindowAnimation 0x7f0f00d9
int style ECSlideOutWindowAnimation 0x7f0f00da
int style EditTextStyle 0x7f0f00db
int style ExpandAnimation 0x7f0f00dc
int style LivePromotionNoAnimationStyle 0x7f0f00dd
int style ODDialogStyle 0x7f0f00de
int style ODFullscreen 0x7f0f00df
int style ODTabTextStyle 0x7f0f00e0
int style OSETDialogAnimation 0x7f0f00e1
int style OSETDialogStyle 0x7f0f00e2
int style Platform_AppCompat 0x7f0f00e3
int style Platform_AppCompat_Light 0x7f0f00e4
int style Platform_MaterialComponents 0x7f0f00e5
int style Platform_MaterialComponents_Dialog 0x7f0f00e6
int style Platform_MaterialComponents_Light 0x7f0f00e7
int style Platform_MaterialComponents_Light_Dialog 0x7f0f00e8
int style Platform_ThemeOverlay_AppCompat 0x7f0f00e9
int style Platform_ThemeOverlay_AppCompat_Dark 0x7f0f00ea
int style Platform_ThemeOverlay_AppCompat_Light 0x7f0f00eb
int style Platform_V21_AppCompat 0x7f0f00ec
int style Platform_V21_AppCompat_Light 0x7f0f00ed
int style Platform_V25_AppCompat 0x7f0f00ee
int style Platform_V25_AppCompat_Light 0x7f0f00ef
int style Platform_Widget_AppCompat_Spinner 0x7f0f00f0
int style PopupWindowFadeAnimationStyle 0x7f0f00f1
int style RtlOverlay_DialogWindowTitle_AppCompat 0x7f0f00f2
int style RtlOverlay_Widget_AppCompat_ActionBar_TitleItem 0x7f0f00f3
int style RtlOverlay_Widget_AppCompat_DialogTitle_Icon 0x7f0f00f4
int style RtlOverlay_Widget_AppCompat_PopupMenuItem 0x7f0f00f5
int style RtlOverlay_Widget_AppCompat_PopupMenuItem_InternalGroup 0x7f0f00f6
int style RtlOverlay_Widget_AppCompat_PopupMenuItem_Shortcut 0x7f0f00f7
int style RtlOverlay_Widget_AppCompat_PopupMenuItem_SubmenuArrow 0x7f0f00f8
int style RtlOverlay_Widget_AppCompat_PopupMenuItem_Text 0x7f0f00f9
int style RtlOverlay_Widget_AppCompat_PopupMenuItem_Title 0x7f0f00fa
int style RtlOverlay_Widget_AppCompat_Search_DropDown 0x7f0f00fb
int style RtlOverlay_Widget_AppCompat_Search_DropDown_Icon1 0x7f0f00fc
int style RtlOverlay_Widget_AppCompat_Search_DropDown_Icon2 0x7f0f00fd
int style RtlOverlay_Widget_AppCompat_Search_DropDown_Query 0x7f0f00fe
int style RtlOverlay_Widget_AppCompat_Search_DropDown_Text 0x7f0f00ff
int style RtlOverlay_Widget_AppCompat_SearchView_MagIcon 0x7f0f0100
int style RtlUnderlay_Widget_AppCompat_ActionButton 0x7f0f0101
int style RtlUnderlay_Widget_AppCompat_ActionButton_Overflow 0x7f0f0102
int style SKUPanelDialogAnimation 0x7f0f0103
int style SlideAnimation 0x7f0f0104
int style StoreAppBottomSheetStyle 0x7f0f0105
int style TextAppearance_AppCompat 0x7f0f0106
int style TextAppearance_AppCompat_Body1 0x7f0f0107
int style TextAppearance_AppCompat_Body2 0x7f0f0108
int style TextAppearance_AppCompat_Button 0x7f0f0109
int style TextAppearance_AppCompat_Caption 0x7f0f010a
int style TextAppearance_AppCompat_Display1 0x7f0f010b
int style TextAppearance_AppCompat_Display2 0x7f0f010c
int style TextAppearance_AppCompat_Display3 0x7f0f010d
int style TextAppearance_AppCompat_Display4 0x7f0f010e
int style TextAppearance_AppCompat_Headline 0x7f0f010f
int style TextAppearance_AppCompat_Inverse 0x7f0f0110
int style TextAppearance_AppCompat_Large 0x7f0f0111
int style TextAppearance_AppCompat_Large_Inverse 0x7f0f0112
int style TextAppearance_AppCompat_Light_SearchResult_Subtitle 0x7f0f0113
int style TextAppearance_AppCompat_Light_SearchResult_Title 0x7f0f0114
int style TextAppearance_AppCompat_Light_Widget_PopupMenu_Large 0x7f0f0115
int style TextAppearance_AppCompat_Light_Widget_PopupMenu_Small 0x7f0f0116
int style TextAppearance_AppCompat_Medium 0x7f0f0117
int style TextAppearance_AppCompat_Medium_Inverse 0x7f0f0118
int style TextAppearance_AppCompat_Menu 0x7f0f0119
int style TextAppearance_AppCompat_SearchResult_Subtitle 0x7f0f011a
int style TextAppearance_AppCompat_SearchResult_Title 0x7f0f011b
int style TextAppearance_AppCompat_Small 0x7f0f011c
int style TextAppearance_AppCompat_Small_Inverse 0x7f0f011d
int style TextAppearance_AppCompat_Subhead 0x7f0f011e
int style TextAppearance_AppCompat_Subhead_Inverse 0x7f0f011f
int style TextAppearance_AppCompat_Title 0x7f0f0120
int style TextAppearance_AppCompat_Title_Inverse 0x7f0f0121
int style TextAppearance_AppCompat_Tooltip 0x7f0f0122
int style TextAppearance_AppCompat_Widget_ActionBar_Menu 0x7f0f0123
int style TextAppearance_AppCompat_Widget_ActionBar_Subtitle 0x7f0f0124
int style TextAppearance_AppCompat_Widget_ActionBar_Subtitle_Inverse 0x7f0f0125
int style TextAppearance_AppCompat_Widget_ActionBar_Title 0x7f0f0126
int style TextAppearance_AppCompat_Widget_ActionBar_Title_Inverse 0x7f0f0127
int style TextAppearance_AppCompat_Widget_ActionMode_Subtitle 0x7f0f0128
int style TextAppearance_AppCompat_Widget_ActionMode_Subtitle_Inverse 0x7f0f0129
int style TextAppearance_AppCompat_Widget_ActionMode_Title 0x7f0f012a
int style TextAppearance_AppCompat_Widget_ActionMode_Title_Inverse 0x7f0f012b
int style TextAppearance_AppCompat_Widget_Button 0x7f0f012c
int style TextAppearance_AppCompat_Widget_Button_Borderless_Colored 0x7f0f012d
int style TextAppearance_AppCompat_Widget_Button_Colored 0x7f0f012e
int style TextAppearance_AppCompat_Widget_Button_Inverse 0x7f0f012f
int style TextAppearance_AppCompat_Widget_DropDownItem 0x7f0f0130
int style TextAppearance_AppCompat_Widget_PopupMenu_Header 0x7f0f0131
int style TextAppearance_AppCompat_Widget_PopupMenu_Large 0x7f0f0132
int style TextAppearance_AppCompat_Widget_PopupMenu_Small 0x7f0f0133
int style TextAppearance_AppCompat_Widget_Switch 0x7f0f0134
int style TextAppearance_AppCompat_Widget_TextView_SpinnerItem 0x7f0f0135
int style TextAppearance_Compat_Notification 0x7f0f0136
int style TextAppearance_Compat_Notification_Info 0x7f0f0137
int style TextAppearance_Compat_Notification_Info_Media 0x7f0f0138
int style TextAppearance_Compat_Notification_Line2 0x7f0f0139
int style TextAppearance_Compat_Notification_Line2_Media 0x7f0f013a
int style TextAppearance_Compat_Notification_Media 0x7f0f013b
int style TextAppearance_Compat_Notification_Time 0x7f0f013c
int style TextAppearance_Compat_Notification_Time_Media 0x7f0f013d
int style TextAppearance_Compat_Notification_Title 0x7f0f013e
int style TextAppearance_Compat_Notification_Title_Media 0x7f0f013f
int style TextAppearance_Design_CollapsingToolbar_Expanded 0x7f0f0140
int style TextAppearance_Design_Counter 0x7f0f0141
int style TextAppearance_Design_Counter_Overflow 0x7f0f0142
int style TextAppearance_Design_Error 0x7f0f0143
int style TextAppearance_Design_HelperText 0x7f0f0144
int style TextAppearance_Design_Hint 0x7f0f0145
int style TextAppearance_Design_Snackbar_Message 0x7f0f0146
int style TextAppearance_Design_Tab 0x7f0f0147
int style TextAppearance_MaterialComponents_Body1 0x7f0f0148
int style TextAppearance_MaterialComponents_Body2 0x7f0f0149
int style TextAppearance_MaterialComponents_Button 0x7f0f014a
int style TextAppearance_MaterialComponents_Caption 0x7f0f014b
int style TextAppearance_MaterialComponents_Chip 0x7f0f014c
int style TextAppearance_MaterialComponents_Headline1 0x7f0f014d
int style TextAppearance_MaterialComponents_Headline2 0x7f0f014e
int style TextAppearance_MaterialComponents_Headline3 0x7f0f014f
int style TextAppearance_MaterialComponents_Headline4 0x7f0f0150
int style TextAppearance_MaterialComponents_Headline5 0x7f0f0151
int style TextAppearance_MaterialComponents_Headline6 0x7f0f0152
int style TextAppearance_MaterialComponents_Overline 0x7f0f0153
int style TextAppearance_MaterialComponents_Subtitle1 0x7f0f0154
int style TextAppearance_MaterialComponents_Subtitle2 0x7f0f0155
int style TextAppearance_MaterialComponents_Tab 0x7f0f0156
int style TextAppearance_Widget_AppCompat_ExpandedMenu_Item 0x7f0f0157
int style TextAppearance_Widget_AppCompat_Toolbar_Subtitle 0x7f0f0158
int style TextAppearance_Widget_AppCompat_Toolbar_Title 0x7f0f0159
int style Theme_AppCompat 0x7f0f015a
int style Theme_AppCompat_CompactMenu 0x7f0f015b
int style Theme_AppCompat_DayNight 0x7f0f015c
int style Theme_AppCompat_DayNight_DarkActionBar 0x7f0f015d
int style Theme_AppCompat_DayNight_Dialog 0x7f0f015e
int style Theme_AppCompat_DayNight_Dialog_Alert 0x7f0f015f
int style Theme_AppCompat_DayNight_Dialog_MinWidth 0x7f0f0160
int style Theme_AppCompat_DayNight_DialogWhenLarge 0x7f0f0161
int style Theme_AppCompat_DayNight_NoActionBar 0x7f0f0162
int style Theme_AppCompat_Dialog 0x7f0f0163
int style Theme_AppCompat_Dialog_Alert 0x7f0f0164
int style Theme_AppCompat_Dialog_MinWidth 0x7f0f0165
int style Theme_AppCompat_DialogWhenLarge 0x7f0f0166
int style Theme_AppCompat_Light 0x7f0f0167
int style Theme_AppCompat_Light_DarkActionBar 0x7f0f0168
int style Theme_AppCompat_Light_Dialog 0x7f0f0169
int style Theme_AppCompat_Light_Dialog_Alert 0x7f0f016a
int style Theme_AppCompat_Light_Dialog_MinWidth 0x7f0f016b
int style Theme_AppCompat_Light_DialogWhenLarge 0x7f0f016c
int style Theme_AppCompat_Light_NoActionBar 0x7f0f016d
int style Theme_AppCompat_NoActionBar 0x7f0f016e
int style Theme_Design 0x7f0f016f
int style Theme_Design_BottomSheetDialog 0x7f0f0170
int style Theme_Design_Light 0x7f0f0171
int style Theme_Design_Light_BottomSheetDialog 0x7f0f0172
int style Theme_Design_Light_NoActionBar 0x7f0f0173
int style Theme_Design_NoActionBar 0x7f0f0174
int style Theme_Dialog_TTDownload 0x7f0f0175
int style Theme_Dialog_TTDownloadOld 0x7f0f0176
int style Theme_MaterialComponents 0x7f0f0177
int style Theme_MaterialComponents_BottomSheetDialog 0x7f0f0178
int style Theme_MaterialComponents_Bridge 0x7f0f0179
int style Theme_MaterialComponents_CompactMenu 0x7f0f017a
int style Theme_MaterialComponents_Dialog 0x7f0f017b
int style Theme_MaterialComponents_Dialog_Alert 0x7f0f017c
int style Theme_MaterialComponents_Dialog_MinWidth 0x7f0f017d
int style Theme_MaterialComponents_DialogWhenLarge 0x7f0f017e
int style Theme_MaterialComponents_Light 0x7f0f017f
int style Theme_MaterialComponents_Light_BottomSheetDialog 0x7f0f0180
int style Theme_MaterialComponents_Light_Bridge 0x7f0f0181
int style Theme_MaterialComponents_Light_DarkActionBar 0x7f0f0182
int style Theme_MaterialComponents_Light_DarkActionBar_Bridge 0x7f0f0183
int style Theme_MaterialComponents_Light_Dialog 0x7f0f0184
int style Theme_MaterialComponents_Light_Dialog_Alert 0x7f0f0185
int style Theme_MaterialComponents_Light_Dialog_MinWidth 0x7f0f0186
int style Theme_MaterialComponents_Light_DialogWhenLarge 0x7f0f0187
int style Theme_MaterialComponents_Light_NoActionBar 0x7f0f0188
int style Theme_MaterialComponents_Light_NoActionBar_Bridge 0x7f0f0189
int style Theme_MaterialComponents_NoActionBar 0x7f0f018a
int style Theme_MaterialComponents_NoActionBar_Bridge 0x7f0f018b
int style Theme_MountainSurvival 0x7f0f018c
int style ThemeOverlay_AppCompat 0x7f0f018d
int style ThemeOverlay_AppCompat_ActionBar 0x7f0f018e
int style ThemeOverlay_AppCompat_Dark 0x7f0f018f
int style ThemeOverlay_AppCompat_Dark_ActionBar 0x7f0f0190
int style ThemeOverlay_AppCompat_Dialog 0x7f0f0191
int style ThemeOverlay_AppCompat_Dialog_Alert 0x7f0f0192
int style ThemeOverlay_AppCompat_Light 0x7f0f0193
int style ThemeOverlay_MaterialComponents 0x7f0f0194
int style ThemeOverlay_MaterialComponents_ActionBar 0x7f0f0195
int style ThemeOverlay_MaterialComponents_Dark 0x7f0f0196
int style ThemeOverlay_MaterialComponents_Dark_ActionBar 0x7f0f0197
int style ThemeOverlay_MaterialComponents_Dialog 0x7f0f0198
int style ThemeOverlay_MaterialComponents_Dialog_Alert 0x7f0f0199
int style ThemeOverlay_MaterialComponents_Light 0x7f0f019a
int style ThemeOverlay_MaterialComponents_TextInputEditText 0x7f0f019b
int style ThemeOverlay_MaterialComponents_TextInputEditText_FilledBox 0x7f0f019c
int style ThemeOverlay_MaterialComponents_TextInputEditText_FilledBox_Dense 0x7f0f019d
int style ThemeOverlay_MaterialComponents_TextInputEditText_OutlinedBox 0x7f0f019e
int style ThemeOverlay_MaterialComponents_TextInputEditText_OutlinedBox_Dense 0x7f0f019f
int style TransparentDialogActivity 0x7f0f01a0
int style Widget_AppCompat_ActionBar 0x7f0f01a1
int style Widget_AppCompat_ActionBar_Solid 0x7f0f01a2
int style Widget_AppCompat_ActionBar_TabBar 0x7f0f01a3
int style Widget_AppCompat_ActionBar_TabText 0x7f0f01a4
int style Widget_AppCompat_ActionBar_TabView 0x7f0f01a5
int style Widget_AppCompat_ActionButton 0x7f0f01a6
int style Widget_AppCompat_ActionButton_CloseMode 0x7f0f01a7
int style Widget_AppCompat_ActionButton_Overflow 0x7f0f01a8
int style Widget_AppCompat_ActionMode 0x7f0f01a9
int style Widget_AppCompat_ActivityChooserView 0x7f0f01aa
int style Widget_AppCompat_AutoCompleteTextView 0x7f0f01ab
int style Widget_AppCompat_Button 0x7f0f01ac
int style Widget_AppCompat_Button_Borderless 0x7f0f01ad
int style Widget_AppCompat_Button_Borderless_Colored 0x7f0f01ae
int style Widget_AppCompat_Button_ButtonBar_AlertDialog 0x7f0f01af
int style Widget_AppCompat_Button_Colored 0x7f0f01b0
int style Widget_AppCompat_Button_Small 0x7f0f01b1
int style Widget_AppCompat_ButtonBar 0x7f0f01b2
int style Widget_AppCompat_ButtonBar_AlertDialog 0x7f0f01b3
int style Widget_AppCompat_CompoundButton_CheckBox 0x7f0f01b4
int style Widget_AppCompat_CompoundButton_RadioButton 0x7f0f01b5
int style Widget_AppCompat_CompoundButton_Switch 0x7f0f01b6
int style Widget_AppCompat_DrawerArrowToggle 0x7f0f01b7
int style Widget_AppCompat_DropDownItem_Spinner 0x7f0f01b8
int style Widget_AppCompat_EditText 0x7f0f01b9
int style Widget_AppCompat_ImageButton 0x7f0f01ba
int style Widget_AppCompat_Light_ActionBar 0x7f0f01bb
int style Widget_AppCompat_Light_ActionBar_Solid 0x7f0f01bc
int style Widget_AppCompat_Light_ActionBar_Solid_Inverse 0x7f0f01bd
int style Widget_AppCompat_Light_ActionBar_TabBar 0x7f0f01be
int style Widget_AppCompat_Light_ActionBar_TabBar_Inverse 0x7f0f01bf
int style Widget_AppCompat_Light_ActionBar_TabText 0x7f0f01c0
int style Widget_AppCompat_Light_ActionBar_TabText_Inverse 0x7f0f01c1
int style Widget_AppCompat_Light_ActionBar_TabView 0x7f0f01c2
int style Widget_AppCompat_Light_ActionBar_TabView_Inverse 0x7f0f01c3
int style Widget_AppCompat_Light_ActionButton 0x7f0f01c4
int style Widget_AppCompat_Light_ActionButton_CloseMode 0x7f0f01c5
int style Widget_AppCompat_Light_ActionButton_Overflow 0x7f0f01c6
int style Widget_AppCompat_Light_ActionMode_Inverse 0x7f0f01c7
int style Widget_AppCompat_Light_ActivityChooserView 0x7f0f01c8
int style Widget_AppCompat_Light_AutoCompleteTextView 0x7f0f01c9
int style Widget_AppCompat_Light_DropDownItem_Spinner 0x7f0f01ca
int style Widget_AppCompat_Light_ListPopupWindow 0x7f0f01cb
int style Widget_AppCompat_Light_ListView_DropDown 0x7f0f01cc
int style Widget_AppCompat_Light_PopupMenu 0x7f0f01cd
int style Widget_AppCompat_Light_PopupMenu_Overflow 0x7f0f01ce
int style Widget_AppCompat_Light_SearchView 0x7f0f01cf
int style Widget_AppCompat_Light_Spinner_DropDown_ActionBar 0x7f0f01d0
int style Widget_AppCompat_ListMenuView 0x7f0f01d1
int style Widget_AppCompat_ListPopupWindow 0x7f0f01d2
int style Widget_AppCompat_ListView 0x7f0f01d3
int style Widget_AppCompat_ListView_DropDown 0x7f0f01d4
int style Widget_AppCompat_ListView_Menu 0x7f0f01d5
int style Widget_AppCompat_PopupMenu 0x7f0f01d6
int style Widget_AppCompat_PopupMenu_Overflow 0x7f0f01d7
int style Widget_AppCompat_PopupWindow 0x7f0f01d8
int style Widget_AppCompat_ProgressBar 0x7f0f01d9
int style Widget_AppCompat_ProgressBar_Horizontal 0x7f0f01da
int style Widget_AppCompat_RatingBar 0x7f0f01db
int style Widget_AppCompat_RatingBar_Indicator 0x7f0f01dc
int style Widget_AppCompat_RatingBar_Small 0x7f0f01dd
int style Widget_AppCompat_SearchView 0x7f0f01de
int style Widget_AppCompat_SearchView_ActionBar 0x7f0f01df
int style Widget_AppCompat_SeekBar 0x7f0f01e0
int style Widget_AppCompat_SeekBar_Discrete 0x7f0f01e1
int style Widget_AppCompat_Spinner 0x7f0f01e2
int style Widget_AppCompat_Spinner_DropDown 0x7f0f01e3
int style Widget_AppCompat_Spinner_DropDown_ActionBar 0x7f0f01e4
int style Widget_AppCompat_Spinner_Underlined 0x7f0f01e5
int style Widget_AppCompat_TextView_SpinnerItem 0x7f0f01e6
int style Widget_AppCompat_Toolbar 0x7f0f01e7
int style Widget_AppCompat_Toolbar_Button_Navigation 0x7f0f01e8
int style Widget_Compat_NotificationActionContainer 0x7f0f01e9
int style Widget_Compat_NotificationActionText 0x7f0f01ea
int style Widget_Design_AppBarLayout 0x7f0f01eb
int style Widget_Design_BottomNavigationView 0x7f0f01ec
int style Widget_Design_BottomSheet_Modal 0x7f0f01ed
int style Widget_Design_CollapsingToolbar 0x7f0f01ee
int style Widget_Design_FloatingActionButton 0x7f0f01ef
int style Widget_Design_NavigationView 0x7f0f01f0
int style Widget_Design_ScrimInsetsFrameLayout 0x7f0f01f1
int style Widget_Design_Snackbar 0x7f0f01f2
int style Widget_Design_TabLayout 0x7f0f01f3
int style Widget_Design_TextInputLayout 0x7f0f01f4
int style Widget_MaterialComponents_BottomAppBar 0x7f0f01f5
int style Widget_MaterialComponents_BottomAppBar_Colored 0x7f0f01f6
int style Widget_MaterialComponents_BottomNavigationView 0x7f0f01f7
int style Widget_MaterialComponents_BottomNavigationView_Colored 0x7f0f01f8
int style Widget_MaterialComponents_BottomSheet_Modal 0x7f0f01f9
int style Widget_MaterialComponents_Button 0x7f0f01fa
int style Widget_MaterialComponents_Button_Icon 0x7f0f01fb
int style Widget_MaterialComponents_Button_OutlinedButton 0x7f0f01fc
int style Widget_MaterialComponents_Button_OutlinedButton_Icon 0x7f0f01fd
int style Widget_MaterialComponents_Button_TextButton 0x7f0f01fe
int style Widget_MaterialComponents_Button_TextButton_Dialog 0x7f0f01ff
int style Widget_MaterialComponents_Button_TextButton_Dialog_Icon 0x7f0f0200
int style Widget_MaterialComponents_Button_TextButton_Icon 0x7f0f0201
int style Widget_MaterialComponents_Button_UnelevatedButton 0x7f0f0202
int style Widget_MaterialComponents_Button_UnelevatedButton_Icon 0x7f0f0203
int style Widget_MaterialComponents_CardView 0x7f0f0204
int style Widget_MaterialComponents_Chip_Action 0x7f0f0205
int style Widget_MaterialComponents_Chip_Choice 0x7f0f0206
int style Widget_MaterialComponents_Chip_Entry 0x7f0f0207
int style Widget_MaterialComponents_Chip_Filter 0x7f0f0208
int style Widget_MaterialComponents_ChipGroup 0x7f0f0209
int style Widget_MaterialComponents_FloatingActionButton 0x7f0f020a
int style Widget_MaterialComponents_NavigationView 0x7f0f020b
int style Widget_MaterialComponents_Snackbar 0x7f0f020c
int style Widget_MaterialComponents_Snackbar_FullWidth 0x7f0f020d
int style Widget_MaterialComponents_TabLayout 0x7f0f020e
int style Widget_MaterialComponents_TabLayout_Colored 0x7f0f020f
int style Widget_MaterialComponents_TextInputEditText_FilledBox 0x7f0f0210
int style Widget_MaterialComponents_TextInputEditText_FilledBox_Dense 0x7f0f0211
int style Widget_MaterialComponents_TextInputEditText_OutlinedBox 0x7f0f0212
int style Widget_MaterialComponents_TextInputEditText_OutlinedBox_Dense 0x7f0f0213
int style Widget_MaterialComponents_TextInputLayout_FilledBox 0x7f0f0214
int style Widget_MaterialComponents_TextInputLayout_FilledBox_Dense 0x7f0f0215
int style Widget_MaterialComponents_TextInputLayout_OutlinedBox 0x7f0f0216
int style Widget_MaterialComponents_TextInputLayout_OutlinedBox_Dense 0x7f0f0217
int style Widget_MaterialComponents_Toolbar 0x7f0f0218
int style Widget_Support_CoordinatorLayout 0x7f0f0219
int style beizi_ad_custom_dialog 0x7f0f021a
int style beizi_custom_dialog 0x7f0f021b
int style bottom_sheet_anime 0x7f0f021c
int style commerce_dialog_dim_non_enter_animation 0x7f0f021d
int style ec_sku_prerender_dialog_anim 0x7f0f021e
int style ksad_LiveSubscriberAvatar 0x7f0f021f
int style ksad_RewardCardBtnInstall 0x7f0f0220
int style ksad_RewardCardTag 0x7f0f0221
int style ksad_RewardCardTagWhite 0x7f0f0222
int style line 0x7f0f0223
int style quick_option_dialog 0x7f0f0224
int style sig_base_theme 0x7f0f0225
int style sig_custom_dialog 0x7f0f0226
int style sig_custom_fullscreen_dialog 0x7f0f0227
int style sig_dialog_window_anim 0x7f0f0228
int style sig_land_theme 0x7f0f0229
int style sig_transparent_lang 0x7f0f022a
int style sig_transparent_style 0x7f0f022b
int style tt_Widget_ProgressBar_Horizontal 0x7f0f022c
int style tt_animation 0x7f0f022d
int style tt_appdownloader_style_detail_download_progress_bar 0x7f0f022e
int style tt_appdownloader_style_notification_text 0x7f0f022f
int style tt_appdownloader_style_notification_title 0x7f0f0230
int style tt_appdownloader_style_progress_bar 0x7f0f0231
int style tt_appdownloader_style_progress_bar_new 0x7f0f0232
int style tt_back_view 0x7f0f0233
int style tt_custom_dialog 0x7f0f0234
int style tt_dialog_full 0x7f0f0235
int style tt_full_screen 0x7f0f0236
int style tt_full_screen_interaction 0x7f0f0237
int style tt_full_screen_no_animation 0x7f0f0238
int style tt_landing_page 0x7f0f0239
int style tt_ss_popup_toast_anim 0x7f0f023a
int style tt_wg_insert_dialog 0x7f0f023b
int style tt_widget_gifView 0x7f0f023c
int style ttdownloader_translucent_dialog 0x7f0f023d
int[] styleable ActionBar { 0x7f030041, 0x7f030042, 0x7f030043, 0x7f0300ad, 0x7f0300ae, 0x7f0300af, 0x7f0300b0, 0x7f0300b1, 0x7f0300b2, 0x7f0300c0, 0x7f0300c5, 0x7f0300c6, 0x7f0300d1, 0x7f0300fd, 0x7f030102, 0x7f030107, 0x7f030108, 0x7f03010a, 0x7f030114, 0x7f03011e, 0x7f0301db, 0x7f0301e7, 0x7f030205, 0x7f030209, 0x7f03020a, 0x7f03023c, 0x7f03023f, 0x7f030285, 0x7f03028f }
int styleable ActionBar_background 0
int styleable ActionBar_backgroundSplit 1
int styleable ActionBar_backgroundStacked 2
int styleable ActionBar_contentInsetEnd 3
int styleable ActionBar_contentInsetEndWithActions 4
int styleable ActionBar_contentInsetLeft 5
int styleable ActionBar_contentInsetRight 6
int styleable ActionBar_contentInsetStart 7
int styleable ActionBar_contentInsetStartWithNavigation 8
int styleable ActionBar_customNavigationLayout 9
int styleable ActionBar_displayOptions 10
int styleable ActionBar_divider 11
int styleable ActionBar_elevation 12
int styleable ActionBar_height 13
int styleable ActionBar_hideOnContentScroll 14
int styleable ActionBar_homeAsUpIndicator 15
int styleable ActionBar_homeLayout 16
int styleable ActionBar_icon 17
int styleable ActionBar_indeterminateProgressStyle 18
int styleable ActionBar_itemPadding 19
int styleable ActionBar_logo 20
int styleable ActionBar_navigationMode 21
int styleable ActionBar_popupTheme 22
int styleable ActionBar_progressBarPadding 23
int styleable ActionBar_progressBarStyle 24
int styleable ActionBar_subtitle 25
int styleable ActionBar_subtitleTextStyle 26
int styleable ActionBar_title 27
int styleable ActionBar_titleTextStyle 28
int[] styleable ActionBarLayout { 0x010100b3 }
int styleable ActionBarLayout_android_layout_gravity 0
int[] styleable ActionMenuItemView { 0x0101013f }
int styleable ActionMenuItemView_android_minWidth 0
int[] styleable ActionMenuView { }
int[] styleable ActionMode { 0x7f030041, 0x7f030042, 0x7f030097, 0x7f0300fd, 0x7f03023f, 0x7f03028f }
int styleable ActionMode_background 0
int styleable ActionMode_backgroundSplit 1
int styleable ActionMode_closeItemLayout 2
int styleable ActionMode_height 3
int styleable ActionMode_subtitleTextStyle 4
int styleable ActionMode_titleTextStyle 5
int[] styleable ActivityChooserView { 0x7f0300d7, 0x7f030115 }
int styleable ActivityChooserView_expandActivityOverflowButtonDrawable 0
int styleable ActivityChooserView_initialActivityCount 1
int[] styleable AdSetSizeLimitView { 0x7f03002f, 0x7f030030 }
int styleable AdSetSizeLimitView_adSetMaxHeight 0
int styleable AdSetSizeLimitView_adSetMaxWidth 1
int[] styleable AdView { 0x7f030040, 0x7f03004f, 0x7f030051, 0x7f0300e0, 0x7f0301da, 0x7f0301ea, 0x7f030211, 0x7f03021d, 0x7f030223, 0x7f03025f, 0x7f030299, 0x7f03029a, 0x7f03029b, 0x7f03029e }
int styleable AdView_auto_refresh_interval 0
int styleable AdView_beizi_adSize 1
int styleable AdView_beizi_adUnitId 2
int styleable AdView_expands_to_fit_screen_width 3
int styleable AdView_load_landing_page_in_background 4
int styleable AdView_opens_native_browser 5
int styleable AdView_resize_ad_to_fit_container 6
int styleable AdView_should_reload_on_resume 7
int styleable AdView_show_loading_indicator 8
int styleable AdView_test 9
int styleable AdView_transition_direction 10
int styleable AdView_transition_duration 11
int styleable AdView_transition_type 12
int styleable AdView_video_scale_type 13
int[] styleable AlertDialog { 0x010100f2, 0x7f03006a, 0x7f03006b, 0x7f0301d1, 0x7f0301d2, 0x7f0301e4, 0x7f030222, 0x7f030225 }
int styleable AlertDialog_android_layout 0
int styleable AlertDialog_buttonIconDimen 1
int styleable AlertDialog_buttonPanelSideLayout 2
int styleable AlertDialog_listItemLayout 3
int styleable AlertDialog_listLayout 4
int styleable AlertDialog_multiChoiceItemLayout 5
int styleable AlertDialog_showTitle 6
int styleable AlertDialog_singleChoiceItemLayout 7
int[] styleable AnimatedStateListDrawableCompat { 0x0101011c, 0x01010194, 0x01010195, 0x01010196, 0x0101030c, 0x0101030d }
int styleable AnimatedStateListDrawableCompat_android_dither 0
int styleable AnimatedStateListDrawableCompat_android_visible 1
int styleable AnimatedStateListDrawableCompat_android_variablePadding 2
int styleable AnimatedStateListDrawableCompat_android_constantSize 3
int styleable AnimatedStateListDrawableCompat_android_enterFadeDuration 4
int styleable AnimatedStateListDrawableCompat_android_exitFadeDuration 5
int[] styleable AnimatedStateListDrawableItem { 0x010100d0, 0x01010199 }
int styleable AnimatedStateListDrawableItem_android_id 0
int styleable AnimatedStateListDrawableItem_android_drawable 1
int[] styleable AnimatedStateListDrawableTransition { 0x01010199, 0x01010449, 0x0101044a, 0x0101044b }
int styleable AnimatedStateListDrawableTransition_android_drawable 0
int styleable AnimatedStateListDrawableTransition_android_toId 1
int styleable AnimatedStateListDrawableTransition_android_fromId 2
int styleable AnimatedStateListDrawableTransition_android_reversible 3
int[] styleable AppBarLayout { 0x010100d4, 0x0101048f, 0x01010540, 0x7f0300d1, 0x7f0300d8, 0x7f0301cc }
int styleable AppBarLayout_android_background 0
int styleable AppBarLayout_android_touchscreenBlocksFocus 1
int styleable AppBarLayout_android_keyboardNavigationCluster 2
int styleable AppBarLayout_elevation 3
int styleable AppBarLayout_expanded 4
int styleable AppBarLayout_liftOnScroll 5
int[] styleable AppBarLayoutStates { 0x7f030232, 0x7f030233, 0x7f030234, 0x7f030235 }
int styleable AppBarLayoutStates_state_collapsed 0
int styleable AppBarLayoutStates_state_collapsible 1
int styleable AppBarLayoutStates_state_liftable 2
int styleable AppBarLayoutStates_state_lifted 3
int[] styleable AppBarLayout_Layout { 0x7f0301ca, 0x7f0301cb }
int styleable AppBarLayout_Layout_layout_scrollFlags 0
int styleable AppBarLayout_Layout_layout_scrollInterpolator 1
int[] styleable AppCompatImageView { 0x01010119, 0x7f03022f, 0x7f030283, 0x7f030284 }
int styleable AppCompatImageView_android_src 0
int styleable AppCompatImageView_srcCompat 1
int styleable AppCompatImageView_tint 2
int styleable AppCompatImageView_tintMode 3
int[] styleable AppCompatSeekBar { 0x01010142, 0x7f030280, 0x7f030281, 0x7f030282 }
int styleable AppCompatSeekBar_android_thumb 0
int styleable AppCompatSeekBar_tickMark 1
int styleable AppCompatSeekBar_tickMarkTint 2
int styleable AppCompatSeekBar_tickMarkTintMode 3
int[] styleable AppCompatTextHelper { 0x01010034, 0x0101016d, 0x0101016e, 0x0101016f, 0x01010170, 0x01010392, 0x01010393 }
int styleable AppCompatTextHelper_android_textAppearance 0
int styleable AppCompatTextHelper_android_drawableTop 1
int styleable AppCompatTextHelper_android_drawableBottom 2
int styleable AppCompatTextHelper_android_drawableLeft 3
int styleable AppCompatTextHelper_android_drawableRight 4
int styleable AppCompatTextHelper_android_drawableStart 5
int styleable AppCompatTextHelper_android_drawableEnd 6
int[] styleable AppCompatTextView { 0x01010034, 0x7f03003b, 0x7f03003c, 0x7f03003d, 0x7f03003e, 0x7f03003f, 0x7f0300ec, 0x7f0300ef, 0x7f03018d, 0x7f0301cd, 0x7f030260 }
int styleable AppCompatTextView_android_textAppearance 0
int styleable AppCompatTextView_autoSizeMaxTextSize 1
int styleable AppCompatTextView_autoSizeMinTextSize 2
int styleable AppCompatTextView_autoSizePresetSizes 3
int styleable AppCompatTextView_autoSizeStepGranularity 4
int styleable AppCompatTextView_autoSizeTextType 5
int styleable AppCompatTextView_firstBaselineToTopHeight 6
int styleable AppCompatTextView_fontFamily 7
int styleable AppCompatTextView_lastBaselineToBottomHeight 8
int styleable AppCompatTextView_lineHeight 9
int styleable AppCompatTextView_textAllCaps 10
int[] styleable AppCompatTheme { 0x01010057, 0x010100ae, 0x7f030007, 0x7f030008, 0x7f030009, 0x7f03000a, 0x7f03000b, 0x7f03000c, 0x7f03000d, 0x7f03000e, 0x7f03000f, 0x7f030010, 0x7f030011, 0x7f030012, 0x7f030013, 0x7f030015, 0x7f030016, 0x7f030017, 0x7f030018, 0x7f030019, 0x7f03001a, 0x7f03001b, 0x7f03001c, 0x7f03001d, 0x7f03001e, 0x7f03001f, 0x7f030020, 0x7f030021, 0x7f030022, 0x7f030023, 0x7f030024, 0x7f030025, 0x7f030028, 0x7f030031, 0x7f030032, 0x7f030033, 0x7f030034, 0x7f03003a, 0x7f030056, 0x7f030064, 0x7f030065, 0x7f030066, 0x7f030067, 0x7f030068, 0x7f03006c, 0x7f03006d, 0x7f030078, 0x7f03007d, 0x7f03009d, 0x7f03009e, 0x7f03009f, 0x7f0300a0, 0x7f0300a1, 0x7f0300a2, 0x7f0300a3, 0x7f0300a4, 0x7f0300a5, 0x7f0300a7, 0x7f0300b9, 0x7f0300c2, 0x7f0300c3, 0x7f0300c4, 0x7f0300c7, 0x7f0300c9, 0x7f0300cc, 0x7f0300cd, 0x7f0300ce, 0x7f0300cf, 0x7f0300d0, 0x7f030107, 0x7f030113, 0x7f0301cf, 0x7f0301d0, 0x7f0301d3, 0x7f0301d4, 0x7f0301d5, 0x7f0301d6, 0x7f0301d7, 0x7f0301d8, 0x7f0301d9, 0x7f0301fc, 0x7f0301fd, 0x7f0301fe, 0x7f030204, 0x7f030206, 0x7f03020d, 0x7f03020e, 0x7f03020f, 0x7f030210, 0x7f030219, 0x7f03021a, 0x7f03021b, 0x7f03021c, 0x7f03022c, 0x7f03022d, 0x7f030243, 0x7f03026b, 0x7f03026c, 0x7f03026d, 0x7f03026e, 0x7f030270, 0x7f030271, 0x7f030272, 0x7f030273, 0x7f030276, 0x7f030277, 0x7f030291, 0x7f030292, 0x7f030293, 0x7f030294, 0x7f03029f, 0x7f0302a1, 0x7f0302a2, 0x7f0302a3, 0x7f0302a4, 0x7f0302a5, 0x7f0302a6, 0x7f0302a7, 0x7f0302a8, 0x7f0302a9, 0x7f0302aa }
int styleable AppCompatTheme_android_windowIsFloating 0
int styleable AppCompatTheme_android_windowAnimationStyle 1
int styleable AppCompatTheme_actionBarDivider 2
int styleable AppCompatTheme_actionBarItemBackground 3
int styleable AppCompatTheme_actionBarPopupTheme 4
int styleable AppCompatTheme_actionBarSize 5
int styleable AppCompatTheme_actionBarSplitStyle 6
int styleable AppCompatTheme_actionBarStyle 7
int styleable AppCompatTheme_actionBarTabBarStyle 8
int styleable AppCompatTheme_actionBarTabStyle 9
int styleable AppCompatTheme_actionBarTabTextStyle 10
int styleable AppCompatTheme_actionBarTheme 11
int styleable AppCompatTheme_actionBarWidgetTheme 12
int styleable AppCompatTheme_actionButtonStyle 13
int styleable AppCompatTheme_actionDropDownStyle 14
int styleable AppCompatTheme_actionMenuTextAppearance 15
int styleable AppCompatTheme_actionMenuTextColor 16
int styleable AppCompatTheme_actionModeBackground 17
int styleable AppCompatTheme_actionModeCloseButtonStyle 18
int styleable AppCompatTheme_actionModeCloseDrawable 19
int styleable AppCompatTheme_actionModeCopyDrawable 20
int styleable AppCompatTheme_actionModeCutDrawable 21
int styleable AppCompatTheme_actionModeFindDrawable 22
int styleable AppCompatTheme_actionModePasteDrawable 23
int styleable AppCompatTheme_actionModePopupWindowStyle 24
int styleable AppCompatTheme_actionModeSelectAllDrawable 25
int styleable AppCompatTheme_actionModeShareDrawable 26
int styleable AppCompatTheme_actionModeSplitBackground 27
int styleable AppCompatTheme_actionModeStyle 28
int styleable AppCompatTheme_actionModeWebSearchDrawable 29
int styleable AppCompatTheme_actionOverflowButtonStyle 30
int styleable AppCompatTheme_actionOverflowMenuStyle 31
int styleable AppCompatTheme_activityChooserViewStyle 32
int styleable AppCompatTheme_alertDialogButtonGroupStyle 33
int styleable AppCompatTheme_alertDialogCenterButtons 34
int styleable AppCompatTheme_alertDialogStyle 35
int styleable AppCompatTheme_alertDialogTheme 36
int styleable AppCompatTheme_autoCompleteTextViewStyle 37
int styleable AppCompatTheme_borderlessButtonStyle 38
int styleable AppCompatTheme_buttonBarButtonStyle 39
int styleable AppCompatTheme_buttonBarNegativeButtonStyle 40
int styleable AppCompatTheme_buttonBarNeutralButtonStyle 41
int styleable AppCompatTheme_buttonBarPositiveButtonStyle 42
int styleable AppCompatTheme_buttonBarStyle 43
int styleable AppCompatTheme_buttonStyle 44
int styleable AppCompatTheme_buttonStyleSmall 45
int styleable AppCompatTheme_checkboxStyle 46
int styleable AppCompatTheme_checkedTextViewStyle 47
int styleable AppCompatTheme_colorAccent 48
int styleable AppCompatTheme_colorBackgroundFloating 49
int styleable AppCompatTheme_colorButtonNormal 50
int styleable AppCompatTheme_colorControlActivated 51
int styleable AppCompatTheme_colorControlHighlight 52
int styleable AppCompatTheme_colorControlNormal 53
int styleable AppCompatTheme_colorError 54
int styleable AppCompatTheme_colorPrimary 55
int styleable AppCompatTheme_colorPrimaryDark 56
int styleable AppCompatTheme_colorSwitchThumbNormal 57
int styleable AppCompatTheme_controlBackground 58
int styleable AppCompatTheme_dialogCornerRadius 59
int styleable AppCompatTheme_dialogPreferredPadding 60
int styleable AppCompatTheme_dialogTheme 61
int styleable AppCompatTheme_dividerHorizontal 62
int styleable AppCompatTheme_dividerVertical 63
int styleable AppCompatTheme_dropDownListViewStyle 64
int styleable AppCompatTheme_dropdownListPreferredItemHeight 65
int styleable AppCompatTheme_editTextBackground 66
int styleable AppCompatTheme_editTextColor 67
int styleable AppCompatTheme_editTextStyle 68
int styleable AppCompatTheme_homeAsUpIndicator 69
int styleable AppCompatTheme_imageButtonStyle 70
int styleable AppCompatTheme_listChoiceBackgroundIndicator 71
int styleable AppCompatTheme_listDividerAlertDialog 72
int styleable AppCompatTheme_listMenuViewStyle 73
int styleable AppCompatTheme_listPopupWindowStyle 74
int styleable AppCompatTheme_listPreferredItemHeight 75
int styleable AppCompatTheme_listPreferredItemHeightLarge 76
int styleable AppCompatTheme_listPreferredItemHeightSmall 77
int styleable AppCompatTheme_listPreferredItemPaddingLeft 78
int styleable AppCompatTheme_listPreferredItemPaddingRight 79
int styleable AppCompatTheme_panelBackground 80
int styleable AppCompatTheme_panelMenuListTheme 81
int styleable AppCompatTheme_panelMenuListWidth 82
int styleable AppCompatTheme_popupMenuStyle 83
int styleable AppCompatTheme_popupWindowStyle 84
int styleable AppCompatTheme_radioButtonStyle 85
int styleable AppCompatTheme_ratingBarStyle 86
int styleable AppCompatTheme_ratingBarStyleIndicator 87
int styleable AppCompatTheme_ratingBarStyleSmall 88
int styleable AppCompatTheme_searchViewStyle 89
int styleable AppCompatTheme_seekBarStyle 90
int styleable AppCompatTheme_selectableItemBackground 91
int styleable AppCompatTheme_selectableItemBackgroundBorderless 92
int styleable AppCompatTheme_spinnerDropDownItemStyle 93
int styleable AppCompatTheme_spinnerStyle 94
int styleable AppCompatTheme_switchStyle 95
int styleable AppCompatTheme_textAppearanceLargePopupMenu 96
int styleable AppCompatTheme_textAppearanceListItem 97
int styleable AppCompatTheme_textAppearanceListItemSecondary 98
int styleable AppCompatTheme_textAppearanceListItemSmall 99
int styleable AppCompatTheme_textAppearancePopupMenuHeader 100
int styleable AppCompatTheme_textAppearanceSearchResultSubtitle 101
int styleable AppCompatTheme_textAppearanceSearchResultTitle 102
int styleable AppCompatTheme_textAppearanceSmallPopupMenu 103
int styleable AppCompatTheme_textColorAlertDialogListItem 104
int styleable AppCompatTheme_textColorSearchUrl 105
int styleable AppCompatTheme_toolbarNavigationButtonStyle 106
int styleable AppCompatTheme_toolbarStyle 107
int styleable AppCompatTheme_tooltipForegroundColor 108
int styleable AppCompatTheme_tooltipFrameBackground 109
int styleable AppCompatTheme_viewInflaterClass 110
int styleable AppCompatTheme_windowActionBar 111
int styleable AppCompatTheme_windowActionBarOverlay 112
int styleable AppCompatTheme_windowActionModeOverlay 113
int styleable AppCompatTheme_windowFixedHeightMajor 114
int styleable AppCompatTheme_windowFixedHeightMinor 115
int styleable AppCompatTheme_windowFixedWidthMajor 116
int styleable AppCompatTheme_windowFixedWidthMinor 117
int styleable AppCompatTheme_windowMinWidthMajor 118
int styleable AppCompatTheme_windowMinWidthMinor 119
int styleable AppCompatTheme_windowNoTitle 120
int[] styleable BeiZiCircleProgressViewStyle { 0x7f030029, 0x7f03002a, 0x7f03002b, 0x7f03002c, 0x7f03002d, 0x7f03002e }
int styleable BeiZiCircleProgressViewStyle_adScopeCircleColor 0
int styleable BeiZiCircleProgressViewStyle_adScopeRadius 1
int styleable BeiZiCircleProgressViewStyle_adScopeRingBgColor 2
int styleable BeiZiCircleProgressViewStyle_adScopeRingColor 3
int styleable BeiZiCircleProgressViewStyle_adScopeStrokeWidth 4
int styleable BeiZiCircleProgressViewStyle_adScopeTextColor 5
int[] styleable BeiZi_BackArrowView { 0x7f030052, 0x7f030053, 0x7f030054 }
int styleable BeiZi_BackArrowView_beizi_bav_arrow_style 0
int styleable BeiZi_BackArrowView_beizi_bav_color 1
int styleable BeiZi_BackArrowView_beizi_bav_stroke_width 2
int[] styleable BottomAppBar { 0x7f030044, 0x7f0300e1, 0x7f0300e2, 0x7f0300e3, 0x7f0300e4, 0x7f030103 }
int styleable BottomAppBar_backgroundTint 0
int styleable BottomAppBar_fabAlignmentMode 1
int styleable BottomAppBar_fabCradleMargin 2
int styleable BottomAppBar_fabCradleRoundedCornerRadius 3
int styleable BottomAppBar_fabCradleVerticalOffset 4
int styleable BottomAppBar_hideOnScroll 5
int[] styleable BottomNavigationView { 0x7f0300d1, 0x7f030118, 0x7f03011a, 0x7f03011c, 0x7f03011d, 0x7f030121, 0x7f030122, 0x7f030123, 0x7f03018c, 0x7f0301e3 }
int styleable BottomNavigationView_elevation 0
int styleable BottomNavigationView_itemBackground 1
int styleable BottomNavigationView_itemHorizontalTranslationEnabled 2
int styleable BottomNavigationView_itemIconSize 3
int styleable BottomNavigationView_itemIconTint 4
int styleable BottomNavigationView_itemTextAppearanceActive 5
int styleable BottomNavigationView_itemTextAppearanceInactive 6
int styleable BottomNavigationView_itemTextColor 7
int styleable BottomNavigationView_labelVisibilityMode 8
int styleable BottomNavigationView_menu 9
int[] styleable BottomSheetBehavior_Layout { 0x7f03004a, 0x7f03004b, 0x7f03004d, 0x7f03004e }
int styleable BottomSheetBehavior_Layout_behavior_fitToContents 0
int styleable BottomSheetBehavior_Layout_behavior_hideable 1
int styleable BottomSheetBehavior_Layout_behavior_peekHeight 2
int styleable BottomSheetBehavior_Layout_behavior_skipCollapsed 3
int[] styleable ButtonBarLayout { 0x7f030035 }
int styleable ButtonBarLayout_allowStacking 0
int[] styleable CardView { 0x0101013f, 0x01010140, 0x7f030070, 0x7f030071, 0x7f030072, 0x7f030073, 0x7f030074, 0x7f030075, 0x7f0300b3, 0x7f0300b4, 0x7f0300b5, 0x7f0300b6, 0x7f0300b7 }
int styleable CardView_android_minWidth 0
int styleable CardView_android_minHeight 1
int styleable CardView_cardBackgroundColor 2
int styleable CardView_cardCornerRadius 3
int styleable CardView_cardElevation 4
int styleable CardView_cardMaxElevation 5
int styleable CardView_cardPreventCornerOverlap 6
int styleable CardView_cardUseCompatPadding 7
int styleable CardView_contentPadding 8
int styleable CardView_contentPaddingBottom 9
int styleable CardView_contentPaddingLeft 10
int styleable CardView_contentPaddingRight 11
int styleable CardView_contentPaddingTop 12
int[] styleable Chip { 0x01010034, 0x010100ab, 0x0101011f, 0x0101014f, 0x010101e5, 0x7f03007a, 0x7f03007b, 0x7f03007c, 0x7f03007e, 0x7f03007f, 0x7f030080, 0x7f030082, 0x7f030083, 0x7f030084, 0x7f030085, 0x7f030086, 0x7f030087, 0x7f03008c, 0x7f03008d, 0x7f03008e, 0x7f030090, 0x7f030091, 0x7f030092, 0x7f030093, 0x7f030094, 0x7f030095, 0x7f030096, 0x7f030101, 0x7f03010b, 0x7f03010f, 0x7f030213, 0x7f030220, 0x7f030278, 0x7f03027a }
int styleable Chip_android_textAppearance 0
int styleable Chip_android_ellipsize 1
int styleable Chip_android_maxWidth 2
int styleable Chip_android_text 3
int styleable Chip_android_checkable 4
int styleable Chip_checkedIcon 5
int styleable Chip_checkedIconEnabled 6
int styleable Chip_checkedIconVisible 7
int styleable Chip_chipBackgroundColor 8
int styleable Chip_chipCornerRadius 9
int styleable Chip_chipEndPadding 10
int styleable Chip_chipIcon 11
int styleable Chip_chipIconEnabled 12
int styleable Chip_chipIconSize 13
int styleable Chip_chipIconTint 14
int styleable Chip_chipIconVisible 15
int styleable Chip_chipMinHeight 16
int styleable Chip_chipStartPadding 17
int styleable Chip_chipStrokeColor 18
int styleable Chip_chipStrokeWidth 19
int styleable Chip_closeIcon 20
int styleable Chip_closeIconEnabled 21
int styleable Chip_closeIconEndPadding 22
int styleable Chip_closeIconSize 23
int styleable Chip_closeIconStartPadding 24
int styleable Chip_closeIconTint 25
int styleable Chip_closeIconVisible 26
int styleable Chip_hideMotionSpec 27
int styleable Chip_iconEndPadding 28
int styleable Chip_iconStartPadding 29
int styleable Chip_rippleColor 30
int styleable Chip_showMotionSpec 31
int styleable Chip_textEndPadding 32
int styleable Chip_textStartPadding 33
int[] styleable ChipGroup { 0x7f030079, 0x7f030088, 0x7f030089, 0x7f03008a, 0x7f030226, 0x7f030227 }
int styleable ChipGroup_checkedChip 0
int styleable ChipGroup_chipSpacing 1
int styleable ChipGroup_chipSpacingHorizontal 2
int styleable ChipGroup_chipSpacingVertical 3
int styleable ChipGroup_singleLine 4
int styleable ChipGroup_singleSelection 5
int[] styleable CollapsingToolbarLayout { 0x7f03009a, 0x7f03009b, 0x7f0300b8, 0x7f0300d9, 0x7f0300da, 0x7f0300db, 0x7f0300dc, 0x7f0300dd, 0x7f0300de, 0x7f0300df, 0x7f030214, 0x7f030216, 0x7f030237, 0x7f030285, 0x7f030286, 0x7f030290 }
int styleable CollapsingToolbarLayout_collapsedTitleGravity 0
int styleable CollapsingToolbarLayout_collapsedTitleTextAppearance 1
int styleable CollapsingToolbarLayout_contentScrim 2
int styleable CollapsingToolbarLayout_expandedTitleGravity 3
int styleable CollapsingToolbarLayout_expandedTitleMargin 4
int styleable CollapsingToolbarLayout_expandedTitleMarginBottom 5
int styleable CollapsingToolbarLayout_expandedTitleMarginEnd 6
int styleable CollapsingToolbarLayout_expandedTitleMarginStart 7
int styleable CollapsingToolbarLayout_expandedTitleMarginTop 8
int styleable CollapsingToolbarLayout_expandedTitleTextAppearance 9
int styleable CollapsingToolbarLayout_scrimAnimationDuration 10
int styleable CollapsingToolbarLayout_scrimVisibleHeightTrigger 11
int styleable CollapsingToolbarLayout_statusBarScrim 12
int styleable CollapsingToolbarLayout_title 13
int styleable CollapsingToolbarLayout_titleEnabled 14
int styleable CollapsingToolbarLayout_toolbarId 15
int[] styleable CollapsingToolbarLayout_Layout { 0x7f030193, 0x7f030194 }
int styleable CollapsingToolbarLayout_Layout_layout_collapseMode 0
int styleable CollapsingToolbarLayout_Layout_layout_collapseParallaxMultiplier 1
int[] styleable ColorStateListItem { 0x010101a5, 0x0101031f, 0x7f030036 }
int styleable ColorStateListItem_android_color 0
int styleable ColorStateListItem_android_alpha 1
int styleable ColorStateListItem_alpha 2
int[] styleable CompoundButton { 0x01010107, 0x7f03006e, 0x7f03006f }
int styleable CompoundButton_android_button 0
int styleable CompoundButton_buttonTint 1
int styleable CompoundButton_buttonTintMode 2
int[] styleable ConstraintLayout_Layout { 0x010100c4, 0x0101011f, 0x01010120, 0x0101013f, 0x01010140, 0x7f030047, 0x7f030048, 0x7f030077, 0x7f0300a9, 0x7f0300aa, 0x7f030195, 0x7f030196, 0x7f030197, 0x7f030198, 0x7f030199, 0x7f03019a, 0x7f03019b, 0x7f03019c, 0x7f03019d, 0x7f03019e, 0x7f03019f, 0x7f0301a0, 0x7f0301a1, 0x7f0301a2, 0x7f0301a3, 0x7f0301a4, 0x7f0301a5, 0x7f0301a6, 0x7f0301a7, 0x7f0301a8, 0x7f0301a9, 0x7f0301aa, 0x7f0301ab, 0x7f0301ac, 0x7f0301ad, 0x7f0301ae, 0x7f0301af, 0x7f0301b0, 0x7f0301b1, 0x7f0301b2, 0x7f0301b3, 0x7f0301b4, 0x7f0301b5, 0x7f0301b6, 0x7f0301b7, 0x7f0301b8, 0x7f0301b9, 0x7f0301ba, 0x7f0301bb, 0x7f0301bc, 0x7f0301bd, 0x7f0301bf, 0x7f0301c0, 0x7f0301c1, 0x7f0301c2, 0x7f0301c3, 0x7f0301c4, 0x7f0301c5, 0x7f0301c6, 0x7f0301c9 }
int styleable ConstraintLayout_Layout_android_orientation 0
int styleable ConstraintLayout_Layout_android_maxWidth 1
int styleable ConstraintLayout_Layout_android_maxHeight 2
int styleable ConstraintLayout_Layout_android_minWidth 3
int styleable ConstraintLayout_Layout_android_minHeight 4
int styleable ConstraintLayout_Layout_barrierAllowsGoneWidgets 5
int styleable ConstraintLayout_Layout_barrierDirection 6
int styleable ConstraintLayout_Layout_chainUseRtl 7
int styleable ConstraintLayout_Layout_constraintSet 8
int styleable ConstraintLayout_Layout_constraint_referenced_ids 9
int styleable ConstraintLayout_Layout_layout_constrainedHeight 10
int styleable ConstraintLayout_Layout_layout_constrainedWidth 11
int styleable ConstraintLayout_Layout_layout_constraintBaseline_creator 12
int styleable ConstraintLayout_Layout_layout_constraintBaseline_toBaselineOf 13
int styleable ConstraintLayout_Layout_layout_constraintBottom_creator 14
int styleable ConstraintLayout_Layout_layout_constraintBottom_toBottomOf 15
int styleable ConstraintLayout_Layout_layout_constraintBottom_toTopOf 16
int styleable ConstraintLayout_Layout_layout_constraintCircle 17
int styleable ConstraintLayout_Layout_layout_constraintCircleAngle 18
int styleable ConstraintLayout_Layout_layout_constraintCircleRadius 19
int styleable ConstraintLayout_Layout_layout_constraintDimensionRatio 20
int styleable ConstraintLayout_Layout_layout_constraintEnd_toEndOf 21
int styleable ConstraintLayout_Layout_layout_constraintEnd_toStartOf 22
int styleable ConstraintLayout_Layout_layout_constraintGuide_begin 23
int styleable ConstraintLayout_Layout_layout_constraintGuide_end 24
int styleable ConstraintLayout_Layout_layout_constraintGuide_percent 25
int styleable ConstraintLayout_Layout_layout_constraintHeight_default 26
int styleable ConstraintLayout_Layout_layout_constraintHeight_max 27
int styleable ConstraintLayout_Layout_layout_constraintHeight_min 28
int styleable ConstraintLayout_Layout_layout_constraintHeight_percent 29
int styleable ConstraintLayout_Layout_layout_constraintHorizontal_bias 30
int styleable ConstraintLayout_Layout_layout_constraintHorizontal_chainStyle 31
int styleable ConstraintLayout_Layout_layout_constraintHorizontal_weight 32
int styleable ConstraintLayout_Layout_layout_constraintLeft_creator 33
int styleable ConstraintLayout_Layout_layout_constraintLeft_toLeftOf 34
int styleable ConstraintLayout_Layout_layout_constraintLeft_toRightOf 35
int styleable ConstraintLayout_Layout_layout_constraintRight_creator 36
int styleable ConstraintLayout_Layout_layout_constraintRight_toLeftOf 37
int styleable ConstraintLayout_Layout_layout_constraintRight_toRightOf 38
int styleable ConstraintLayout_Layout_layout_constraintStart_toEndOf 39
int styleable ConstraintLayout_Layout_layout_constraintStart_toStartOf 40
int styleable ConstraintLayout_Layout_layout_constraintTop_creator 41
int styleable ConstraintLayout_Layout_layout_constraintTop_toBottomOf 42
int styleable ConstraintLayout_Layout_layout_constraintTop_toTopOf 43
int styleable ConstraintLayout_Layout_layout_constraintVertical_bias 44
int styleable ConstraintLayout_Layout_layout_constraintVertical_chainStyle 45
int styleable ConstraintLayout_Layout_layout_constraintVertical_weight 46
int styleable ConstraintLayout_Layout_layout_constraintWidth_default 47
int styleable ConstraintLayout_Layout_layout_constraintWidth_max 48
int styleable ConstraintLayout_Layout_layout_constraintWidth_min 49
int styleable ConstraintLayout_Layout_layout_constraintWidth_percent 50
int styleable ConstraintLayout_Layout_layout_editor_absoluteX 51
int styleable ConstraintLayout_Layout_layout_editor_absoluteY 52
int styleable ConstraintLayout_Layout_layout_goneMarginBottom 53
int styleable ConstraintLayout_Layout_layout_goneMarginEnd 54
int styleable ConstraintLayout_Layout_layout_goneMarginLeft 55
int styleable ConstraintLayout_Layout_layout_goneMarginRight 56
int styleable ConstraintLayout_Layout_layout_goneMarginStart 57
int styleable ConstraintLayout_Layout_layout_goneMarginTop 58
int styleable ConstraintLayout_Layout_layout_optimizationLevel 59
int[] styleable ConstraintLayout_placeholder { 0x7f0300ab, 0x7f0300d2 }
int styleable ConstraintLayout_placeholder_content 0
int styleable ConstraintLayout_placeholder_emptyVisibility 1
int[] styleable ConstraintSet { 0x010100c4, 0x010100d0, 0x010100dc, 0x010100f4, 0x010100f5, 0x010100f7, 0x010100f8, 0x010100f9, 0x010100fa, 0x0101011f, 0x01010120, 0x0101013f, 0x01010140, 0x0101031f, 0x01010320, 0x01010321, 0x01010322, 0x01010323, 0x01010324, 0x01010325, 0x01010326, 0x01010327, 0x01010328, 0x010103b5, 0x010103b6, 0x010103fa, 0x01010440, 0x7f030047, 0x7f030048, 0x7f030077, 0x7f0300aa, 0x7f030195, 0x7f030196, 0x7f030197, 0x7f030198, 0x7f030199, 0x7f03019a, 0x7f03019b, 0x7f03019c, 0x7f03019d, 0x7f03019e, 0x7f03019f, 0x7f0301a0, 0x7f0301a1, 0x7f0301a2, 0x7f0301a3, 0x7f0301a4, 0x7f0301a5, 0x7f0301a6, 0x7f0301a7, 0x7f0301a8, 0x7f0301a9, 0x7f0301aa, 0x7f0301ab, 0x7f0301ac, 0x7f0301ad, 0x7f0301ae, 0x7f0301af, 0x7f0301b0, 0x7f0301b1, 0x7f0301b2, 0x7f0301b3, 0x7f0301b4, 0x7f0301b5, 0x7f0301b6, 0x7f0301b7, 0x7f0301b8, 0x7f0301b9, 0x7f0301ba, 0x7f0301bb, 0x7f0301bc, 0x7f0301bd, 0x7f0301bf, 0x7f0301c0, 0x7f0301c1, 0x7f0301c2, 0x7f0301c3, 0x7f0301c4, 0x7f0301c5, 0x7f0301c6 }
int styleable ConstraintSet_android_orientation 0
int styleable ConstraintSet_android_id 1
int styleable ConstraintSet_android_visibility 2
int styleable ConstraintSet_android_layout_width 3
int styleable ConstraintSet_android_layout_height 4
int styleable ConstraintSet_android_layout_marginLeft 5
int styleable ConstraintSet_android_layout_marginTop 6
int styleable ConstraintSet_android_layout_marginRight 7
int styleable ConstraintSet_android_layout_marginBottom 8
int styleable ConstraintSet_android_maxWidth 9
int styleable ConstraintSet_android_maxHeight 10
int styleable ConstraintSet_android_minWidth 11
int styleable ConstraintSet_android_minHeight 12
int styleable ConstraintSet_android_alpha 13
int styleable ConstraintSet_android_transformPivotX 14
int styleable ConstraintSet_android_transformPivotY 15
int styleable ConstraintSet_android_translationX 16
int styleable ConstraintSet_android_translationY 17
int styleable ConstraintSet_android_scaleX 18
int styleable ConstraintSet_android_scaleY 19
int styleable ConstraintSet_android_rotation 20
int styleable ConstraintSet_android_rotationX 21
int styleable ConstraintSet_android_rotationY 22
int styleable ConstraintSet_android_layout_marginStart 23
int styleable ConstraintSet_android_layout_marginEnd 24
int styleable ConstraintSet_android_translationZ 25
int styleable ConstraintSet_android_elevation 26
int styleable ConstraintSet_barrierAllowsGoneWidgets 27
int styleable ConstraintSet_barrierDirection 28
int styleable ConstraintSet_chainUseRtl 29
int styleable ConstraintSet_constraint_referenced_ids 30
int styleable ConstraintSet_layout_constrainedHeight 31
int styleable ConstraintSet_layout_constrainedWidth 32
int styleable ConstraintSet_layout_constraintBaseline_creator 33
int styleable ConstraintSet_layout_constraintBaseline_toBaselineOf 34
int styleable ConstraintSet_layout_constraintBottom_creator 35
int styleable ConstraintSet_layout_constraintBottom_toBottomOf 36
int styleable ConstraintSet_layout_constraintBottom_toTopOf 37
int styleable ConstraintSet_layout_constraintCircle 38
int styleable ConstraintSet_layout_constraintCircleAngle 39
int styleable ConstraintSet_layout_constraintCircleRadius 40
int styleable ConstraintSet_layout_constraintDimensionRatio 41
int styleable ConstraintSet_layout_constraintEnd_toEndOf 42
int styleable ConstraintSet_layout_constraintEnd_toStartOf 43
int styleable ConstraintSet_layout_constraintGuide_begin 44
int styleable ConstraintSet_layout_constraintGuide_end 45
int styleable ConstraintSet_layout_constraintGuide_percent 46
int styleable ConstraintSet_layout_constraintHeight_default 47
int styleable ConstraintSet_layout_constraintHeight_max 48
int styleable ConstraintSet_layout_constraintHeight_min 49
int styleable ConstraintSet_layout_constraintHeight_percent 50
int styleable ConstraintSet_layout_constraintHorizontal_bias 51
int styleable ConstraintSet_layout_constraintHorizontal_chainStyle 52
int styleable ConstraintSet_layout_constraintHorizontal_weight 53
int styleable ConstraintSet_layout_constraintLeft_creator 54
int styleable ConstraintSet_layout_constraintLeft_toLeftOf 55
int styleable ConstraintSet_layout_constraintLeft_toRightOf 56
int styleable ConstraintSet_layout_constraintRight_creator 57
int styleable ConstraintSet_layout_constraintRight_toLeftOf 58
int styleable ConstraintSet_layout_constraintRight_toRightOf 59
int styleable ConstraintSet_layout_constraintStart_toEndOf 60
int styleable ConstraintSet_layout_constraintStart_toStartOf 61
int styleable ConstraintSet_layout_constraintTop_creator 62
int styleable ConstraintSet_layout_constraintTop_toBottomOf 63
int styleable ConstraintSet_layout_constraintTop_toTopOf 64
int styleable ConstraintSet_layout_constraintVertical_bias 65
int styleable ConstraintSet_layout_constraintVertical_chainStyle 66
int styleable ConstraintSet_layout_constraintVertical_weight 67
int styleable ConstraintSet_layout_constraintWidth_default 68
int styleable ConstraintSet_layout_constraintWidth_max 69
int styleable ConstraintSet_layout_constraintWidth_min 70
int styleable ConstraintSet_layout_constraintWidth_percent 71
int styleable ConstraintSet_layout_editor_absoluteX 72
int styleable ConstraintSet_layout_editor_absoluteY 73
int styleable ConstraintSet_layout_goneMarginBottom 74
int styleable ConstraintSet_layout_goneMarginEnd 75
int styleable ConstraintSet_layout_goneMarginLeft 76
int styleable ConstraintSet_layout_goneMarginRight 77
int styleable ConstraintSet_layout_goneMarginStart 78
int styleable ConstraintSet_layout_goneMarginTop 79
int[] styleable CoordinatorLayout { 0x7f030124, 0x7f030236 }
int styleable CoordinatorLayout_keylines 0
int styleable CoordinatorLayout_statusBarBackground 1
int[] styleable CoordinatorLayout_Layout { 0x010100b3, 0x7f030190, 0x7f030191, 0x7f030192, 0x7f0301be, 0x7f0301c7, 0x7f0301c8 }
int styleable CoordinatorLayout_Layout_android_layout_gravity 0
int styleable CoordinatorLayout_Layout_layout_anchor 1
int styleable CoordinatorLayout_Layout_layout_anchorGravity 2
int styleable CoordinatorLayout_Layout_layout_behavior 3
int styleable CoordinatorLayout_Layout_layout_dodgeInsetEdges 4
int styleable CoordinatorLayout_Layout_layout_insetEdge 5
int styleable CoordinatorLayout_Layout_layout_keyline 6
int[] styleable DesignTheme { 0x7f030059, 0x7f03005a }
int styleable DesignTheme_bottomSheetDialogTheme 0
int styleable DesignTheme_bottomSheetStyle 1
int[] styleable DrawerArrowToggle { 0x7f030038, 0x7f030039, 0x7f030046, 0x7f03009c, 0x7f0300ca, 0x7f0300fa, 0x7f03022b, 0x7f03027c }
int styleable DrawerArrowToggle_arrowHeadLength 0
int styleable DrawerArrowToggle_arrowShaftLength 1
int styleable DrawerArrowToggle_barLength 2
int styleable DrawerArrowToggle_color 3
int styleable DrawerArrowToggle_drawableSize 4
int styleable DrawerArrowToggle_gapBetweenBars 5
int styleable DrawerArrowToggle_spinBars 6
int styleable DrawerArrowToggle_thickness 7
int[] styleable FloatingActionButton { 0x7f030044, 0x7f030045, 0x7f030055, 0x7f0300d1, 0x7f0300e5, 0x7f0300e6, 0x7f030101, 0x7f030109, 0x7f0301e1, 0x7f030208, 0x7f030213, 0x7f030220, 0x7f03029d }
int styleable FloatingActionButton_backgroundTint 0
int styleable FloatingActionButton_backgroundTintMode 1
int styleable FloatingActionButton_borderWidth 2
int styleable FloatingActionButton_elevation 3
int styleable FloatingActionButton_fabCustomSize 4
int styleable FloatingActionButton_fabSize 5
int styleable FloatingActionButton_hideMotionSpec 6
int styleable FloatingActionButton_hoveredFocusedTranslationZ 7
int styleable FloatingActionButton_maxImageSize 8
int styleable FloatingActionButton_pressedTranslationZ 9
int styleable FloatingActionButton_rippleColor 10
int styleable FloatingActionButton_showMotionSpec 11
int styleable FloatingActionButton_useCompatPadding 12
int[] styleable FloatingActionButton_Behavior_Layout { 0x7f030049 }
int styleable FloatingActionButton_Behavior_Layout_behavior_autoHide 0
int[] styleable FlowLayout { 0x7f03011f, 0x7f0301ce }
int styleable FlowLayout_itemSpacing 0
int styleable FlowLayout_lineSpacing 1
int[] styleable FontFamily { 0x7f0300f0, 0x7f0300f1, 0x7f0300f2, 0x7f0300f3, 0x7f0300f4, 0x7f0300f5 }
int styleable FontFamily_fontProviderAuthority 0
int styleable FontFamily_fontProviderCerts 1
int styleable FontFamily_fontProviderFetchStrategy 2
int styleable FontFamily_fontProviderFetchTimeout 3
int styleable FontFamily_fontProviderPackage 4
int styleable FontFamily_fontProviderQuery 5
int[] styleable FontFamilyFont { 0x01010532, 0x01010533, 0x0101053f, 0x0101056f, 0x01010570, 0x7f0300ee, 0x7f0300f6, 0x7f0300f7, 0x7f0300f8, 0x7f03029c }
int styleable FontFamilyFont_android_font 0
int styleable FontFamilyFont_android_fontWeight 1
int styleable FontFamilyFont_android_fontStyle 2
int styleable FontFamilyFont_android_ttcIndex 3
int styleable FontFamilyFont_android_fontVariationSettings 4
int styleable FontFamilyFont_font 5
int styleable FontFamilyFont_fontStyle 6
int styleable FontFamilyFont_fontVariationSettings 7
int styleable FontFamilyFont_fontWeight 8
int styleable FontFamilyFont_ttcIndex 9
int[] styleable ForegroundLinearLayout { 0x01010109, 0x01010200, 0x7f0300f9 }
int styleable ForegroundLinearLayout_android_foreground 0
int styleable ForegroundLinearLayout_android_foregroundGravity 1
int styleable ForegroundLinearLayout_foregroundInsidePadding 2
int[] styleable GradientColor { 0x0101019d, 0x0101019e, 0x010101a1, 0x010101a2, 0x010101a3, 0x010101a4, 0x01010201, 0x0101020b, 0x01010510, 0x01010511, 0x01010512, 0x01010513 }
int styleable GradientColor_android_startColor 0
int styleable GradientColor_android_endColor 1
int styleable GradientColor_android_type 2
int styleable GradientColor_android_centerX 3
int styleable GradientColor_android_centerY 4
int styleable GradientColor_android_gradientRadius 5
int styleable GradientColor_android_tileMode 6
int styleable GradientColor_android_centerColor 7
int styleable GradientColor_android_startX 8
int styleable GradientColor_android_startY 9
int styleable GradientColor_android_endX 10
int styleable GradientColor_android_endY 11
int[] styleable GradientColorItem { 0x010101a5, 0x01010514 }
int styleable GradientColorItem_android_color 0
int styleable GradientColorItem_android_offset 1
int[] styleable LinearConstraintLayout { 0x010100c4 }
int styleable LinearConstraintLayout_android_orientation 0
int[] styleable LinearLayoutCompat { 0x010100af, 0x010100c4, 0x01010126, 0x01010127, 0x01010128, 0x7f0300c6, 0x7f0300c8, 0x7f0301e2, 0x7f03021f }
int styleable LinearLayoutCompat_android_gravity 0
int styleable LinearLayoutCompat_android_orientation 1
int styleable LinearLayoutCompat_android_baselineAligned 2
int styleable LinearLayoutCompat_android_baselineAlignedChildIndex 3
int styleable LinearLayoutCompat_android_weightSum 4
int styleable LinearLayoutCompat_divider 5
int styleable LinearLayoutCompat_dividerPadding 6
int styleable LinearLayoutCompat_measureWithLargestChild 7
int styleable LinearLayoutCompat_showDividers 8
int[] styleable LinearLayoutCompat_Layout { 0x010100b3, 0x010100f4, 0x010100f5, 0x01010181 }
int styleable LinearLayoutCompat_Layout_android_layout_gravity 0
int styleable LinearLayoutCompat_Layout_android_layout_width 1
int styleable LinearLayoutCompat_Layout_android_layout_height 2
int styleable LinearLayoutCompat_Layout_android_layout_weight 3
int[] styleable ListPopupWindow { 0x010102ac, 0x010102ad }
int styleable ListPopupWindow_android_dropDownHorizontalOffset 0
int styleable ListPopupWindow_android_dropDownVerticalOffset 1
int[] styleable MaterialButton { 0x010101b7, 0x010101b8, 0x010101b9, 0x010101ba, 0x7f030044, 0x7f030045, 0x7f0300bb, 0x7f03010a, 0x7f03010c, 0x7f03010d, 0x7f03010e, 0x7f030110, 0x7f030111, 0x7f030213, 0x7f030238, 0x7f030239 }
int styleable MaterialButton_android_insetLeft 0
int styleable MaterialButton_android_insetRight 1
int styleable MaterialButton_android_insetTop 2
int styleable MaterialButton_android_insetBottom 3
int styleable MaterialButton_backgroundTint 4
int styleable MaterialButton_backgroundTintMode 5
int styleable MaterialButton_cornerRadius 6
int styleable MaterialButton_icon 7
int styleable MaterialButton_iconGravity 8
int styleable MaterialButton_iconPadding 9
int styleable MaterialButton_iconSize 10
int styleable MaterialButton_iconTint 11
int styleable MaterialButton_iconTintMode 12
int styleable MaterialButton_rippleColor 13
int styleable MaterialButton_strokeColor 14
int styleable MaterialButton_strokeWidth 15
int[] styleable MaterialCardView { 0x7f030238, 0x7f030239 }
int styleable MaterialCardView_strokeColor 0
int styleable MaterialCardView_strokeWidth 1
int[] styleable MaterialComponentsTheme { 0x7f030059, 0x7f03005a, 0x7f030081, 0x7f03008b, 0x7f03008f, 0x7f03009d, 0x7f03009e, 0x7f0300a4, 0x7f0300a5, 0x7f0300a6, 0x7f0300d0, 0x7f0300ed, 0x7f0301dd, 0x7f0301de, 0x7f0301e8, 0x7f030215, 0x7f030228, 0x7f03025b, 0x7f030261, 0x7f030262, 0x7f030263, 0x7f030264, 0x7f030265, 0x7f030266, 0x7f030267, 0x7f030268, 0x7f030269, 0x7f03026a, 0x7f03026f, 0x7f030274, 0x7f030275, 0x7f030279 }
int styleable MaterialComponentsTheme_bottomSheetDialogTheme 0
int styleable MaterialComponentsTheme_bottomSheetStyle 1
int styleable MaterialComponentsTheme_chipGroupStyle 2
int styleable MaterialComponentsTheme_chipStandaloneStyle 3
int styleable MaterialComponentsTheme_chipStyle 4
int styleable MaterialComponentsTheme_colorAccent 5
int styleable MaterialComponentsTheme_colorBackgroundFloating 6
int styleable MaterialComponentsTheme_colorPrimary 7
int styleable MaterialComponentsTheme_colorPrimaryDark 8
int styleable MaterialComponentsTheme_colorSecondary 9
int styleable MaterialComponentsTheme_editTextStyle 10
int styleable MaterialComponentsTheme_floatingActionButtonStyle 11
int styleable MaterialComponentsTheme_materialButtonStyle 12
int styleable MaterialComponentsTheme_materialCardViewStyle 13
int styleable MaterialComponentsTheme_navigationViewStyle 14
int styleable MaterialComponentsTheme_scrimBackground 15
int styleable MaterialComponentsTheme_snackbarButtonStyle 16
int styleable MaterialComponentsTheme_tabStyle 17
int styleable MaterialComponentsTheme_textAppearanceBody1 18
int styleable MaterialComponentsTheme_textAppearanceBody2 19
int styleable MaterialComponentsTheme_textAppearanceButton 20
int styleable MaterialComponentsTheme_textAppearanceCaption 21
int styleable MaterialComponentsTheme_textAppearanceHeadline1 22
int styleable MaterialComponentsTheme_textAppearanceHeadline2 23
int styleable MaterialComponentsTheme_textAppearanceHeadline3 24
int styleable MaterialComponentsTheme_textAppearanceHeadline4 25
int styleable MaterialComponentsTheme_textAppearanceHeadline5 26
int styleable MaterialComponentsTheme_textAppearanceHeadline6 27
int styleable MaterialComponentsTheme_textAppearanceOverline 28
int styleable MaterialComponentsTheme_textAppearanceSubtitle1 29
int styleable MaterialComponentsTheme_textAppearanceSubtitle2 30
int styleable MaterialComponentsTheme_textInputStyle 31
int[] styleable MenuGroup { 0x0101000e, 0x010100d0, 0x01010194, 0x010101de, 0x010101df, 0x010101e0 }
int styleable MenuGroup_android_enabled 0
int styleable MenuGroup_android_id 1
int styleable MenuGroup_android_visible 2
int styleable MenuGroup_android_menuCategory 3
int styleable MenuGroup_android_orderInCategory 4
int styleable MenuGroup_android_checkableBehavior 5
int[] styleable MenuItem { 0x01010002, 0x0101000e, 0x010100d0, 0x01010106, 0x01010194, 0x010101de, 0x010101df, 0x010101e1, 0x010101e2, 0x010101e3, 0x010101e4, 0x010101e5, 0x0101026f, 0x7f030014, 0x7f030026, 0x7f030027, 0x7f030037, 0x7f0300ac, 0x7f030110, 0x7f030111, 0x7f0301e9, 0x7f03021e, 0x7f030295 }
int styleable MenuItem_android_icon 0
int styleable MenuItem_android_enabled 1
int styleable MenuItem_android_id 2
int styleable MenuItem_android_checked 3
int styleable MenuItem_android_visible 4
int styleable MenuItem_android_menuCategory 5
int styleable MenuItem_android_orderInCategory 6
int styleable MenuItem_android_title 7
int styleable MenuItem_android_titleCondensed 8
int styleable MenuItem_android_alphabeticShortcut 9
int styleable MenuItem_android_numericShortcut 10
int styleable MenuItem_android_checkable 11
int styleable MenuItem_android_onClick 12
int styleable MenuItem_actionLayout 13
int styleable MenuItem_actionProviderClass 14
int styleable MenuItem_actionViewClass 15
int styleable MenuItem_alphabeticModifiers 16
int styleable MenuItem_contentDescription 17
int styleable MenuItem_iconTint 18
int styleable MenuItem_iconTintMode 19
int styleable MenuItem_numericModifiers 20
int styleable MenuItem_showAsAction 21
int styleable MenuItem_tooltipText 22
int[] styleable MenuView { 0x010100ae, 0x0101012c, 0x0101012d, 0x0101012e, 0x0101012f, 0x01010130, 0x01010131, 0x7f030207, 0x7f03023a }
int styleable MenuView_android_windowAnimationStyle 0
int styleable MenuView_android_itemTextAppearance 1
int styleable MenuView_android_horizontalDivider 2
int styleable MenuView_android_verticalDivider 3
int styleable MenuView_android_headerBackground 4
int styleable MenuView_android_itemBackground 5
int styleable MenuView_android_itemIconDisabledAlpha 6
int styleable MenuView_preserveIconSpacing 7
int styleable MenuView_subMenuArrow 8
int[] styleable NavigationView { 0x010100d4, 0x010100dd, 0x0101011f, 0x7f0300d1, 0x7f0300fc, 0x7f030118, 0x7f030119, 0x7f03011b, 0x7f03011d, 0x7f030120, 0x7f030123, 0x7f0301e3 }
int styleable NavigationView_android_background 0
int styleable NavigationView_android_fitsSystemWindows 1
int styleable NavigationView_android_maxWidth 2
int styleable NavigationView_elevation 3
int styleable NavigationView_headerLayout 4
int styleable NavigationView_itemBackground 5
int styleable NavigationView_itemHorizontalPadding 6
int styleable NavigationView_itemIconPadding 7
int styleable NavigationView_itemIconTint 8
int styleable NavigationView_itemTextAppearance 9
int styleable NavigationView_itemTextColor 10
int styleable NavigationView_menu 11
int[] styleable OSETCircularProgressView { 0x7f030000, 0x7f030001, 0x7f030002, 0x7f030003, 0x7f030004, 0x7f030005, 0x7f030006 }
int styleable OSETCircularProgressView_OSETbackColor 0
int styleable OSETCircularProgressView_OSETbackWidth 1
int styleable OSETCircularProgressView_OSETprogColor 2
int styleable OSETCircularProgressView_OSETprogFirstColor 3
int styleable OSETCircularProgressView_OSETprogStartColor 4
int styleable OSETCircularProgressView_OSETprogWidth 5
int styleable OSETCircularProgressView_OSETprogress 6
int[] styleable OSETRoundedImageView { 0x0101011d, 0x7f0301eb, 0x7f0301ec, 0x7f0301ed, 0x7f0301ee, 0x7f0301ef, 0x7f0301f0, 0x7f0301f1, 0x7f0301f2, 0x7f0301f3, 0x7f0301f4, 0x7f0301f5, 0x7f0301f6 }
int styleable OSETRoundedImageView_android_scaleType 0
int styleable OSETRoundedImageView_oset_riv_border_color 1
int styleable OSETRoundedImageView_oset_riv_border_width 2
int styleable OSETRoundedImageView_oset_riv_corner_radius 3
int styleable OSETRoundedImageView_oset_riv_corner_radius_bottom_left 4
int styleable OSETRoundedImageView_oset_riv_corner_radius_bottom_right 5
int styleable OSETRoundedImageView_oset_riv_corner_radius_top_left 6
int styleable OSETRoundedImageView_oset_riv_corner_radius_top_right 7
int styleable OSETRoundedImageView_oset_riv_mutate_background 8
int styleable OSETRoundedImageView_oset_riv_oval 9
int styleable OSETRoundedImageView_oset_riv_tile_mode 10
int styleable OSETRoundedImageView_oset_riv_tile_mode_x 11
int styleable OSETRoundedImageView_oset_riv_tile_mode_y 12
int[] styleable PopupWindow { 0x01010176, 0x010102c9, 0x7f0301f7 }
int styleable PopupWindow_android_popupBackground 0
int styleable PopupWindow_android_popupAnimationStyle 1
int styleable PopupWindow_overlapAnchor 2
int[] styleable PopupWindowBackgroundState { 0x7f030231 }
int styleable PopupWindowBackgroundState_state_above_anchor 0
int[] styleable RecycleListView { 0x7f0301f8, 0x7f0301fb }
int styleable RecycleListView_paddingBottomNoButtons 0
int styleable RecycleListView_paddingTopNoTitle 1
int[] styleable RecyclerView { 0x010100c4, 0x010100f1, 0x7f0300e7, 0x7f0300e8, 0x7f0300e9, 0x7f0300ea, 0x7f0300eb, 0x7f03018f, 0x7f030212, 0x7f03022a, 0x7f030230 }
int styleable RecyclerView_android_orientation 0
int styleable RecyclerView_android_descendantFocusability 1
int styleable RecyclerView_fastScrollEnabled 2
int styleable RecyclerView_fastScrollHorizontalThumbDrawable 3
int styleable RecyclerView_fastScrollHorizontalTrackDrawable 4
int styleable RecyclerView_fastScrollVerticalThumbDrawable 5
int styleable RecyclerView_fastScrollVerticalTrackDrawable 6
int styleable RecyclerView_layoutManager 7
int styleable RecyclerView_reverseLayout 8
int styleable RecyclerView_spanCount 9
int styleable RecyclerView_stackFromEnd 10
int[] styleable ScrimInsetsFrameLayout { 0x7f030116 }
int styleable ScrimInsetsFrameLayout_insetForeground 0
int[] styleable ScrollingViewBehavior_Layout { 0x7f03004c }
int styleable ScrollingViewBehavior_Layout_behavior_overlapTop 0
int[] styleable SearchView { 0x010100da, 0x0101011f, 0x01010220, 0x01010264, 0x7f030090, 0x7f0300a8, 0x7f0300c1, 0x7f0300fb, 0x7f030112, 0x7f03018e, 0x7f03020b, 0x7f03020c, 0x7f030217, 0x7f030218, 0x7f03023b, 0x7f030240, 0x7f0302a0 }
int styleable SearchView_android_focusable 0
int styleable SearchView_android_maxWidth 1
int styleable SearchView_android_inputType 2
int styleable SearchView_android_imeOptions 3
int styleable SearchView_closeIcon 4
int styleable SearchView_commitIcon 5
int styleable SearchView_defaultQueryHint 6
int styleable SearchView_goIcon 7
int styleable SearchView_iconifiedByDefault 8
int styleable SearchView_layout 9
int styleable SearchView_queryBackground 10
int styleable SearchView_queryHint 11
int styleable SearchView_searchHintIcon 12
int styleable SearchView_searchIcon 13
int styleable SearchView_submitBackground 14
int styleable SearchView_suggestionRowLayout 15
int styleable SearchView_voiceIcon 16
int[] styleable SigAdInfoView { 0x7f030224 }
int styleable SigAdInfoView_sig_isSmall 0
int[] styleable Snackbar { 0x7f030228, 0x7f030229 }
int styleable Snackbar_snackbarButtonStyle 0
int styleable Snackbar_snackbarStyle 1
int[] styleable SnackbarLayout { 0x0101011f, 0x7f0300d1, 0x7f0301df }
int styleable SnackbarLayout_android_maxWidth 0
int styleable SnackbarLayout_elevation 1
int styleable SnackbarLayout_maxActionInlineWidth 2
int[] styleable Spinner { 0x010100b2, 0x01010176, 0x0101017b, 0x01010262, 0x7f030205 }
int styleable Spinner_android_entries 0
int styleable Spinner_android_popupBackground 1
int styleable Spinner_android_prompt 2
int styleable Spinner_android_dropDownWidth 3
int styleable Spinner_popupTheme 4
int[] styleable StateListDrawable { 0x0101011c, 0x01010194, 0x01010195, 0x01010196, 0x0101030c, 0x0101030d }
int styleable StateListDrawable_android_dither 0
int styleable StateListDrawable_android_visible 1
int styleable StateListDrawable_android_variablePadding 2
int styleable StateListDrawable_android_constantSize 3
int styleable StateListDrawable_android_enterFadeDuration 4
int styleable StateListDrawable_android_exitFadeDuration 5
int[] styleable StateListDrawableItem { 0x01010199 }
int styleable StateListDrawableItem_android_drawable 0
int[] styleable SwitchCompat { 0x01010124, 0x01010125, 0x01010142, 0x7f030221, 0x7f03022e, 0x7f030241, 0x7f030242, 0x7f030244, 0x7f03027d, 0x7f03027e, 0x7f03027f, 0x7f030296, 0x7f030297, 0x7f030298 }
int styleable SwitchCompat_android_textOn 0
int styleable SwitchCompat_android_textOff 1
int styleable SwitchCompat_android_thumb 2
int styleable SwitchCompat_showText 3
int styleable SwitchCompat_splitTrack 4
int styleable SwitchCompat_switchMinWidth 5
int styleable SwitchCompat_switchPadding 6
int styleable SwitchCompat_switchTextAppearance 7
int styleable SwitchCompat_thumbTextPadding 8
int styleable SwitchCompat_thumbTint 9
int styleable SwitchCompat_thumbTintMode 10
int styleable SwitchCompat_track 11
int styleable SwitchCompat_trackTint 12
int styleable SwitchCompat_trackTintMode 13
int[] styleable TabItem { 0x01010002, 0x010100f2, 0x0101014f }
int styleable TabItem_android_icon 0
int styleable TabItem_android_layout 1
int styleable TabItem_android_text 2
int[] styleable TabLayout { 0x7f030245, 0x7f030246, 0x7f030247, 0x7f030248, 0x7f030249, 0x7f03024a, 0x7f03024b, 0x7f03024c, 0x7f03024d, 0x7f03024e, 0x7f03024f, 0x7f030250, 0x7f030251, 0x7f030252, 0x7f030253, 0x7f030254, 0x7f030255, 0x7f030256, 0x7f030257, 0x7f030258, 0x7f030259, 0x7f03025a, 0x7f03025c, 0x7f03025d, 0x7f03025e }
int styleable TabLayout_tabBackground 0
int styleable TabLayout_tabContentStart 1
int styleable TabLayout_tabGravity 2
int styleable TabLayout_tabIconTint 3
int styleable TabLayout_tabIconTintMode 4
int styleable TabLayout_tabIndicator 5
int styleable TabLayout_tabIndicatorAnimationDuration 6
int styleable TabLayout_tabIndicatorColor 7
int styleable TabLayout_tabIndicatorFullWidth 8
int styleable TabLayout_tabIndicatorGravity 9
int styleable TabLayout_tabIndicatorHeight 10
int styleable TabLayout_tabInlineLabel 11
int styleable TabLayout_tabMaxWidth 12
int styleable TabLayout_tabMinWidth 13
int styleable TabLayout_tabMode 14
int styleable TabLayout_tabPadding 15
int styleable TabLayout_tabPaddingBottom 16
int styleable TabLayout_tabPaddingEnd 17
int styleable TabLayout_tabPaddingStart 18
int styleable TabLayout_tabPaddingTop 19
int styleable TabLayout_tabRippleColor 20
int styleable TabLayout_tabSelectedTextColor 21
int styleable TabLayout_tabTextAppearance 22
int styleable TabLayout_tabTextColor 23
int styleable TabLayout_tabUnboundedRipple 24
int[] styleable TextAppearance { 0x01010095, 0x01010096, 0x01010097, 0x01010098, 0x0101009a, 0x0101009b, 0x01010161, 0x01010162, 0x01010163, 0x01010164, 0x010103ac, 0x7f0300ef, 0x7f030260 }
int styleable TextAppearance_android_textSize 0
int styleable TextAppearance_android_typeface 1
int styleable TextAppearance_android_textStyle 2
int styleable TextAppearance_android_textColor 3
int styleable TextAppearance_android_textColorHint 4
int styleable TextAppearance_android_textColorLink 5
int styleable TextAppearance_android_shadowColor 6
int styleable TextAppearance_android_shadowDx 7
int styleable TextAppearance_android_shadowDy 8
int styleable TextAppearance_android_shadowRadius 9
int styleable TextAppearance_android_fontFamily 10
int styleable TextAppearance_fontFamily 11
int styleable TextAppearance_textAllCaps 12
int[] styleable TextInputLayout { 0x0101009a, 0x01010150, 0x7f03005b, 0x7f03005c, 0x7f03005d, 0x7f03005e, 0x7f03005f, 0x7f030060, 0x7f030061, 0x7f030062, 0x7f030063, 0x7f0300bc, 0x7f0300bd, 0x7f0300be, 0x7f0300bf, 0x7f0300d5, 0x7f0300d6, 0x7f0300fe, 0x7f0300ff, 0x7f030100, 0x7f030104, 0x7f030105, 0x7f030106, 0x7f0301ff, 0x7f030200, 0x7f030201, 0x7f030202, 0x7f030203 }
int styleable TextInputLayout_android_textColorHint 0
int styleable TextInputLayout_android_hint 1
int styleable TextInputLayout_boxBackgroundColor 2
int styleable TextInputLayout_boxBackgroundMode 3
int styleable TextInputLayout_boxCollapsedPaddingTop 4
int styleable TextInputLayout_boxCornerRadiusBottomEnd 5
int styleable TextInputLayout_boxCornerRadiusBottomStart 6
int styleable TextInputLayout_boxCornerRadiusTopEnd 7
int styleable TextInputLayout_boxCornerRadiusTopStart 8
int styleable TextInputLayout_boxStrokeColor 9
int styleable TextInputLayout_boxStrokeWidth 10
int styleable TextInputLayout_counterEnabled 11
int styleable TextInputLayout_counterMaxLength 12
int styleable TextInputLayout_counterOverflowTextAppearance 13
int styleable TextInputLayout_counterTextAppearance 14
int styleable TextInputLayout_errorEnabled 15
int styleable TextInputLayout_errorTextAppearance 16
int styleable TextInputLayout_helperText 17
int styleable TextInputLayout_helperTextEnabled 18
int styleable TextInputLayout_helperTextTextAppearance 19
int styleable TextInputLayout_hintAnimationEnabled 20
int styleable TextInputLayout_hintEnabled 21
int styleable TextInputLayout_hintTextAppearance 22
int styleable TextInputLayout_passwordToggleContentDescription 23
int styleable TextInputLayout_passwordToggleDrawable 24
int styleable TextInputLayout_passwordToggleEnabled 25
int styleable TextInputLayout_passwordToggleTint 26
int styleable TextInputLayout_passwordToggleTintMode 27
int[] styleable ThemeEnforcement { 0x01010034, 0x7f0300d3, 0x7f0300d4 }
int styleable ThemeEnforcement_android_textAppearance 0
int styleable ThemeEnforcement_enforceMaterialTheme 1
int styleable ThemeEnforcement_enforceTextAppearance 2
int[] styleable Toolbar { 0x010100af, 0x01010140, 0x7f030069, 0x7f030098, 0x7f030099, 0x7f0300ad, 0x7f0300ae, 0x7f0300af, 0x7f0300b0, 0x7f0300b1, 0x7f0300b2, 0x7f0301db, 0x7f0301dc, 0x7f0301e0, 0x7f0301e5, 0x7f0301e6, 0x7f030205, 0x7f03023c, 0x7f03023d, 0x7f03023e, 0x7f030285, 0x7f030287, 0x7f030288, 0x7f030289, 0x7f03028a, 0x7f03028b, 0x7f03028c, 0x7f03028d, 0x7f03028e }
int styleable Toolbar_android_gravity 0
int styleable Toolbar_android_minHeight 1
int styleable Toolbar_buttonGravity 2
int styleable Toolbar_collapseContentDescription 3
int styleable Toolbar_collapseIcon 4
int styleable Toolbar_contentInsetEnd 5
int styleable Toolbar_contentInsetEndWithActions 6
int styleable Toolbar_contentInsetLeft 7
int styleable Toolbar_contentInsetRight 8
int styleable Toolbar_contentInsetStart 9
int styleable Toolbar_contentInsetStartWithNavigation 10
int styleable Toolbar_logo 11
int styleable Toolbar_logoDescription 12
int styleable Toolbar_maxButtonHeight 13
int styleable Toolbar_navigationContentDescription 14
int styleable Toolbar_navigationIcon 15
int styleable Toolbar_popupTheme 16
int styleable Toolbar_subtitle 17
int styleable Toolbar_subtitleTextAppearance 18
int styleable Toolbar_subtitleTextColor 19
int styleable Toolbar_title 20
int styleable Toolbar_titleMargin 21
int styleable Toolbar_titleMarginBottom 22
int styleable Toolbar_titleMarginEnd 23
int styleable Toolbar_titleMarginStart 24
int styleable Toolbar_titleMarginTop 25
int styleable Toolbar_titleMargins 26
int styleable Toolbar_titleTextAppearance 27
int styleable Toolbar_titleTextColor 28
int[] styleable View { 0x01010000, 0x010100da, 0x7f0301f9, 0x7f0301fa, 0x7f03027b }
int styleable View_android_theme 0
int styleable View_android_focusable 1
int styleable View_paddingEnd 2
int styleable View_paddingStart 3
int styleable View_theme 4
int[] styleable ViewBackgroundHelper { 0x010100d4, 0x7f030044, 0x7f030045 }
int styleable ViewBackgroundHelper_android_background 0
int styleable ViewBackgroundHelper_backgroundTint 1
int styleable ViewBackgroundHelper_backgroundTintMode 2
int[] styleable ViewStubCompat { 0x010100d0, 0x010100f2, 0x010100f3 }
int styleable ViewStubCompat_android_id 0
int styleable ViewStubCompat_android_layout 1
int styleable ViewStubCompat_android_inflatedId 2
int[] styleable ksad_ComplianceTextView { 0x7f03015c, 0x7f030165, 0x7f03018b }
int styleable ksad_ComplianceTextView_ksad_privacy_color 0
int styleable ksad_ComplianceTextView_ksad_show_clickable_underline 1
int styleable ksad_ComplianceTextView_ksad_width_in_landscape 2
int[] styleable ksad_DividerView { 0x7f03013d, 0x7f03013e, 0x7f03013f, 0x7f030140, 0x7f030158 }
int styleable ksad_DividerView_ksad_color 0
int styleable ksad_DividerView_ksad_dashGap 1
int styleable ksad_DividerView_ksad_dashLength 2
int styleable ksad_DividerView_ksad_dashThickness 3
int styleable ksad_DividerView_ksad_orientation 4
int[] styleable ksad_DownloadProgressView { 0x7f030139, 0x7f030146, 0x7f030147, 0x7f030148, 0x7f030149, 0x7f03014a, 0x7f03015d }
int styleable ksad_DownloadProgressView_ksad_backgroundDrawable 0
int styleable ksad_DownloadProgressView_ksad_downloadLeftTextColor 1
int styleable ksad_DownloadProgressView_ksad_downloadRightTextColor 2
int styleable ksad_DownloadProgressView_ksad_downloadTextColor 3
int styleable ksad_DownloadProgressView_ksad_downloadTextSize 4
int styleable ksad_DownloadProgressView_ksad_downloadingFormat 5
int styleable ksad_DownloadProgressView_ksad_progressDrawable 6
int[] styleable ksad_JinniuCouponLayout { 0x7f030159, 0x7f03018a }
int styleable ksad_JinniuCouponLayout_ksad_outerRadius 0
int styleable ksad_JinniuCouponLayout_ksad_verticalRadius 1
int[] styleable ksad_KSCornerImageView { 0x7f03013a, 0x7f030156, 0x7f030160, 0x7f030187 }
int styleable ksad_KSCornerImageView_ksad_bottomLeftCorner 0
int styleable ksad_KSCornerImageView_ksad_leftTopCorner 1
int styleable ksad_KSCornerImageView_ksad_rightBottomCorner 2
int styleable ksad_KSCornerImageView_ksad_topRightCorner 3
int[] styleable ksad_KSCouponLabelTextView { 0x7f030155, 0x7f030166, 0x7f03016f, 0x7f030170 }
int styleable ksad_KSCouponLabelTextView_ksad_labelRadius 0
int styleable ksad_KSCouponLabelTextView_ksad_sideRadius 1
int styleable ksad_KSCouponLabelTextView_ksad_strokeColor 2
int styleable ksad_KSCouponLabelTextView_ksad_strokeSize 3
int[] styleable ksad_KSLayout { 0x7f03013c, 0x7f03015e, 0x7f03015f }
int styleable ksad_KSLayout_ksad_clipBackground 0
int styleable ksad_KSLayout_ksad_radius 1
int styleable ksad_KSLayout_ksad_ratio 2
int[] styleable ksad_KSRatingBar { 0x7f03013b, 0x7f03014f, 0x7f030168, 0x7f030169, 0x7f03016a, 0x7f03016b, 0x7f03016c, 0x7f03016d, 0x7f03016e, 0x7f030188 }
int styleable ksad_KSRatingBar_ksad_clickable 0
int styleable ksad_KSRatingBar_ksad_halfstart 1
int styleable ksad_KSRatingBar_ksad_starCount 2
int styleable ksad_KSRatingBar_ksad_starEmpty 3
int styleable ksad_KSRatingBar_ksad_starFill 4
int styleable ksad_KSRatingBar_ksad_starHalf 5
int styleable ksad_KSRatingBar_ksad_starImageHeight 6
int styleable ksad_KSRatingBar_ksad_starImagePadding 7
int styleable ksad_KSRatingBar_ksad_starImageWidth 8
int styleable ksad_KSRatingBar_ksad_totalStarCount 9
int[] styleable ksad_KsRadiusStrokeTextView { 0x7f030174, 0x7f030175, 0x7f030176, 0x7f030177, 0x7f030178, 0x7f030179, 0x7f03017a, 0x7f03017b, 0x7f03017c, 0x7f03017d, 0x7f03017e, 0x7f03017f, 0x7f030180, 0x7f030181, 0x7f030182, 0x7f030184, 0x7f030185 }
int styleable ksad_KsRadiusStrokeTextView_ksad_textDrawable 0
int styleable ksad_KsRadiusStrokeTextView_ksad_textIsSelected 1
int styleable ksad_KsRadiusStrokeTextView_ksad_textLeftBottomRadius 2
int styleable ksad_KsRadiusStrokeTextView_ksad_textLeftTopRadius 3
int styleable ksad_KsRadiusStrokeTextView_ksad_textNoBottomStroke 4
int styleable ksad_KsRadiusStrokeTextView_ksad_textNoLeftStroke 5
int styleable ksad_KsRadiusStrokeTextView_ksad_textNoRightStroke 6
int styleable ksad_KsRadiusStrokeTextView_ksad_textNoTopStroke 7
int styleable ksad_KsRadiusStrokeTextView_ksad_textNormalSolidColor 8
int styleable ksad_KsRadiusStrokeTextView_ksad_textNormalTextColor 9
int styleable ksad_KsRadiusStrokeTextView_ksad_textPressedSolidColor 10
int styleable ksad_KsRadiusStrokeTextView_ksad_textRadius 11
int styleable ksad_KsRadiusStrokeTextView_ksad_textRightBottomRadius 12
int styleable ksad_KsRadiusStrokeTextView_ksad_textRightTopRadius 13
int styleable ksad_KsRadiusStrokeTextView_ksad_textSelectedTextColor 14
int styleable ksad_KsRadiusStrokeTextView_ksad_textStrokeColor 15
int styleable ksad_KsRadiusStrokeTextView_ksad_textStrokeWidth 16
int[] styleable ksad_KsShadowImageView { 0x7f03014b, 0x7f03014c, 0x7f03014d, 0x7f03014e, 0x7f030161, 0x7f030162 }
int styleable ksad_KsShadowImageView_ksad_enableBottomShadow 0
int styleable ksad_KsShadowImageView_ksad_enableLeftShadow 1
int styleable ksad_KsShadowImageView_ksad_enableRightShadow 2
int styleable ksad_KsShadowImageView_ksad_enableTopShadow 3
int styleable ksad_KsShadowImageView_ksad_shadowColor 4
int styleable ksad_KsShadowImageView_ksad_shadowSize 5
int[] styleable ksad_KsShakeView { 0x7f030151, 0x7f030152, 0x7f030153, 0x7f03015a, 0x7f03015b, 0x7f030163, 0x7f030164, 0x7f030167 }
int styleable ksad_KsShakeView_ksad_innerCirclePadding 0
int styleable ksad_KsShakeView_ksad_innerCircleStrokeColor 1
int styleable ksad_KsShakeView_ksad_innerCircleStrokeWidth 2
int styleable ksad_KsShakeView_ksad_outerStrokeColor 3
int styleable ksad_KsShakeView_ksad_outerStrokeWidth 4
int styleable ksad_KsShakeView_ksad_shakeIcon 5
int styleable ksad_KsShakeView_ksad_shakeViewStyle 6
int styleable ksad_KsShakeView_ksad_solidColor 7
int[] styleable ksad_KsVerticalMarqueeTextView { 0x7f030138, 0x7f030157, 0x7f030171, 0x7f030172, 0x7f030173, 0x7f030183, 0x7f030186, 0x7f030189 }
int styleable ksad_KsVerticalMarqueeTextView_ksad_autoStartMarquee 0
int styleable ksad_KsVerticalMarqueeTextView_ksad_marqueeSpeed 1
int styleable ksad_KsVerticalMarqueeTextView_ksad_text 2
int styleable ksad_KsVerticalMarqueeTextView_ksad_textAppearance 3
int styleable ksad_KsVerticalMarqueeTextView_ksad_textColor 4
int styleable ksad_KsVerticalMarqueeTextView_ksad_textSize 5
int styleable ksad_KsVerticalMarqueeTextView_ksad_textStyle 6
int styleable ksad_KsVerticalMarqueeTextView_ksad_typeface 7
int[] styleable ksad_SeekBar { 0x7f030125, 0x7f030126, 0x7f030127, 0x7f030128, 0x7f030129, 0x7f03012a, 0x7f03012b, 0x7f03012c, 0x7f03012d, 0x7f03012e, 0x7f03012f, 0x7f030130, 0x7f030131, 0x7f030132, 0x7f030133, 0x7f030134, 0x7f030135, 0x7f030136, 0x7f030137 }
int styleable ksad_SeekBar_ksad_SeekBarBackground 0
int styleable ksad_SeekBar_ksad_SeekBarDefaultIndicator 1
int styleable ksad_SeekBar_ksad_SeekBarDefaultIndicatorPass 2
int styleable ksad_SeekBar_ksad_SeekBarDisplayProgressText 3
int styleable ksad_SeekBar_ksad_SeekBarHeight 4
int styleable ksad_SeekBar_ksad_SeekBarLimitProgressText100 5
int styleable ksad_SeekBar_ksad_SeekBarPaddingBottom 6
int styleable ksad_SeekBar_ksad_SeekBarPaddingLeft 7
int styleable ksad_SeekBar_ksad_SeekBarPaddingRight 8
int styleable ksad_SeekBar_ksad_SeekBarPaddingTop 9
int styleable ksad_SeekBar_ksad_SeekBarProgress 10
int styleable ksad_SeekBar_ksad_SeekBarProgressTextColor 11
int styleable ksad_SeekBar_ksad_SeekBarProgressTextMargin 12
int styleable ksad_SeekBar_ksad_SeekBarProgressTextSize 13
int styleable ksad_SeekBar_ksad_SeekBarRadius 14
int styleable ksad_SeekBar_ksad_SeekBarSecondProgress 15
int styleable ksad_SeekBar_ksad_SeekBarShowProgressText 16
int styleable ksad_SeekBar_ksad_SeekBarThumb 17
int styleable ksad_SeekBar_ksad_SeekBarWidth 18
int[] styleable ksad_SlideTipsView { 0x7f030154 }
int styleable ksad_SlideTipsView_ksad_is_left_slide 0
int[] styleable ksad_ViewPagerIndicator { 0x7f030141, 0x7f030142, 0x7f030143, 0x7f030144, 0x7f030145, 0x7f030150 }
int styleable ksad_ViewPagerIndicator_ksad_default_color 0
int styleable ksad_ViewPagerIndicator_ksad_dot_distance 1
int styleable ksad_ViewPagerIndicator_ksad_dot_height 2
int styleable ksad_ViewPagerIndicator_ksad_dot_selected_width 3
int styleable ksad_ViewPagerIndicator_ksad_dot_unselected_width 4
int styleable ksad_ViewPagerIndicator_ksad_height_color 5
int xml backup_rules 0x7f110000
int xml beizi_file_path 0x7f110001
int xml data_extraction_rules 0x7f110002
int xml file_paths 0x7f110003
int xml gdt_file_path 0x7f110004
int xml ksad_file_paths 0x7f110005
int xml network_security_config 0x7f110006
int xml oset_file_paths 0x7f110007
int xml oset_filepath 0x7f110008
int xml oset_gdt_filepaths 0x7f110009
int xml oset_gm_filepaths 0x7f11000a
int xml sigmob_provider_paths 0x7f11000b
