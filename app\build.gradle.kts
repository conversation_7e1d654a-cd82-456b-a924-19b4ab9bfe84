plugins {
    alias(libs.plugins.android.application)
    alias(libs.plugins.kotlin.android)
}

android {
    namespace = "com.ainative.mountainsurvival"
    compileSdk = 35

    defaultConfig {
        applicationId = "com.ainative.mountainsurvival"
        minSdk = 24
        targetSdk = 35
        versionCode = 3
        versionName = "1.2.0"

        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
        multiDexEnabled = true
    }

    // 确保AAR文件被正确处理
    repositories {
        flatDir {
            dirs("libs")
        }
        // GroMore
        maven { url = uri("https://artifact.bytedance.com/repository/Volcengine/") }
        maven { url = uri("https://artifact.bytedance.com/repository/pangle") }
        // 荣耀
        maven { url = uri("https://developer.hihonor.com/repo") }
        // adset
        maven {
            isAllowInsecureProtocol = true
            url = uri("http://maven.shenshiads.com/nexus/repository/adset/")
        }
    }

    signingConfigs {
        create("release") {
            storeFile = file("E:/Ai/AiCode/game/miyao.jks")
            storePassword = "nihaoshijie233"
            keyAlias = "mountainsurvival"
            keyPassword = "js42fiqn"
            // 启用V1和V2签名
            enableV1Signing = true
            enableV2Signing = true
        }
    }

    buildTypes {
        debug {
            isMinifyEnabled = false
            isDebuggable = true
            // Debug版本不使用混淆，加快编译速度
        }

        release {
            // 禁用混淆和资源压缩（保留混淆配置文件以备后用）
            isMinifyEnabled = false
            isShrinkResources = false
            // 保留ProGuard配置文件，如需重新启用混淆，将上面两行改为true即可
            proguardFiles(

                "proguard-ignore-warnings.pro",
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro"
            )
            signingConfig = signingConfigs.getByName("release")

            // R8优化配置，减少wind-sdk警告
            isDebuggable = false
            isJniDebuggable = false
            isPseudoLocalesEnabled = false
        }
    }

    // 编译优化选项
    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_11
        targetCompatibility = JavaVersion.VERSION_11
    }

    kotlinOptions {
        jvmTarget = "11"
    }
    buildFeatures {
        viewBinding = true
    }
}

dependencies {

    implementation("com.google.code.gson:gson:2.9.0")

    // Support库依赖
    implementation("com.android.support:appcompat-v7:28.0.0")
    implementation("com.android.support.constraint:constraint-layout:1.1.3")
    implementation("com.android.support:support-v4:28.0.0")
    implementation("com.android.support:design:28.0.0")
    implementation("com.android.support:recyclerview-v7:28.0.0")

    // 神蓍广告SDK - 使用AAR方式
    implementation(fileTree(mapOf("dir" to "libs", "include" to listOf("*.aar"))))

    // 广告SDK必需的第三方依赖
    implementation("com.android.support:appcompat-v7:28.0.0")
    implementation("com.android.support:recyclerview-v7:28.0.0")
    implementation("com.android.support:design:28.0.0")
    implementation("com.squareup.okhttp3:okhttp:4.12.0")
    implementation("io.github.aliyun-sls:aliyun-log-android-sdk:2.7.0")
    implementation("com.hihonor.mcs:ads-identifier:1.0.2.301")



    // 多dex支持
    implementation("com.android.support:multidex:1.0.3")

    // 微信小程序广告预算相关，引入可提升ecpm
    implementation("com.tencent.mm.opensdk:wechat-sdk-android:6.8.28")

    // 测试依赖
    testImplementation("junit:junit:4.13.2")
    androidTestImplementation("com.android.support.test:runner:1.0.2")
    androidTestImplementation("com.android.support.test.espresso:espresso-core:3.0.2")
}