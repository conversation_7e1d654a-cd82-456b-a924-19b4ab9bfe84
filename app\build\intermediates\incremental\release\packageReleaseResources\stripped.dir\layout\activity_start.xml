<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <!-- 背景图片 -->
    <ImageView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:src="@drawable/background1"
        android:scaleType="centerCrop"
        android:contentDescription="开始界面背景" />

    <!-- 虚化遮罩层 -->
    <View
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/start_overlay" />

    <!-- 内容容器 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:gravity="center"
        android:padding="32dp">

    <!-- 游戏标题图片 -->
    <ImageView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/title"
        android:layout_marginBottom="48dp"
        android:contentDescription="@string/title_content_description" />

    <!-- 游戏描述 -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/game_description"
        android:textSize="16sp"
        android:textColor="#ffffff"
        android:lineSpacingExtra="4dp"
        android:gravity="center"
        android:layout_marginBottom="64dp"
        android:layout_marginStart="32dp"
        android:layout_marginEnd="32dp"
        android:shadowColor="#000000"
        android:shadowDx="2"
        android:shadowDy="2"
        android:shadowRadius="4" />

    <!-- 开始游戏按钮 -->
    <Button
        android:id="@+id/startGameButton"
        android:layout_width="200dp"
        android:layout_height="60dp"
        android:text="@string/start_game"
        android:textSize="18sp"
        android:textColor="#ffffff"
        android:textStyle="bold"
        android:background="@drawable/button_black_frame"
        android:layout_marginBottom="16dp" />

    <!-- 音乐控制按钮 -->
    <Button
        android:id="@+id/musicToggleButton"
        android:layout_width="200dp"
        android:layout_height="60dp"
        android:text="@string/music_on"
        android:textSize="18sp"
        android:textColor="#ffffff"
        android:background="@drawable/button_black_frame"
        android:layout_marginBottom="16dp" />

    <!-- 语言选择按钮 -->
    <Button
        android:id="@+id/languageButton"
        android:layout_width="200dp"
        android:layout_height="60dp"
        android:text="@string/language"
        android:textSize="18sp"
        android:textColor="#ffffff"
        android:background="@drawable/button_black_frame"
        android:layout_marginBottom="16dp" />

    <!-- 退出游戏按钮 -->
    <Button
        android:id="@+id/exitGameButton"
        android:layout_width="200dp"
        android:layout_height="60dp"
        android:text="@string/exit_game"
        android:textSize="18sp"
        android:textColor="#ffffff"
        android:background="@drawable/button_black_frame"
        android:layout_marginBottom="32dp" />

    <!-- 版本信息 -->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/version"
        android:textSize="12sp"
        android:textColor="#888888"
        android:layout_marginTop="32dp" />

    </LinearLayout>

</FrameLayout>
